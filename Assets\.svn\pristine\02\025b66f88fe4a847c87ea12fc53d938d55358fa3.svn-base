
local M = {}

local C2GS = {}
local GS2C = {}
M.C2GS = C2GS
M.GS2C = GS2C

local C2GS_BY_NAME = {}
local GS2C_BY_NAME = {}
M.C2GS_BY_NAME = C2GS_BY_NAME
M.GS2C_BY_NAME = GS2C_BY_NAME

local C2GS_DEFINES = {}
--C2GS BEGIN

C2GS_DEFINES.login = {
    C2GSLoginAccount = 1001,
    C2GSLoginRole = 1002,
    C2GSCreateRole = 1003,
    C2GSSetInviteCode = 1004,
    C2GSLogoutByOneKey = 1005,
    C2GSChangeAccount = 1006,
    C2GSReLoginRole = 1007,
    C2GSQueryLogin = 1008,
}

C2GS_DEFINES.scene = {
    C2GSFlyToPos = 2001,
    C2GSTransfer = 2002,
    C2GSClickWorldMap = 2003,
    C2GSClickTrapMineMap = 2004,
    C2GSChangeSceneModel = 2006,
    C2GSSyncPosQueue = 2007,
}

C2GS_DEFINES.other = {
    C2GSHeartBeat = 3001,
    C2GSGMCmd = 3002,
    C2GSCallback = 3003,
    C2GSNotActive = 3004,
    C2GSBarrage = 3005,
    C2GSBigPacket = 3006,
    C2GSQueryClientUpdateRes = 3007,
    C2GSForceLeaveWar = 3008,
    C2GSClientSession = 3009,
    C2GSDoBackup = 3010,
    C2GSRequestPay = 3011,
    C2GSGMRequire = 3012,
    C2GSAnswerGM = 3013,
    C2GSQueryBack = 3014,
    C2GSSendXGToken = 3015,
}

C2GS_DEFINES.war = {
    C2GSWarSkill = 4001,
    C2GSWarNormalAttack = 4002,
    C2GSWarProtect = 4003,
    C2GSWarEscape = 4004,
    C2GSWarDefense = 4005,
    C2GSWarPrepareCommand = 4006,
    C2GSWarPartner = 4007,
    C2GSWarStop = 4008,
    C2GSWarStart = 4009,
    C2GSWarTarget = 4010,
    C2GSWarAutoFight = 4011,
    C2GSChangeAutoSkill = 4012,
    C2GSSolveKaji = 4013,
    C2GSEndFilmBout = 4014,
    C2GSSelectCmd = 4015,
    C2GSNextBoutStart = 4016,
    C2GSWarSetPlaySpeed = 4017,
    C2GSDebugPerform = 4018,
    C2GSWarBattleCommand = 4019,
    C2GSCleanWarBattleCommand = 4020,
    C2GSNextActionEnd = 4021,
}

C2GS_DEFINES.item = {
    C2GSItemUse = 5001,
    C2GSItemInfo = 5002,
    C2GSChooseItem = 5003,
    C2GSDeComposeItem = 5004,
    C2GSComposeItem = 5005,
    C2GSRecycleItem = 5006,
    C2GSFixEquip = 5007,
    C2GSPromoteEquipLevel = 5008,
    C2GSItemPrice = 5009,
    C2GSResetFuWen = 5010,
    C2GSSaveFuWen = 5011,
    C2GSEquipStrength = 5012,
    C2GSInlayGem = 5013,
    C2GSAddGemExp = 5014,
    C2GSFastAddGemExp = 5015,
    C2GSFastStrength = 5016,
    C2GSRecycleItemList = 5017,
    C2GSArrangeItem = 5018,
    C2GSUseFuWenPlan = 5019,
    C2GSReNameFuWen = 5020,
    C2GSCompoundItem = 5021,
    C2GSExChangeEquip = 5022,
    C2GSDeCompose = 5023,
    C2GSLockEquip = 5024,
    C2GSAddItemExtendSize = 5025,
    C2GSBuffStoneOp = 5026,
    C2GSInlayAllGem = 5027,
    C2GSComposeGem = 5028,
    C2GSUpgradeEquip = 5029,
    C2GSComposeEquip = 5030,
}

C2GS_DEFINES.player = {
    C2GSGetPlayerInfo = 6001,
    C2GSPlayerItemInfo = 6002,
    C2GSChangeSchool = 6003,
    C2GSUpvotePlayer = 6004,
    C2GSInitRoleName = 6005,
    C2GSRename = 6006,
    C2GSPlayerPK = 6007,
    C2GSWatchWar = 6008,
    C2GSLeaveWatchWar = 6009,
    C2GSPlayerTop4Partner = 6010,
    C2GSGamePushSetting = 6011,
    C2GSGameShare = 6012,
    C2GSChangeShape = 6013,
}

C2GS_DEFINES.task = {
    C2GSClickTask = 7001,
    C2GSTaskEvent = 7002,
    C2GSCommitTask = 7003,
    C2GSAbandonTask = 7004,
    C2GSAcceptTask = 7005,
    C2GSTaskItemChange = 7006,
    C2GSClickTaskInScene = 7007,
    C2GSSetTaskBarrage = 7008,
    C2GSGetTaskBarrage = 7009,
    C2GSEnterShow = 7010,
    C2GSSyncTraceInfo = 7011,
    C2GSAcceptSideTask = 7012,
    C2GSGetAchieveTaskReward = 7013,
    C2GSTriggerPatrolFight = 7014,
    C2GSFinishAchieveTask = 7015,
    C2GSFinishShimenTask = 7016,
    C2GSAcceptShimenTask = 7017,
}

C2GS_DEFINES.npc = {
    C2GSClickNpc = 8001,
    C2GSNpcRespond = 8002,
    C2GSClickConvoyNpc = 8003,
}

C2GS_DEFINES.openui = {
    C2GSOpenScheduleUI = 9001,
    C2GSEditBattlCommand = 9002,
    C2GSScheduleReward = 9003,
    C2GSOpenInterface = 9004,
    C2GSCloseInterface = 9005,
    C2GSClickSchedule = 9006,
}

C2GS_DEFINES.warehouse = {
    C2GSSwitchWareHouse = 10001,
    C2GSBuyWareHouse = 10002,
    C2GSRenameWareHouse = 10003,
    C2GSWareHouseWithStore = 10004,
    C2GSWareHouseWithDraw = 10005,
    C2GSWareHouseArrange = 10006,
}

C2GS_DEFINES.team = {
    C2GSCreateTeam = 11001,
    C2GSTeamInfo = 11002,
    C2GSApplyTeam = 11003,
    C2GSTeamApplyInfo = 11004,
    C2GSApplyTeamPass = 11005,
    C2GSClearApply = 11006,
    C2GSInviteTeam = 11007,
    C2GSInvitePass = 11008,
    C2GSClearInvite = 11009,
    C2GSShortLeave = 11010,
    C2GSLeaveTeam = 11011,
    C2GSKickOutTeam = 11012,
    C2GSBackTeam = 11013,
    C2GSTeamSummon = 11014,
    C2GSApplyLeader = 11015,
    C2GSSetLeader = 11016,
    C2GSTeamInviteInfo = 11017,
    C2GSTeamAutoMatch = 11018,
    C2GSTeamCancelAutoMatch = 11019,
    C2GSPlayerAutoMatch = 11020,
    C2GSPlayerCancelAutoMatch = 11021,
    C2GSGetTargetTeamInfo = 11022,
    C2GSCancelApply = 11023,
    C2GSTakeOverLeader = 11024,
    C2GSSetTeamTarget = 11025,
    C2GSInviteAll = 11026,
    C2GSLeaderSleep = 11027,
    C2GSChangeTeamSetting = 11028,
    C2GSLeaveLiLianTeam = 11029,
    C2GSGetMingleiTeamInfo = 11030,
    C2GSTrapmineTeamInfo = 11031,
    C2GSInviteFriendList = 11032,
    C2GSAwardWarBattleCommand = 11033,
    C2GSGetTargetMemList = 11034,
}

C2GS_DEFINES.chat = {
    C2GSChat = 12001,
    C2GSReportPlayer = 12002,
    C2GSHongBaoOption = 12003,
}

C2GS_DEFINES.achieve = {
    C2GSAchieveMain = 13001,
    C2GSAchieveDirection = 13002,
    C2GSAchieveReward = 13003,
    C2GSAchievePointReward = 13004,
    C2GSOpenPicture = 13005,
    C2GSPictureReward = 13006,
    C2GSCloseMainUI = 13007,
    C2GSSevenDayReward = 13008,
    C2GSSevenDayPointReward = 13009,
    C2GSOpenSevenDay = 13010,
    C2GSBuySevenDayGift = 13011,
    C2GSOpenSevenDayMain = 13012,
}

C2GS_DEFINES.skill = {
    C2GSLearnSkill = 14001,
    C2GSLearnCultivateSkill = 14002,
    C2GSWashSchoolSkill = 14003,
}

C2GS_DEFINES.store = {
    C2GSExchangeGold = 15001,
    C2GSExchangeSilver = 15002,
    C2GSNpcStoreBuy = 15003,
    C2GSOpenShop = 15004,
    C2GSOpenGold2Coin = 15005,
    C2GSGold2Coin = 15006,
    C2GSRefreshShop = 15007,
    C2GSExchangeTrapminePoint = 15008,
    C2GSStoreBuyList = 15009,
    C2GSBuyItemByCoin = 15010,
}

C2GS_DEFINES.state = {
    C2GSClickState = 16001,
}

C2GS_DEFINES.mail = {
    C2GSOpenMail = 17001,
    C2GSAcceptAttach = 17002,
    C2GSAcceptAllAttach = 17003,
    C2GSDeleteMail = 17004,
    C2GSDeleteAllMail = 17005,
}

C2GS_DEFINES.partner = {
    C2GSPartnerFight = 18001,
    C2GSPartnerSwitch = 18002,
    C2GSDrawWuLingCard = 18003,
    C2GSDrawWuHunCard = 18004,
    C2GSSwapPartnerEquip = 18005,
    C2GSUpgradePartnerStar = 18006,
    C2GSSetPartnerLock = 18007,
    C2GSRenamePartner = 18008,
    C2GSComposePartner = 18009,
    C2GSAwakePartner = 18010,
    C2GSComposeAwakeItem = 18011,
    C2GSPartnerEquipPlanSave = 18012,
    C2GSPartnerEquipPlanUse = 18013,
    C2GSAddPartnerComment = 18014,
    C2GSPartnerCommentInfo = 18015,
    C2GSUpVotePartnerComment = 18016,
    C2GSGetOuQi = 18017,
    C2GSPartnerPictureSwitchPos = 18018,
    C2GSUsePartnerItem = 18019,
    C2GSComposePartnerEquip = 18020,
    C2GSStrengthPartnerEquip = 18021,
    C2GSLockPartnerItem = 18022,
    C2GSSetFollowPartner = 18023,
    C2GSCloseDrawCardUI = 18024,
    C2GSOpenDrawCardUI = 18025,
    C2GSQuickWearPartnerEquip = 18026,
    C2GSUpGradePartner = 18027,
    C2GSOpenPartnerUI = 18028,
    C2GSComposePartnerStone = 18029,
    C2GSAddPartnerSkill = 18030,
    C2GSBuyPartnerBaseEquip = 18031,
    C2GSInlayPartnerStone = 18032,
    C2GSUpstarPartnerEquip = 18033,
    C2GSRecyclePartnerEquipList = 18034,
    C2GSUsePartnerSoul = 18035,
    C2GSUpgradePartnerSoul = 18036,
    C2GSUsePartnerSoulType = 18037,
    C2GSReceivePartnerChip = 18038,
    C2GSReDrawPartner = 18039,
    C2GSSwapPartnerEquipByPos = 18040,
    C2GSAddParSoulPlan = 18041,
    C2GSDelParSoulPlan = 18042,
    C2GSModifyParSoulPlan = 18043,
    C2GSParSoulPlanUse = 18044,
    C2GSExchangePartnerChip = 18045,
}

C2GS_DEFINES.house = {
    C2GSEnterHouse = 19001,
    C2GSLeaveHouse = 19002,
    C2GSHousePromoteFurniture = 19003,
    C2GSOpenWorkDesk = 19004,
    C2GSTalentShow = 19005,
    C2GSTalentDrawGift = 19006,
    C2GSHelpFriendWorkDesk = 19007,
    C2GSUseFriendWorkDesk = 19008,
    C2GSOpenExchangeUI = 19009,
    C2GSLovePartner = 19010,
    C2GSGivePartnerGift = 19011,
    C2GSTrainPartner = 19012,
    C2GSHouseSpeedFurniture = 19013,
    C2GSUnChainPartnerReward = 19014,
    C2GSRecievePartnerTrain = 19015,
    C2GSFriendHouseProfile = 19016,
    C2GSRecieveHouseCoin = 19017,
    C2GSAddPartnerGift = 19018,
    C2GSWorkDeskSpeedFinish = 19019,
}

C2GS_DEFINES.test = {
    C2GSTestBigPacket = 20001,
    C2GSTestOnlineAdd = 20002,
    C2GSCheckProxy = 20003,
}

C2GS_DEFINES.friend = {
    C2GSQueryFriendProfile = 21001,
    C2GSChatTo = 21002,
    C2GSAckChatFrom = 21003,
    C2GSApplyAddFriend = 21004,
    C2GSDelApply = 21005,
    C2GSFindFriend = 21006,
    C2GSFriendShield = 21007,
    C2GSFriendUnshield = 21008,
    C2GSAgreeApply = 21009,
    C2GSDeleteFriend = 21010,
    C2GSEditDocument = 21011,
    C2GSTakeDocunment = 21012,
    C2GSFriendSetting = 21013,
    C2GSRecommendFriends = 21014,
    C2GSBroadcastList = 21015,
    C2GSQueryFriendApply = 21016,
    C2GSNearByFriend = 21017,
    C2GSSetPhoto = 21018,
    C2GSSetShowPartner = 21019,
    C2GSGetShowPartnerInfo = 21020,
    C2GSSetShowEquip = 21021,
    C2GSGetEquipDesc = 21022,
    C2GSSimpleFriendList = 21023,
}

C2GS_DEFINES.link = {
    C2GSClickLink = 22001,
    C2GSLinkName = 22002,
    C2GSLinkItem = 22003,
    C2GSLinkPartner = 22004,
    C2GSEditCommonChat = 22005,
    C2GSGetCommonChat = 22006,
    C2GSLinkPlayer = 22007,
}

C2GS_DEFINES.huodong = {
    C2GSAnswerQuestion = 23001,
    C2GSOpenBossUI = 23002,
    C2GSQuestionEnterMember = 23003,
    C2GSEnterBossWar = 23004,
    C2GSCloseBossUI = 23005,
    C2GSPataOption = 23006,
    C2GSPataEnterWar = 23007,
    C2GSPataInvite = 23008,
    C2GSPataFrdInfo = 23009,
    C2GSGetEndlessList = 23010,
    C2GSEndlessPVEStart = 23011,
    C2GSQuestionEndReward = 23012,
    C2GSOpenEquipFBMain = 23013,
    C2GSGooutEquipFB = 23014,
    C2GSOpenEquipFB = 23015,
    C2GSEnterEquiFB = 23016,
    C2GSRefreshEquipFBScene = 23017,
    C2GSSetAutoEquipFuBen = 23018,
    C2GSOpenPEMain = 23019,
    C2GSPELock = 23020,
    C2GSPEStartTurn = 23021,
    C2GSEnterPEFuBen = 23022,
    C2GSStartTrapmine = 23023,
    C2GSCancelTrapmine = 23024,
    C2GSFindHuodongNpc = 23025,
    C2GSGetLoginReward = 23026,
    C2GSBuyMingleiTimes = 23027,
    C2GSBossRemoveDeadBuff = 23028,
    C2GSNpcFight = 23029,
    C2GSBuyEquipPlayCnt = 23030,
    C2GSBuyPEFuBen = 23031,
    C2GSFinishGetReward = 23032,
    C2GSTerrawarMine = 23033,
    C2GSTerrawarWorldRank = 23034,
    C2GSTerrawarOperate = 23035,
    C2GSTerrawarOrgRank = 23036,
    C2GSGetTerraInfo = 23037,
    C2GSTerrawarMain = 23038,
    C2GSAttackTerra = 23039,
    C2GSTerrawarMapInfo = 23040,
    C2GSSetGuard = 23041,
    C2GSAutoSetGuard = 23042,
    C2GSGetListInfo = 23043,
    C2GSHelpFirst = 23044,
    C2GSLeaveQueue = 23045,
    C2GSEnterYJFuben = 23046,
    C2GSBuyYJFuben = 23047,
    C2GSYJFubenOp = 23048,
    C2GSYJFubenView = 23049,
    C2GSYJFindNpc = 23050,
    C2GSBuyLingli = 23051,
    C2GSSocailDisplay = 23052,
    C2GSYJGuidanceReward = 23053,
    C2GSGuideMingleiWar = 23054,
    C2GSOpenFieldBossUI = 23055,
    C2GSFieldBossInfo = 23056,
    C2GSLeaveBattle = 23057,
    C2GSFieldBossPk = 23058,
    C2GSLeaveLegendFB = 23059,
    C2GSDailySign = 23060,
    C2GSGetOnlineGift = 23061,
    C2GSGetEquipFBReward = 23062,
    C2GSGetStarReward = 23063,
    C2GSGetExtraReward = 23064,
    C2GSSweepChapterFb = 23065,
    C2GSGiveUpConvoy = 23066,
    C2GSFightChapterFb = 23067,
    C2GSGetChapterInfo = 23068,
    C2GSStartOfflineTrapmine = 23069,
    C2GSCancelOfflineTrapmine = 23070,
    C2GSStarConvoy = 23071,
    C2GSRefreshTarget = 23072,
    C2GSOpenPEFuBenSchedule = 23073,
    C2GSCostSelectPEFuBen = 23074,
    C2GSChargeRewardGradeGift = 23075,
    C2GSEnterQuestionScene = 23076,
    C2GSApplyQuestionScene = 23077,
    C2GSAddFullBreedVal = 23078,
    C2GSGetBreedValRwd = 23079,
    C2GSLeaveQuestionScene = 23080,
    C2GSChargeCardReward = 23081,
    C2GSFightAttackMoster = 23082,
    C2GSShowConvoy = 23083,
    C2GSReceiveEnergy = 23084,
    C2GSLeaveWorldBossScene = 23085,
    C2GSFindWorldBoss = 23086,
    C2GSBuyBossBuff = 23087,
    C2GSPataTgReward = 23088,
    C2GSCancelSocailDisplay = 23089,
    C2GSWorldBoossRank = 23090,
    C2GSContinueTraining = 23091,
    C2GSSetTrainReward = 23092,
    C2GSOrgWarPK = 23093,
    C2GSOrgWarOption = 23094,
    C2GSOrgWarCanCelState = 23095,
    C2GSOrgWarGuide = 23096,
    C2GSHuntSoul = 23097,
    C2GSTerraAskForHelp = 23098,
    C2GSSaleSoulByOneKey = 23099,
    C2GSSetHuntAutoSale = 23100,
    C2GSPickUpSoulByOneKey = 23101,
    C2GSPickUpSoul = 23102,
    C2GSCallHuntNpc = 23103,
    C2GSQuitTrain = 23104,
    C2GSHirePartner = 23105,
    C2GSExpressResponse = 23106,
    C2GSChangeLoversTitle = 23107,
    C2GSSendExpress = 23108,
    C2GSTerrawarsLog = 23109,
    C2GSGoToHelpTerra = 23110,
    C2GSReceiveFreeGift = 23111,
    C2GSBuyCSItem = 23112,
    C2GSSweepEquipFB = 23113,
    C2GSDoMingleiCmd = 23114,
    C2GSReceiveAddCharge = 23115,
    C2GSReceiveDayCharge = 23116,
    C2GSGetTimeResumeReward = 23117,
    C2GSGetResumeRestoreReward = 23118,
    C2GSGetClearTerra = 23119,
}

C2GS_DEFINES.rank = {
    C2GSGetRankInfo = 24001,
    C2GSGetRankTop3 = 24002,
    C2GSMyRank = 24003,
    C2GSGetOrgRankInfo = 24004,
    C2GSRankUpvoteInfo = 24005,
    C2GSMyOrgRank = 24006,
    C2GSOpenRankUI = 24007,
    C2GSGetRankParInfo = 24008,
    C2GSGetRankMsattack = 24009,
    C2GSGetRankFirstInfo = 24010,
    C2GSPartnerRank = 24011,
}

C2GS_DEFINES.arena = {
    C2GSOpenArena = 25001,
    C2GSArenaMatch = 25002,
    C2GSArenaCancelMatch = 25003,
    C2GSConfigEqualArena = 25004,
    C2GSArenaHistory = 25005,
    C2GSArenaSetShowing = 25006,
    C2GSArenaReplayByRecordId = 25007,
    C2GSArenaOpenWatch = 25008,
    C2GSArenaDetailRank = 25009,
    C2GSArenaPraise = 25010,
    C2GSArenaReplayByPlayerId = 25011,
    C2GSEqualArenaMatch = 25012,
    C2GSSelectEqualArena = 25013,
    C2GSOpenEqualArena = 25014,
    C2GSEqualArenaCancelMatch = 25015,
    C2GSSyncSelectInfo = 25016,
    C2GSSetEqualArenaPartner = 25017,
    C2GSEqualArenaOpenWatch = 25018,
    C2GSEqualArenaSetShowing = 25019,
    C2GSEqualArenaHistory = 25020,
    C2GSGuaidArenaWar = 25021,
    C2GSTeamPVPCancelMatch = 25022,
    C2GSTeamPVPMatch = 25023,
    C2GSOpenTeamPVPRank = 25024,
    C2GSGetTeamPVPInviteList = 25025,
    C2GSTeamPVPToInviteList = 25026,
    C2GSTeamPVPLeaveScene = 25027,
    C2GSTeamPVPLeader = 25028,
    C2GSTeamPVPLeave = 25029,
    C2GSTeamPVPKickout = 25030,
    C2GSOpenClubArenaMain = 25031,
    C2GSClubArenaFight = 25032,
    C2GSClubArenaAddFightCnt = 25033,
    C2GSResetClubArena = 25034,
    C2GSOpenClubArenaInfo = 25035,
    C2GSSaveClubArenaLineup = 25036,
    C2GSShowClubArenaHistory = 25037,
    C2GSOpenClubArenaDefense = 25038,
    C2GSCleanClubArenaCD = 25039,
}

C2GS_DEFINES.title = {
    C2GSUseTitle = 26001,
    C2GSTitleInfoList = 26002,
}

C2GS_DEFINES.org = {
    C2GSOrgList = 27001,
    C2GSSearchOrg = 27002,
    C2GSApplyJoinOrg = 27003,
    C2GSMultiApplyJoinOrg = 27004,
    C2GSGetOrgInfo = 27005,
    C2GSCreateOrg = 27006,
    C2GSOrgMainInfo = 27007,
    C2GSOrgMemberList = 27008,
    C2GSOrgApplyList = 27009,
    C2GSOrgDealApply = 27010,
    C2GSUpdateAim = 27011,
    C2GSRejectAllApply = 27012,
    C2GSOrgSetPosition = 27013,
    C2GSLeaveOrg = 27014,
    C2GSSpreadOrg = 27015,
    C2GSKickMember = 27016,
    C2GSInvited2Org = 27017,
    C2GSDealInvited2Org = 27018,
    C2GSSetApplyLimit = 27019,
    C2GSUpdateFlagID = 27020,
    C2GSGetAim = 27021,
    C2GSBanChat = 27022,
    C2GSGiveOrgWish = 27023,
    C2GSOrgSignReward = 27024,
    C2GSOrgWish = 27025,
    C2GSOrgWishList = 27026,
    C2GSDoneOrgBuild = 27027,
    C2GSSpeedOrgBuild = 27028,
    C2GSOpenOrgRedPacket = 27029,
    C2GSDrawOrgRedPacket = 27030,
    C2GSOrgBuild = 27031,
    C2GSOrgRedPacket = 27032,
    C2GSLeaveOrgWishUI = 27033,
    C2GSOrgLog = 27034,
    C2GSPromoteOrgLevel = 27035,
    C2GSOrgRecruit = 27036,
    C2GSClickSpreadOrg = 27037,
    C2GSOpenOrgFBUI = 27038,
    C2GSClickOrgFBBoss = 27039,
    C2GSRestOrgFuBen = 27040,
    C2GSOrgOnlineCount = 27041,
    C2GSGiveOrgEquipWish = 27042,
    C2GSOrgEquipWish = 27043,
    C2GSOrgSendMail = 27044,
    C2GSJoinOrgBySpread = 27045,
    C2GSOrgQQAction = 27046,
    C2GSOrgRename = 27047,
}

C2GS_DEFINES.teach = {
    C2GSGetTaskReward = 28001,
    C2GSGetProgressReward = 28002,
    C2GSFinishGuidance = 28003,
    C2GSClearGuidance = 28004,
}

C2GS_DEFINES.handbook = {
    C2GSEnterName = 29001,
    C2GSRepairDraw = 29002,
    C2GSUnlockBook = 29003,
    C2GSUnlockChapter = 29004,
    C2GSReadChapter = 29005,
    C2GSOpenBookChapter = 29006,
    C2GSCloseHandBookUI = 29007,
}

C2GS_DEFINES.image = {
    C2GSGetImages = 30001,
    C2GSAddImage = 30002,
}

C2GS_DEFINES.travel = {
    C2GSAcceptFrdTravelRwd = 31001,
    C2GSGetFrdTravelInfo = 31002,
    C2GSInviteTravel = 31003,
    C2GSSetPartnerTravelPos = 31004,
    C2GSStopTravel = 31005,
    C2GSStartTravel = 31006,
    C2GSAcceptTravelRwd = 31007,
    C2GSCancelSpeedTravel = 31008,
    C2GSSetFrdPartnerTravel = 31009,
    C2GSClearTravelInvite = 31010,
    C2GSDelTravelInvite = 31011,
    C2GSStopTravelCard = 31012,
    C2GSShowTravelCard = 31013,
    C2GSStartTravelCard = 31014,
    C2GSFirstOpenTraderUI = 31015,
    C2GSQueryTravelInvite = 31016,
}

C2GS_DEFINES.minigame = {
    C2GSMiniGameOp = 32001,
    C2GSGameCardEnd = 32002,
}

C2GS_DEFINES.fuli = {
    C2GSChargeReward = 33001,
    C2GSSetBackPartner = 33002,
    C2GSGetBackPartnerInfo = 33003,
    C2GSGetRewardBack = 33004,
    C2GSBuyFuliPointItem = 33005,
    C2GSGetFuliPointInfo = 33006,
    C2GSGiveLuckDraw = 33007,
    C2GSGetLuckDrawInfo = 33008,
    C2GSStartLuckDraw = 33009,
    C2GSReceiveFirstCharge = 33010,
    C2GSRedeemcode = 33011,
    C2GSOpenChargeBackUI = 33012,
}
--C2GS END

local GS2C_DEFINES = {}
--GS2C BEGIN

GS2C_DEFINES.login = {
    GS2CHello = 1001,
    GS2CLoginError = 1002,
    GS2CLoginAccount = 1003,
    GS2CLoginRole = 1004,
    GS2CCreateRole = 1005,
    GS2CCheckInviteCodeResult = 1006,
    GS2CSetInviteCodeResult = 1007,
    GS2CQueryLogin = 1008,
    GS2CLoginErrorNotify = 1009,
}

GS2C_DEFINES.scene = {
    GS2CShowScene = 2001,
    GS2CEnterScene = 2002,
    GS2CEnterAoiBlock = 2003,
    GS2CLeaveAoi = 2004,
    GS2CSyncAoi = 2005,
    GS2CEnterAoiPos = 2006,
    GS2CAutoFindPath = 2007,
    GS2CSceneCreateTeam = 2008,
    GS2CSceneRemoveTeam = 2009,
    GS2CSceneUpdateTeam = 2010,
    GS2CSceneModel = 2011,
    GS2CSyncPosQueue = 2012,
    GS2CSTrunBackPos = 2013,
}

GS2C_DEFINES.other = {
    GS2CHeartBeat = 3001,
    GS2CGMMessage = 3002,
    GS2CBarrage = 3003,
    GS2CBigPacket = 3004,
    GS2CClientUpdateResVersion = 3005,
    GS2CClientUpdateRes = 3006,
    GS2CClientUpdateCode = 3007,
    GS2CSessionResponse = 3008,
    GS2CShowVoice = 3009,
    GS2CDoBackup = 3010,
    GS2CPayInfo = 3011,
    GS2CMergePacket = 3012,
    GS2CQRCScanSuccess = 3013,
    GS2CQRCInvalid = 3014,
    GS2CQRCToken = 3015,
    GS2CQRCAccountInfo = 3016,
    GS2CGMRequireInfo = 3017,
    GS2CAnswerGMInfo = 3018,
    GS2CAnswerBack = 3019,
    GS2CClosePay = 3020,
}

GS2C_DEFINES.war = {
    GS2CShowWar = 4001,
    GS2CWarResult = 4002,
    GS2CWarBoutStart = 4003,
    GS2CWarBoutEnd = 4004,
    GS2CWarAddWarrior = 4005,
    GS2CWarDelWarrior = 4006,
    GS2CWarNormalAttack = 4007,
    GS2CWarSkill = 4008,
    GS2CWarProtect = 4009,
    GS2CWarEscape = 4010,
    GS2CWarDamage = 4011,
    GS2CWarWarriorStatus = 4012,
    GS2CWarGoback = 4013,
    GS2CWarBuffBout = 4014,
    GS2CWarPasssiveSkill = 4015,
    GS2CPlayerWarriorEnter = 4016,
    GS2CWarConfig = 4017,
    GS2CWarCommand = 4018,
    GS2CWarSpeed = 4019,
    GS2CWarAction = 4020,
    GS2CWarStatus = 4021,
    GS2CWarSkillCD = 4022,
    GS2CWarSP = 4023,
    GS2CWarEndUI = 4024,
    GS2CConfigFinish = 4025,
    GS2CWarTarget = 4026,
    GS2CWarFloat = 4027,
    GS2CSwitchPos = 4028,
    GS2CWarWave = 4029,
    GS2CShowWarSkill = 4030,
    GS2CWarNotify = 4031,
    GS2CSelectCmd = 4032,
    GS2CEnterWar = 4033,
    GS2CWarSetPlaySpeed = 4034,
    GS2CWarBattleCmd = 4035,
    GS2CWarChapterInfo = 4036,
    GS2CActionEnd = 4037,
    GS2CActionStart = 4039,
}

GS2C_DEFINES.item = {
    GS2CLoginItem = 5001,
    GS2CAddItem = 5002,
    GS2CDelItem = 5003,
    GS2CItemAmount = 5004,
    GS2CItemQuickUse = 5005,
    GS2CItemExtendSize = 5006,
    GS2CEquipLast = 5007,
    GS2CFuWenInfo = 5008,
    GS2CItemPrice = 5009,
    GS2CRefreshPartnerEquipInfo = 5010,
    GS2CComposePartnerEquip = 5011,
    GS2CClientShowReward = 5012,
    GS2CFuWenPlanName = 5013,
    GS2CCompoundSuccess = 5014,
    GS2CExchangeEquip = 5015,
    GS2CDeComposeSuccess = 5016,
    GS2CUpdateBuffItem = 5017,
    GS2CRemoveBuffItem = 5018,
    GS2CAddBuffItem = 5019,
    GS2CRefreshPartnerEquip = 5020,
    GS2CRefreshItemApply = 5021,
    GS2CRefreshPartnerSoul = 5022,
    GS2CLockItem = 5023,
    GS2CGemCompose = 5024,
}

GS2C_DEFINES.player = {
    GS2CPropChange = 6001,
    GS2CServerGradeInfo = 6002,
    GS2CGetPlayerInfo = 6003,
    GS2CGetSecondProp = 6004,
    GS2CPlayerItemInfo = 6005,
    GS2CUpvotePlayer = 6006,
    GS2COpenPkTipsWnd = 6007,
    GS2CPlayerTop4Partner = 6008,
    GS2CTodayInfo = 6009,
    GS2CInitRoleNameResult = 6010,
    GS2CGamePushSetting = 6011,
    GS2CGameShare = 6012,
    GS2CShapeList = 6013,
}

GS2C_DEFINES.task = {
    GS2CLoginTask = 7001,
    GS2CAddTask = 7002,
    GS2CDelTask = 7003,
    GS2CDialog = 7004,
    GS2CRefreshTask = 7005,
    GS2CRemoveTaskNpc = 7006,
    GS2CRefreshTaskInfo = 7007,
    GS2CContinueClickTask = 7008,
    GS2CSendTaskBarrage = 7009,
    GS2CRefreshPartnerTask = 7010,
    GS2CRefreshAchieveTask = 7011,
    GS2CDelAchieveTask = 7012,
    GS2CAddAchieveTask = 7013,
    GS2CLoginAchieveTask = 7014,
    GS2CStarPatrol = 7015,
    GS2CUpdateShimenStatus = 7016,
    GS2CStartEscort = 7017,
    GS2CFindTaskPath = 7018,
    GS2CRemoveTeamNpc = 7019,
}

GS2C_DEFINES.npc = {
    GS2CNpcSay = 8001,
    GS2CNpcFightInfoList = 8002,
}

GS2C_DEFINES.openui = {
    GS2CLoadUI = 9001,
    GS2CPopTaskItem = 9002,
    GS2CShortWay = 9003,
    GS2CConfirmUI = 9004,
    GS2CSchedule = 9005,
    GS2CLoginSchedule = 9006,
    GS2CGetScheduleReward = 9007,
    GS2CHongBaoUI = 9008,
    GS2COpenShop = 9009,
    GS2CRefreshSchedule = 9010,
    GS2CXunLuo = 9011,
    GS2COpenCultivateUI = 9012,
    GS2CCloseConfirmUI = 9013,
    GS2CItemShortWay = 9014,
    GS2CShowOpenBtn = 9015,
    GS2COpenLotteryUI = 9016,
    GS2COpenView = 9017,
    GS2CNewDay = 9018,
    GS2CShowItem = 9019,
    GS2CCloseWarResultUI = 9020,
    GS2CTeamEnterGameUI = 9021,
    GS2CTeamEnterGameUIClose = 9022,
    GS2CUpdateTeamEnterGameUI = 9023,
    GS2COpenScheuleUI = 9024,
    GS2CCloseScheuleUI = 9025,
}

GS2C_DEFINES.notify = {
    GS2CNotify = 10001,
}

GS2C_DEFINES.warehouse = {
    GS2CWareHouseInfo = 11001,
    GS2CRefreshWareHouse = 11002,
    GS2CWareHouseName = 11003,
    GS2CAddWareHouseItem = 11004,
    GS2CDelWareHouseItem = 11005,
    GS2CWHItemArrange = 11006,
    GS2CWHItemAmount = 11007,
}

GS2C_DEFINES.team = {
    GS2CAddTeam = 12001,
    GS2CDelTeam = 12002,
    GS2CAddTeamMember = 12003,
    GS2CRefreshTeamStatus = 12004,
    GS2CTeamApplyInfo = 12005,
    GS2CDelTeamApplyInfo = 12006,
    GS2CInviteInfo = 12007,
    GS2CRemoveInvite = 12008,
    GS2CAddTeamApplyInfo = 12009,
    GS2CAddInviteInfo = 12010,
    GS2CTargetInfo = 12011,
    GS2CNotifyAutoMatch = 12012,
    GS2CTargetTeamInfoList = 12013,
    GS2CCountAutoMatch = 12014,
    GS2CRefreshMemberInfo = 12015,
    GS2CTargetTeamInfo = 12016,
    GS2CMemPartnerInfoChange = 12017,
    GS2CPlayerMatchTargetInfo = 12018,
    GS2CChangePosInfo = 12019,
    GS2CPlayerMatchSuccess = 12020,
    GS2CCancelTeamAutoMatch = 12021,
    GS2CTeamMingleiInfo = 12022,
    GS2CTeamTrapmineInfo = 12023,
    GS2CInviteFriendList = 12024,
    GS2CCreateTeam = 12025,
    GS2CTargetMemList = 12026,
}

GS2C_DEFINES.chat = {
    GS2CChat = 13001,
    GS2CSysChat = 13002,
    GS2CConsumeMsg = 13003,
    GS2CHongBaoInfo = 13004,
    GS2CPlayerHBInfo = 13005,
    GS2CReportResult = 13006,
}

GS2C_DEFINES.achieve = {
    GS2CAchieveMain = 14001,
    GS2CAchieveDone = 14002,
    GS2CAchieveRedDot = 14003,
    GS2CAchieveDirection = 14004,
    GS2CAchieveDegree = 14005,
    GS2CPictureDegree = 14006,
    GS2CPictureRedDot = 14007,
    GS2CPictureInfo = 14008,
    GS2CSevenDayDegree = 14009,
    GS2CSevenDayRedDot = 14010,
    GS2CSevenDayMain = 14011,
    GS2CSevenDayInfo = 14012,
    GS2CSevenDayBuy = 14013,
}

GS2C_DEFINES.skill = {
    GS2CLoginSkill = 15001,
    GS2CRefreshSkill = 15002,
    GS2CRefreshCultivateSKill = 15003,
}

GS2C_DEFINES.state = {
    GS2CLoginState = 16001,
    GS2CAddState = 16002,
    GS2CRemoveState = 16003,
    GS2CRefreshState = 16004,
}

GS2C_DEFINES.mail = {
    GS2CLoginMail = 17001,
    GS2CMailInfo = 17002,
    GS2CDelMail = 17003,
    GS2CAddMail = 17004,
    GS2CDelAttach = 17005,
    GS2CMailOpened = 17006,
}

GS2C_DEFINES.partner = {
    GS2CAddPartner = 18001,
    GS2CDelPartner = 18002,
    GS2CLoginPartner = 18003,
    GS2CRefreshFightPartner = 18004,
    GS2CDrawCardUI = 18005,
    GS2CDrawCardResult = 18006,
    GS2CPartnerPropChange = 18007,
    GS2CRefreshPartnerChip = 18008,
    GS2CRefreshAwakeItem = 18009,
    GS2CPartnerCommentInfo = 18010,
    GS2CAwakePartner = 18011,
    GS2CLoginPartnerList = 18012,
    GS2CPartnerPicturePosList = 18013,
    GS2CAddPartnerList = 18014,
    GS2CShowPartnerSkin = 18015,
    GS2CShowNewPartnerUI = 18016,
    GS2CPartnerStarUpStar = 18017,
    GS2COpenPartnerSkillUI = 18018,
    GS2CComposePartner = 18019,
    GS2CUpGradePartner = 18020,
    GS2COpenPartnerUI = 18021,
    GS2CComposePartnerStone = 18022,
    GS2CDelParSoulPlan = 18023,
    GS2CAddParSoulPlan = 18024,
    GS2CLoginParSoulPlan = 18025,
    GS2CUpdateParSoulPlan = 18026,
    GS2CExchangePartnerChip = 18027,
}

GS2C_DEFINES.house = {
    GS2CEnterHouse = 19001,
    GS2CFurnitureInfo = 19002,
    GS2CPartnerInfo = 19003,
    GS2COpenWorkDesk = 19004,
    GS2CRefreshWorkDesk = 19005,
    GS2CPartnerExchangeUI = 19006,
    GS2CHouseAddItem = 19007,
    GS2CHouseDelItem = 19008,
    GS2CHouseItemAmount = 19009,
    GS2CRefreshHouseWarm = 19010,
    GS2CRefreshTalent = 19011,
    GS2CFriendHouseProfile = 19012,
    GS2CGivePartnerGift = 19013,
    GS2CAddHousePartner = 19014,
    GS2CRefreshHouseBuff = 19015,
    GS2CRecieveHouseCoin = 19016,
    GS2CUseFriendWorkDesk = 19017,
}

GS2C_DEFINES.test = {
    GS2CTestBigPacket = 20001,
    GS2CTestOnlineUpdate = 20002,
    GS2CTestOnlineAdd = 20003,
    GS2CTestEncode = 20004,
    GS2CTestNotice = 20005,
    GS2CCheckProxy = 20006,
    GS2CCheckProxyMerge = 20007,
}

GS2C_DEFINES.store = {
    GS2CNpcStoreInfo = 21001,
    GS2COpenGold2Coin = 21002,
    GS2CGold2Coin = 21003,
    GS2CStoreRefresh = 21004,
    GS2CPayForColorCoinInfo = 21005,
    GS2CRefreshChargeColorCoin = 21006,
}

GS2C_DEFINES.friend = {
    GS2CSendSimpleInfo = 22001,
    GS2CLoginFriend = 22005,
    GS2CAddFriend = 22006,
    GS2CDelFriend = 22007,
    GS2CAckChatTo = 22009,
    GS2CChatFrom = 22010,
    GS2CRecommendFriends = 22011,
    GS2CStrangerProfile = 22012,
    GS2CFriendShield = 22013,
    GS2CFriendUnshield = 22014,
    GS2COnlineStatus = 22015,
    GS2CSendDocument = 22017,
    GS2CFriendSetting = 22018,
    GS2CApplyList = 22019,
    GS2CFriendDegree = 22020,
    GS2CFriendGrade = 22021,
    GS2CApplyProfile = 22022,
    GS2CSearchFriend = 22023,
    GS2CSysFriendChat = 22024,
    GS2CNearbyFriend = 22025,
    GS2CSendFriendPartnerInfo = 22026,
    GS2CSendFriendEquipInfo = 22027,
}

GS2C_DEFINES.link = {
    GS2CreateCLink = 23001,
    GS2CLinkInfo = 23002,
    GS2CSendCommonChat = 23003,
}

GS2C_DEFINES.huodong = {
    GS2CNotifyQuestion = 24001,
    GS2CQuestionInfo = 24002,
    GS2CAnswerResult = 24003,
    GS2CScoreRankInfoList = 24004,
    GS2CBossMain = 24005,
    GS2CScoreInfoChange = 24006,
    GS2CBossHPNotify = 24007,
    GS2CPataUIInfo = 24008,
    GS2CSweepLevel = 24009,
    GS2CPataInviteInfo = 24010,
    GS2CPataFrdPtnInfo = 24011,
    GS2CPataRwItemUI = 24012,
    GS2CPataWarUI = 24013,
    GS2CSweepInfo = 24014,
    GS2CEndlessFightList = 24015,
    GS2CWarRingInfo = 24016,
    GS2CQuestionEndReward = 24017,
    GS2CBossWarEnd = 24018,
    GS2CTgRewardResult = 24019,
    GS2CEndlessWarEnd = 24020,
    GS2CHuoDongStatus = 24021,
    GS2COpenEquipFubenMain = 24022,
    GS2COpenEquiFuben = 24023,
    GS2CRefreshEquipFBfloor = 24024,
    GS2CRefreshEquipFBScene = 24025,
    GS2CEquipFBWarResult = 24026,
    GS2CWorldBossRank = 24027,
    GS2CShowNormalReward = 24028,
    GS2CRemoveHuodongNpc = 24029,
    GS2CLoginHuodongInfo = 24030,
    GS2CCreateHuodongNpc = 24031,
    GS2CTreasureNormalResult = 24032,
    GS2CEndFBScene = 24033,
    GS2CShowPlayBoyWnd = 24034,
    GS2CShowCaiQuanWnd = 24035,
    GS2CGetLegendTeam = 24036,
    GS2CCaiQuanGameEnd = 24037,
    GS2CShowCaiQuanResult = 24038,
    GS2CNpcBeenDefeate = 24039,
    GS2CMainPEFuben = 24040,
    GS2CPELockResult = 24041,
    GS2CPETurnResult = 24042,
    GS2CTrapmineStatus = 24043,
    GS2CTrapmineTotalReward = 24044,
    GS2CGetMingleiTeam = 24045,
    GS2CLoginRewardInfo = 24046,
    GS2CLoginRewardDay = 24047,
    GS2CShowBuyTimeWnd = 24048,
    GS2CWorldBossLeftTime = 24049,
    GS2CShowLoginRewardUI = 24050,
    GS2CTerrawarMapInfo = 24051,
    GS2CTerraWarsMainUI = 24052,
    GS2CMyTerraInfo = 24053,
    GS2CTerraInfo = 24054,
    GS2CSetGuard = 24055,
    GS2CSetGuardSuccess = 24056,
    GS2CGiveUpSuccess = 24057,
    GS2CListInfo = 24058,
    GS2CMainYJFuben = 24059,
    GS2CEnterYJFuben = 24060,
    GS2CLeaveYJFuben = 24061,
    GS2CYJFubenView = 24062,
    GS2CFieldBossHPNotify = 24063,
    GS2CSocialDisplayInfo = 24064,
    GS2CFieldBossBattle = 24065,
    GS2CFieldBossMainUI = 24066,
    GS2CFieldBossInfo = 24067,
    GS2CLeaveFieldBoss = 24068,
    GS2CStartPick = 24069,
    GS2CNewFieldBoss = 24070,
    GS2CFieldBossDied = 24071,
    GS2CFieldBossAttack = 24072,
    GS2CTerraQueueStatus = 24073,
    GS2CTerraReadyInfo = 24074,
    GS2CTerraWarState = 24075,
    GS2CTerrawarsCountDown = 24076,
    GS2CDailySignInfo = 24077,
    GS2CRefreshMinglei = 24078,
    GS2COnlineGiftStatus = 24079,
    GS2COnlineGift = 24080,
    GS2CChapterOpen = 24081,
    GS2CUpdateChapterExtraReward = 24082,
    GS2CLoginChapterInfo = 24083,
    GS2CUpdateChapterTotalStar = 24084,
    GS2CSweepChapterReward = 24085,
    GS2CChapterInfo = 24086,
    GS2CUpdateChapter = 24087,
    GS2CChapterFbWinUI = 24088,
    GS2CUpdateConvoyInfo = 24089,
    GS2CPEFuBenSchedule = 24090,
    GS2CChargeGiftInfo = 24091,
    GS2CChargeRefreshUnit = 24092,
    GS2CShowConvoyMainUI = 24093,
    GS2CSceneAnswerList = 24094,
    GS2CQtionSceneStatus = 24095,
    GS2CChargeCard = 24096,
    GS2CPopBuyMonthCard = 24097,
    GS2CAddAttackMoster = 24098,
    GS2CMultiAttackMoster = 24099,
    GS2CRushRankInfo = 24100,
    GS2CDelAttackMoster = 24101,
    GS2COpenMsAttackUI = 24102,
    GS2CTramineOfflineInfo = 24103,
    GS2CLoginTrapmine = 24104,
    GS2CMSBossWarEnd = 24105,
    GS2CMSBossHPNotify = 24106,
    GS2CInWorldBossScene = 24107,
    GS2CLeaveWorldBossScene = 24108,
    GS2CTrainInfo = 24109,
    GS2CTrainRewardSwitch = 24110,
    GS2COrgWarLeaveSc = 24111,
    GS2COrgWarEnterSc = 24112,
    GS2COrgWarTip = 24113,
    GS2COrgWarState = 24114,
    GS2COrgWarUI = 24115,
    GS2COrgWarList = 24116,
    GS2CFastCreateTeam = 24117,
    GS2CRefreshTrainTimes = 24118,
    GS2CHuntSuccess = 24119,
    GS2CHuntInfo = 24120,
    GS2CDelHuntSoul = 24121,
    GS2CAddHuntSoul = 24122,
    GS2CRefreshHireInfo = 24123,
    GS2CQuitTrain = 24124,
    GS2CExpressPop = 24125,
    GS2CLoversTitleUI = 24126,
    GS2CExpressWaitUI = 24127,
    GS2CExpressResult = 24128,
    GS2CExpressEnterUI = 24129,
    GS2CExpressOver = 24130,
    GS2CHeroBoxMainUI = 24131,
    GS2CExpressAction = 24132,
    GS2CTerrawarsLog = 24133,
    GS2CHeroBoxRecord = 24134,
    GS2CGradeGiftInfo = 24135,
    GS2CWorldBossDeath = 24136,
    GS2CUpdateCSBuyTimes = 24137,
    GS2CChargeScore = 24138,
    GS2CChargeRewrad = 24139,
    GS2CRefreshChargeReward = 24140,
    GS2COrgWarRevive = 24141,
    GS2CSweepEquipFBResult = 24142,
    GS2COneRMBGift = 24143,
    GS2COpenMingleiUI = 24144,
    GS2CHDAddChargeProgress = 24145,
    GS2CHDAddChargeInfo = 24146,
    GS2CHDUpdateAddCharge = 24147,
    GS2CHDUpdateDayCharge = 24148,
    GS2CCloseHuodong = 24149,
    GS2CHDDayChargeProgress = 24150,
    GS2CHDDayChargeInfo = 24151,
    GS2CRefreshMingleiTime = 24152,
    GS2CRefreshTimeResume = 24153,
    GS2CUpdateOneRMBGift = 24154,
    GS2CTimeResumeInfo = 24155,
    GS2CRankBack = 24156,
    GS2CMSBossTip = 24157,
    GS2CResumeRestore = 24158,
    GS2CRefreshResumeRestore = 24159,
}

GS2C_DEFINES.rank = {
    GS2CGetRankInfo = 25001,
    GS2CGetRankTop3 = 25002,
    GS2CMyRank = 25003,
    GS2CRankUpvoteInfo = 25004,
    GS2CClearAllRankData = 25005,
    GS2CRankPartnerInfo = 25006,
    GS2CRankMsattackInfo = 25007,
    GS2CMsattackMyInfo = 25008,
    GS2CRankFirstInfoList = 25009,
    GS2CPartnerRank = 25010,
}

GS2C_DEFINES.arena = {
    GS2COpenArena = 26001,
    GS2CArenaMatch = 26002,
    GS2CArenaFight = 26003,
    GS2CArenaHistory = 26004,
    GS2CArenaStartMath = 26005,
    GS2CArenaSetShowing = 26006,
    GS2CArenaFightResult = 26007,
    GS2CArenaOpenWatch = 26008,
    GS2CArenaLeftTime = 26009,
    GS2CShowArenaWarConfig = 26010,
    GS2CArenaReplay = 26011,
    GS2COpenEqualArena = 26012,
    GS2CConfigEqualArena = 26013,
    GS2CShowEqualArenaWarConfig = 26014,
    GS2CEqualArenaMatch = 26015,
    GS2CEqualArenaFight = 26016,
    GS2CEqualArenaFightResult = 26017,
    GS2CEqualArenaStartMath = 26018,
    GS2CSyncSelectInfo = 26019,
    GS2CSelectEqualArena = 26020,
    GS2CSetEqualArenaParner = 26021,
    GS2CEqualArenaConfigDone = 26022,
    GS2CEqualArenaHistory = 26023,
    GS2CEqaulArenaSetShowing = 26024,
    GS2CEqualArenaOpenWatch = 26025,
    GS2CCloseEqualArenaUI = 26026,
    GS2CSyncConfig = 26027,
    GS2CTeamPVPSceneInfo = 26028,
    GS2CTeamPVPRank = 26029,
    GS2CTeamPVPMatch = 26030,
    GS2CTeamPVPStartMath = 26031,
    GS2CLeaveTeamPVPScene = 26032,
    GS2CShowTeamPVPWarConfig = 26033,
    GS2CTeamPVPFightResult = 26034,
    GS2CShowTeamPVPInvite = 26035,
    GS2CRefreshTeamArenaLeftTime = 26036,
    GS2CEqualArenaLeftTime = 26037,
    GS2CClubArenaMainUI = 26038,
    GS2CClubArenaInfo = 26039,
    GS2CClubArenaDefenseLineUp = 26040,
    GS2CClubArenaHistory = 26041,
    GS2CShowClubArenaWarConfig = 26042,
    GS2CClubArenaFightResult = 26043,
    GS2CEqualArenaStartWarFail = 26044,
}

GS2C_DEFINES.title = {
    GS2CTitleInfoList = 27001,
    GS2CAddTitleInfo = 27002,
    GS2CRemoveTitles = 27003,
    GS2CUpdateTitleInfo = 27005,
}

GS2C_DEFINES.org = {
    GS2COrgList = 28001,
    GS2CSearchOrg = 28002,
    GS2CApplyJoinOrg = 28003,
    GS2CGetOrgInfo = 28004,
    GS2COrgMainInfo = 28005,
    GS2COrgMemberInfo = 28006,
    GS2COrgApplyList = 28007,
    GS2COrgDealApply = 28008,
    GS2CUpdateAimResult = 28009,
    GS2CRejectAllApplyResult = 28010,
    GS2CSetPositionResult = 28011,
    GS2CDelMember = 28012,
    GS2CSpreadOrgResult = 28013,
    GS2CInvited2Org = 28014,
    GS2CSetApplyLimitResult = 28015,
    GS2COrgAim = 28016,
    GS2CUpdateFlagID = 28017,
    GS2CUpdateOrgInfo = 28018,
    GS2CRefreshOrgMember = 28019,
    GS2COrgWishList = 28020,
    GS2COrgRedPacket = 28021,
    GS2CDrawOrgRedPacket = 28022,
    GS2COrgLog = 28023,
    GS2COrgFBBossList = 28024,
    GS2COrgFBBossHpNotify = 28025,
    GS2COrgOnlineCount = 28026,
    GS2COrgFuBenWarEnd = 28027,
    GS2CLeaveOrgTips = 28028,
    GS2CMailResult = 28029,
    GS2CControlRedPacketUI = 28030,
    GS2COpenOrgMainUI = 28031,
    GS2COrgQQAction = 28032,
}

GS2C_DEFINES.teach = {
    GS2CTeachProgress = 29001,
    GS2CGuidanceInfo = 29002,
}

GS2C_DEFINES.handbook = {
    GS2CLoginBookList = 30001,
    GS2CPartnerProgress = 30002,
    GS2CBookInfoChange = 30003,
    GS2CPartnerEquipProgress = 30004,
    GS2CHandBookRedPoint = 30005,
}

GS2C_DEFINES.image = {
    GS2CImages = 31001,
}

GS2C_DEFINES.travel = {
    GS2CLoginTravelPartner = 32001,
    GS2CFrdTravelPartnerInfo = 32002,
    GS2CDelTravelInvite = 32003,
    GS2CTravelPartnerPos = 32004,
    GS2CTravelPartnerInfo = 32005,
    GS2CTravelItemInfo = 32006,
    GS2CAddTravelInvite = 32007,
    GS2CMineTravelPartnerInfo = 32008,
    GS2CAddTravelContent = 32009,
    GS2CDelFrdTravel = 32010,
    GS2CDelMineTravel = 32011,
    GS2CClearTravelInvite = 32012,
    GS2CDelTravelItem = 32013,
    GS2CFrdTravelList = 32014,
    GS2CClearTravelContent = 32015,
    GS2CRefreshMineInvite = 32016,
    GS2CTravelShowCardInfo = 32017,
    GS2CFirstOpenTraderUI = 32018,
    GS2CInviteInfoList = 32019,
    GS2CUpdateTravelPartner = 32020,
    GS2CRefreshTravelCardGrid = 32021,
    GS2CRemoveTravelGame = 32022,
    GS2CTravelGameResult = 32023,
}

GS2C_DEFINES.minigame = {
    GS2COpenCardInfo = 33001,
    GS2CGameCardStart = 33002,
    GS2CMemCardInfo = 33003,
    GS2CFinalMemCardInfo = 33004,
}

GS2C_DEFINES.fuli = {
    GS2CFuliReddot = 34001,
    GS2CHistoryCharge = 34002,
    GS2CBackPartnerInfo = 34003,
    GS2CSetBackResult = 34004,
    GS2CFirstChargeUI = 34005,
    GS2CRefreshRewardBack = 34006,
    GS2CFuliPointUI = 34007,
    GS2CLuckDrawPos = 34008,
    GS2CLuckDrawUI = 34009,
    GS2CCrazyHappyUI = 34010,
    GS2CFuliPoint = 34011,
    GS2CLuckDrawCnt = 34012,
    GS2CChargeBackUI = 34013,
    GS2CFuliTime = 34014,
}
--GS2C END

for k, v in pairs(C2GS_DEFINES) do
    for k2, v2 in pairs(v) do
        assert(not C2GS[v2], string.format("netdefines C2GS error %s %s %s", k, k2, v2))
        assert(not C2GS_BY_NAME[k2], string.format("netdefines C2GS_BY_NAME error %s %s %s", k, k2, v2))
        C2GS[v2] = {k, k2}
        C2GS_BY_NAME[k2] = v2
    end
end

for k, v in pairs(GS2C_DEFINES) do
    for k2, v2 in pairs(v) do
        assert(not GS2C[v2], string.format("netdefines GS2C error %s %s %s", k, k2, v2))
        assert(not GS2C_BY_NAME[k2], string.format("netdefines GS2C_BY_NAME error %s %s %s", k, k2, v2))
        GS2C[v2] = {k, k2}
        GS2C_BY_NAME[k2] = v2
    end
end

return M
