//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class CLASSTAG_CameraPathPointListWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(CameraPathPointList), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("Init",FUNCTAG_Init);
		<PERSON><PERSON>RegFunction("CleanUp",FUNCTAG_CleanUp);
		L.RegFunction(".geti",FUNCTAG_get_Item);
		L.RegFunction("get_Item",FUNCTAG_get_Item);
		<PERSON>.RegFunction("IndexOf",FUNCTAG_IndexOf);
		<PERSON><PERSON>RegFunction("AddPoint",FUNCTAG_AddPoint);
		<PERSON><PERSON>RegFunction("RemovePoint",FUNCTAG_RemovePoint);
		L.RegFunction("PathPointAddedEvent",FUNCTAG_PathPointAddedEvent);
		<PERSON>.RegFunction("PathPointRemovedEvent",FUNCTAG_PathPointRemovedEvent);
		<PERSON><PERSON>un<PERSON>("CheckPointCullEventFromStart",FUNCTAG_CheckPointCullEventFromStart);
		<PERSON><PERSON>unction("CheckPointCullEventFromEnd",FUNCTAG_CheckPointCullEventFromEnd);
		L.RegFunction("GetPoint",FUNCTAG_GetPoint);
		L.RegFunction("Clear",FUNCTAG_Clear);
		L.RegFunction("DuplicatePointCheck",FUNCTAG_DuplicatePointCheck);
		L.RegFunction("ReassignCP",FUNCTAG_ReassignCP);
		L.RegFunction("ToXML",FUNCTAG_ToXML);
		L.RegFunction("FromXML",FUNCTAG_FromXML);
		L.RegVar("this",FUNCTAG__this, null);
		L.RegVar("numberOfPoints",FUNCTAG_get_numberOfPoints, null);
		L.RegVar("realNumberOfPoints",FUNCTAG_get_realNumberOfPoints, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG__get_this(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CameraPathPointList obj = (CameraPathPointList)ToLua.CheckObject(L, 1, typeof(CameraPathPointList));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			CameraPathPoint o = obj[arg0];
			ToLua.Push(L, o);
			return 1;

		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG__this(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushvalue(L, 1);
			LuaDLL.tolua_bindthis(L, FUNCTAG__get_this, null);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_Init(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CameraPathPointList obj = (CameraPathPointList)ToLua.CheckObject(L, 1, typeof(CameraPathPointList));
			CameraPath arg0 = (CameraPath)ToLua.CheckUnityObject(L, 2, typeof(CameraPath));
			obj.Init(arg0);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_CleanUp(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraPathPointList obj = (CameraPathPointList)ToLua.CheckObject(L, 1, typeof(CameraPathPointList));
			obj.CleanUp();
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_Item(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CameraPathPointList obj = (CameraPathPointList)ToLua.CheckObject(L, 1, typeof(CameraPathPointList));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			CameraPathPoint o = obj[arg0];
			ToLua.Push(L, o);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_IndexOf(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CameraPathPointList obj = (CameraPathPointList)ToLua.CheckObject(L, 1, typeof(CameraPathPointList));
			CameraPathPoint arg0 = (CameraPathPoint)ToLua.CheckUnityObject(L, 2, typeof(CameraPathPoint));
			int o = obj.IndexOf(arg0);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_AddPoint(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3 && TypeChecker.CheckTypes(L, 1, typeof(CameraPathPointList), typeof(CameraPathPoint), typeof(CameraPathControlPoint)))
			{
				CameraPathPointList obj = (CameraPathPointList)ToLua.ToObject(L, 1);
				CameraPathPoint arg0 = (CameraPathPoint)ToLua.ToObject(L, 2);
				CameraPathControlPoint arg1 = (CameraPathControlPoint)ToLua.ToObject(L, 3);
				obj.AddPoint(arg0, arg1);
				return 0;
			}
			else if (count == 3 && TypeChecker.CheckTypes(L, 1, typeof(CameraPathPointList), typeof(CameraPathPoint), typeof(float)))
			{
				CameraPathPointList obj = (CameraPathPointList)ToLua.ToObject(L, 1);
				CameraPathPoint arg0 = (CameraPathPoint)ToLua.ToObject(L, 2);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 3);
				obj.AddPoint(arg0, arg1);
				return 0;
			}
			else if (count == 5 && TypeChecker.CheckTypes(L, 1, typeof(CameraPathPointList), typeof(CameraPathPoint), typeof(CameraPathControlPoint), typeof(CameraPathControlPoint), typeof(float)))
			{
				CameraPathPointList obj = (CameraPathPointList)ToLua.ToObject(L, 1);
				CameraPathPoint arg0 = (CameraPathPoint)ToLua.ToObject(L, 2);
				CameraPathControlPoint arg1 = (CameraPathControlPoint)ToLua.ToObject(L, 3);
				CameraPathControlPoint arg2 = (CameraPathControlPoint)ToLua.ToObject(L, 4);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 5);
				obj.AddPoint(arg0, arg1, arg2, arg3);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: CameraPathPointList.AddPoint");
			}
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_RemovePoint(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CameraPathPointList obj = (CameraPathPointList)ToLua.CheckObject(L, 1, typeof(CameraPathPointList));
			CameraPathPoint arg0 = (CameraPathPoint)ToLua.CheckUnityObject(L, 2, typeof(CameraPathPoint));
			obj.RemovePoint(arg0);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_PathPointAddedEvent(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CameraPathPointList obj = (CameraPathPointList)ToLua.CheckObject(L, 1, typeof(CameraPathPointList));
			CameraPathControlPoint arg0 = (CameraPathControlPoint)ToLua.CheckUnityObject(L, 2, typeof(CameraPathControlPoint));
			obj.PathPointAddedEvent(arg0);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_PathPointRemovedEvent(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CameraPathPointList obj = (CameraPathPointList)ToLua.CheckObject(L, 1, typeof(CameraPathPointList));
			CameraPathControlPoint arg0 = (CameraPathControlPoint)ToLua.CheckUnityObject(L, 2, typeof(CameraPathControlPoint));
			obj.PathPointRemovedEvent(arg0);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_CheckPointCullEventFromStart(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CameraPathPointList obj = (CameraPathPointList)ToLua.CheckObject(L, 1, typeof(CameraPathPointList));
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.CheckPointCullEventFromStart(arg0);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_CheckPointCullEventFromEnd(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CameraPathPointList obj = (CameraPathPointList)ToLua.CheckObject(L, 1, typeof(CameraPathPointList));
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.CheckPointCullEventFromEnd(arg0);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_GetPoint(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes(L, 1, typeof(CameraPathPointList), typeof(CameraPathControlPoint)))
			{
				CameraPathPointList obj = (CameraPathPointList)ToLua.ToObject(L, 1);
				CameraPathControlPoint arg0 = (CameraPathControlPoint)ToLua.ToObject(L, 2);
				CameraPathPoint o = obj.GetPoint(arg0);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes(L, 1, typeof(CameraPathPointList), typeof(int)))
			{
				CameraPathPointList obj = (CameraPathPointList)ToLua.ToObject(L, 1);
				int arg0 = (int)LuaDLL.lua_tonumber(L, 2);
				CameraPathPoint o = obj.GetPoint(arg0);
				ToLua.Push(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: CameraPathPointList.GetPoint");
			}
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_Clear(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraPathPointList obj = (CameraPathPointList)ToLua.CheckObject(L, 1, typeof(CameraPathPointList));
			obj.Clear();
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_DuplicatePointCheck(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraPathPointList obj = (CameraPathPointList)ToLua.CheckObject(L, 1, typeof(CameraPathPointList));
			CameraPathPoint o = obj.DuplicatePointCheck();
			ToLua.Push(L, o);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_ReassignCP(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			CameraPathPointList obj = (CameraPathPointList)ToLua.CheckObject(L, 1, typeof(CameraPathPointList));
			CameraPathControlPoint arg0 = (CameraPathControlPoint)ToLua.CheckUnityObject(L, 2, typeof(CameraPathControlPoint));
			CameraPathControlPoint arg1 = (CameraPathControlPoint)ToLua.CheckUnityObject(L, 3, typeof(CameraPathControlPoint));
			obj.ReassignCP(arg0, arg1);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_ToXML(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraPathPointList obj = (CameraPathPointList)ToLua.CheckObject(L, 1, typeof(CameraPathPointList));
			string o = obj.ToXML();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_FromXML(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CameraPathPointList obj = (CameraPathPointList)ToLua.CheckObject(L, 1, typeof(CameraPathPointList));
			System.Xml.XmlNodeList arg0 = (System.Xml.XmlNodeList)ToLua.CheckObject(L, 2, typeof(System.Xml.XmlNodeList));
			obj.FromXML(arg0);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_numberOfPoints(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraPathPointList obj = (CameraPathPointList)o;
			int ret = obj.numberOfPoints;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index numberOfPoints on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_realNumberOfPoints(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraPathPointList obj = (CameraPathPointList)o;
			int ret = obj.realNumberOfPoints;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index realNumberOfPoints on a nil value" : e.Message);
		}
	}
}

