Object reference not set to an instance of an object
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: at 0x53982c80
	[C]: in function '__index'
	[string "logic/ui/CWrapContent"]:33: in function 'SetData'
	[string "logic/mainmenu/CExpandWorldBossPage"]:251: in function 'RefreshRank'
	[string "logic/mainmenu/CExpandWorldBossPage"]:87: in function 'InitContent'
	[string "logic/mainmenu/CExpandWorldBossPage"]:34: in function 'OnInitPage'
	[string "logic/ui/CPageBase"]:18: in function 'ShowPage'
	[string "logic/base/CGameObjContainer"]:64: in function 'ShowSubPage'
	[string "logic/mainmenu/CMainMenuExpandBox"]:160: in function 'ShowSubPage'
	[string "logic/mainmenu/CMainMenuExpandBox"]:231: in function 'ShowWorldBossPage'
	[string "logic/mainmenu/CMainMenuExpandBox"]:270: in function <[string "logic/mainmenu/CMainMenuExpandBox"]:251>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004CFD3A6B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004CFD3954 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x000000004D097775 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x0000000098AB0CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x0000000098AB0DE5 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x000000004D097E83 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x000000004D0978D4 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x000000004D097B07 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FF97A8064D7 ((<unknown>)) 
0x00007FF97A758A31 ((<unknown>)) 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004CFD3A6B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004CFD3954 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x000000004D097775 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x0000000098AB0CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x0000000098AB0AB0 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x0000000098AB0847 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x000000004D331483 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x0000000053982C98 ((<unknown>)) 
0x000000005399CF70 ((<unknown>)) 
0x00000000539A1100 ((<unknown>)) 
0x00000000539A1759 ((<unknown>)) 
0x000000005399CF70 ((<unknown>)) 
0x0000000053981EDD ((<unknown>)) 
0x000000005399CF70 ((<unknown>)) 
0x00000000539AE7D2 ((<unknown>)) 
0x00000000562B2987 (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x00000000562B280A (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x00000000562C27C7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x00000000562C2610 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000008D819134 (Mono JIT Code) [LuaMain.cs:111] LuaMain:Update () 
0x00000000007174A2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FF97A8064D7 ((<unknown>)) 
0x00007FF97A758A31 ((<unknown>)) 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF9B6AC7034 (KERNEL32) BaseThreadInitThunk
0x00007FF9B7F426A1 (ntdll) RtlUserThreadStart


Object reference not set to an instance of an object
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: at 0x53982c80
	[C]: in function '__index'
	[string "logic/ui/CWrapContent"]:33: in function 'SetData'
	[string "logic/mainmenu/CExpandWorldBossPage"]:251: in function 'RefreshRank'
	[string "logic/mainmenu/CExpandWorldBossPage"]:87: in function 'InitContent'
	[string "logic/mainmenu/CExpandWorldBossPage"]:34: in function 'OnInitPage'
	[string "logic/ui/CPageBase"]:18: in function 'ShowPage'
	[string "logic/base/CGameObjContainer"]:64: in function 'ShowSubPage'
	[string "logic/mainmenu/CMainMenuExpandBox"]:160: in function 'ShowSubPage'
	[string "logic/mainmenu/CMainMenuExpandBox"]:231: in function 'ShowWorldBossPage'
	[string "logic/mainmenu/CMainMenuExpandBox"]:270: in function <[string "logic/mainmenu/CMainMenuExpandBox"]:251>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004CFD3A6B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004CFD3954 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x000000004D097775 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x0000000098AB0CD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x0000000098AB0AB0 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x0000000098AB0847 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x000000004D331483 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x0000000053982C98 ((<unknown>)) 
0x000000005399CF70 ((<unknown>)) 
0x00000000539A1100 ((<unknown>)) 
0x00000000539A1759 ((<unknown>)) 
0x000000005399CF70 ((<unknown>)) 
0x0000000053981EDD ((<unknown>)) 
0x000000005399CF70 ((<unknown>)) 
0x00000000539AE7D2 ((<unknown>)) 
0x00000000562B2987 (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x00000000562B280A (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x00000000562C27C7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x00000000562C2610 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000008D819134 (Mono JIT Code) [LuaMain.cs:111] LuaMain:Update () 
0x00000000007174A2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FF97A8064D7 ((<unknown>)) 
0x00007FF97A758A31 ((<unknown>)) 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF9B6AC7034 (KERNEL32) BaseThreadInitThunk
0x00007FF9B7F426A1 (ntdll) RtlUserThreadStart


Object reference not set to an instance of an object
stack traceback:
	[string "core/global"]:81: in function <[string "core/global"]:73>
	[C]: at 0x53982c80
	[C]: in function '__index'
	[string "logic/ui/CWrapContent"]:33: in function 'SetData'
	[string "logic/mainmenu/CExpandWorldBossPage"]:251: in function 'RefreshRank'
	[string "logic/mainmenu/CExpandWorldBossPage"]:87: in function 'InitContent'
	[string "logic/mainmenu/CExpandWorldBossPage"]:34: in function 'OnInitPage'
	[string "logic/ui/CPageBase"]:18: in function 'ShowPage'
	[string "logic/base/CGameObjContainer"]:64: in function 'ShowSubPage'
	[string "logic/mainmenu/CMainMenuExpandBox"]:160: in function 'ShowSubPage'
	[string "logic/mainmenu/CMainMenuExpandBox"]:231: in function 'ShowWorldBossPage'
	[string "logic/mainmenu/CMainMenuExpandBox"]:270: in function <[string "logic/mainmenu/CMainMenuExpandBox"]:251>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>

