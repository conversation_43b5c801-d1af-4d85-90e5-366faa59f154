fileFormatVersion: 2
guid: 065fc29cc43a7b14dbf8708c4516e603
timeCreated: **********
licenseType: Pro
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: Bip001
    100002: Bip001 Footsteps
    100004: Bip001 Head
    100006: Bip001 HeadNub
    100008: Bip001 L Calf
    100010: Bip001 L Clavicle
    100012: Bip001 L Finger0
    100014: Bip001 L Finger01
    100016: Bip001 L Finger0Nub
    100018: Bip001 L Finger1
    100020: Bip001 L Finger11
    100022: Bip001 L Finger1Nub
    100024: Bip001 L Foot
    100026: Bip001 L Forearm
    100028: Bip001 L Hand
    100030: Bip001 L Thigh
    100032: Bip001 L Toe0
    100034: Bip001 L Toe0Nub
    100036: Bip001 L UpperArm
    100038: Bip001 Neck
    100040: Bip001 Pelvis
    100042: Bip001 Prop1
    100044: Bip001 R Calf
    100046: Bip001 R Clavicle
    100048: Bip001 R Finger0
    100050: Bip001 R Finger01
    100052: Bip001 R Finger0Nub
    100054: Bip001 R Finger1
    100056: Bip001 R Finger11
    100058: Bip001 R Finger1Nub
    100060: Bip001 R Foot
    100062: Bip001 R Forearm
    100064: Bip001 R Hand
    100066: Bip001 R Thigh
    100068: Bip001 R Toe0
    100070: Bip001 R Toe0Nub
    100072: Bip001 R UpperArm
    100074: Bip001 Spine
    100076: Bip001 Spine1
    100078: Bone001
    100080: Bone002
    100082: Bone003
    100084: Bone004
    100086: Bone005
    100088: Bone006
    100090: Bone007
    100092: Bone008
    100094: Bone009
    100096: Bone010
    100098: Bone011
    100100: Bone012
    100102: Bone013
    100104: Bone014
    100106: Bone015
    100108: Bone016
    100110: Bone017
    100112: Bone018
    100114: Bone019
    100116: Bone020
    100118: Bone021
    100120: Bone022
    100122: Bone023
    100124: Bone024
    100126: Bone025
    100128: Bone026
    100130: Bone027
    100132: Bone028
    100134: //RootNode
    100136: pet_401
    100138: weapon401_1
    100140: Bip001 L Finger2
    100142: Bip001 L Finger2Nub
    100144: Bip001 R Finger2
    100146: Bip001 R Finger2Nub
    100148: Bone029
    100150: Bone030
    100152: Bone031
    100154: Bone032
    100156: Bone033
    100158: Bone034
    100160: Bone035
    100162: Bone036
    100164: Bone037
    100166: Bone038
    100168: Bone039
    100170: Bone040
    100172: Bone041
    100174: Bone042
    100176: Bone043
    100178: model507
    100180: Bone005(mirrored)
    100182: Bone006(mirrored)
    100184: Bone007(mirrored)
    100186: Bone008(mirrored)
    100188: Bone009(mirrored)
    100190: Bone010(mirrored)
    100192: Bone011(mirrored)
    100194: Bone012(mirrored)
    100196: Bone013(mirrored)
    100198: Bone020(mirrored)
    100200: Bone029(mirrored)
    100202: Bone030(mirrored)
    100204: Bone031(mirrored)
    100206: Bone032(mirrored)
    100208: model503
    100210: weapon503_01
    100212: model504
    100214: weapon504_01
    100216: Bip001 Prop2
    100218: Bone003(mirrored)
    100220: Bone004(mirrored)
    100222: Bone014(mirrored)
    100224: Bone015(mirrored)
    100226: Bone016(mirrored)
    100228: Bone017(mirrored)
    100230: Bone018(mirrored)
    100232: Bone019(mirrored)
    100234: Bone022(mirrored)
    100236: model505
    100238: weapon505_01
    400000: Bip001
    400002: Bip001 Footsteps
    400004: Bip001 Head
    400006: Bip001 HeadNub
    400008: Bip001 L Calf
    400010: Bip001 L Clavicle
    400012: Bip001 L Finger0
    400014: Bip001 L Finger01
    400016: Bip001 L Finger0Nub
    400018: Bip001 L Finger1
    400020: Bip001 L Finger11
    400022: Bip001 L Finger1Nub
    400024: Bip001 L Foot
    400026: Bip001 L Forearm
    400028: Bip001 L Hand
    400030: Bip001 L Thigh
    400032: Bip001 L Toe0
    400034: Bip001 L Toe0Nub
    400036: Bip001 L UpperArm
    400038: Bip001 Neck
    400040: Bip001 Pelvis
    400042: Bip001 Prop1
    400044: Bip001 R Calf
    400046: Bip001 R Clavicle
    400048: Bip001 R Finger0
    400050: Bip001 R Finger01
    400052: Bip001 R Finger0Nub
    400054: Bip001 R Finger1
    400056: Bip001 R Finger11
    400058: Bip001 R Finger1Nub
    400060: Bip001 R Foot
    400062: Bip001 R Forearm
    400064: Bip001 R Hand
    400066: Bip001 R Thigh
    400068: Bip001 R Toe0
    400070: Bip001 R Toe0Nub
    400072: Bip001 R UpperArm
    400074: Bip001 Spine
    400076: Bip001 Spine1
    400078: Bone001
    400080: Bone002
    400082: Bone003
    400084: Bone004
    400086: Bone005
    400088: Bone006
    400090: Bone007
    400092: Bone008
    400094: Bone009
    400096: Bone010
    400098: Bone011
    400100: Bone012
    400102: Bone013
    400104: Bone014
    400106: Bone015
    400108: Bone016
    400110: Bone017
    400112: Bone018
    400114: Bone019
    400116: Bone020
    400118: Bone021
    400120: Bone022
    400122: Bone023
    400124: Bone024
    400126: Bone025
    400128: Bone026
    400130: Bone027
    400132: Bone028
    400134: //RootNode
    400136: pet_401
    400138: weapon401_1
    400140: Bip001 L Finger2
    400142: Bip001 L Finger2Nub
    400144: Bip001 R Finger2
    400146: Bip001 R Finger2Nub
    400148: Bone029
    400150: Bone030
    400152: Bone031
    400154: Bone032
    400156: Bone033
    400158: Bone034
    400160: Bone035
    400162: Bone036
    400164: Bone037
    400166: Bone038
    400168: Bone039
    400170: Bone040
    400172: Bone041
    400174: Bone042
    400176: Bone043
    400178: model507
    400180: Bone005(mirrored)
    400182: Bone006(mirrored)
    400184: Bone007(mirrored)
    400186: Bone008(mirrored)
    400188: Bone009(mirrored)
    400190: Bone010(mirrored)
    400192: Bone011(mirrored)
    400194: Bone012(mirrored)
    400196: Bone013(mirrored)
    400198: Bone020(mirrored)
    400200: Bone029(mirrored)
    400202: Bone030(mirrored)
    400204: Bone031(mirrored)
    400206: Bone032(mirrored)
    400208: model503
    400210: weapon503_01
    400212: model504
    400214: weapon504_01
    400216: Bip001 Prop2
    400218: Bone003(mirrored)
    400220: Bone004(mirrored)
    400222: Bone014(mirrored)
    400224: Bone015(mirrored)
    400226: Bone016(mirrored)
    400228: Bone017(mirrored)
    400230: Bone018(mirrored)
    400232: Bone019(mirrored)
    400234: Bone022(mirrored)
    400236: model505
    400238: weapon505_01
    4300000: pet_401
    4300002: weapon401_1
    4300004: model507
    4300006: model503
    4300008: weapon503_01
    4300010: model504
    4300012: weapon504_01
    4300014: model505
    4300016: weapon505_01
    9500000: //RootNode
    13700000: pet_401
    13700002: weapon401_1
    13700004: model507
    13700006: model503
    13700008: weapon503_01
    13700010: model504
    13700012: weapon504_01
    13700014: model505
    13700016: weapon505_01
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
