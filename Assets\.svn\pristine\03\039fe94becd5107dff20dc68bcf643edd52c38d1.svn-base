InvalidOperationException: Cannot override system-specified headers
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004CC0D0FB (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004CC0CFE4 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x000000004CCD16C5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004EAC99A3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004EAC9665 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x000000004CCD1DD3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x000000004CCD1824 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x000000004CCD1A57 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFD1D0264D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFD1CF78A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x0000000140A2CE8A (Unity) Scripting::LogException
0x0000000140A2D914 (Unity) ScriptingInvocation::Invoke
0x00000001409EEB26 (Unity) Coroutine::InvokeMoveNext
0x00000001409F706C (Unity) Coroutine::Run
0x00000001409F76C8 (Unity) MonoBehaviour::TryCreateAndRunCoroutine
0x00000001409FB0D9 (Unity) MonoBehaviour::StartCoroutineManaged2
0x000000014145865F (Unity) MonoBehaviour_CUSTOM_StartCoroutine_Auto_Internal
0x000000004CC580D5 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.MonoBehaviour:StartCoroutine_Auto_Internal (System.Collections.IEnumerator)
0x000000004CC57F47 (Mono JIT Code) [MonoBehaviourBindings.gen.cs:62] UnityEngine.MonoBehaviour:StartCoroutine (System.Collections.IEnumerator) 
0x000000004EABD474 (Mono JIT Code) [SimpleWWW.cs:25] SimpleWWW:Receive (CLASSTAG_Request,System.Action`1<byte[]>,System.Action`1<single>,System.Action`1<System.Exception>,bool,SimpleWWW/ConnectionType) 
0x000000004EABCEF1 (Mono JIT Code) [HttpController.cs:243] CLASSTAG_HttpController:RequestWithUrl (string,System.Action`1<CLASSTAG_ByteArray>,System.Action`1<single>,System.Action`1<System.Exception>,bool,SimpleWWW/ConnectionType,System.Collections.Hashtable) 
0x000000004EAB3199 (Mono JIT Code) [HttpController.cs:148] CLASSTAG_HttpController:DownLoad (string,System.Action`1<CLASSTAG_ByteArray>,System.Action`1<single>,System.Action`1<System.Exception>,bool,SimpleWWW/ConnectionType,System.Collections.Hashtable) 
0x000000004EAB2512 (Mono JIT Code) [AssetUpdate.cs:226] AssetPipeline.AssetUpdate:LoadStaticConfig (string,System.Action`1<string>,System.Action`1<string>) 
0x000000004EAB1CEF (Mono JIT Code) [AssetUpdate.cs:209] AssetPipeline.AssetUpdate:LoadStaticConfig (bool,System.Action,System.Action`1<string>) 
0x000000004EAB165D (Mono JIT Code) [GameLauncher.cs:302] GameLauncher:LoadStaticConfig () 
0x000000004EA9F5DC (Mono JIT Code) [GameLauncher.cs:205] GameLauncher:<CheckUpdate>m__1 (bool) 
0x000000004CCD45CE (Mono JIT Code) [AssetUpdate.cs:434] AssetPipeline.AssetUpdate/<CheckOutGameRes>c__Iterator1:MoveNext () 
0x000000004CC582D1 (Mono JIT Code) [Coroutines.cs:17] UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr) 
0x000000004CC5844B (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_intptr (object,intptr,intptr,intptr)
0x00007FFD1D0264D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFD1CF78A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EEB26 (Unity) Coroutine::InvokeMoveNext
0x00000001409F706C (Unity) Coroutine::Run
0x00000001409F71C0 (Unity) Coroutine::Run
0x0000000140505965 (Unity) DelayedCallManager::Update
0x000000014072504C (Unity) `InitPlayerLoopCallbacks'::`39'::UpdateScriptRunDelayedDynamicFrameRateRegistrator::Forward
0x00000001407221B6 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFD86AC7604 (KERNEL32) BaseThreadInitThunk
0x00007FFD88AA26A1 (ntdll) RtlUserThreadStart


InvalidOperationException: Cannot override system-specified headers
UnityEngine.Networking.UnityWebRequest.SetRequestHeader (System.String name, System.String value) (at C:/buildslave/unity/build/Modules/UnityWebRequest/Public/UnityWebRequest.bindings.cs:425)
UnityEngine.WWW..ctor (System.String url, System.Byte[] postData, System.Collections.Generic.Dictionary`2 headers) (at C:/buildslave/unity/build/Modules/UnityWebRequestWWW/Public/WWW.cs:113)
SimpleWWW+<FUNCTAG__Receive>c__Iterator0.MoveNext () (at Assets/Standard Assets/GameFramework/Network/HTTP/SimpleWWW.cs:74)
UnityEngine.SetupCoroutine.InvokeMoveNext (IEnumerator enumerator, IntPtr returnValueAddress) (at C:/buildslave/unity/build/Runtime/Export/Coroutines.cs:17)
UnityEngine.MonoBehaviour:StartCoroutine(IEnumerator)
SimpleWWW:Receive(CLASSTAG_Request, Action`1, Action`1, Action`1, Boolean, ConnectionType) (at Assets/Standard Assets/GameFramework/Network/HTTP/SimpleWWW.cs:25)
CLASSTAG_HttpController:RequestWithUrl(String, Action`1, Action`1, Action`1, Boolean, ConnectionType, Hashtable) (at Assets/Standard Assets/GameFramework/Network/HTTP/HttpController.cs:243)
CLASSTAG_HttpController:DownLoad(String, Action`1, Action`1, Action`1, Boolean, ConnectionType, Hashtable) (at Assets/Standard Assets/GameFramework/Network/HTTP/HttpController.cs:148)
AssetPipeline.AssetUpdate:LoadStaticConfig(String, Action`1, Action`1) (at Assets/Standard Assets/GameFramework/AssetPipeline/AssetUpdate.cs:226)
AssetPipeline.AssetUpdate:LoadStaticConfig(Boolean, Action, Action`1) (at Assets/Standard Assets/GameFramework/AssetPipeline/AssetUpdate.cs:209)
GameLauncher:LoadStaticConfig() (at Assets/Standard Assets/GameFramework/GameLauncher/GameLauncher.cs:302)
GameLauncher:<CheckUpdate>m__1(Boolean) (at Assets/Standard Assets/GameFramework/GameLauncher/GameLauncher.cs:205)
AssetPipeline.<CheckOutGameRes>c__Iterator1:MoveNext() (at Assets/Standard Assets/GameFramework/AssetPipeline/AssetUpdate.cs:434)
UnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator, IntPtr)


Script Merge Error! ScriptVersion=0 PatchVersion=0
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004C9CD0FB (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004C9CCFE4 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x000000004CA916C5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E97A393 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004E97A055 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x000000004CA91DD3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x000000004CA91824 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x000000004CA91A57 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFD1D0264D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFD1CF78A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004C9CD0FB (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004C9CCFE4 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x000000004CA916C5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E97A393 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004E97A055 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x000000004E97970E (Mono JIT Code) [LuaScript.cs:181] CLASSTAG_LuaScript:MergePatch (CLASSTAG_LuaScript) 
0x000000004E96A873 (Mono JIT Code) [AssetUpdate.cs:892] AssetPipeline.AssetUpdate/<UpdateScriptFile>c__Iterator7/<UpdateScriptFile>c__AnonStorey1B:<>m__0 (UnityEngine.Networking.UnityWebRequest) 
0x000000004E943DE8 (Mono JIT Code) [AssetUpdate.cs:1952] AssetPipeline.AssetUpdate/<DownloadByUnityWebRequest>c__Iterator11:MoveNext () 
0x000000004CA18511 (Mono JIT Code) [Coroutines.cs:17] UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr) 
0x000000004CA1868B (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_intptr (object,intptr,intptr,intptr)
0x00007FFD1D0264D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFD1CF78A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EEB26 (Unity) Coroutine::InvokeMoveNext
0x00000001409F706C (Unity) Coroutine::Run
0x0000000140700EFD (Unity) AsyncOperation::InvokeCoroutine
0x0000000141B34777 (Unity) UnityWebRequestAsyncOperation::InvokeCoroutine
0x0000000141B35826 (Unity) UnityWebRequestProto<UnityWebRequestTransport,AtomicRefCounter,RedirectHelper,ResponseHelper,DownloadHandler,UploadHandler,HeaderHelper,AsyncOperation>::Job_InvokeCoroutine
0x00000001406D94CA (Unity) BackgroundJobQueue::ExecuteMainThreadJobs
0x0000000140723DB2 (Unity) `InitPlayerLoopCallbacks'::`5'::EarlyUpdateExecuteMainThreadJobsRegistrator::Forward
0x0000000140721ED0 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFD86AC7604 (KERNEL32) BaseThreadInitThunk
0x00007FFD88AA26A1 (ntdll) RtlUserThreadStart


Script Merge Error! ScriptVersion=0 PatchVersion=0
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004C9CD0FB (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004C9CCFE4 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x000000004CA916C5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E97A393 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004E97A055 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x000000004E97970E (Mono JIT Code) [LuaScript.cs:181] CLASSTAG_LuaScript:MergePatch (CLASSTAG_LuaScript) 
0x000000004E96A873 (Mono JIT Code) [AssetUpdate.cs:892] AssetPipeline.AssetUpdate/<UpdateScriptFile>c__Iterator7/<UpdateScriptFile>c__AnonStorey1B:<>m__0 (UnityEngine.Networking.UnityWebRequest) 
0x000000004E943DE8 (Mono JIT Code) [AssetUpdate.cs:1952] AssetPipeline.AssetUpdate/<DownloadByUnityWebRequest>c__Iterator11:MoveNext () 
0x000000004CA18511 (Mono JIT Code) [Coroutines.cs:17] UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr) 
0x000000004CA1868B (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_intptr (object,intptr,intptr,intptr)
0x00007FFD1D0264D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFD1CF78A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EEB26 (Unity) Coroutine::InvokeMoveNext
0x00000001409F706C (Unity) Coroutine::Run
0x0000000140700EFD (Unity) AsyncOperation::InvokeCoroutine
0x0000000141B34777 (Unity) UnityWebRequestAsyncOperation::InvokeCoroutine
0x0000000141B35826 (Unity) UnityWebRequestProto<UnityWebRequestTransport,AtomicRefCounter,RedirectHelper,ResponseHelper,DownloadHandler,UploadHandler,HeaderHelper,AsyncOperation>::Job_InvokeCoroutine
0x00000001406D94CA (Unity) BackgroundJobQueue::ExecuteMainThreadJobs
0x0000000140723DB2 (Unity) `InitPlayerLoopCallbacks'::`5'::EarlyUpdateExecuteMainThreadJobsRegistrator::Forward
0x0000000140721ED0 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFD86AC7604 (KERNEL32) BaseThreadInitThunk
0x00007FFD88AA26A1 (ntdll) RtlUserThreadStart


Script Merge Error! ScriptVersion=0 PatchVersion=0
   at GameDebug.LogError(System.String msg, System.String stackTrace)
   at CLASSTAG_LuaScript.MergePatch(.CLASSTAG_LuaScript patch)
   at AssetPipeline.AssetUpdate+<UpdateScriptFile>c__Iterator7+<UpdateScriptFile>c__AnonStorey1B.<>m__0(UnityEngine.Networking.UnityWebRequest www)
   at AssetPipeline.AssetUpdate+<DownloadByUnityWebRequest>c__Iterator11.MoveNext()
   at UnityEngine.SetupCoroutine.InvokeMoveNext(IEnumerator enumerator, IntPtr returnValueAddress)

Script Merge Error! ScriptVersion=0 PatchVersion=0
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004C9CD0FB (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004C9CCFE4 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x000000004CA916C5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E97A393 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004E97A055 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x000000004CA91DD3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x000000004CA91824 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x000000004CA91A57 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFD1D0264D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFD1CF78A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004C9CD0FB (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004C9CCFE4 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x000000004CA916C5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E97A393 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004E97A055 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x000000004E97970E (Mono JIT Code) [LuaScript.cs:181] CLASSTAG_LuaScript:MergePatch (CLASSTAG_LuaScript) 
0x000000004E96A873 (Mono JIT Code) [AssetUpdate.cs:892] AssetPipeline.AssetUpdate/<UpdateScriptFile>c__Iterator7/<UpdateScriptFile>c__AnonStorey1B:<>m__0 (UnityEngine.Networking.UnityWebRequest) 
0x000000004E943DE8 (Mono JIT Code) [AssetUpdate.cs:1952] AssetPipeline.AssetUpdate/<DownloadByUnityWebRequest>c__Iterator11:MoveNext () 
0x000000004CA18511 (Mono JIT Code) [Coroutines.cs:17] UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr) 
0x000000004CA1868B (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_intptr (object,intptr,intptr,intptr)
0x00007FFD1D0264D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFD1CF78A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EEB26 (Unity) Coroutine::InvokeMoveNext
0x00000001409F706C (Unity) Coroutine::Run
0x0000000140700EFD (Unity) AsyncOperation::InvokeCoroutine
0x0000000141B34777 (Unity) UnityWebRequestAsyncOperation::InvokeCoroutine
0x0000000141B35826 (Unity) UnityWebRequestProto<UnityWebRequestTransport,AtomicRefCounter,RedirectHelper,ResponseHelper,DownloadHandler,UploadHandler,HeaderHelper,AsyncOperation>::Job_InvokeCoroutine
0x00000001406D94CA (Unity) BackgroundJobQueue::ExecuteMainThreadJobs
0x0000000140723DB2 (Unity) `InitPlayerLoopCallbacks'::`5'::EarlyUpdateExecuteMainThreadJobsRegistrator::Forward
0x0000000140721ED0 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFD86AC7604 (KERNEL32) BaseThreadInitThunk
0x00007FFD88AA26A1 (ntdll) RtlUserThreadStart


Script Merge Error! ScriptVersion=0 PatchVersion=0
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004C9CD0FB (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004C9CCFE4 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x000000004CA916C5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E97A393 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004E97A055 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x000000004E97970E (Mono JIT Code) [LuaScript.cs:181] CLASSTAG_LuaScript:MergePatch (CLASSTAG_LuaScript) 
0x000000004E96A873 (Mono JIT Code) [AssetUpdate.cs:892] AssetPipeline.AssetUpdate/<UpdateScriptFile>c__Iterator7/<UpdateScriptFile>c__AnonStorey1B:<>m__0 (UnityEngine.Networking.UnityWebRequest) 
0x000000004E943DE8 (Mono JIT Code) [AssetUpdate.cs:1952] AssetPipeline.AssetUpdate/<DownloadByUnityWebRequest>c__Iterator11:MoveNext () 
0x000000004CA18511 (Mono JIT Code) [Coroutines.cs:17] UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr) 
0x000000004CA1868B (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_intptr (object,intptr,intptr,intptr)
0x00007FFD1D0264D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFD1CF78A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EEB26 (Unity) Coroutine::InvokeMoveNext
0x00000001409F706C (Unity) Coroutine::Run
0x0000000140700EFD (Unity) AsyncOperation::InvokeCoroutine
0x0000000141B34777 (Unity) UnityWebRequestAsyncOperation::InvokeCoroutine
0x0000000141B35826 (Unity) UnityWebRequestProto<UnityWebRequestTransport,AtomicRefCounter,RedirectHelper,ResponseHelper,DownloadHandler,UploadHandler,HeaderHelper,AsyncOperation>::Job_InvokeCoroutine
0x00000001406D94CA (Unity) BackgroundJobQueue::ExecuteMainThreadJobs
0x0000000140723DB2 (Unity) `InitPlayerLoopCallbacks'::`5'::EarlyUpdateExecuteMainThreadJobsRegistrator::Forward
0x0000000140721ED0 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFD86AC7604 (KERNEL32) BaseThreadInitThunk
0x00007FFD88AA26A1 (ntdll) RtlUserThreadStart


Script Merge Error! ScriptVersion=0 PatchVersion=0
   at GameDebug.LogError(System.String msg, System.String stackTrace)
   at CLASSTAG_LuaScript.MergePatch(.CLASSTAG_LuaScript patch)
   at AssetPipeline.AssetUpdate+<UpdateScriptFile>c__Iterator7+<UpdateScriptFile>c__AnonStorey1B.<>m__0(UnityEngine.Networking.UnityWebRequest www)
   at AssetPipeline.AssetUpdate+<DownloadByUnityWebRequest>c__Iterator11.MoveNext()
   at UnityEngine.SetupCoroutine.InvokeMoveNext(IEnumerator enumerator, IntPtr returnValueAddress)

ArgumentException: An element with the same key already exists in the dictionary.
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004C9CD0FB (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004C9CCFE4 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x000000004CA916C5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004E97A393 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004E97A055 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x000000004CA91DD3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x000000004CA91824 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x000000004CA91A57 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFD1D0264D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFD1CF78A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x0000000140A2CE8A (Unity) Scripting::LogException
0x0000000140A2D914 (Unity) ScriptingInvocation::Invoke
0x00000001409EEB26 (Unity) Coroutine::InvokeMoveNext
0x00000001409F706C (Unity) Coroutine::Run
0x0000000140700EFD (Unity) AsyncOperation::InvokeCoroutine
0x0000000140806BE9 (Unity) PreloadManager::UpdatePreloadingSingleStep
0x0000000140806F3D (Unity) PreloadManager::UpdatePreloading
0x000000014072403D (Unity) `InitPlayerLoopCallbacks'::`10'::EarlyUpdateUpdatePreloadingRegistrator::Forward
0x0000000140721F40 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFD86AC7604 (KERNEL32) BaseThreadInitThunk
0x00007FFD88AA26A1 (ntdll) RtlUserThreadStart


ArgumentException: An element with the same key already exists in the dictionary.
System.Collections.Generic.Dictionary`2[System.String,UnityEngine.Shader].Add (System.String key, UnityEngine.Shader value) (at /Users/<USER>/buildslave/mono/build/mcs/class/corlib/System.Collections.Generic/Dictionary.cs:404)
AssetPipeline.AssetManager+<PreloadCommonAsset>c__Iterator0.MoveNext () (at Assets/Standard Assets/GameFramework/AssetPipeline/AssetManager.cs:350)
UnityEngine.SetupCoroutine.InvokeMoveNext (IEnumerator enumerator, IntPtr returnValueAddress) (at C:/buildslave/unity/build/Runtime/Export/Coroutines.cs:17)


InvalidOperationException: Cannot override system-specified headers
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004C9CD57B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000004C9CD464 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x000000004CA96AA5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004CE58473 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004CE58135 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x000000004CA971B3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x000000004CA96C04 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x000000004CA96E37 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFD1D0264D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFD1CF78A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x0000000140A2CE8A (Unity) Scripting::LogException
0x0000000140A2D914 (Unity) ScriptingInvocation::Invoke
0x00000001409EEB26 (Unity) Coroutine::InvokeMoveNext
0x00000001409F706C (Unity) Coroutine::Run
0x00000001409F76C8 (Unity) MonoBehaviour::TryCreateAndRunCoroutine
0x00000001409FB0D9 (Unity) MonoBehaviour::StartCoroutineManaged2
0x000000014145865F (Unity) MonoBehaviour_CUSTOM_StartCoroutine_Auto_Internal
0x000000004CA1DA25 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.MonoBehaviour:StartCoroutine_Auto_Internal (System.Collections.IEnumerator)
0x000000004CA1D897 (Mono JIT Code) [MonoBehaviourBindings.gen.cs:62] UnityEngine.MonoBehaviour:StartCoroutine (System.Collections.IEnumerator) 
0x000000004CE4C4B4 (Mono JIT Code) [SimpleWWW.cs:25] SimpleWWW:Receive (CLASSTAG_Request,System.Action`1<byte[]>,System.Action`1<single>,System.Action`1<System.Exception>,bool,SimpleWWW/ConnectionType) 
0x000000004CE4BF31 (Mono JIT Code) [HttpController.cs:243] CLASSTAG_HttpController:RequestWithUrl (string,System.Action`1<CLASSTAG_ByteArray>,System.Action`1<single>,System.Action`1<System.Exception>,bool,SimpleWWW/ConnectionType,System.Collections.Hashtable) 
0x000000004CE421D9 (Mono JIT Code) [HttpController.cs:148] CLASSTAG_HttpController:DownLoad (string,System.Action`1<CLASSTAG_ByteArray>,System.Action`1<single>,System.Action`1<System.Exception>,bool,SimpleWWW/ConnectionType,System.Collections.Hashtable) 
0x000000004CE41552 (Mono JIT Code) [AssetUpdate.cs:226] AssetPipeline.AssetUpdate:LoadStaticConfig (string,System.Action`1<string>,System.Action`1<string>) 
0x000000004CE40D2F (Mono JIT Code) [AssetUpdate.cs:209] AssetPipeline.AssetUpdate:LoadStaticConfig (bool,System.Action,System.Action`1<string>) 
0x000000004CE4069D (Mono JIT Code) [GameLauncher.cs:302] GameLauncher:LoadStaticConfig () 
0x000000004CE2E61C (Mono JIT Code) [GameLauncher.cs:205] GameLauncher:<CheckUpdate>m__1 (bool) 
0x000000004CA999AE (Mono JIT Code) [AssetUpdate.cs:434] AssetPipeline.AssetUpdate/<CheckOutGameRes>c__Iterator1:MoveNext () 
0x000000004CA1DC21 (Mono JIT Code) [Coroutines.cs:17] UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr) 
0x000000004CA1DD9B (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_intptr (object,intptr,intptr,intptr)
0x00007FFD1D0264D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFD1CF78A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EEB26 (Unity) Coroutine::InvokeMoveNext
0x00000001409F706C (Unity) Coroutine::Run
0x00000001409F71C0 (Unity) Coroutine::Run
0x0000000140505965 (Unity) DelayedCallManager::Update
0x000000014072504C (Unity) `InitPlayerLoopCallbacks'::`39'::UpdateScriptRunDelayedDynamicFrameRateRegistrator::Forward
0x00000001407221B6 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFD86AC7604 (KERNEL32) BaseThreadInitThunk
0x00007FFD88AA26A1 (ntdll) RtlUserThreadStart


InvalidOperationException: Cannot override system-specified headers
UnityEngine.Networking.UnityWebRequest.SetRequestHeader (System.String name, System.String value) (at C:/buildslave/unity/build/Modules/UnityWebRequest/Public/UnityWebRequest.bindings.cs:425)
UnityEngine.WWW..ctor (System.String url, System.Byte[] postData, System.Collections.Generic.Dictionary`2 headers) (at C:/buildslave/unity/build/Modules/UnityWebRequestWWW/Public/WWW.cs:113)
SimpleWWW+<FUNCTAG__Receive>c__Iterator0.MoveNext () (at Assets/Standard Assets/GameFramework/Network/HTTP/SimpleWWW.cs:75)
UnityEngine.SetupCoroutine.InvokeMoveNext (IEnumerator enumerator, IntPtr returnValueAddress) (at C:/buildslave/unity/build/Runtime/Export/Coroutines.cs:17)
UnityEngine.MonoBehaviour:StartCoroutine(IEnumerator)
SimpleWWW:Receive(CLASSTAG_Request, Action`1, Action`1, Action`1, Boolean, ConnectionType) (at Assets/Standard Assets/GameFramework/Network/HTTP/SimpleWWW.cs:25)
CLASSTAG_HttpController:RequestWithUrl(String, Action`1, Action`1, Action`1, Boolean, ConnectionType, Hashtable) (at Assets/Standard Assets/GameFramework/Network/HTTP/HttpController.cs:243)
CLASSTAG_HttpController:DownLoad(String, Action`1, Action`1, Action`1, Boolean, ConnectionType, Hashtable) (at Assets/Standard Assets/GameFramework/Network/HTTP/HttpController.cs:148)
AssetPipeline.AssetUpdate:LoadStaticConfig(String, Action`1, Action`1) (at Assets/Standard Assets/GameFramework/AssetPipeline/AssetUpdate.cs:226)
AssetPipeline.AssetUpdate:LoadStaticConfig(Boolean, Action, Action`1) (at Assets/Standard Assets/GameFramework/AssetPipeline/AssetUpdate.cs:209)
GameLauncher:LoadStaticConfig() (at Assets/Standard Assets/GameFramework/GameLauncher/GameLauncher.cs:302)
GameLauncher:<CheckUpdate>m__1(Boolean) (at Assets/Standard Assets/GameFramework/GameLauncher/GameLauncher.cs:205)
AssetPipeline.<CheckOutGameRes>c__Iterator1:MoveNext() (at Assets/Standard Assets/GameFramework/AssetPipeline/AssetUpdate.cs:434)
UnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator, IntPtr)


InvalidOperationException: Cannot override system-specified headers
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000000048E2D29B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000048E2D184 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x0000000048EF1535 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004CE26A83 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004CE26745 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x0000000048EF1C43 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x0000000048EF1694 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x0000000048EF18C7 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFD1D0264D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFD1CF78A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x0000000140A2CE8A (Unity) Scripting::LogException
0x0000000140A2D914 (Unity) ScriptingInvocation::Invoke
0x00000001409EEB26 (Unity) Coroutine::InvokeMoveNext
0x00000001409F706C (Unity) Coroutine::Run
0x00000001409F76C8 (Unity) MonoBehaviour::TryCreateAndRunCoroutine
0x00000001409FB0D9 (Unity) MonoBehaviour::StartCoroutineManaged2
0x000000014145865F (Unity) MonoBehaviour_CUSTOM_StartCoroutine_Auto_Internal
0x0000000048E784B5 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.MonoBehaviour:StartCoroutine_Auto_Internal (System.Collections.IEnumerator)
0x0000000048E78327 (Mono JIT Code) [MonoBehaviourBindings.gen.cs:62] UnityEngine.MonoBehaviour:StartCoroutine (System.Collections.IEnumerator) 
0x000000004CE1AAC4 (Mono JIT Code) [SimpleWWW.cs:25] SimpleWWW:Receive (CLASSTAG_Request,System.Action`1<byte[]>,System.Action`1<single>,System.Action`1<System.Exception>,bool,SimpleWWW/ConnectionType) 
0x000000004CE1A541 (Mono JIT Code) [HttpController.cs:243] CLASSTAG_HttpController:RequestWithUrl (string,System.Action`1<CLASSTAG_ByteArray>,System.Action`1<single>,System.Action`1<System.Exception>,bool,SimpleWWW/ConnectionType,System.Collections.Hashtable) 
0x000000004CE107E9 (Mono JIT Code) [HttpController.cs:148] CLASSTAG_HttpController:DownLoad (string,System.Action`1<CLASSTAG_ByteArray>,System.Action`1<single>,System.Action`1<System.Exception>,bool,SimpleWWW/ConnectionType,System.Collections.Hashtable) 
0x000000004CE0FB62 (Mono JIT Code) [AssetUpdate.cs:226] AssetPipeline.AssetUpdate:LoadStaticConfig (string,System.Action`1<string>,System.Action`1<string>) 
0x000000004CE0F33F (Mono JIT Code) [AssetUpdate.cs:209] AssetPipeline.AssetUpdate:LoadStaticConfig (bool,System.Action,System.Action`1<string>) 
0x000000004CE0ECAD (Mono JIT Code) [GameLauncher.cs:302] GameLauncher:LoadStaticConfig () 
0x000000004CDFCC2C (Mono JIT Code) [GameLauncher.cs:205] GameLauncher:<CheckUpdate>m__1 (bool) 
0x0000000048EF443E (Mono JIT Code) [AssetUpdate.cs:434] AssetPipeline.AssetUpdate/<CheckOutGameRes>c__Iterator1:MoveNext () 
0x0000000048E786B1 (Mono JIT Code) [Coroutines.cs:17] UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr) 
0x0000000048E7882B (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_intptr (object,intptr,intptr,intptr)
0x00007FFD1D0264D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFD1CF78A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EEB26 (Unity) Coroutine::InvokeMoveNext
0x00000001409F706C (Unity) Coroutine::Run
0x00000001409F71C0 (Unity) Coroutine::Run
0x0000000140505965 (Unity) DelayedCallManager::Update
0x000000014072504C (Unity) `InitPlayerLoopCallbacks'::`39'::UpdateScriptRunDelayedDynamicFrameRateRegistrator::Forward
0x00000001407221B6 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FFD86AC7604 (KERNEL32) BaseThreadInitThunk
0x00007FFD88AA26A1 (ntdll) RtlUserThreadStart


InvalidOperationException: Cannot override system-specified headers
UnityEngine.Networking.UnityWebRequest.SetRequestHeader (System.String name, System.String value) (at C:/buildslave/unity/build/Modules/UnityWebRequest/Public/UnityWebRequest.bindings.cs:425)
UnityEngine.WWW..ctor (System.String url, System.Byte[] postData, System.Collections.Generic.Dictionary`2 headers) (at C:/buildslave/unity/build/Modules/UnityWebRequestWWW/Public/WWW.cs:113)
SimpleWWW+<FUNCTAG__Receive>c__Iterator0.MoveNext () (at Assets/Standard Assets/GameFramework/Network/HTTP/SimpleWWW.cs:75)
UnityEngine.SetupCoroutine.InvokeMoveNext (IEnumerator enumerator, IntPtr returnValueAddress) (at C:/buildslave/unity/build/Runtime/Export/Coroutines.cs:17)
UnityEngine.MonoBehaviour:StartCoroutine(IEnumerator)
SimpleWWW:Receive(CLASSTAG_Request, Action`1, Action`1, Action`1, Boolean, ConnectionType) (at Assets/Standard Assets/GameFramework/Network/HTTP/SimpleWWW.cs:25)
CLASSTAG_HttpController:RequestWithUrl(String, Action`1, Action`1, Action`1, Boolean, ConnectionType, Hashtable) (at Assets/Standard Assets/GameFramework/Network/HTTP/HttpController.cs:243)
CLASSTAG_HttpController:DownLoad(String, Action`1, Action`1, Action`1, Boolean, ConnectionType, Hashtable) (at Assets/Standard Assets/GameFramework/Network/HTTP/HttpController.cs:148)
AssetPipeline.AssetUpdate:LoadStaticConfig(String, Action`1, Action`1) (at Assets/Standard Assets/GameFramework/AssetPipeline/AssetUpdate.cs:226)
AssetPipeline.AssetUpdate:LoadStaticConfig(Boolean, Action, Action`1) (at Assets/Standard Assets/GameFramework/AssetPipeline/AssetUpdate.cs:209)
GameLauncher:LoadStaticConfig() (at Assets/Standard Assets/GameFramework/GameLauncher/GameLauncher.cs:302)
GameLauncher:<CheckUpdate>m__1(Boolean) (at Assets/Standard Assets/GameFramework/GameLauncher/GameLauncher.cs:205)
AssetPipeline.<CheckOutGameRes>c__Iterator1:MoveNext() (at Assets/Standard Assets/GameFramework/AssetPipeline/AssetUpdate.cs:434)
UnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator, IntPtr)


