//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class CLASSTAG_UnityEngine_LineRendererWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.LineRenderer), typeof(UnityEngine.Renderer));
		<PERSON><PERSON>RegFunction("SetWidth",FUNCTAG_SetWidth);
		<PERSON><PERSON>Function("SetColors",FUNCTAG_SetColors);
		<PERSON><PERSON>unction("SetVertexCount",FUNCTAG_SetVertexCount);
		<PERSON><PERSON>RegFunction("SetPosition",FUNCTAG_SetPosition);
		<PERSON><PERSON>RegFunction("SetPositions",FUNCTAG_SetPositions);
		<PERSON><PERSON>unction("New",FUNCTAG__CreateUnityEngine_LineRenderer);
		<PERSON><PERSON>RegFunction("__eq",FUNCTAG_op_Equality);
		L.RegFunction("__tostring",ToLua.op_ToString);
		<PERSON><PERSON>("useWorldSpace",FUNCTAG_get_useWorldSpace, FUNCTAG_set_useWorldSpace);
		<PERSON><PERSON>Class();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG__CreateUnityEngine_LineRenderer(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				UnityEngine.LineRenderer obj = new UnityEngine.LineRenderer();
				ToLua.Push(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.LineRenderer.New");
			}
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_SetWidth(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.LineRenderer obj = (UnityEngine.LineRenderer)ToLua.CheckObject(L, 1, typeof(UnityEngine.LineRenderer));
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			obj.SetWidth(arg0, arg1);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_SetColors(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.LineRenderer obj = (UnityEngine.LineRenderer)ToLua.CheckObject(L, 1, typeof(UnityEngine.LineRenderer));
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			UnityEngine.Color arg1 = ToLua.ToColor(L, 3);
			obj.SetColors(arg0, arg1);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_SetVertexCount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.LineRenderer obj = (UnityEngine.LineRenderer)ToLua.CheckObject(L, 1, typeof(UnityEngine.LineRenderer));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetVertexCount(arg0);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_SetPosition(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.LineRenderer obj = (UnityEngine.LineRenderer)ToLua.CheckObject(L, 1, typeof(UnityEngine.LineRenderer));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
			obj.SetPosition(arg0, arg1);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_SetPositions(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.LineRenderer obj = (UnityEngine.LineRenderer)ToLua.CheckObject(L, 1, typeof(UnityEngine.LineRenderer));
			UnityEngine.Vector3[] arg0 = ToLua.CheckObjectArray<UnityEngine.Vector3>(L, 2);
			obj.SetPositions(arg0);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_useWorldSpace(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LineRenderer obj = (UnityEngine.LineRenderer)o;
			bool ret = obj.useWorldSpace;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index useWorldSpace on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_set_useWorldSpace(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LineRenderer obj = (UnityEngine.LineRenderer)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.useWorldSpace = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index useWorldSpace on a nil value" : e.Message);
		}
	}
}

