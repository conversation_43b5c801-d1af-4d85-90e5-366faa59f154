using System;
using UnityEngine;
using System.Collections;
using System.Runtime.InteropServices;
using LuaInterface;

public class SdkCallbackInfo
{
    public string type;
    public int code;
    public string data;
}


public class SPSDK
{
#if UNITY_IPHONE || UNITY_EDITOR
    //[DllImport("__Internal")]
    private static uint __spsdk_init() { return 0; }

    //[DllImport("__Internal")]
    private static string __spsdk_getChannelId() { return ""; }

    //[DllImport("__Internal")]
    private static string __spsdk_getSubChannelId() { return ""; }

    //[DllImport("__Internal")]
    private static string __spsdk_getMutilPackageId() { return ""; }

    //[DllImport("__Internal")]
    private static void __spsdk_login() { }

    //[DllImport("__Internal")]
    private static void __spsdk_submitRoleData(string json) { }

    //[DllImport("__Internal")]
    private static void __spsdk_bind() { }

    //[DllImport("__Internal")]
    private static void __spsdk_logout() { }

    //[DllImport("__Internal")]
    private static bool __spsdk_isSupportUserCenter() { return true; }

    //[DllImport("__Internal")]
    private static void __spsdk_enterUserCenter() { }

    //[DllImport("__Internal")]
    private static bool __spsdk_isSupportBBS() { return true; }

    //[DllImport("__Internal")]
    private static void __spsdk_enterSdkBBS() { }

    //[DllImport("__Internal")]
    private static void __spsdk_pay(string json) { }
	
	//[DllImport("__Internal")]
	private static string _GetIdfv() { return ""; }

    //[DllImport("__Internal")]
    //private static extern string __spsdk_getAppName();

    //[DllImport("__Internal")]
    //private static extern void __spsdk_payExt(string payWay, string dataJson);

#endif

    private const string SDK_JAVA_CLASS = "com.cilugame.h1.CLSDKPlugin";
	private static AndroidJavaClass cls;
    private static LuaFunction luaCallback;

    private static AndroidJavaClass Sdk_Class
    {
        get
        {
            if (cls == null)
            {
                cls = CLASSTAG_AndroidAPI.GetAndroidJavaClass(SDK_JAVA_CLASS);
            }
            return cls;
        }
    }

	//Unity初始化完成
	internal static void UnityInitFinish()
	{

#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
		//unity初始化完成，通知android层，android层去掉自绘闪屏
		callSdkApi("UnityInitFinish");
#endif
	}

	public static void UnityUpdateFinish()
	{

#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
		callSdkApi("UnityUpdateFinish");
#endif
	}

    public static void FUNCTAG_Setup()
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        //执行延后初始化处理
        callSdkApi("AfterInit");
#endif
    }

    private static void callSdkApi(string apiName, params object[] args)
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
		CLASSTAG_AndroidAPI.CallStatic(Sdk_Class, apiName, args);
#endif
    }

    public static void Init()
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        Sdk_Class.CallStatic("Init");
#elif UNITY_IPHONE && !UNITY_EDITOR
        __spsdk_init();
#endif
    }

    public static void Login()
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        Sdk_Class.CallStatic("Login");
#elif UNITY_IPHONE && !UNITY_EDITOR
        __spsdk_login();
#endif
    }

    public static void Bind()
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        Sdk_Class.CallStatic("Bind");
#elif UNITY_IPHONE && !UNITY_EDITOR
        __spsdk_bind();
#endif
    }

    public static bool IsSupportLogout()
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
		return Sdk_Class.CallStatic<bool>("IsSupportLogout");
#elif UNITY_IPHONE && !UNITY_EDITOR
		return true;
#else
        return true;
#endif
    }

    public static bool Logout()
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        Sdk_Class.CallStatic("Logout");
#elif UNITY_IPHONE && !UNITY_EDITOR
        __spsdk_logout();
#endif
        return false;
    }


    public static bool DoExiter()
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        Sdk_Class.CallStatic("DoExiter");
#else
#endif
        return false;
    }

    public static void Exit()
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        Sdk_Class.CallStatic("Exit");
#endif
    }

    public static void Regster(string account, string uid)
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        Sdk_Class.CallStatic("Regster", account, uid);
#elif UNITY_IPHONE && !UNITY_EDITOR

#endif
    }

    public static void UpdateUserInfo(string uid)
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        Sdk_Class.CallStatic("UpdateUserInfo", uid);
#elif UNITY_IPHONE && !UNITY_EDITOR

#endif
    }

    public static void SubmitRoleData(string roleJson)
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        Sdk_Class.CallStatic("SubmitRoleData", roleJson);
#elif UNITY_IPHONE && !UNITY_EDITOR
        __spsdk_submitRoleData(roleJson);
#endif
    }

    public static void GainGameCoin(string jsonStr)
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        Sdk_Class.CallStatic("GainGameCoin", jsonStr);
#elif UNITY_IPHONE
        return;
#endif
    }

    public static void ConsumeGameCoin(string jsonStr)
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        Sdk_Class.CallStatic("ConsumeGameCoin", jsonStr);
#elif UNITY_IPHONE 
        return;
#endif
    }

    public static string GetChannelId()
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        return Sdk_Class.CallStatic<string>("GetChannelId");
#elif UNITY_IPHONE && !UNITY_EDITOR
        return __spsdk_getChannelId();
#endif
        return "";
    }

    public static string GetSubChannelId()
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        return Sdk_Class.CallStatic<string>("GetSubChannelId");
#elif UNITY_IPHONE && !UNITY_EDITOR
        return __spsdk_getSubChannelId();
#endif
        return "";
    }

    public static string GetMutilPackageId()
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        return Sdk_Class.CallStatic<string>("GetMutilPackageId");
#elif UNITY_IPHONE && !UNITY_EDITOR
        return __spsdk_getMutilPackageId();
#endif
        return "";
    }

    public static string GetAppName()
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        return Sdk_Class.CallStatic<string>("GetAppName");
#elif UNITY_IPHONE && !UNITY_EDITOR
        return "";
#endif
        return "";
    }

    public static bool IsSupportUserCenter()
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        return Sdk_Class.CallStatic<bool>("IsSupportUserCenter");
#elif UNITY_IPHONE && !UNITY_EDITOR
        return __spsdk_isSupportUserCenter();
#else
        return false;
#endif
    }


    public static void EnterUserCenter()
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        Sdk_Class.CallStatic("EnterUserCenter");
#elif UNITY_IPHONE && !UNITY_EDITOR
		__spsdk_enterUserCenter();
#else
        return;
#endif
    }


    public static bool IsSupportBBS()
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        return Sdk_Class.CallStatic<bool>("IsSupportBBS");
#elif UNITY_IPHONE && !UNITY_EDITOR
        return __spsdk_isSupportBBS();
#else
        return false;
#endif
    }


    public static void EnterSdkBBS()
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        Sdk_Class.CallStatic("EnterSdkBBS");
#elif UNITY_IPHONE && !UNITY_EDITOR
		__spsdk_enterSdkBBS();
#else
        return;
#endif
    }

    public static bool IsSupportShowOrHideToolbar()
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        return Sdk_Class.CallStatic<bool>("IsSupportShowOrHideToolbar");
#else
        return false;
#endif
    }


    public static void ShowFloatToolBar()
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        Sdk_Class.CallStatic("ShowFloatToolBar");
#else
        return;
#endif
    }

    public static void HideFloatToolBar()
    {
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        Sdk_Class.CallStatic("HideFloatToolBar");
#else
        return;
#endif
    }

	public static bool IsSupportService()
	{
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
		return Sdk_Class.CallStatic<bool>("IsSupportService");
#else
		return false;
#endif

	}

	public static void OpenService(string json)
	{
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
		Sdk_Class.CallStatic("OpenService", json);
#endif
	}

    public static void DoPay(string json)
    {

#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
        Sdk_Class.CallStatic("DoPay", json);
#elif UNITY_IPHONE && !UNITY_EDITOR
        __spsdk_pay(json);
#else
        return;
#endif
    }

	public static void startPlayCG(int timeMS)
	{
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
		Sdk_Class.CallStatic("startPlayCG", timeMS);
#endif
	}

	public static void stopPlayCG()
	{
#if UNITY_ANDROID && !UNITY_EDITOR && SP_SDK
		Sdk_Class.CallStatic("stopPlayCG");
#endif
	}

	//获取手机唯一设备ID， 安卓是IMEI， ios是IDFA
	public static string GetDeviceId()
	{
#if UNITY_EDITOR || !SP_SDK
        return "";
#elif UNITY_STANDALONE
        return "";
#elif UNITY_ANDROID
        try
        {
			var javaCls = CLASSTAG_AndroidAPI.GetAndroidJavaClass("com.cilugame.android.commons.DeviceUtils");
			string IMEI = CLASSTAG_AndroidAPI.CallStatic<string>(javaCls, "GetDeviceId");
            return IMEI;
        }
        catch (Exception e)
        {
            Debug.LogException(e);
            return "";
        }
#elif UNITY_IPHONE
        return _GetIdfv();
#else
        return "";
#endif
	}

	public static string GetChannelConfig(string key)
	{
#if UNITY_ANDROID && SP_SDK
        return Sdk_Class.CallStatic<string>("GetChannelConfig", key);
#else
		return "";
#endif
	}


    public static string GetGameType()
    {
		string gametype = "";
#if UNITY_ANDROID && SP_SDK
        gametype = Sdk_Class.CallStatic<string>("GetChannelConfig", "GameType");
#endif
		if (String.IsNullOrEmpty(gametype))
		{
			return CLASSTAG_GameSetting._gameSettingData.gameType;
		}
		else
		{
			return gametype;
		}
    }

    public static void SetCallback(LuaFunction func)
    { 
        if(luaCallback != null)
        {
            luaCallback.Dispose();
            luaCallback = null;
        }
        luaCallback = func;
    }

    public static void OnSdkCallback(string json)
    {
        Debug.Log("OnSdkCallback " + json);


        if (!string.IsNullOrEmpty(json))
        {
            //SdkCallbackInfo info = null;
            //try
            //{
            //    info = JsonHelper.ToObject<SdkCallbackInfo>(json);
            //    if (info.type == "skipcg")
            //    {
            //        Debug.Log("skipcg");
            //        CGPlayer.skipcg = true;
            //        return;
            //    }

            //}
            //catch (Exception e)
            //{
            //    GameDebug.LogError(e.ToString());
            //}

			if (luaCallback != null)
			{
				luaCallback.Call(json);
			}

		}

        //    if (info != null)
        //    {
        //        GameDebug.Log("info.type=" + info.type + " code=" + info.code + " data=" + info.data);
        //        switch (info.type)
        //        {
        //            //电量
        //            //case "power":
        //            //    //int intPower = 0;
        //            //    //int.TryParse(info.data, out intPower);

        //            //    //if (intPower == BaoyugameSdk.BATTERY_CHARGING)
        //            //    //{
        //            //    //    BaoyugameSdk.batteryChargingOfAndroid = true;
        //            //    //}
        //            //    //else
        //            //    //{
        //            //    //    BaoyugameSdk.batteryChargingOfAndroid = false;
        //            //    //    BaoyugameSdk.batteryLevelOfAndroid = intPower;
        //            //    //}
        //            //    break;
        //            ////信鸽注册
        //            //case "XGRegisterResult":
        //            //    //0 success  1 fail
        //            //    //ModelManager.DailyPush.SetXgState(info.data);
        //            //    break;
        //            ////信鸽账号注册
        //            //case "XGRegisterWithAccountResult":
        //            //    //0 success  1 fail
        //            //    //ServiceRequestAction.requestServer(PlayerService.pigeon(GameSetting.BundleId, info.data == "0", GameSetting.PlatformTypeId));
        //            //    break;
        //            //SDK 初始化
        //            case "init":
        //                //SPSdkManager.Instance.CallbackInit(info.code == 0);
        //                break;
        //            //SDK 登陆
        //            case "login":
        //                //0 accountLogin 1 guestLogin  2 logincancel 3 loginFail
        //                //if (info.code == 0)
        //                //{
        //                //    SdkLoginCallbackDto dto = JsHelper.ToObject<SdkLoginCallbackDto>(info.data);
        //                //    ServerManager.Instance.uid = dto.uid;//保持uid，sso验证登录的时候传递
        //                //    ServerManager.Instance.payExt = dto.payExt;
        //                //    SPSdkManager.Instance.CallbackLoginSuccess(false, dto.sessionId);
        //                //}
        //                //else if (info.code == 1)
        //                //{
        //                //    SdkLoginCallbackDto dto = JsHelper.ToObject<SdkLoginCallbackDto>(info.data);
        //                //    ServerManager.Instance.uid = dto.uid;//保持uid，sso验证登录的时候传递
        //                //    ServerManager.Instance.payExt = dto.payExt;
        //                //    SPSdkManager.Instance.CallbackLoginSuccess(true, dto.sessionId);
        //                //}
        //                //else if (info.code == 2)
        //                //{
        //                //    SPSdkManager.Instance.CallbackLoginCancel();
        //                //}
        //                //else
        //                //{
        //                //    SPSdkManager.Instance.CallbackLoginFail();
        //                //}
        //                break;
        //            //SDK 登出
        //            case "logout":
        //                //SPSdkManager.Instance.CallbackLogout(info.code == 0);
        //                break;
        //            //SDK 没有退出提供
        //            case "noExiterProvide":
        //                //SPSdkManager.Instance.CallbackNoExiterProvide();
        //                break;
        //            //退出
        //            case "exit":
        //                //SPSdkManager.Instance.CallbackExit(info.code == 0);
        //                break;
        //            //错误
        //            case "error":
        //                //GameDebuger.LogError(string.Format("错误 {0}，原因 {1}", info.code, info.data));
        //                break;
        //            //支付
        //            case "pay":
        //                //0 paySuccess 1 payCancle  2 payFail
        //                //if (info.code == 0)
        //                //{
        //                //    SPSdkManager.Instance.CallbackPay(true);
        //                //}
        //                //else
        //                //{
        //                //    SPSdkManager.Instance.CallbackPay(false);
        //                //}
        //                break;
        //        }
        //    }
        //}
    }
}
