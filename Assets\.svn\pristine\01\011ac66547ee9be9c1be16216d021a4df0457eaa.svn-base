module(...)
--magic editor build
DATA={
	atk_stophit=true,
	cmds={
		[1]={args={alive_time=0.5,},func_name=[[Name]],start_time=0,},
		[2]={args={},func_name=[[MagcAnimStart]],start_time=0,},
		[3]={
			args={sound_path=[[Magic/sound_magic_30602_1.wav]],sound_rate=1,},
			func_name=[[PlaySound]],
			start_time=0.15,
		},
		[4]={
			args={
				alive_time=3.5,
				effect={
					is_cached=true,
					magic_layer=[[center]],
					path=[[Effect/Magic/magic_eff_306/Prefabs/magic_eff_30602_att.prefab]],
					preload=true,
				},
				effect_dir_type=[[forward]],
				effect_pos={base_pos=[[atk]],depth=0,relative_angle=0,relative_dis=0,},
				excutor=[[atkobj]],
			},
			func_name=[[StandEffect]],
			start_time=0.3,
		},
		[5]={
			args={action_name=[[attack2]],excutor=[[atkobj]],},
			func_name=[[PlayAction]],
			start_time=0.3,
		},
		[6]={
			args={
				alive_time=0.85,
				ease_hide_time=0.45,
				ease_show_time=0.2,
				excutor=[[atkobj]],
				mat_path=[[Material/effect_Fresnel_Green02.mat]],
			},
			func_name=[[ActorMaterial]],
			start_time=2.8,
		},
		[7]={
			args={
				consider_hight=false,
				damage_follow=true,
				face_atk=false,
				hurt_delta=0,
				play_anim=false,
			},
			func_name=[[VicHitInfo]],
			start_time=3.3,
		},
		[8]={args={},func_name=[[MagcAnimEnd]],start_time=3.4,},
		[9]={args={},func_name=[[End]],start_time=3.45,},
	},
	group_cmds={},
	magic_anim_end_time=3.4,
	magic_anim_start_time=0,
	pre_load_res={[1]=[[Effect/Magic/magic_eff_306/Prefabs/magic_eff_30602_att.prefab]],},
	run_env=[[war]],
	type=1,
	wait_goback=true,
}
