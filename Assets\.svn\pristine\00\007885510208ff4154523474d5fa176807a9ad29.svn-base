with:832  hight:437
[logic/misc/CShareCtrl.lua:18]:ShareCallback ctor
[logic/login/CLoginCtrl.lua:5]:公告初始化...
[logic/base/CHttpCtrl.lua:32]:http get ->    https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials&client_id=rlU84dprH6I7sMjBkqM0gizl&client_secret=c15395c2433c4a904d22aa27eab8321b&
[logic/ui/CViewCtrl.lua:94]:CNotifyView ShowView
[logic/ui/CViewCtrl.lua:94]:CBottomView ShowView
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x23a55bd8"
|  json_result = true
|  timer = 16
}
[logic/audio/CSpeechCtrl.lua:72]:OnTokenGet err
[logic/base/CResCtrl.lua:198]:-->resource init done!!! 12
[logic/ui/CViewBase.lua:125]:CNotifyView LoadDone!
[logic/ui/CViewBase.lua:125]:CBottomView LoadDone!
[logic/login/CServerCtrl.lua:181]:测试 =     table:0x386BC2E0
[logic/login/CServerCtrl.lua:190]:测试 =     1001    table:0x22BDCDE0
[core/table.lua:94]:CServerCtrl.SetServerData: = {
|  [1] = {
|  |  group_id = 1
|  |  name = "1-10区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线1区"
|  |  |  |  new = 0
|  |  |  |  server_id = 1001
|  |  |  |  state = 2
|  |  |  }
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:94]:CLoginView ShowView
[logic/base/CHttpCtrl.lua:32]:http get ->    http://************:88/Note/note.txt
[logic/login/CLoginAccountPage.lua:78]:SendToCenterServer:    http://************:88/Note/note.txt    
[logic/ui/CViewBase.lua:125]:CLoginView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x38bf31f0"
|  json_result = true
|  timer = 51
}
[core/table.lua:94]:cb tResult-> = {}
[logic/login/CLoginAccountPage.lua:85]:测试 =tResult     table:0x38BF9100    nil    table
[core/table.lua:107]:���� table.keys1=     table:0x25336290
[core/table.lua:111]:���� table.keys2=     table:0x25336290
[core/table.lua:107]:���� table.keys1=     table:0x38BFB048
[core/table.lua:111]:���� table.keys2=     table:0x38BFB048
[core/table.lua:107]:���� table.keys1=     table:0x38BFC0C8
[core/table.lua:111]:���� table.keys2=     table:0x38BFC0C8
