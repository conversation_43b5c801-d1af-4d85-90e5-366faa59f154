module(...)
--magic editor build
DATA={
	atk_stophit=true,
	cmds={
		[1]={
			args={action_name=[[attack2]],excutor=[[atkobj]],},
			func_name=[[PlayAction]],
			start_time=0,
		},
		[2]={args={alive_time=0.5,},func_name=[[Name]],start_time=0,},
		[3]={
			args={
				excutor=[[atkobj]],
				face_to=[[look_at]],
				pos={base_pos=[[vic]],depth=0,relative_angle=0,relative_dis=0,},
				time=0.5,
			},
			func_name=[[FaceTo]],
			start_time=0,
		},
		[4]={
			args={
				alive_time=4.5,
				effect={
					is_cached=true,
					magic_layer=[[center]],
					path=[[Effect/Magic/magic_eff_315/Prefabs/magic_eff_31502_att01.prefab]],
					preload=true,
				},
				effect_dir_type=[[forward]],
				effect_pos={base_pos=[[atk]],depth=0,relative_angle=0,relative_dis=0,},
				excutor=[[atkobj]],
			},
			func_name=[[StandEffect]],
			start_time=0,
		},
		[5]={
			args={
				alive_time=4.5,
				effect={
					is_cached=true,
					magic_layer=[[center]],
					path=[[Effect/Magic/magic_eff_315/Prefabs/magic_eff_31502_att02.prefab]],
					preload=true,
				},
				effect_dir_type=[[forward]],
				effect_pos={base_pos=[[center]],depth=0,relative_angle=0,relative_dis=0,},
				excutor=[[vicobj]],
			},
			func_name=[[StandEffect]],
			start_time=0,
		},
		[6]={args={},func_name=[[MagcAnimStart]],start_time=0.2,},
		[7]={
			args={
				alive_time=4,
				bind_type=[[pos]],
				body_pos=[[waist]],
				effect={
					is_cached=true,
					magic_layer=[[center]],
					path=[[Effect/Magic/magic_eff_315/Prefabs/magic_eff_31502_hit02.prefab]],
					preload=true,
				},
				excutor=[[ally_alive]],
				height=0,
			},
			func_name=[[BodyEffect]],
			start_time=3.5,
		},
		[8]={args={},func_name=[[MagcAnimEnd]],start_time=4,},
		[9]={args={},func_name=[[End]],start_time=4,},
	},
	group_cmds={},
	magic_anim_end_time=4,
	magic_anim_start_time=0.2,
	pre_load_res={
		[1]=[[Effect/Magic/magic_eff_315/Prefabs/magic_eff_31502_att01.prefab]],
		[2]=[[Effect/Magic/magic_eff_315/Prefabs/magic_eff_31502_att02.prefab]],
		[3]=[[Effect/Magic/magic_eff_315/Prefabs/magic_eff_31502_hit02.prefab]],
	},
	run_env=[[dialogueani]],
	type=1,
	wait_goback=false,
}
