with:824  hight:465
[logic/misc/CShareCtrl.lua:28]:jit    false    SSE2    SSE3    SSE4.1    BMI2    fold    cse    dce    fwd    dse    narrow    loop    abc    sink    fuse
[logic/login/CLoginCtrl.lua:5]:公告初始化...
[logic/base/CHttpCtrl.lua:32]:http get ->    https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials&client_id=rlU84dprH6I7sMjBkqM0gizl&client_secret=c15395c2433c4a904d22aa27eab8321b&
[logic/ui/CViewCtrl.lua:94]:CNotifyView ShowView
[logic/ui/CViewCtrl.lua:94]:CBottomView ShowView
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x557ac3a0"
|  json_result = true
|  timer = 16
}
[logic/audio/CSpeechCtrl.lua:72]:OnTokenGet err
[logic/base/CResCtrl.lua:199]:-->resource init done!!! 12
[logic/ui/CViewBase.lua:125]:CBottomView LoadDone!
[logic/ui/CViewBase.lua:125]:CNotifyView LoadDone!
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {}
[logic/ui/CViewCtrl.lua:94]:CLoginView ShowView
[logic/base/CHttpCtrl.lua:32]:http get ->    http://42.51.32.254:88/Note_zf/note.json
[logic/ui/CViewBase.lua:125]:CLoginView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x7f2e04d8"
|  json_result = true
|  timer = 58
}
[core/table.lua:94]:cb tResult-> = {
|  info = {
|  |  role_list = {}
|  |  server_info = {
|  |  |  groups = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 1
|  |  |  |  |  name = "1-20区"
|  |  |  |  }
|  |  |  }
|  |  |  infoList = {
|  |  |  |  [1] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]活动时间：永久[-]"
|  |  |  |  |  |  |  |  title = "服务器冠名活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]全体玩家[-]"
|  |  |  |  |  |  |  |  title = "【活动对象】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "[ff0000]我的服务器，我为自己代言。[-]"
|  |  |  |  |  |  |  |  title = "【活动宣言】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "[ff0000]区服冠名权*1、红色可选御灵*5、一发入魂碎片*400、30元礼包*1000[-]"
|  |  |  |  |  |  |  |  title = "【冠名奖励】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [5] = {
|  |  |  |  |  |  |  |  text = "[ff0000]亲爱的玩家：	也许你在游戏里已经称霸全区，但是出了服务器，出了游戏，还有谁认识你现在就给你一个机会，获得至高无上的荣誉，让更多的人知道你的强大。特此举行“我的服务器，我为自己代言”，服务器玩家冠名活动。即日起，只要满足以下条件，即可获得服务器冠名权：
1）开服前3天累计充值满1000元
2）开服前7日累计充值满3000元，且区服处于未冠名状态，获得服务器冠名权  
备注：本活动遵循先到先得原则。如出现多个满足条件的，冠名资格由第一个满足条件的获得。[-]"
|  |  |  |  |  |  |  |  title = "【活动内容】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [6] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1、活动时间内有玩家满足条件后活动结束，活动期间没有玩家满足条件则保持原服务器名称。				
 2、服务器冠名长度为4个字。（必须遵守相关法律法规，不得使用负面或影响社会风气，不得出现敏感词的名称）
 3、由于冠名操作需要时间，工作人员会在2个工作日内，完成冠名操作。		
 4，每个服，冠名只有一次。被冠名的服务器不予再次冠名[-]"
|  |  |  |  |  |  |  |  title = "备注"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "冠名活动"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]活动时间：永久[-]"
|  |  |  |  |  |  |  |  title = "单日大额充值活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.活动为单日累计充值，仅计算单日内玩家累计充值总额，次日重新计算。
此活动申请方式为：充值玩家向客服申请，次日发送邮件给玩家（节假日顺延）    
单日最低充值金额：1000
该款游戏大额充值可申请额外奖励，具体福利请联系客服咨询[-]"
|  |  |  |  |  |  |  |  title = "活动说明"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "[ff0000]单日大额福利存在变动，以咨询客服为准[-]"
|  |  |  |  |  |  |  |  title = "注意"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "单日充值"
|  |  |  |  }
|  |  |  }
|  |  |  ports = {}
|  |  |  role_list = ""
|  |  |  servers = {
|  |  |  |  [1] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10001
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "三千世界(双线1区)"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1692806400
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10001"
|  |  |  |  |  start_time = 1692806400
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 24
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线10区"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1693274100
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 9111
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10021"
|  |  |  |  |  start_time = 1693274100
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 32
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线18区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1693965300
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 9011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10020"
|  |  |  |  |  start_time = 1693965300
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 33
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "月见星河(双线19区)"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1694051700
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 9211
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10022"
|  |  |  |  |  start_time = 1694051700
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [13] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 34
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线20区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = **********
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 9211
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10022"
|  |  |  |  |  start_time = **********
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10002
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "七宝(双线2区)"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1692842100
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10001"
|  |  |  |  |  start_time = 1692842100
|  |  |  |  |  state = 3
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10003
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "浮世三千(双线3区)"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1692867300
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10001"
|  |  |  |  |  start_time = 1692867300
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10004
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "黯然销魂(双线4区)"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = **********
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10001"
|  |  |  |  |  start_time = **********
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10005
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "11-17区(含5区)"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1692953700
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10001"
|  |  |  |  |  start_time = 1692953700
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 20
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线6区"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1693014900
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 9011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10020"
|  |  |  |  |  start_time = 1693014900
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 21
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线7区"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1693040100
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 9111
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10021"
|  |  |  |  |  start_time = 1693040100
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 22
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线8区"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1693101300
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 9211
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10022"
|  |  |  |  |  start_time = 1693101300
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 23
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线9区"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1693187700
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 9111
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10021"
|  |  |  |  |  start_time = 1693187700
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  }
|  |  }
|  |  token = ""
|  }
}
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {
|  [1] = {
|  |  group_id = 1
|  |  id = 1
|  |  name = "1-20区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  id = 34
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线20区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10022"
|  |  |  |  open_time = **********
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9211
|  |  |  |  }
|  |  |  |  server_id = 34
|  |  |  |  start_time = **********
|  |  |  |  state = 1
|  |  |  }
|  |  |  [10] = {
|  |  |  |  group = 1
|  |  |  |  id = 10004
|  |  |  |  ip = "**************"
|  |  |  |  name = "黯然销魂(双线4区)"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = **********
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  }
|  |  |  |  server_id = 10004
|  |  |  |  start_time = **********
|  |  |  |  state = 2
|  |  |  }
|  |  |  [11] = {
|  |  |  |  group = 1
|  |  |  |  id = 10003
|  |  |  |  ip = "**************"
|  |  |  |  name = "浮世三千(双线3区)"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = 1692867300
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  }
|  |  |  |  server_id = 10003
|  |  |  |  start_time = 1692867300
|  |  |  |  state = 2
|  |  |  }
|  |  |  [12] = {
|  |  |  |  group = 1
|  |  |  |  id = 10001
|  |  |  |  ip = "**************"
|  |  |  |  name = "三千世界(双线1区)"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = 1692806400
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  }
|  |  |  |  server_id = 10001
|  |  |  |  start_time = 1692806400
|  |  |  |  state = 2
|  |  |  }
|  |  |  [13] = {
|  |  |  |  group = 1
|  |  |  |  id = 10005
|  |  |  |  ip = "**************"
|  |  |  |  name = "11-17区(含5区)"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = 1692953700
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  }
|  |  |  |  server_id = 10005
|  |  |  |  start_time = 1692953700
|  |  |  |  state = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  group = 1
|  |  |  |  id = 32
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线18区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10020"
|  |  |  |  open_time = 1693965300
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9011
|  |  |  |  }
|  |  |  |  server_id = 32
|  |  |  |  start_time = 1693965300
|  |  |  |  state = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  group = 1
|  |  |  |  id = 33
|  |  |  |  ip = "**************"
|  |  |  |  name = "月见星河(双线19区)"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10022"
|  |  |  |  open_time = 1694051700
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9211
|  |  |  |  }
|  |  |  |  server_id = 33
|  |  |  |  start_time = 1694051700
|  |  |  |  state = 1
|  |  |  }
|  |  |  [4] = {
|  |  |  |  group = 1
|  |  |  |  id = 24
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线10区"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10021"
|  |  |  |  open_time = 1693274100
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9111
|  |  |  |  }
|  |  |  |  server_id = 24
|  |  |  |  start_time = 1693274100
|  |  |  |  state = 2
|  |  |  }
|  |  |  [5] = {
|  |  |  |  group = 1
|  |  |  |  id = 10002
|  |  |  |  ip = "**************"
|  |  |  |  name = "七宝(双线2区)"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = 1692842100
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  }
|  |  |  |  server_id = 10002
|  |  |  |  start_time = 1692842100
|  |  |  |  state = 3
|  |  |  }
|  |  |  [6] = {
|  |  |  |  group = 1
|  |  |  |  id = 22
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线8区"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10022"
|  |  |  |  open_time = 1693101300
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9211
|  |  |  |  }
|  |  |  |  server_id = 22
|  |  |  |  start_time = 1693101300
|  |  |  |  state = 2
|  |  |  }
|  |  |  [7] = {
|  |  |  |  group = 1
|  |  |  |  id = 21
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线7区"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10021"
|  |  |  |  open_time = 1693040100
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9111
|  |  |  |  }
|  |  |  |  server_id = 21
|  |  |  |  start_time = 1693040100
|  |  |  |  state = 2
|  |  |  }
|  |  |  [8] = {
|  |  |  |  group = 1
|  |  |  |  id = 20
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线6区"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10020"
|  |  |  |  open_time = 1693014900
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9011
|  |  |  |  }
|  |  |  |  server_id = 20
|  |  |  |  start_time = 1693014900
|  |  |  |  state = 2
|  |  |  }
|  |  |  [9] = {
|  |  |  |  group = 1
|  |  |  |  id = 23
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线9区"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10021"
|  |  |  |  open_time = 1693187700
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9111
|  |  |  |  }
|  |  |  |  server_id = 23
|  |  |  |  start_time = 1693187700
|  |  |  |  state = 1
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:设置公告: = {
|  [1] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]活动时间：永久[-]"
|  |  |  |  |  title = "服务器冠名活动"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]全体玩家[-]"
|  |  |  |  |  title = "【活动对象】"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "[ff0000]我的服务器，我为自己代言。[-]"
|  |  |  |  |  title = "【活动宣言】"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "[ff0000]区服冠名权*1、红色可选御灵*5、一发入魂碎片*400、30元礼包*1000[-]"
|  |  |  |  |  title = "【冠名奖励】"
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  text = "[ff0000]亲爱的玩家：	也许你在游戏里已经称霸全区，但是出了服务器，出了游戏，还有谁认识你现在就给你一个机会，获得至高无上的荣誉，让更多的人知道你的强大。特此举行“我的服务器，我为自己代言”，服务器玩家冠名活动。即日起，只要满足以下条件，即可获得服务器冠名权：
1）开服前3天累计充值满1000元
2）开服前7日累计充值满3000元，且区服处于未冠名状态，获得服务器冠名权  
备注：本活动遵循先到先得原则。如出现多个满足条件的，冠名资格由第一个满足条件的获得。[-]"
|  |  |  |  |  title = "【活动内容】"
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  text = "[ff0000]1、活动时间内有玩家满足条件后活动结束，活动期间没有玩家满足条件则保持原服务器名称。				
 2、服务器冠名长度为4个字。（必须遵守相关法律法规，不得使用负面或影响社会风气，不得出现敏感词的名称）
 3、由于冠名操作需要时间，工作人员会在2个工作日内，完成冠名操作。		
 4，每个服，冠名只有一次。被冠名的服务器不予再次冠名[-]"
|  |  |  |  |  title = "备注"
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "冠名活动"
|  }
|  [2] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]活动时间：永久[-]"
|  |  |  |  |  title = "单日大额充值活动"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]1.活动为单日累计充值，仅计算单日内玩家累计充值总额，次日重新计算。
此活动申请方式为：充值玩家向客服申请，次日发送邮件给玩家（节假日顺延）    
单日最低充值金额：1000
该款游戏大额充值可申请额外奖励，具体福利请联系客服咨询[-]"
|  |  |  |  |  title = "活动说明"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "[ff0000]单日大额福利存在变动，以咨询客服为准[-]"
|  |  |  |  |  title = "注意"
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "单日充值"
|  }
}
[core/table.lua:94]:http post -> = {
|  headers = {
|  |  Content-Type = "application/x-www-form-urlencoded"
|  }
|  url = "http://**************:8081/common/accountList"
}
[core/global.lua:59]:<color=#ffeb04>CLoginServerPage.SetServer</color>
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x55b161c8"
|  json_result = true
|  timer = 64
}
[core/table.lua:94]:cb tResult-> = {
|  code = 0
|  data = {
|  |  [1] = {
|  |  |  account = "t8672862968"
|  |  |  grade = 30
|  |  |  icon = 110
|  |  |  name = "抽象与糯米糍"
|  |  |  now_server = "gs10016"
|  |  |  pid = 160100
|  |  |  platform = 1
|  |  }
|  |  [2] = {
|  |  |  account = "t8672862968"
|  |  |  grade = 9
|  |  |  icon = 140
|  |  |  name = "狮子座女公关"
|  |  |  now_server = "gs10019"
|  |  |  pid = 190051
|  |  |  platform = 1
|  |  }
|  }
|  msg = "操作成功"
}
[core/table.lua:94]:tResult.data ---->  = {
|  [1] = {
|  |  account = "t8672862968"
|  |  grade = 30
|  |  icon = 110
|  |  name = "抽象与糯米糍"
|  |  now_server = "gs10016"
|  |  pid = 160100
|  |  platform = 1
|  }
|  [2] = {
|  |  account = "t8672862968"
|  |  grade = 9
|  |  icon = 140
|  |  name = "狮子座女公关"
|  |  now_server = "gs10019"
|  |  pid = 190051
|  |  platform = 1
|  }
}
[logic/login/CLoginCtrl.lua:154]:ConnectServer =     table:0x7F2E4870    table:0x55B3F788
[net/CNetCtrl.lua:114]:Test连接    **************    7011
[core/table.lua:94]:-->Net Receive: login.GS2CHello = {
|  time = **********
}
[core/table.lua:94]:<--Net Send: login.C2GSQueryLogin = {
|  res_file_version = {}
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:21:28
[net/netlogin.lua:208]:netlogin.GS2CQueryLogin-->
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 1001
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginAccount = {
|  account = "t8672862968"
|  client_svn_version = 96
|  client_version = "********"
|  device = "B760M GAMING (JGINYUE)"
|  imei = ""
|  is_qrcode = 0
|  mac = "00-E0-1A-9A-48-AB"
|  os = "Windows 10  (10.0.0) 64bit"
|  platform = 4
|  udid = "6370fc50ee13b3bf1fd91cc507270f729c390721"
}
[core/global.lua:59]:<color=#ffeb04>更新代码: 1049</color>
[logic/base/Utils.lua:400]:local framever, dllver, resver = C_api.Utils.GetResVersion()
if resver >= 96 or Utils.IsEditor() then
	function CWelfareCtrl.IsOpenCostSave(self)
		local b = false
		local t = g_TimeCtrl:GetTimeS()	
		if self.m_CostSaveStartTime ~= 0 and self.m_CostSaveEndTime ~= 0 and t > self.m_CostSaveStartTime and t < self.m_CostSaveEndTime then
			b = true
		end
		return b
	end
	function CPartnerHireView.IsCanYFRH(self, iParID)
		local d = data.partnerhiredata.DATA
		if table.index(d[iParID]["recommand_list"], "一发入魂") then
			return self:IsCanSSRDraw()
		end
	end
	function CPartnerEquipPage.ChangePart(self)
		local oItem = g_ItemCtrl:GetItem(self.m_CurItemID)
		if oItem and oItem:GetValue("level") == define.Partner.ParEquip.MaxLevel and oItem:GetValue("star") < define.Partner.ParEquip.MaxStar then
			if self.m_CurPart ~= self.m_UpStarPart and self.m_CurPart ~= self.m_UpStonePart then
				self.m_UpStarBtn:SetSelected(true)
				self:SwitchPart()
			end
		end
	end
	data.netdata.BAN["proto"]["herobox"]["item"]["GS2CItemQuickUse"] = true
end

[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 1001
}
[core/table.lua:94]:-->Net Receive: login.GS2CCheckInviteCodeResult = {
|  msg = "邀请码验证已关闭"
|  result = 1
}
[core/table.lua:94]:-->Net Receive: login.GS2CLoginAccount = {
|  account = "t8672862968"
}
[core/global.lua:59]:<color=#ffeb04>读取系统设置本地数据，帐号为  t8672862968</color>
[core/global.lua:59]:<color=#ffeb04>t8672862968 的系统设置本地数据为 nil，使用默认数据</color>
[core/global.lua:59]:<color=#ffeb04>=00000=  userdata: 0x55b6a478 nil</color>
[core/global.lua:59]:<color=#ffeb04>读取地图： 6100</color>
[core/global.lua:59]:<color=#ffeb04>Unity2017.4.30f1 localScale</color>
[logic/ui/CViewCtrl.lua:104]:CLoginView     CloseView
[logic/base/CResCtrl.lua:599]:res gc step start!
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[core/global.lua:59]:<color=#ffeb04>地图加载完成: 6100 ,当前地图: nil</color>
[core/table.lua:94]:Table = {}
[logic/ui/CViewCtrl.lua:94]:CCreateRoleView ShowView
[logic/misc/CUploadDataCtrl.lua:9]:流派1
[core/table.lua:94]:http post -> = {
|  headers = {
|  |  Content-Type = "application/x-www-form-urlencoded"
|  }
|  url = "http://42.51.32.254:88/clientdata/"
}
[logic/misc/CUploadDataCtrl.lua:9]:性别female
[core/table.lua:94]:http post -> = {
|  headers = {
|  |  Content-Type = "application/x-www-form-urlencoded"
|  }
|  url = "http://42.51.32.254:88/clientdata/"
}
[logic/misc/CUploadDataCtrl.lua:9]:职业2
[core/table.lua:94]:http post -> = {
|  headers = {
|  |  Content-Type = "application/x-www-form-urlencoded"
|  }
|  url = "http://42.51.32.254:88/clientdata/"
}
[logic/ui/CViewBase.lua:125]:CCreateRoleView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  timer = 101
}
[core/table.lua:94]:CommonProcess-> = {
|  timer = 114
}
[core/table.lua:94]:CommonProcess-> = {
|  timer = 117
}
[logic/model/CModel.lua:181]:造型:    141    ,没有武器    2000
[logic/base/CResCtrl.lua:626]:res gc finish!
[logic/ui/CViewCtrl.lua:94]:CLoginView ShowView
[logic/ui/CViewCtrl.lua:104]:CCreateRoleView     CloseView
[logic/misc/CUploadDataCtrl.lua:9]:返回登录界面
[core/table.lua:94]:http post -> = {
|  headers = {
|  |  Content-Type = "application/x-www-form-urlencoded"
|  }
|  url = "http://42.51.32.254:88/clientdata/"
}
[core/table.lua:94]:CommonProcess-> = {
|  timer = 135
}
[logic/base/CHttpCtrl.lua:32]:http get ->    http://42.51.32.254:88/Note_zf/note.json
[logic/ui/CViewBase.lua:125]:CLoginView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x56763b28"
|  json_result = true
|  timer = 159
}
[core/table.lua:94]:cb tResult-> = {
|  info = {
|  |  role_list = {}
|  |  server_info = {
|  |  |  groups = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 1
|  |  |  |  |  name = "1-20区"
|  |  |  |  }
|  |  |  }
|  |  |  infoList = {
|  |  |  |  [1] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]活动时间：永久[-]"
|  |  |  |  |  |  |  |  title = "服务器冠名活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]全体玩家[-]"
|  |  |  |  |  |  |  |  title = "【活动对象】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "[ff0000]我的服务器，我为自己代言。[-]"
|  |  |  |  |  |  |  |  title = "【活动宣言】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "[ff0000]区服冠名权*1、红色可选御灵*5、一发入魂碎片*400、30元礼包*1000[-]"
|  |  |  |  |  |  |  |  title = "【冠名奖励】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [5] = {
|  |  |  |  |  |  |  |  text = "[ff0000]亲爱的玩家：	也许你在游戏里已经称霸全区，但是出了服务器，出了游戏，还有谁认识你现在就给你一个机会，获得至高无上的荣誉，让更多的人知道你的强大。特此举行“我的服务器，我为自己代言”，服务器玩家冠名活动。即日起，只要满足以下条件，即可获得服务器冠名权：
1）开服前3天累计充值满1000元
2）开服前7日累计充值满3000元，且区服处于未冠名状态，获得服务器冠名权  
备注：本活动遵循先到先得原则。如出现多个满足条件的，冠名资格由第一个满足条件的获得。[-]"
|  |  |  |  |  |  |  |  title = "【活动内容】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [6] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1、活动时间内有玩家满足条件后活动结束，活动期间没有玩家满足条件则保持原服务器名称。				
 2、服务器冠名长度为4个字。（必须遵守相关法律法规，不得使用负面或影响社会风气，不得出现敏感词的名称）
 3、由于冠名操作需要时间，工作人员会在2个工作日内，完成冠名操作。		
 4，每个服，冠名只有一次。被冠名的服务器不予再次冠名[-]"
|  |  |  |  |  |  |  |  title = "备注"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "冠名活动"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]活动时间：永久[-]"
|  |  |  |  |  |  |  |  title = "单日大额充值活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.活动为单日累计充值，仅计算单日内玩家累计充值总额，次日重新计算。
此活动申请方式为：充值玩家向客服申请，次日发送邮件给玩家（节假日顺延）    
单日最低充值金额：1000
该款游戏大额充值可申请额外奖励，具体福利请联系客服咨询[-]"
|  |  |  |  |  |  |  |  title = "活动说明"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "[ff0000]单日大额福利存在变动，以咨询客服为准[-]"
|  |  |  |  |  |  |  |  title = "注意"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "单日充值"
|  |  |  |  }
|  |  |  }
|  |  |  ports = {}
|  |  |  role_list = ""
|  |  |  servers = {
|  |  |  |  [1] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10001
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "三千世界(双线1区)"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1692806400
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10001"
|  |  |  |  |  start_time = 1692806400
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 24
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线10区"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1693274100
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 9111
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10021"
|  |  |  |  |  start_time = 1693274100
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 32
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线18区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1693965300
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 9011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10020"
|  |  |  |  |  start_time = 1693965300
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 33
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "月见星河(双线19区)"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1694051700
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 9211
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10022"
|  |  |  |  |  start_time = 1694051700
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [13] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 34
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线20区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = **********
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 9211
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10022"
|  |  |  |  |  start_time = **********
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10002
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "七宝(双线2区)"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1692842100
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10001"
|  |  |  |  |  start_time = 1692842100
|  |  |  |  |  state = 3
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10003
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "浮世三千(双线3区)"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1692867300
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10001"
|  |  |  |  |  start_time = 1692867300
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10004
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "黯然销魂(双线4区)"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = **********
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10001"
|  |  |  |  |  start_time = **********
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10005
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "11-17区(含5区)"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1692953700
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10001"
|  |  |  |  |  start_time = 1692953700
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 20
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线6区"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1693014900
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 9011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10020"
|  |  |  |  |  start_time = 1693014900
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 21
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线7区"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1693040100
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 9111
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10021"
|  |  |  |  |  start_time = 1693040100
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 22
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线8区"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1693101300
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 9211
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10022"
|  |  |  |  |  start_time = 1693101300
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 23
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线9区"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1693187700
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 9111
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10021"
|  |  |  |  |  start_time = 1693187700
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  }
|  |  }
|  |  token = ""
|  }
}
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {
|  [1] = {
|  |  group_id = 1
|  |  id = 1
|  |  name = "1-20区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  id = 34
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线20区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10022"
|  |  |  |  open_time = **********
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9211
|  |  |  |  }
|  |  |  |  server_id = 34
|  |  |  |  start_time = **********
|  |  |  |  state = 1
|  |  |  }
|  |  |  [10] = {
|  |  |  |  group = 1
|  |  |  |  id = 10004
|  |  |  |  ip = "**************"
|  |  |  |  name = "黯然销魂(双线4区)"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = **********
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  }
|  |  |  |  server_id = 10004
|  |  |  |  start_time = **********
|  |  |  |  state = 2
|  |  |  }
|  |  |  [11] = {
|  |  |  |  group = 1
|  |  |  |  id = 10003
|  |  |  |  ip = "**************"
|  |  |  |  name = "浮世三千(双线3区)"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = 1692867300
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  }
|  |  |  |  server_id = 10003
|  |  |  |  start_time = 1692867300
|  |  |  |  state = 2
|  |  |  }
|  |  |  [12] = {
|  |  |  |  group = 1
|  |  |  |  id = 10001
|  |  |  |  ip = "**************"
|  |  |  |  name = "三千世界(双线1区)"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = 1692806400
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  }
|  |  |  |  server_id = 10001
|  |  |  |  start_time = 1692806400
|  |  |  |  state = 2
|  |  |  }
|  |  |  [13] = {
|  |  |  |  group = 1
|  |  |  |  id = 10005
|  |  |  |  ip = "**************"
|  |  |  |  name = "11-17区(含5区)"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = 1692953700
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  }
|  |  |  |  server_id = 10005
|  |  |  |  start_time = 1692953700
|  |  |  |  state = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  group = 1
|  |  |  |  id = 32
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线18区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10020"
|  |  |  |  open_time = 1693965300
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9011
|  |  |  |  }
|  |  |  |  server_id = 32
|  |  |  |  start_time = 1693965300
|  |  |  |  state = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  group = 1
|  |  |  |  id = 33
|  |  |  |  ip = "**************"
|  |  |  |  name = "月见星河(双线19区)"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10022"
|  |  |  |  open_time = 1694051700
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9211
|  |  |  |  }
|  |  |  |  server_id = 33
|  |  |  |  start_time = 1694051700
|  |  |  |  state = 1
|  |  |  }
|  |  |  [4] = {
|  |  |  |  group = 1
|  |  |  |  id = 24
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线10区"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10021"
|  |  |  |  open_time = 1693274100
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9111
|  |  |  |  }
|  |  |  |  server_id = 24
|  |  |  |  start_time = 1693274100
|  |  |  |  state = 2
|  |  |  }
|  |  |  [5] = {
|  |  |  |  group = 1
|  |  |  |  id = 10002
|  |  |  |  ip = "**************"
|  |  |  |  name = "七宝(双线2区)"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = 1692842100
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  }
|  |  |  |  server_id = 10002
|  |  |  |  start_time = 1692842100
|  |  |  |  state = 3
|  |  |  }
|  |  |  [6] = {
|  |  |  |  group = 1
|  |  |  |  id = 22
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线8区"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10022"
|  |  |  |  open_time = 1693101300
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9211
|  |  |  |  }
|  |  |  |  server_id = 22
|  |  |  |  start_time = 1693101300
|  |  |  |  state = 2
|  |  |  }
|  |  |  [7] = {
|  |  |  |  group = 1
|  |  |  |  id = 21
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线7区"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10021"
|  |  |  |  open_time = 1693040100
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9111
|  |  |  |  }
|  |  |  |  server_id = 21
|  |  |  |  start_time = 1693040100
|  |  |  |  state = 2
|  |  |  }
|  |  |  [8] = {
|  |  |  |  group = 1
|  |  |  |  id = 20
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线6区"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10020"
|  |  |  |  open_time = 1693014900
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9011
|  |  |  |  }
|  |  |  |  server_id = 20
|  |  |  |  start_time = 1693014900
|  |  |  |  state = 2
|  |  |  }
|  |  |  [9] = {
|  |  |  |  group = 1
|  |  |  |  id = 23
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线9区"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10021"
|  |  |  |  open_time = 1693187700
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9111
|  |  |  |  }
|  |  |  |  server_id = 23
|  |  |  |  start_time = 1693187700
|  |  |  |  state = 1
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:设置公告: = {
|  [1] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]活动时间：永久[-]"
|  |  |  |  |  title = "服务器冠名活动"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]全体玩家[-]"
|  |  |  |  |  title = "【活动对象】"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "[ff0000]我的服务器，我为自己代言。[-]"
|  |  |  |  |  title = "【活动宣言】"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "[ff0000]区服冠名权*1、红色可选御灵*5、一发入魂碎片*400、30元礼包*1000[-]"
|  |  |  |  |  title = "【冠名奖励】"
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  text = "[ff0000]亲爱的玩家：	也许你在游戏里已经称霸全区，但是出了服务器，出了游戏，还有谁认识你现在就给你一个机会，获得至高无上的荣誉，让更多的人知道你的强大。特此举行“我的服务器，我为自己代言”，服务器玩家冠名活动。即日起，只要满足以下条件，即可获得服务器冠名权：
1）开服前3天累计充值满1000元
2）开服前7日累计充值满3000元，且区服处于未冠名状态，获得服务器冠名权  
备注：本活动遵循先到先得原则。如出现多个满足条件的，冠名资格由第一个满足条件的获得。[-]"
|  |  |  |  |  title = "【活动内容】"
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  text = "[ff0000]1、活动时间内有玩家满足条件后活动结束，活动期间没有玩家满足条件则保持原服务器名称。				
 2、服务器冠名长度为4个字。（必须遵守相关法律法规，不得使用负面或影响社会风气，不得出现敏感词的名称）
 3、由于冠名操作需要时间，工作人员会在2个工作日内，完成冠名操作。		
 4，每个服，冠名只有一次。被冠名的服务器不予再次冠名[-]"
|  |  |  |  |  title = "备注"
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "冠名活动"
|  }
|  [2] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]活动时间：永久[-]"
|  |  |  |  |  title = "单日大额充值活动"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]1.活动为单日累计充值，仅计算单日内玩家累计充值总额，次日重新计算。
此活动申请方式为：充值玩家向客服申请，次日发送邮件给玩家（节假日顺延）    
单日最低充值金额：1000
该款游戏大额充值可申请额外奖励，具体福利请联系客服咨询[-]"
|  |  |  |  |  title = "活动说明"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "[ff0000]单日大额福利存在变动，以咨询客服为准[-]"
|  |  |  |  |  title = "注意"
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "单日充值"
|  }
}
[core/table.lua:94]:http post -> = {
|  headers = {
|  |  Content-Type = "application/x-www-form-urlencoded"
|  }
|  url = "http://**************:8081/common/accountList"
}
[core/global.lua:59]:<color=#ffeb04>CLoginServerPage.SetServer</color>
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x11e59660"
|  json_result = true
|  timer = 165
}
[core/table.lua:94]:cb tResult-> = {
|  code = 0
|  data = {
|  |  [1] = {
|  |  |  account = "t8672862968"
|  |  |  grade = 30
|  |  |  icon = 110
|  |  |  name = "抽象与糯米糍"
|  |  |  now_server = "gs10016"
|  |  |  pid = 160100
|  |  |  platform = 1
|  |  }
|  |  [2] = {
|  |  |  account = "t8672862968"
|  |  |  grade = 9
|  |  |  icon = 140
|  |  |  name = "狮子座女公关"
|  |  |  now_server = "gs10019"
|  |  |  pid = 190051
|  |  |  platform = 1
|  |  }
|  }
|  msg = "操作成功"
}
[core/table.lua:94]:tResult.data ---->  = {
|  [1] = {
|  |  account = "t8672862968"
|  |  grade = 30
|  |  icon = 110
|  |  name = "抽象与糯米糍"
|  |  now_server = "gs10016"
|  |  pid = 160100
|  |  platform = 1
|  }
|  [2] = {
|  |  account = "t8672862968"
|  |  grade = 9
|  |  icon = 140
|  |  name = "狮子座女公关"
|  |  now_server = "gs10019"
|  |  pid = 190051
|  |  platform = 1
|  }
}
[logic/ui/CViewCtrl.lua:94]:CSelectServerView ShowView
[core/table.lua:94]:GroupServers------------------------> = {
|  [1] = {
|  |  group_id = 1
|  |  id = 1
|  |  name = "1-20区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  id = 34
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线20区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10022"
|  |  |  |  open_time = **********
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9211
|  |  |  |  }
|  |  |  |  server_id = 34
|  |  |  |  start_time = **********
|  |  |  |  state = 1
|  |  |  }
|  |  |  [10] = {
|  |  |  |  group = 1
|  |  |  |  id = 10004
|  |  |  |  ip = "**************"
|  |  |  |  name = "黯然销魂(双线4区)"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = **********
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  }
|  |  |  |  server_id = 10004
|  |  |  |  start_time = **********
|  |  |  |  state = 2
|  |  |  }
|  |  |  [11] = {
|  |  |  |  group = 1
|  |  |  |  id = 10003
|  |  |  |  ip = "**************"
|  |  |  |  name = "浮世三千(双线3区)"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = 1692867300
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  }
|  |  |  |  server_id = 10003
|  |  |  |  start_time = 1692867300
|  |  |  |  state = 2
|  |  |  }
|  |  |  [12] = {
|  |  |  |  group = 1
|  |  |  |  id = 10001
|  |  |  |  ip = "**************"
|  |  |  |  name = "三千世界(双线1区)"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = 1692806400
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  }
|  |  |  |  server_id = 10001
|  |  |  |  start_time = 1692806400
|  |  |  |  state = 2
|  |  |  }
|  |  |  [13] = {
|  |  |  |  group = 1
|  |  |  |  id = 10005
|  |  |  |  ip = "**************"
|  |  |  |  name = "11-17区(含5区)"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = 1692953700
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  }
|  |  |  |  server_id = 10005
|  |  |  |  start_time = 1692953700
|  |  |  |  state = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  group = 1
|  |  |  |  id = 32
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线18区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10020"
|  |  |  |  open_time = 1693965300
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9011
|  |  |  |  }
|  |  |  |  server_id = 32
|  |  |  |  start_time = 1693965300
|  |  |  |  state = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  group = 1
|  |  |  |  id = 33
|  |  |  |  ip = "**************"
|  |  |  |  name = "月见星河(双线19区)"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10022"
|  |  |  |  open_time = 1694051700
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9211
|  |  |  |  }
|  |  |  |  server_id = 33
|  |  |  |  start_time = 1694051700
|  |  |  |  state = 1
|  |  |  }
|  |  |  [4] = {
|  |  |  |  group = 1
|  |  |  |  id = 24
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线10区"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10021"
|  |  |  |  open_time = 1693274100
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9111
|  |  |  |  }
|  |  |  |  server_id = 24
|  |  |  |  start_time = 1693274100
|  |  |  |  state = 2
|  |  |  }
|  |  |  [5] = {
|  |  |  |  group = 1
|  |  |  |  id = 10002
|  |  |  |  ip = "**************"
|  |  |  |  name = "七宝(双线2区)"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = 1692842100
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  }
|  |  |  |  server_id = 10002
|  |  |  |  start_time = 1692842100
|  |  |  |  state = 3
|  |  |  }
|  |  |  [6] = {
|  |  |  |  group = 1
|  |  |  |  id = 22
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线8区"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10022"
|  |  |  |  open_time = 1693101300
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9211
|  |  |  |  }
|  |  |  |  server_id = 22
|  |  |  |  start_time = 1693101300
|  |  |  |  state = 2
|  |  |  }
|  |  |  [7] = {
|  |  |  |  group = 1
|  |  |  |  id = 21
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线7区"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10021"
|  |  |  |  open_time = 1693040100
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9111
|  |  |  |  }
|  |  |  |  server_id = 21
|  |  |  |  start_time = 1693040100
|  |  |  |  state = 2
|  |  |  }
|  |  |  [8] = {
|  |  |  |  group = 1
|  |  |  |  id = 20
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线6区"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10020"
|  |  |  |  open_time = 1693014900
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9011
|  |  |  |  }
|  |  |  |  server_id = 20
|  |  |  |  start_time = 1693014900
|  |  |  |  state = 2
|  |  |  }
|  |  |  [9] = {
|  |  |  |  group = 1
|  |  |  |  id = 23
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线9区"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10021"
|  |  |  |  open_time = 1693187700
|  |  |  |  ports = {
|  |  |  |  |  [1] = 9111
|  |  |  |  }
|  |  |  |  server_id = 23
|  |  |  |  start_time = 1693187700
|  |  |  |  state = 1
|  |  |  }
|  |  }
|  }
}
[logic/ui/CViewBase.lua:125]:CSelectServerView LoadDone!
[core/table.lua:94]:是否获取到服务器列表： = "nil"
[core/table.lua:94]:是否获取到服务器列表1： = {
|  group = 1
|  id = 10001
|  ip = "**************"
|  name = "合服区"
|  new = 0
|  now_server_id = "bus_gs10001"
|  open_time = 1692806400
|  ports = {
|  |  [1] = 7011
|  }
|  server_id = 10001
|  start_time = 1692806400
|  state = 2
}
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[logic/login/CLoginCtrl.lua:154]:ConnectServer =     table:0x7C6EE290    table:0x7C82B680
[net/CNetCtrl.lua:114]:Test连接    **************    7011
[core/table.lua:94]:-->Net Receive: login.GS2CHello = {
|  time = **********
}
[core/table.lua:94]:<--Net Send: login.C2GSQueryLogin = {
|  res_file_version = {}
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:21:36
[net/netlogin.lua:208]:netlogin.GS2CQueryLogin-->
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 1001
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginAccount = {
|  account = "t8672862968"
|  client_svn_version = 96
|  client_version = "********"
|  device = "B760M GAMING (JGINYUE)"
|  imei = ""
|  is_qrcode = 0
|  mac = "00-E0-1A-9A-48-AB"
|  os = "Windows 10  (10.0.0) 64bit"
|  platform = 1
|  udid = "6370fc50ee13b3bf1fd91cc507270f729c390721"
}
[core/global.lua:59]:<color=#ffeb04>更新代码: 1049</color>
[logic/base/Utils.lua:400]:local framever, dllver, resver = C_api.Utils.GetResVersion()
if resver >= 96 or Utils.IsEditor() then
	function CWelfareCtrl.IsOpenCostSave(self)
		local b = false
		local t = g_TimeCtrl:GetTimeS()	
		if self.m_CostSaveStartTime ~= 0 and self.m_CostSaveEndTime ~= 0 and t > self.m_CostSaveStartTime and t < self.m_CostSaveEndTime then
			b = true
		end
		return b
	end
	function CPartnerHireView.IsCanYFRH(self, iParID)
		local d = data.partnerhiredata.DATA
		if table.index(d[iParID]["recommand_list"], "一发入魂") then
			return self:IsCanSSRDraw()
		end
	end
	function CPartnerEquipPage.ChangePart(self)
		local oItem = g_ItemCtrl:GetItem(self.m_CurItemID)
		if oItem and oItem:GetValue("level") == define.Partner.ParEquip.MaxLevel and oItem:GetValue("star") < define.Partner.ParEquip.MaxStar then
			if self.m_CurPart ~= self.m_UpStarPart and self.m_CurPart ~= self.m_UpStonePart then
				self.m_UpStarBtn:SetSelected(true)
				self:SwitchPart()
			end
		end
	end
	data.netdata.BAN["proto"]["herobox"]["item"]["GS2CItemQuickUse"] = true
end

[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 1001
}
[core/table.lua:94]:-->Net Receive: login.GS2CCheckInviteCodeResult = {
|  msg = "邀请码验证已关闭"
|  result = 1
}
[core/table.lua:94]:-->Net Receive: login.GS2CLoginAccount = {
|  account = "t8672862968"
|  role_list = {
|  |  [1] = {
|  |  |  grade = 30
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 110
|  |  |  |  weapon = 2500
|  |  |  }
|  |  |  pid = 160100
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginRole = {
|  account = "t8672862968"
|  pid = 160100
}
[core/global.lua:59]:<color=#ffeb04>读取系统设置本地数据，帐号为  t8672862968</color>
[core/global.lua:59]:<color=#ffeb04>t8672862968 的系统设置本地数据为 nil，使用默认数据</color>
[core/table.lua:94]:-->Net Receive: login.GS2CLoginRole = {
|  account = "t8672862968"
|  pid = 160100
|  role = {
|  |  abnormal_attr_ratio = 630
|  |  active = 37
|  |  attack = 405
|  |  bcmd = {
|  |  |  [1] = {
|  |  |  |  idx = 208
|  |  |  }
|  |  |  [10] = {
|  |  |  |  cmd = "集火"
|  |  |  |  idx = 201
|  |  |  }
|  |  |  [11] = {
|  |  |  |  idx = 106
|  |  |  }
|  |  |  [12] = {
|  |  |  |  cmd = "减速"
|  |  |  |  idx = 203
|  |  |  }
|  |  |  [13] = {
|  |  |  |  cmd = "禁疗"
|  |  |  |  idx = 204
|  |  |  }
|  |  |  [14] = {
|  |  |  |  idx = 205
|  |  |  }
|  |  |  [15] = {
|  |  |  |  idx = 206
|  |  |  }
|  |  |  [16] = {
|  |  |  |  idx = 207
|  |  |  }
|  |  |  [2] = {
|  |  |  |  cmd = "封印"
|  |  |  |  idx = 202
|  |  |  }
|  |  |  [3] = {
|  |  |  |  idx = 107
|  |  |  }
|  |  |  [4] = {
|  |  |  |  idx = 105
|  |  |  }
|  |  |  [5] = {
|  |  |  |  idx = 108
|  |  |  }
|  |  |  [6] = {
|  |  |  |  cmd = "治疗"
|  |  |  |  idx = 101
|  |  |  }
|  |  |  [7] = {
|  |  |  |  cmd = "复活"
|  |  |  |  idx = 102
|  |  |  }
|  |  |  [8] = {
|  |  |  |  cmd = "加速"
|  |  |  |  idx = 103
|  |  |  }
|  |  |  [9] = {
|  |  |  |  cmd = "解封"
|  |  |  |  idx = 104
|  |  |  }
|  |  }
|  |  coin = 638664
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 46
|  |  energy = 199
|  |  exp = 79554
|  |  goldcoin = 70750
|  |  grade = 30
|  |  kp_sdk_info = {
|  |  |  create_time = 1693366877
|  |  |  upgrade_time = 1694392499
|  |  }
|  |  mask = "7e6683d1ffffffe"
|  |  max_hp = 4578
|  |  model_info = {
|  |  |  color = {
|  |  |  |  [1] = 0
|  |  |  }
|  |  |  shape = 110
|  |  |  weapon = 2500
|  |  }
|  |  name = "抽象与糯米糍"
|  |  open_day = 155
|  |  org_fuben_cnt = 2
|  |  org_offer = 200
|  |  power = 1436
|  |  res_abnormal_ratio = 630
|  |  res_critical_ratio = 500
|  |  school = 1
|  |  school_branch = 1
|  |  sex = 1
|  |  show_id = 160100
|  |  skill_point = 38
|  |  speed = 767
|  |  systemsetting = {}
|  }
|  role_token = "**************"
|  xg_account = "bus160100"
}
[logic/ui/CViewCtrl.lua:94]:CMainMenuView ShowView
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 630
|  active = 37
|  arenamedal = 0
|  attack = 405
|  bcmd = {
|  |  [1] = {
|  |  |  idx = 208
|  |  }
|  |  [10] = {
|  |  |  cmd = "集火"
|  |  |  idx = 201
|  |  }
|  |  [11] = {
|  |  |  idx = 106
|  |  }
|  |  [12] = {
|  |  |  cmd = "减速"
|  |  |  idx = 203
|  |  }
|  |  [13] = {
|  |  |  cmd = "禁疗"
|  |  |  idx = 204
|  |  }
|  |  [14] = {
|  |  |  idx = 205
|  |  }
|  |  [15] = {
|  |  |  idx = 206
|  |  }
|  |  [16] = {
|  |  |  idx = 207
|  |  }
|  |  [2] = {
|  |  |  cmd = "封印"
|  |  |  idx = 202
|  |  }
|  |  [3] = {
|  |  |  idx = 107
|  |  }
|  |  [4] = {
|  |  |  idx = 105
|  |  }
|  |  [5] = {
|  |  |  idx = 108
|  |  }
|  |  [6] = {
|  |  |  cmd = "治疗"
|  |  |  idx = 101
|  |  }
|  |  [7] = {
|  |  |  cmd = "复活"
|  |  |  idx = 102
|  |  }
|  |  [8] = {
|  |  |  cmd = "加速"
|  |  |  idx = 103
|  |  }
|  |  [9] = {
|  |  |  cmd = "解封"
|  |  |  idx = 104
|  |  }
|  }
|  camp = 0
|  chatself = false
|  chubeiexp = 0
|  coin = 638664
|  coin_over = 0
|  color_coin = 0
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 46
|  energy = 199
|  exp = 79554
|  followers = {}
|  goldcoin = 70750
|  grade = 30
|  hp = 0
|  kp_sdk_info = {
|  |  create_time = 1693366877
|  |  upgrade_time = 1694392499
|  }
|  max_hp = 4578
|  medal = 0
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 110
|  |  weapon = 2500
|  }
|  name = "抽象与糯米糍"
|  open_day = 155
|  org_fuben_cnt = 2
|  org_offer = 200
|  power = 1436
|  res_abnormal_ratio = 630
|  res_critical_ratio = 500
|  school = 1
|  school_branch = 1
|  sex = 1
|  show_id = 160100
|  skill_point = 38
|  skin = 0
|  speed = 767
|  systemsetting = {}
|  title_info = {}
|  trapmine_point = 0
|  travel_score = 0
|  upvote_amount = 0
}
[logic/misc/CTimeCtrl.lua:64]:HeartBeat-->开启心跳包检测
[core/table.lua:94]:<--Net Send: title.C2GSTitleInfoList = {}
[core/table.lua:94]:-->Net Receive: player.GS2CGamePushSetting = {}
[core/table.lua:94]:-->Net Receive: player.GS2CShapeList = {
|  shapes = {
|  |  [1] = 110
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CGuidanceInfo = {
|  guidanceinfo = {
|  |  key = {
|  |  |  [1] = 7000041
|  |  |  [10] = 2002099
|  |  |  [11] = 7000081
|  |  |  [12] = 2006099
|  |  |  [13] = 1003099
|  |  |  [14] = 3006099
|  |  |  [15] = 3007099
|  |  |  [16] = 3008099
|  |  |  [17] = 3003099
|  |  |  [18] = 7000091
|  |  |  [19] = 3004099
|  |  |  [2] = 7000051
|  |  |  [20] = 3005099
|  |  |  [21] = 5000300
|  |  |  [22] = 1004099
|  |  |  [23] = 3009099
|  |  |  [24] = 3010099
|  |  |  [25] = 3011099
|  |  |  [26] = 1006099
|  |  |  [27] = 3016099
|  |  |  [28] = 1010099
|  |  |  [29] = 1005099
|  |  |  [3] = 6010002
|  |  |  [30] = 3012099
|  |  |  [31] = 7000071
|  |  |  [32] = 3014099
|  |  |  [33] = 3015099
|  |  |  [34] = 3013099
|  |  |  [35] = 1007099
|  |  |  [36] = 3019099
|  |  |  [37] = 1012099
|  |  |  [38] = 5007099
|  |  |  [39] = 1008099
|  |  |  [4] = 1027099
|  |  |  [40] = 3020099
|  |  |  [41] = 1009099
|  |  |  [42] = 2004099
|  |  |  [43] = 2005099
|  |  |  [44] = 1011099
|  |  |  [45] = 5011099
|  |  |  [46] = 5006099
|  |  |  [47] = 3028099
|  |  |  [48] = 3026099
|  |  |  [49] = 3027099
|  |  |  [5] = 1028099
|  |  |  [50] = 1013099
|  |  |  [51] = 1014099
|  |  |  [52] = 5000900
|  |  |  [53] = 1015099
|  |  |  [54] = 5000901
|  |  |  [6] = 2001099
|  |  |  [7] = 7000061
|  |  |  [8] = 1001099
|  |  |  [9] = 3002099
|  |  }
|  |  version = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>>>>>>>>>>>>>>>>>>>>> GS2CGuidanceInfo </color>
[core/table.lua:94]:-->Net Receive: travel.GS2CLoginTravelPartner = {
|  travel_partner = {
|  |  server_time = **********
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CServerGradeInfo = {
|  server_grade = 100
}
[core/table.lua:94]:-->Net Receive: scene.GS2CShowScene = {
|  map_id = 101000
|  scene_id = 12
|  scene_name = "帝都"
}
[core/global.lua:59]:<color=#ffeb04>CResCtrl.MoveToSecondary</color>
[core/table.lua:94]:-->Net Receive: task.GS2CLoginTask = {
|  shimen_status = 1
|  taskdata = {
|  |  [1] = {
|  |  |  acceptgrade = 20
|  |  |  acceptnpc = 10610
|  |  |  clientnpc = {
|  |  |  |  [1] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 302
|  |  |  |  |  }
|  |  |  |  |  name = "重华"
|  |  |  |  |  npcid = 1449
|  |  |  |  |  npctype = 10610
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 45260
|  |  |  |  |  |  y = 13640
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 313
|  |  |  |  |  }
|  |  |  |  |  name = "檀"
|  |  |  |  |  npcid = 1450
|  |  |  |  |  npctype = 10611
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 45670
|  |  |  |  |  |  y = 12860
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  detaildesc = "离开教堂之后竟然再次遇到重华。"
|  |  |  name = "偶遇"
|  |  |  patrolinfo = {}
|  |  |  pickiteminfo = {}
|  |  |  placeinfo = {}
|  |  |  shapeinfo = {}
|  |  |  statusinfo = {
|  |  |  |  note = "CreateTask"
|  |  |  |  status = 2
|  |  |  }
|  |  |  submitnpc = 10610
|  |  |  target = 10610
|  |  |  targetdesc = "选手们"
|  |  |  taskid = 10030
|  |  |  taskitem = {}
|  |  |  tasktype = 1
|  |  |  traceinfo = {}
|  |  |  type = 2
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>login task</color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x4bee2508"
|  |  AssociatedPick = "function: 0x4bee2568"
|  |  AssociatedSubmit = "function: 0x4bee2538"
|  |  CreateDefalutData = "function: 0x4bee04e8"
|  |  GetChaptetFubenData = "function: 0x4bee2228"
|  |  GetProgressThing = "function: 0x4bee25c8"
|  |  GetRemainTime = "function: 0x4bee0480"
|  |  GetStatus = "function: 0x4bee2628"
|  |  GetTaskClientExtStrDic = "function: 0x4bee2598"
|  |  GetTaskTypeSpriteteName = "function: 0x4bee21f8"
|  |  GetTraceInfo = "function: 0x4bee2350"
|  |  GetTraceNpcType = "function: 0x4bee21c8"
|  |  GetValue = "function: 0x4bee2290"
|  |  IsAbandon = "function: 0x4bee22f0"
|  |  IsAddEscortDynamicNpc = "function: 0x4bee2960"
|  |  IsMissMengTask = "function: 0x4bee04b0"
|  |  IsPassChaterFuben = "function: 0x4bee2258"
|  |  IsTaskSpecityAction = "function: 0x4bee2320"
|  |  IsTaskSpecityCategory = "function: 0x4bee24d8"
|  |  New = "function: 0x4bee6e58"
|  |  NewByData = "function: 0x4bedfa28"
|  |  RaiseProgressIdx = "function: 0x4bee25f8"
|  |  RefreshTask = "function: 0x4bee22c0"
|  |  ResetEndTime = "function: 0x4bee0550"
|  |  SetStatus = "function: 0x4bee2990"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4bee0450"
|  }
|  m_CData = {
|  |  ChapterFb = "1,8"
|  |  autoDoNextTask = 10031
|  |  clientExtStr = ""
|  |  name = "偶遇"
|  |  submitNpcId = 10610
|  |  submitRewardStr = {
|  |  |  [1] = "R1030"
|  |  }
|  |  taskWalkingTips = "要怎么样才能潜入教会呢？;none;none"
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 20
|  |  acceptnpc = 10610
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 302
|  |  |  |  }
|  |  |  |  name = "重华"
|  |  |  |  npcid = 1449
|  |  |  |  npctype = 10610
|  |  |  |  pos_info = {
|  |  |  |  |  x = 45260
|  |  |  |  |  y = 13640
|  |  |  |  }
|  |  |  }
|  |  |  [2] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 313
|  |  |  |  }
|  |  |  |  name = "檀"
|  |  |  |  npcid = 1450
|  |  |  |  npctype = 10611
|  |  |  |  pos_info = {
|  |  |  |  |  x = 45670
|  |  |  |  |  y = 12860
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "离开教堂之后竟然再次遇到重华。"
|  |  isdone = 0
|  |  name = "偶遇"
|  |  patrolinfo = {}
|  |  pickiteminfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10610
|  |  target = 10610
|  |  targetdesc = "选手们"
|  |  taskid = 10030
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CTeachProgress = {}
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshPartnerTask = {
|  partnertask_progress = {
|  |  [1] = {
|  |  |  parid = 313
|  |  |  status = 1
|  |  |  taskid = 62195
|  |  }
|  |  [2] = {
|  |  |  parid = 403
|  |  |  status = 1
|  |  |  taskid = 62169
|  |  }
|  |  [3] = {
|  |  |  parid = 502
|  |  |  status = 1
|  |  |  taskid = 62090
|  |  }
|  |  [4] = {
|  |  |  parid = 501
|  |  |  status = 1
|  |  |  taskid = 62085
|  |  }
|  |  [5] = {
|  |  |  parid = 302
|  |  |  status = 1
|  |  |  taskid = 62080
|  |  }
|  |  [6] = {
|  |  |  parid = 507
|  |  |  status = 1
|  |  |  taskid = 62100
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  day_task = {
|  |  [1] = 1016
|  |  [10] = 1003
|  |  [2] = 3005
|  |  [3] = 1017
|  |  [4] = 1001
|  |  [5] = 3008
|  |  [6] = 3009
|  |  [7] = 1011
|  |  [8] = 1024
|  |  [9] = 2003
|  }
}
[core/table.lua:94]:-->Net Receive: state.GS2CLoginState = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginHuodongInfo = {
|  convoyinfo = {
|  |  convoy_partner = 1505
|  |  free_time = 1
|  |  pool_info = {
|  |  |  [1] = {
|  |  |  |  level = 1
|  |  |  |  partnerid = 1505
|  |  |  |  pos = 1
|  |  |  |  rewardid = 1001
|  |  |  }
|  |  |  [2] = {
|  |  |  |  level = 2
|  |  |  |  partnerid = 1200
|  |  |  |  pos = 2
|  |  |  |  rewardid = 1002
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [3] = {
|  |  |  |  level = 3
|  |  |  |  partnerid = 1018
|  |  |  |  pos = 3
|  |  |  |  rewardid = 1003
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [4] = {
|  |  |  |  level = 4
|  |  |  |  partnerid = 1508
|  |  |  |  pos = 4
|  |  |  |  rewardid = 1004
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [5] = {
|  |  |  |  level = 5
|  |  |  |  partnerid = 1020
|  |  |  |  pos = 5
|  |  |  |  rewardid = 1005
|  |  |  |  weight = 25
|  |  |  }
|  |  }
|  |  selected_pos = 1
|  |  target_npc = 5016
|  }
|  dailytrain = {
|  |  reward_times = 420
|  }
|  hireinfo = {
|  |  [1] = {
|  |  |  parid = 403
|  |  |  times = 1
|  |  }
|  |  [2] = {
|  |  |  parid = 501
|  |  |  times = 1
|  |  }
|  |  [3] = {
|  |  |  parid = 502
|  |  |  times = 1
|  |  }
|  |  [4] = {
|  |  |  parid = 507
|  |  |  times = 1
|  |  }
|  }
|  huntinfo = {
|  |  npcinfo = {
|  |  |  [1] = {
|  |  |  |  level = 1
|  |  |  |  status = 1
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginChapterInfo = {
|  extrareward_info = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  level = 6
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  chapter = 1
|  |  |  level = 8
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  chapter = 1
|  |  |  level = 4
|  |  |  type = 1
|  |  }
|  }
|  finalchapter = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  level = 8
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  type = 1
|  |  }
|  }
|  totalstar_info = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  star = 24
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: handbook.GS2CLoginBookList = {
|  book_list = {
|  |  [1] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 313131
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 113131
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100313
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [2] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 314031
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 114031
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100403
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [3] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 315071
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115071
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100507
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [4] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 315011
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  id = 315012
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115011
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100501
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [5] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 313021
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = {
|  |  |  |  |  |  [1] = 113023
|  |  |  |  |  }
|  |  |  |  |  id = 313022
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 113021
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100302
|  |  |  red_point = 3
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [6] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 315021
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115021
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100502
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  }
|  red_points = {
|  |  [1] = {
|  |  |  book_type = 1
|  |  |  red_point = 1
|  |  }
|  |  [2] = {
|  |  |  book_type = 2
|  |  }
|  |  [3] = {
|  |  |  book_type = 3
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: team.GS2CInviteInfo = {}
[core/table.lua:94]:-->Net Receive: mail.GS2CLoginMail = {
|  simpleinfo = {
|  |  [1] = {
|  |  |  createtime = 1694252582
|  |  |  hasattach = 2
|  |  |  keeptime = 1296000
|  |  |  mailid = 1
|  |  |  opened = 1
|  |  |  title = "补发水晶"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: friend.GS2CLoginFriend = {
|  friend_onlinestatus_list = {
|  |  [1] = {
|  |  |  pid = 160114
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>好友版本一致，无需更新</color>
[core/table.lua:94]:<--Net Send: friend.C2GSSimpleFriendList = {
|  pidlist = {
|  |  [1] = 160114
|  }
}
[core/table.lua:94]:-->Net Receive: friend.GS2CApplyList = {
|  pidlist = {
|  |  [1] = 12616
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 21016
}
[core/table.lua:94]:<--Net Send: friend.C2GSQueryFriendApply = {
|  pid_list = {
|  |  [1] = 12616
|  }
}
[core/table.lua:94]:-->Net Receive: friend.GS2CFriendSetting = {
|  setting = {
|  |  notify = 1
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHDAddChargeInfo = {
|  endtime = 1694530739
|  list = {
|  |  [1] = {
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  id = 2
|  |  }
|  |  [3] = {
|  |  |  id = 3
|  |  }
|  |  [4] = {
|  |  |  id = 4
|  |  }
|  |  [5] = {
|  |  |  id = 5
|  |  }
|  }
|  starttime = 1694444400
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 2304
|  |  |  type = "yk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 2304
|  |  |  type = "zsk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
|  mask = "c"
}
[core/table.lua:94]:CNetCtrl解析mask: GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 2304
|  |  |  type = "yk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 2304
|  |  |  type = "zsk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: store.GS2CPayForColorCoinInfo = {
|  colorcoin_list = {
|  |  [1] = {
|  |  |  key = "goldcoinstore_1005"
|  |  }
|  |  [10] = {
|  |  |  key = "goldcoinstore_2004"
|  |  }
|  |  [11] = {
|  |  |  key = "goldcoinstore_2005"
|  |  }
|  |  [12] = {
|  |  |  key = "goldcoinstore_2006"
|  |  }
|  |  [13] = {
|  |  |  key = "goldcoinstore_2007"
|  |  }
|  |  [14] = {
|  |  |  key = "goldcoinstore_1003"
|  |  }
|  |  [15] = {
|  |  |  key = "goldcoinstore_1004"
|  |  }
|  |  [2] = {
|  |  |  key = "goldcoinstore_1006"
|  |  }
|  |  [3] = {
|  |  |  key = "goldcoinstore_1007"
|  |  }
|  |  [4] = {
|  |  |  key = "goldcoinstore_1008"
|  |  }
|  |  [5] = {
|  |  |  key = "goldcoinstore_1001"
|  |  }
|  |  [6] = {
|  |  |  key = "goldcoinstore_1002"
|  |  }
|  |  [7] = {
|  |  |  key = "goldcoinstore_2001"
|  |  }
|  |  [8] = {
|  |  |  key = "goldcoinstore_2002"
|  |  }
|  |  [9] = {
|  |  |  key = "goldcoinstore_2003"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeRewrad = {
|  end_time = 1694530739
|  reward_info = {
|  |  [1] = {
|  |  |  left_amount = 2
|  |  |  rmb = 328
|  |  }
|  |  [2] = {
|  |  |  left_amount = 2
|  |  |  rmb = 648
|  |  }
|  }
|  schedule = 1
|  start_time = 1694444400
}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcFightInfoList = {
|  info_list = {
|  |  [1] = {
|  |  |  npc_type = 10024
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [10] = {
|  |  |  npc_type = 10007
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [11] = {
|  |  |  npc_type = 10008
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [12] = {
|  |  |  npc_type = 10009
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [13] = {
|  |  |  npc_type = 10011
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [14] = {
|  |  |  npc_type = 10012
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [15] = {
|  |  |  npc_type = 10013
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [16] = {
|  |  |  npc_type = 10000
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [17] = {
|  |  |  npc_type = 10023
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [18] = {
|  |  |  npc_type = 10014
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [19] = {
|  |  |  npc_type = 10015
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [2] = {
|  |  |  npc_type = 10002
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [20] = {
|  |  |  npc_type = 10016
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [21] = {
|  |  |  npc_type = 10017
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [22] = {
|  |  |  npc_type = 10018
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [23] = {
|  |  |  npc_type = 10019
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [24] = {
|  |  |  npc_type = 10020
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [25] = {
|  |  |  npc_type = 10021
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [26] = {
|  |  |  npc_type = 10022
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [3] = {
|  |  |  npc_type = 10010
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [4] = {
|  |  |  npc_type = 10001
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [5] = {
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [6] = {
|  |  |  npc_type = 10003
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [7] = {
|  |  |  npc_type = 10004
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [8] = {
|  |  |  npc_type = 10005
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [9] = {
|  |  |  npc_type = 10006
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginTrapmine = {
|  rare_monster = {
|  |  [1] = {
|  |  |  map_id = 202000
|  |  |  partypes = {
|  |  |  |  [1] = 316
|  |  |  |  [2] = 401
|  |  |  |  [3] = 314
|  |  |  }
|  |  }
|  |  [2] = {
|  |  |  map_id = 204000
|  |  |  partypes = {
|  |  |  |  [1] = 404
|  |  |  |  [2] = 402
|  |  |  |  [3] = 403
|  |  |  }
|  |  }
|  |  [3] = {
|  |  |  map_id = 205000
|  |  |  partypes = {
|  |  |  |  [1] = 508
|  |  |  |  [2] = 509
|  |  |  |  [3] = 514
|  |  |  }
|  |  }
|  |  [4] = {
|  |  |  map_id = 206000
|  |  |  partypes = {
|  |  |  |  [1] = 412
|  |  |  |  [2] = 413
|  |  |  |  [3] = 414
|  |  |  }
|  |  }
|  |  [5] = {
|  |  |  map_id = 210400
|  |  |  partypes = {
|  |  |  |  [1] = 504
|  |  |  |  [2] = 506
|  |  |  |  [3] = 507
|  |  |  }
|  |  }
|  |  [6] = {
|  |  |  map_id = 200000
|  |  |  partypes = {
|  |  |  |  [1] = 416
|  |  |  |  [2] = 417
|  |  |  |  [3] = 502
|  |  |  }
|  |  }
|  |  [7] = {
|  |  |  map_id = 201000
|  |  |  partypes = {
|  |  |  |  [1] = 308
|  |  |  |  [2] = 301
|  |  |  |  [3] = 302
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2COneRMBGift = {
|  endtime = 1694530739
|  gift = {
|  |  [1] = {
|  |  |  key = 1
|  |  }
|  |  [2] = {
|  |  |  key = 2
|  |  }
|  |  [3] = {
|  |  |  key = 3
|  |  }
|  }
|  starttime = 1694444400
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraWarState = {
|  time = 1694769546
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraQueueStatus = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHeroBoxRecord = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2COnlineGift = {
|  onlinetime = 2563
|  reward = {
|  |  [1] = {
|  |  |  random_reward = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 4
|  |  |  |  |  sid = 27408
|  |  |  |  }
|  |  |  }
|  |  |  rewardid = 1
|  |  |  stable_reward = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 2
|  |  |  |  |  sid = 10030
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  amount = 1
|  |  |  |  |  sid = 1002
|  |  |  |  }
|  |  |  }
|  |  }
|  }
|  status = 1
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CRefreshRewardBack = {
|  info = {
|  |  [1] = {
|  |  |  left = 2
|  |  |  sid = 2006
|  |  |  vip = 100
|  |  }
|  |  [2] = {
|  |  |  left = 1
|  |  |  sid = 2001
|  |  |  vip = 30
|  |  }
|  }
}
[logic/ui/CSprite.lua:159]:DynamicSprite dSprInfo    Item    10012
[logic/ui/CViewBase.lua:125]:CMainMenuView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: item.GS2CLoginItem = {
|  itemdata = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  create_time = 1694252582
|  |  |  id = 1
|  |  |  itemlevel = 4
|  |  |  name = "压测道具3"
|  |  |  sid = 15002
|  |  }
|  |  [10] = {
|  |  |  amount = 1
|  |  |  create_time = 1694252582
|  |  |  id = 10
|  |  |  itemlevel = 4
|  |  |  name = "压测道具2"
|  |  |  sid = 15001
|  |  }
|  |  [11] = {
|  |  |  amount = 20
|  |  |  create_time = 1694416664
|  |  |  id = 11
|  |  |  itemlevel = 3
|  |  |  name = "焦糖包"
|  |  |  sid = 14011
|  |  }
|  |  [12] = {
|  |  |  amount = 1
|  |  |  create_time = 1693642979
|  |  |  id = 18
|  |  |  itemlevel = 4
|  |  |  name = "武器原石"
|  |  |  sid = 11004
|  |  }
|  |  [13] = {
|  |  |  amount = 3
|  |  |  create_time = 1693642990
|  |  |  id = 19
|  |  |  itemlevel = 3
|  |  |  name = "淬灵云晶"
|  |  |  sid = 11101
|  |  }
|  |  [14] = {
|  |  |  amount = 1
|  |  |  create_time = 1693556687
|  |  |  id = 20
|  |  |  itemlevel = 2
|  |  |  name = "3级绯红宝石"
|  |  |  sid = 18002
|  |  }
|  |  [15] = {
|  |  |  amount = 1
|  |  |  create_time = 1693555721
|  |  |  id = 12
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 2
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101006
|  |  }
|  |  [16] = {
|  |  |  amount = 1
|  |  |  create_time = 1693367088
|  |  |  id = 13
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 1
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101002
|  |  }
|  |  [17] = {
|  |  |  amount = 1
|  |  |  create_time = 1693880134
|  |  |  id = 14
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 3
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101002
|  |  }
|  |  [18] = {
|  |  |  amount = 1
|  |  |  create_time = 1693880128
|  |  |  id = 15
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 4
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101003
|  |  }
|  |  [19] = {
|  |  |  amount = 1
|  |  |  create_time = 1693556113
|  |  |  id = 16
|  |  |  itemlevel = 3
|  |  |  name = "梦觉书·低"
|  |  |  sid = 27402
|  |  }
|  |  [2] = {
|  |  |  amount = 1
|  |  |  create_time = 1694252582
|  |  |  id = 2
|  |  |  itemlevel = 4
|  |  |  name = "压测道具1"
|  |  |  sid = 15000
|  |  }
|  |  [20] = {
|  |  |  amount = 1
|  |  |  create_time = 1694392519
|  |  |  id = 17
|  |  |  itemlevel = 3
|  |  |  name = "妖风铃·低"
|  |  |  sid = 27408
|  |  }
|  |  [21] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1693366877
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 3
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 1
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  pos = 1
|  |  |  |  stone_sid = 3100000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 21
|  |  |  itemlevel = 1
|  |  |  name = "练习红魔钺"
|  |  |  power = 57
|  |  |  sid = 2100000
|  |  }
|  |  [22] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 121
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 24
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1693366877
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 1
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 17
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  pos = 2
|  |  |  |  stone_sid = 4000000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 26
|  |  |  itemlevel = 1
|  |  |  name = "练习飘逸巾"
|  |  |  power = 24
|  |  |  sid = 2200000
|  |  }
|  |  [23] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1693366877
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 1
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 17
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  pos = 3
|  |  |  |  stone_sid = 4100000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 22
|  |  |  itemlevel = 1
|  |  |  name = "练习剑麻甲"
|  |  |  power = 32
|  |  |  sid = 2310000
|  |  }
|  |  [24] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1693366877
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 3
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "speed"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 7
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  pos = 4
|  |  |  |  stone_sid = 4200000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 24
|  |  |  itemlevel = 1
|  |  |  name = "练习草织戒"
|  |  |  power = 19
|  |  |  sid = 2400000
|  |  }
|  |  [25] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 364
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 72
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1693366877
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 3
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 17
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  pos = 5
|  |  |  |  stone_sid = 4300000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 23
|  |  |  itemlevel = 1
|  |  |  name = "练习折桂腰"
|  |  |  power = 72
|  |  |  sid = 2510000
|  |  }
|  |  [26] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "speed"
|  |  |  |  |  value = 33
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 11
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 77
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1693366877
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 3
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "speed"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 7
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  pos = 6
|  |  |  |  stone_sid = 4400000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 25
|  |  |  itemlevel = 1
|  |  |  name = "练习虬草鞋"
|  |  |  power = 77
|  |  |  sid = 2610000
|  |  }
|  |  [3] = {
|  |  |  amount = 1
|  |  |  create_time = 1694255892
|  |  |  id = 3
|  |  |  itemlevel = 4
|  |  |  name = "一发入魂碎片"
|  |  |  sid = 13212
|  |  }
|  |  [4] = {
|  |  |  amount = 1
|  |  |  create_time = 1693642840
|  |  |  id = 4
|  |  |  itemlevel = 4
|  |  |  name = "50级橙武礼包"
|  |  |  sid = 12083
|  |  }
|  |  [5] = {
|  |  |  amount = 5
|  |  |  create_time = 1694392498
|  |  |  id = 5
|  |  |  itemlevel = 4
|  |  |  name = "星象图"
|  |  |  sid = 10024
|  |  |  treasure_info = {
|  |  |  |  treasure_mapid = 202000
|  |  |  |  treasure_posx = 6
|  |  |  |  treasure_posy = 7
|  |  |  }
|  |  }
|  |  [6] = {
|  |  |  amount = 6
|  |  |  create_time = 1694408046
|  |  |  id = 6
|  |  |  itemlevel = 4
|  |  |  name = "灵魂钥匙"
|  |  |  sid = 10040
|  |  }
|  |  [7] = {
|  |  |  amount = 4
|  |  |  create_time = 1693556183
|  |  |  id = 7
|  |  |  itemlevel = 2
|  |  |  name = "一星云母"
|  |  |  sid = 14031
|  |  }
|  |  [8] = {
|  |  |  amount = 24
|  |  |  create_time = 1693880156
|  |  |  id = 8
|  |  |  itemlevel = 2
|  |  |  name = "深蓝琥珀"
|  |  |  sid = 14021
|  |  }
|  |  [9] = {
|  |  |  amount = 7
|  |  |  create_time = 1694392519
|  |  |  id = 9
|  |  |  itemlevel = 5
|  |  |  name = "扫荡券"
|  |  |  sid = 10030
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>loginitem</color>
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliReddot = {
|  cday = 1
}
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenPlanName = {
|  fuwen_name = {
|  |  [1] = {
|  |  |  name = "默认方案"
|  |  |  plan = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CDailySignInfo = {
|  sign_info = {
|  |  [1] = {
|  |  |  key = "week"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeScore = {
|  cur_id = 1
|  score_info = {}
}
[core/table.lua:94]:-->Net Receive: partner.GS2CDrawCardUI = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTimeResumeInfo = {
|  end_time = 1694530739
|  plan_id = 1
|  start_time = 1694444400
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CRefreshTimeResume = {
|  resume_amount = 30300
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CGradeGiftInfo = {
|  buy_gift = {
|  |  items = {
|  |  |  [1] = {
|  |  |  |  amount = 5
|  |  |  |  sid = 10040
|  |  |  |  virtual = 10040
|  |  |  }
|  |  |  [2] = {
|  |  |  |  amount = 150
|  |  |  |  sid = 11001
|  |  |  |  virtual = 11001
|  |  |  }
|  |  |  [3] = {
|  |  |  |  amount = 66666
|  |  |  |  sid = 1002
|  |  |  |  virtual = 1002
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  discount = 12
|  endtime = 1694488896
|  free_gift = {
|  |  items = {
|  |  |  [1] = {
|  |  |  |  amount = 10
|  |  |  |  sid = 11001
|  |  |  |  virtual = 11001
|  |  |  }
|  |  |  [2] = {
|  |  |  |  amount = 6666
|  |  |  |  sid = 1002
|  |  |  |  virtual = 1002
|  |  |  }
|  |  }
|  }
|  grade = 40
|  ios_payid = "com.kaopu.ylq.appstore.lb.12"
|  now_price = 12
|  old_price = 100
|  payid = "com.kaopu.ylq.lb.12"
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CNewFieldBoss = {
|  bossid = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterScene = {
|  eid = 35
|  pos_info = {
|  |  face_y = 22025
|  |  x = 31103
|  |  y = 13083
|  }
|  scene_id = 12
}
[core/global.lua:59]:<color=#ffeb04>result  nil</color>
[core/global.lua:59]:<color=#ffeb04>=00000=  userdata: 0x55b6a478 nil</color>
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 21016
}
[core/global.lua:59]:<color=#ffeb04>读取地图： 1020</color>
[core/global.lua:59]:<color=#ffeb04>Unity2017.4.30f1 localScale</color>
[logic/ui/CViewCtrl.lua:104]:CLoginView     CloseView
[logic/base/CResCtrl.lua:599]:res gc step start!
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[core/global.lua:59]:<color=#ffeb04>地图加载完成: 1020 ,当前地图: 6100</color>
[core/global.lua:59]:<color=#ffeb04>删除地图: 6100</color>
[core/table.lua:94]:Table = {}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayBuy = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CHistoryCharge = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFirstChargeUI = {
|  bOpen = true
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliPoint = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawCnt = {
|  cnt = 5
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliTime = {
|  endtime = 1693015505
|  starttime = 1693015385
}
[core/table.lua:94]:-->Net Receive: house.GS2CEnterHouse = {
|  buff_info = {}
|  furniture_info = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  lock_status = 1
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  type = 2
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  type = 3
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  type = 4
|  |  }
|  |  [5] = {
|  |  |  level = 1
|  |  |  type = 5
|  |  }
|  }
|  handle_type = 1
|  max_train = 1
|  max_warm_degree = 1000
|  owner_pid = 160100
|  partner_info = {
|  |  [1] = {
|  |  |  coin = 2726
|  |  |  love_level = 1
|  |  |  love_ship = 213
|  |  |  type = 1001
|  |  }
|  |  [2] = {
|  |  |  type = 1003
|  |  }
|  }
|  talent_level = 1
|  warm_degree = 82
}
[core/table.lua:94]:-->Net Receive: house.GS2CPartnerExchangeUI = {
|  handle_type = 1
|  love_cnt = 10
|  max_gift_cnt = 10
|  max_love_cnt = 10
|  partner_gift_cnt = 10
}
[core/table.lua:94]:-->Net Receive: house.GS2COpenWorkDesk = {
|  desk_info = {
|  |  [1] = {
|  |  |  item_sid = 30606
|  |  |  lock_status = 1
|  |  |  pos = 1
|  |  |  status = 3
|  |  }
|  |  [2] = {
|  |  |  pos = 2
|  |  |  status = 1
|  |  }
|  |  [3] = {
|  |  |  pos = 3
|  |  |  status = 1
|  |  }
|  |  [4] = {
|  |  |  lock_status = 1
|  |  |  pos = 4
|  |  |  status = 1
|  |  }
|  }
|  handle_type = 1
|  owner_pid = 160100
|  talent_level = 1
|  talent_schedule = 100
}
[core/table.lua:94]:-->Net Receive: player.GS2CTodayInfo = {
|  info = {
|  |  mask = "0"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: todyInfo = {}
[core/table.lua:94]:-->Net Receive: player.GS2CGameShare = {
|  game_share = {
|  |  [1] = {
|  |  |  type = "picture_share"
|  |  }
|  |  [2] = {
|  |  |  type = "skin_share"
|  |  }
|  |  [3] = {
|  |  |  type = "draw_card_share"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginRewardInfo = {
|  breed_val = 37
|  login_day = 8
|  rewarded_day = 143
}
[logic/ui/CViewCtrl.lua:94]:CLoginRewardView ShowView
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartnerList = {
|  partner_list = {
|  |  [1] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 606
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 147
|  |  |  equip_list = {
|  |  |  |  [1] = 13
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 49160
|  |  |  grade = 26
|  |  |  hp = 5376
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 5376
|  |  |  model_info = {
|  |  |  |  shape = 302
|  |  |  |  skin = 203020
|  |  |  }
|  |  |  name = "重华"
|  |  |  parid = 1
|  |  |  partner_type = 302
|  |  |  patahp = 5376
|  |  |  power = 1754
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 600
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30201
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30202
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30203
|  |  |  |  }
|  |  |  }
|  |  |  speed = 420
|  |  |  star = 1
|  |  }
|  |  [2] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 633
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 135
|  |  |  equip_list = {
|  |  |  |  [1] = 12
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 44340
|  |  |  grade = 24
|  |  |  hp = 4932
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 4932
|  |  |  model_info = {
|  |  |  |  shape = 502
|  |  |  |  skin = 205020
|  |  |  }
|  |  |  name = "马面面"
|  |  |  parid = 2
|  |  |  partner_type = 502
|  |  |  patahp = 4932
|  |  |  power = 1686
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 500
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50201
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50202
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50203
|  |  |  |  }
|  |  |  }
|  |  |  speed = 315
|  |  |  star = 1
|  |  }
|  |  [3] = {
|  |  |  abnormal_attr_ratio = 600
|  |  |  amount = 1
|  |  |  attack = 575
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 151
|  |  |  equip_list = {
|  |  |  |  [1] = 14
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 37040
|  |  |  grade = 22
|  |  |  hp = 5060
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 5060
|  |  |  model_info = {
|  |  |  |  shape = 313
|  |  |  |  skin = 203130
|  |  |  }
|  |  |  name = "檀"
|  |  |  parid = 3
|  |  |  partner_type = 313
|  |  |  patahp = 5060
|  |  |  power = 1905
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 700
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31301
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31302
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31303
|  |  |  |  }
|  |  |  }
|  |  |  speed = 665
|  |  |  star = 2
|  |  }
|  |  [4] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 608
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 600
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 136
|  |  |  equip_list = {
|  |  |  |  [1] = 15
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 41760
|  |  |  grade = 24
|  |  |  hp = 5469
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 5469
|  |  |  model_info = {
|  |  |  |  shape = 403
|  |  |  |  skin = 204030
|  |  |  }
|  |  |  name = "蛇姬"
|  |  |  parid = 4
|  |  |  partner_type = 403
|  |  |  patahp = 5469
|  |  |  power = 1761
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 600
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40301
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40302
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40303
|  |  |  |  }
|  |  |  }
|  |  |  speed = 735
|  |  |  star = 1
|  |  }
|  |  [5] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 362
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 1000
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 67
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 6000
|  |  |  grade = 7
|  |  |  hp = 2555
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 2555
|  |  |  model_info = {
|  |  |  |  shape = 501
|  |  |  |  skin = 205010
|  |  |  }
|  |  |  name = "阿坊"
|  |  |  parid = 5
|  |  |  partner_type = 501
|  |  |  patahp = 2555
|  |  |  power = 897
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 400
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50101
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50102
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50103
|  |  |  |  }
|  |  |  }
|  |  |  speed = 70
|  |  |  star = 1
|  |  }
|  |  [6] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 245
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 1000
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 54
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  grade = 1
|  |  |  mask = "fffffefe"
|  |  |  max_hp = 2240
|  |  |  model_info = {
|  |  |  |  shape = 507
|  |  |  |  skin = 205070
|  |  |  }
|  |  |  name = "魃女"
|  |  |  parid = 6
|  |  |  partner_type = 507
|  |  |  patahp = 2240
|  |  |  power = 742
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 400
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50702
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50701
|  |  |  |  }
|  |  |  }
|  |  |  speed = 945
|  |  |  star = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartner = {
|  fight_info = {
|  |  [1] = {
|  |  |  parid = 1
|  |  |  pos = 1
|  |  }
|  |  [2] = {
|  |  |  parid = 2
|  |  |  pos = 2
|  |  }
|  |  [3] = {
|  |  |  parid = 4
|  |  |  pos = 3
|  |  }
|  |  [4] = {
|  |  |  parid = 3
|  |  |  pos = 4
|  |  }
|  }
|  owned_equip_list = {
|  |  [1] = 6101001
|  }
|  owned_partner_list = {
|  |  [1] = 313
|  |  [2] = 403
|  |  [3] = 507
|  |  [4] = 501
|  |  [5] = 302
|  |  [6] = 502
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPicturePosList = {}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  mask = "1997c2e0000000"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  give_org_equip = {}
|  give_org_wish = {}
|  is_equip_wish = 0
|  is_org_wish = 0
|  org_build_status = 0
|  org_build_time = 0
|  org_id = 0
|  org_leader = ""
|  org_level = 0
|  org_pos = 0
|  org_red_packet = 0
|  org_sign_reward = 0
|  org_status = 0
|  orgname = ""
}
[core/table.lua:94]:-->Net Receive: skill.GS2CLoginSkill = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3001
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3002
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  needcost = 15
|  |  |  sk = 3004
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  needcost = 25
|  |  |  sk = 3005
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3006
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3007
|  |  |  type = 1
|  |  }
|  }
}
[logic/skill/CSkillCtrl.lua:112]:技能数据登陆协议返回:
[core/table.lua:94]:Table = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3001
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3002
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  needcost = 15
|  |  |  sk = 3004
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  needcost = 25
|  |  |  sk = 3005
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3006
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3007
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginParSoulPlan = {}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {
|  infolist = {
|  |  [1] = {
|  |  |  blist = {
|  |  |  |  [1] = 2
|  |  |  }
|  |  |  id = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayMain = {
|  server_day = 12
}
[core/table.lua:94]:-->Net Receive: task.GS2CLoginAchieveTask = {
|  info = {
|  |  [1] = {
|  |  |  achievetype = 1
|  |  |  describe = "主角任意技能升至2级"
|  |  |  name = "提升技能"
|  |  |  target = 1
|  |  |  taskid = 31003
|  |  }
|  |  [2] = {
|  |  |  achievetype = 2
|  |  |  degree = 2
|  |  |  describe = "升级任意4件符文到+3"
|  |  |  name = "升级符文（3）"
|  |  |  target = 4
|  |  |  taskid = 31531
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>LoginAchieveTask</color>
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  abnormal_attr_ratio = 630
|  |  attack = 405
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 46
|  |  mask = "87ff00"
|  |  max_hp = 4578
|  |  power = 1436
|  |  res_abnormal_ratio = 630
|  |  res_critical_ratio = 500
|  |  speed = 767
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 630
|  attack = 405
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 46
|  hp = 0
|  max_hp = 4578
|  power = 1436
|  res_abnormal_ratio = 630
|  res_critical_ratio = 500
|  speed = 767
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1102
|  |  |  }
|  |  |  name = "峰馆长"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 2
|  |  npctype = 5040
|  }
|  eid = 1
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 1
|  pos_info = {
|  |  x = 31800
|  |  y = 17600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1102
|  }
|  name = "峰馆长"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1508
|  |  |  }
|  |  |  name = "小叶"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 5
|  |  npctype = 5041
|  }
|  eid = 2
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 2
|  pos_info = {
|  |  x = 40000
|  |  y = 14600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1508
|  }
|  name = "小叶"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1510
|  |  |  }
|  |  |  name = "小佳"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 11
|  |  npctype = 5043
|  }
|  eid = 4
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 4
|  pos_info = {
|  |  x = 27000
|  |  y = 4000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1510
|  }
|  name = "小佳"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10117
|  |  triggerNpc = 5043
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1510
|  |  |  }
|  |  |  name = "小兰"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 14
|  |  npctype = 5044
|  }
|  eid = 5
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 5
|  pos_info = {
|  |  x = 19000
|  |  y = 4000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1510
|  }
|  name = "小兰"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10117
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1503
|  |  |  }
|  |  |  name = "喵小布"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 21
|  |  npctype = 5047
|  }
|  eid = 6
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 6
|  pos_info = {
|  |  x = 33000
|  |  y = 9500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1503
|  }
|  name = "喵小布"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10117
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1012
|  |  |  }
|  |  |  name = "克瑞斯汀"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 24
|  |  npctype = 5048
|  }
|  eid = 7
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 7
|  pos_info = {
|  |  x = 20200
|  |  y = 21100
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1012
|  }
|  name = "克瑞斯汀"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1510
|  |  |  }
|  |  |  name = "小佳"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 57
|  |  npctype = 5064
|  }
|  eid = 8
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 8
|  pos_info = {
|  |  x = 13800
|  |  y = 20000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1510
|  }
|  name = "小佳"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10117
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1014
|  |  |  }
|  |  |  name = "乔焱"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 66
|  |  npctype = 5003
|  }
|  eid = 11
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 11
|  pos_info = {
|  |  x = 13200
|  |  y = 24600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1014
|  }
|  name = "乔焱"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1011
|  |  |  }
|  |  |  name = "遥"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 72
|  |  npctype = 5005
|  }
|  eid = 13
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 13
|  pos_info = {
|  |  x = 45500
|  |  y = 20000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1011
|  }
|  name = "遥"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 414
|  |  |  }
|  |  |  name = "蓝堂昼"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 75
|  |  npctype = 5006
|  }
|  eid = 14
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 14
|  pos_info = {
|  |  x = 28000
|  |  y = 14500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 414
|  }
|  name = "蓝堂昼"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 311
|  |  |  }
|  |  |  name = "花常酒"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 78
|  |  npctype = 5007
|  }
|  eid = 15
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 15
|  pos_info = {
|  |  x = 35600
|  |  y = 13400
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 311
|  }
|  name = "花常酒"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 508
|  |  |  }
|  |  |  name = "魂夕"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 81
|  |  npctype = 5008
|  }
|  eid = 16
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 16
|  pos_info = {
|  |  x = 24000
|  |  y = 12900
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 508
|  }
|  name = "魂夕"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1016
|  |  |  }
|  |  |  name = "水生"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 196
|  |  npctype = 5009
|  }
|  eid = 17
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 17
|  pos_info = {
|  |  x = 24000
|  |  y = 22000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1016
|  }
|  name = "水生"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1150
|  |  |  }
|  |  |  name = "飞龙哥"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 199
|  |  npctype = 5010
|  }
|  eid = 18
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 18
|  pos_info = {
|  |  x = 29000
|  |  y = 3500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1150
|  }
|  name = "飞龙哥"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1151
|  |  |  }
|  |  |  name = "邓酒爷"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 202
|  |  npctype = 5011
|  }
|  eid = 19
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 19
|  pos_info = {
|  |  x = 22300
|  |  y = 5100
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1151
|  }
|  name = "邓酒爷"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 417
|  |  |  }
|  |  |  name = "松姑子"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 220
|  |  npctype = 5019
|  }
|  eid = 21
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 21
|  pos_info = {
|  |  x = 42300
|  |  y = 24600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 417
|  }
|  name = "松姑子"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_player = {
|  |  block = {
|  |  |  mask = "63e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 130
|  |  |  |  weapon = 2000
|  |  |  }
|  |  |  name = "光头"
|  |  |  show_id = 12583
|  |  }
|  |  pid = 12583
|  }
|  eid = 32
|  scene_id = 12
|  type = 1
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 32
|  pos_info = {
|  |  face_y = 48426
|  |  x = 29430
|  |  y = 12583
|  }
|  scene_id = 12
|  type = 1
}
[core/table.lua:94]:CNetCtrl解析mask: PlayerAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 130
|  |  weapon = 2000
|  }
|  name = "光头"
|  show_id = 12583
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_player = {
|  |  block = {
|  |  |  mask = "63e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 140
|  |  |  |  weapon = 2000
|  |  |  }
|  |  |  name = "小可乐呀"
|  |  |  show_id = 12616
|  |  |  title_info = {
|  |  |  |  [1] = {
|  |  |  |  |  create_time = 1694445329
|  |  |  |  |  left_time = 1694445674
|  |  |  |  |  name = "阿巴阿巴新人"
|  |  |  |  |  tid = 1077
|  |  |  |  }
|  |  |  }
|  |  |  war_tag = 1
|  |  }
|  |  pid = 12616
|  }
|  eid = 34
|  scene_id = 12
|  type = 1
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 34
|  pos_info = {
|  |  face_y = 96906
|  |  x = 44469
|  |  y = 10754
|  }
|  scene_id = 12
|  type = 1
}
[core/table.lua:94]:CNetCtrl解析mask: PlayerAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 140
|  |  weapon = 2000
|  }
|  name = "小可乐呀"
|  show_id = 12616
|  title_info = {
|  |  [1] = {
|  |  |  create_time = 1694445329
|  |  |  left_time = 1694445674
|  |  |  name = "阿巴阿巴新人"
|  |  |  tid = 1077
|  |  }
|  }
|  war_tag = 1
}
[core/table.lua:94]:-->Net Receive: title.GS2CTitleInfoList = {
|  infos = {
|  |  [1] = {
|  |  |  name = "8段"
|  |  |  tid = 1008
|  |  }
|  |  [10] = {
|  |  |  name = "应用宝贵族"
|  |  |  tid = 1017
|  |  }
|  |  [11] = {
|  |  |  name = "王者公会"
|  |  |  tid = 1018
|  |  }
|  |  [12] = {
|  |  |  name = "王者公会会长"
|  |  |  tid = 1019
|  |  }
|  |  [13] = {
|  |  |  name = "黄金公会"
|  |  |  tid = 1020
|  |  }
|  |  [14] = {
|  |  |  name = "黄金公会会长"
|  |  |  tid = 1021
|  |  }
|  |  [15] = {
|  |  |  name = "白银公会"
|  |  |  tid = 1022
|  |  }
|  |  [16] = {
|  |  |  name = "白银公会会长"
|  |  |  tid = 1023
|  |  }
|  |  [17] = {
|  |  |  name = "明日之星"
|  |  |  tid = 1024
|  |  }
|  |  [18] = {
|  |  |  name = "独孤求败"
|  |  |  tid = 1025
|  |  }
|  |  [19] = {
|  |  |  name = "谁与争锋"
|  |  |  tid = 1026
|  |  }
|  |  [2] = {
|  |  |  name = "测-贵族宝"
|  |  |  tid = 1009
|  |  }
|  |  [20] = {
|  |  |  name = "所向披靡"
|  |  |  tid = 1027
|  |  }
|  |  [21] = {
|  |  |  name = "地牢终结者"
|  |  |  tid = 1028
|  |  }
|  |  [22] = {
|  |  |  name = "地牢征服者"
|  |  |  tid = 1029
|  |  }
|  |  [23] = {
|  |  |  name = "地牢破坏者"
|  |  |  tid = 1030
|  |  }
|  |  [24] = {
|  |  |  name = "眯眯眼是变态"
|  |  |  tid = 1031
|  |  }
|  |  [25] = {
|  |  |  name = "邻家妹妹"
|  |  |  tid = 1032
|  |  }
|  |  [26] = {
|  |  |  name = "兄控"
|  |  |  tid = 1033
|  |  }
|  |  [27] = {
|  |  |  name = "农夫三拳"
|  |  |  tid = 1034
|  |  }
|  |  [28] = {
|  |  |  name = "赌神"
|  |  |  tid = 1035
|  |  }
|  |  [29] = {
|  |  |  name = "神之右手"
|  |  |  tid = 1036
|  |  }
|  |  [3] = {
|  |  |  name = "测-我就是托"
|  |  |  tid = 1010
|  |  }
|  |  [30] = {
|  |  |  name = "茶会噩梦"
|  |  |  tid = 1037
|  |  }
|  |  [31] = {
|  |  |  name = "我是傲娇"
|  |  |  tid = 1038
|  |  }
|  |  [32] = {
|  |  |  name = "闷骚美男子"
|  |  |  tid = 1039
|  |  }
|  |  [33] = {
|  |  |  name = "脸炮"
|  |  |  tid = 1040
|  |  }
|  |  [34] = {
|  |  |  name = "妹控"
|  |  |  tid = 1041
|  |  }
|  |  [35] = {
|  |  |  name = "守夜人"
|  |  |  tid = 1042
|  |  }
|  |  [36] = {
|  |  |  name = "泡泡娘"
|  |  |  tid = 1043
|  |  }
|  |  [37] = {
|  |  |  name = "姐控"
|  |  |  tid = 1044
|  |  }
|  |  [38] = {
|  |  |  name = "迷妹"
|  |  |  tid = 1045
|  |  }
|  |  [39] = {
|  |  |  name = "小天使"
|  |  |  tid = 1046
|  |  }
|  |  [4] = {
|  |  |  name = "测-粗壮大腿"
|  |  |  tid = 1011
|  |  }
|  |  [40] = {
|  |  |  name = "欺诈兔子"
|  |  |  tid = 1047
|  |  }
|  |  [41] = {
|  |  |  name = "天然呆"
|  |  |  tid = 1048
|  |  }
|  |  [42] = {
|  |  |  name = "开局一条狗"
|  |  |  tid = 1049
|  |  }
|  |  [43] = {
|  |  |  name = "种竹专业户"
|  |  |  tid = 1050
|  |  }
|  |  [44] = {
|  |  |  name = "顾家好男人"
|  |  |  tid = 1051
|  |  }
|  |  [45] = {
|  |  |  name = "无敌小钢炮"
|  |  |  tid = 1052
|  |  }
|  |  [46] = {
|  |  |  name = "小豆丁"
|  |  |  tid = 1053
|  |  }
|  |  [47] = {
|  |  |  name = "浪里白条"
|  |  |  tid = 1054
|  |  }
|  |  [48] = {
|  |  |  name = "话唠"
|  |  |  tid = 1055
|  |  }
|  |  [49] = {
|  |  |  name = "角落种蘑菇"
|  |  |  tid = 1056
|  |  }
|  |  [5] = {
|  |  |  name = "攻防战第一公会"
|  |  |  tid = 1012
|  |  }
|  |  [50] = {
|  |  |  name = "天然疯"
|  |  |  tid = 1057
|  |  }
|  |  [51] = {
|  |  |  name = "吃货"
|  |  |  tid = 1058
|  |  }
|  |  [52] = {
|  |  |  name = "控制狂"
|  |  |  tid = 1059
|  |  }
|  |  [53] = {
|  |  |  name = "女王大人"
|  |  |  tid = 1060
|  |  }
|  |  [54] = {
|  |  |  name = "八卦小王子"
|  |  |  tid = 1061
|  |  }
|  |  [55] = {
|  |  |  name = "三无"
|  |  |  tid = 1062
|  |  }
|  |  [56] = {
|  |  |  name = "养得累"
|  |  |  tid = 1063
|  |  }
|  |  [57] = {
|  |  |  name = "扑街之王"
|  |  |  tid = 1064
|  |  }
|  |  [58] = {
|  |  |  name = "抖S"
|  |  |  tid = 1065
|  |  }
|  |  [59] = {
|  |  |  name = "工作狂人"
|  |  |  tid = 1066
|  |  }
|  |  [6] = {
|  |  |  name = "攻防战第一司令"
|  |  |  tid = 1013
|  |  }
|  |  [60] = {
|  |  |  name = "精分中毒患者"
|  |  |  tid = 1067
|  |  }
|  |  [61] = {
|  |  |  name = "神神叨叨"
|  |  |  tid = 1068
|  |  }
|  |  [62] = {
|  |  |  name = "浮夸之神"
|  |  |  tid = 1069
|  |  }
|  |  [63] = {
|  |  |  name = "抖M"
|  |  |  tid = 1070
|  |  }
|  |  [64] = {
|  |  |  name = "自恋狂"
|  |  |  tid = 1071
|  |  }
|  |  [65] = {
|  |  |  name = "天然黑"
|  |  |  tid = 1072
|  |  }
|  |  [66] = {
|  |  |  name = "为所欲为"
|  |  |  tid = 1073
|  |  }
|  |  [67] = {
|  |  |  name = "氪金大佬"
|  |  |  tid = 1074
|  |  }
|  |  [68] = {
|  |  |  name = "应用宝帝王"
|  |  |  tid = 1075
|  |  }
|  |  [69] = {
|  |  |  name = "应用宝至尊"
|  |  |  tid = 1076
|  |  }
|  |  [7] = {
|  |  |  name = "梦魇之主"
|  |  |  tid = 1014
|  |  }
|  |  [70] = {
|  |  |  name = "公会新人"
|  |  |  tid = 1077
|  |  }
|  |  [71] = {
|  |  |  name = "公会成员"
|  |  |  tid = 1078
|  |  }
|  |  [72] = {
|  |  |  name = "公会精英"
|  |  |  tid = 1079
|  |  }
|  |  [73] = {
|  |  |  name = "公会副会"
|  |  |  tid = 1080
|  |  }
|  |  [74] = {
|  |  |  name = "公会会长"
|  |  |  tid = 1081
|  |  }
|  |  [75] = {
|  |  |  name = "情侣专属称谓"
|  |  |  tid = 1082
|  |  }
|  |  [76] = {
|  |  |  name = "命运之子"
|  |  |  tid = 1083
|  |  }
|  |  [77] = {
|  |  |  name = "不做大多数"
|  |  |  tid = 1084
|  |  }
|  |  [78] = {
|  |  |  name = "1段"
|  |  |  tid = 1001
|  |  }
|  |  [79] = {
|  |  |  name = "2段"
|  |  |  tid = 1002
|  |  }
|  |  [8] = {
|  |  |  name = "梦魇骑士"
|  |  |  tid = 1015
|  |  }
|  |  [80] = {
|  |  |  name = "3段"
|  |  |  tid = 1003
|  |  }
|  |  [81] = {
|  |  |  name = "4段"
|  |  |  tid = 1004
|  |  }
|  |  [82] = {
|  |  |  name = "5段"
|  |  |  tid = 1005
|  |  }
|  |  [83] = {
|  |  |  name = "6段"
|  |  |  tid = 1006
|  |  }
|  |  [84] = {
|  |  |  name = "7段"
|  |  |  tid = 1007
|  |  }
|  |  [9] = {
|  |  |  name = "梦魇猎人"
|  |  |  tid = 1016
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: friend.GS2CSendSimpleInfo = {
|  frdlist = {
|  |  [1] = {
|  |  |  grade = 25
|  |  |  name = "连城"
|  |  |  pid = 160114
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: friend.GS2CApplyProfile = {
|  profile_list = {
|  |  [1] = {
|  |  |  pro = {
|  |  |  |  grade = 17
|  |  |  |  mask = "fe"
|  |  |  |  name = "小可乐呀"
|  |  |  |  pid = 12616
|  |  |  |  school = 2
|  |  |  |  shape = 140
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: friend = {
|  friend_degree = 0
|  grade = 17
|  name = "小可乐呀"
|  pid = 12616
|  relation = 0
|  school = 2
|  shape = 140
}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = **********
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:21:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewBase.lua:125]:CLoginRewardView LoadDone!
[logic/platform/CSdkCtrl.lua:380]:sdk upload: not init
[core/table.lua:94]:Table = {
|  [1] = "16"
|  [2] = "20"
}
[core/global.lua:59]:<color=#ffeb04>30 16 20</color>
[core/table.lua:94]:Table = {
|  [1] = "21"
|  [2] = "24"
}
[core/global.lua:59]:<color=#ffeb04>30 21 24</color>
[core/table.lua:94]:Table = {
|  [1] = "25"
|  [2] = "27"
}
[core/global.lua:59]:<color=#ffeb04>30 25 27</color>
[logic/base/CResCtrl.lua:626]:res gc finish!
[logic/ui/CViewCtrl.lua:104]:CLoginRewardView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445706
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:21:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: scene.GS2CSyncAoi = {
|  aoi_player_block = {
|  |  mask = "8"
|  }
|  eid = 34
|  scene_id = 12
|  type = 1
}
[core/table.lua:94]:CNetCtrl解析mask: PlayerAoiBlock = {
|  war_tag = 0
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445716
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:21:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 34
|  scene_id = 12
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445726
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:22:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445736
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:22:16
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445746
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:22:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445756
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:22:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445766
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:22:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445776
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:22:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445786
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:23:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445796
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:23:16
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445806
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:23:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445816
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:23:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445826
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:23:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445836
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:23:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445846
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:24:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445856
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:24:16
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445866
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:24:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CFriendMainView ShowView
[logic/ui/CViewBase.lua:125]:CFriendMainView LoadDone!
[logic/ui/CViewCtrl.lua:94]:CFriendApplyView ShowView
[logic/ui/CViewBase.lua:125]:CFriendApplyView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 21009
}
[core/table.lua:94]:<--Net Send: friend.C2GSAgreeApply = {
|  pidlist = {
|  |  [1] = 12616
|  }
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 21009
}
[core/table.lua:94]:-->Net Receive: friend.GS2CApplyList = {}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "添加好友成功"
}
[core/table.lua:94]:-->Net Receive: friend.GS2CAddFriend = {
|  profile_list = {
|  |  [1] = {
|  |  |  grade = 20
|  |  |  mask = "fe"
|  |  |  name = "小可乐呀"
|  |  |  pid = 12616
|  |  |  school = 2
|  |  |  shape = 140
|  |  }
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: friend = {
|  friend_degree = 0
|  grade = 20
|  name = "小可乐呀"
|  pid = 12616
|  relation = 0
|  school = 2
|  shape = 140
}
[core/table.lua:94]:-->Net Receive: friend.GS2COnlineStatus = {
|  onlinestatus = {
|  |  [1] = {
|  |  |  onlinestatus = 1
|  |  |  pid = 12616
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: friend.GS2CFriendDegree = {
|  degree = 1
|  pid = 12616
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 21009
}
[core/table.lua:94]:<--Net Send: friend.C2GSAgreeApply = {
|  pidlist = {}
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 21009
}
[logic/ui/CViewCtrl.lua:104]:CFriendApplyView     CloseView
[logic/ui/CViewCtrl.lua:104]:CFriendMainView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445876
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:24:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10107</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  喵小布 1503 [32,1.3,0] -6 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [34.2,9,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 喵~喵呜喵~</color>
[logic/ui/CViewCtrl.lua:94]:CLimitRewardView ShowView
[core/table.lua:94]:<--Net Send: fuli.C2GSGetLuckDrawInfo = {}
[logic/ui/CViewBase.lua:125]:CLimitRewardView LoadDone!
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawUI = {
|  cnt = 5
|  cost = 100
|  idxlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  |  [8] = 8
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445886
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:24:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [35.1,13.8,0] 30</color>
[logic/ui/CViewCtrl.lua:104]:CLimitRewardView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 15004
}
[core/table.lua:94]:<--Net Send: store.C2GSOpenShop = {
|  shop_id = 208
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 15004
}
[core/table.lua:94]:-->Net Receive: store.GS2CNpcStoreInfo = {
|  goodslist = {
|  |  [1] = {
|  |  |  amount = 4294967295
|  |  |  item_id = 208004
|  |  |  pos = 1
|  |  }
|  |  [2] = {
|  |  |  amount = 10
|  |  |  item_id = 208005
|  |  |  limit = 1
|  |  |  pos = 2
|  |  }
|  |  [3] = {
|  |  |  amount = 20
|  |  |  item_id = 208006
|  |  |  limit = 1
|  |  |  pos = 3
|  |  }
|  |  [4] = {
|  |  |  amount = 4294967295
|  |  |  item_id = 208007
|  |  |  pos = 4
|  |  }
|  }
|  shop_id = 208
}
[logic/ui/CViewCtrl.lua:94]:CNpcShopView ShowView
[core/global.lua:59]:<color=#ffeb04>渠道: </color>
[core/global.lua:59]:<color=#ffeb04>包名: ylq</color>
[logic/ui/CViewBase.lua:125]:CNpcShopView LoadDone!
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445896
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:24:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CWelfareView ShowView
[logic/ui/CViewBase.lua:125]:CWelfareView LoadDone!
[core/global.lua:59]:<color=#ffeb04>哈哈哈哈10001 === 160100</color>
[core/table.lua:94]:<--Net Send: other.C2GSGMCmd = {
|  cmd = "huodong charge 202"
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "你不是gm,无法执行gm指令"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445906
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:25:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445916
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:25:16
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445926
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:25:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445936
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:25:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445946
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:25:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445956
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:25:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_player = {
|  |  block = {
|  |  |  mask = "63e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 140
|  |  |  |  weapon = 2000
|  |  |  }
|  |  |  name = "小可乐呀"
|  |  |  show_id = 12616
|  |  |  title_info = {
|  |  |  |  [1] = {
|  |  |  |  |  create_time = 1694445329
|  |  |  |  |  left_time = 1694445961
|  |  |  |  |  name = "阿巴阿巴新人"
|  |  |  |  |  tid = 1077
|  |  |  |  }
|  |  |  }
|  |  }
|  |  pid = 12616
|  }
|  eid = 34
|  scene_id = 12
|  type = 1
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 34
|  pos_info = {
|  |  x = 25049
|  |  y = 17440
|  }
|  scene_id = 12
|  type = 1
}
[core/table.lua:94]:CNetCtrl解析mask: PlayerAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 140
|  |  weapon = 2000
|  }
|  name = "小可乐呀"
|  show_id = 12616
|  title_info = {
|  |  [1] = {
|  |  |  create_time = 1694445329
|  |  |  left_time = 1694445961
|  |  |  name = "阿巴阿巴新人"
|  |  |  tid = 1077
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445966
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:26:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 34
|  scene_id = 12
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445976
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:26:16
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_player = {
|  |  block = {
|  |  |  mask = "63e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 140
|  |  |  |  weapon = 2000
|  |  |  }
|  |  |  name = "小可乐呀"
|  |  |  show_id = 12616
|  |  |  title_info = {
|  |  |  |  [1] = {
|  |  |  |  |  create_time = 1694445329
|  |  |  |  |  left_time = 1694445961
|  |  |  |  |  name = "阿巴阿巴新人"
|  |  |  |  |  tid = 1077
|  |  |  |  }
|  |  |  }
|  |  }
|  |  pid = 12616
|  }
|  eid = 34
|  scene_id = 12
|  type = 1
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 34
|  pos_info = {
|  |  x = 27644
|  |  y = 17440
|  }
|  scene_id = 12
|  type = 1
}
[core/table.lua:94]:CNetCtrl解析mask: PlayerAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 140
|  |  weapon = 2000
|  }
|  name = "小可乐呀"
|  show_id = 12616
|  title_info = {
|  |  [1] = {
|  |  |  create_time = 1694445329
|  |  |  left_time = 1694445961
|  |  |  name = "阿巴阿巴新人"
|  |  |  tid = 1077
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445986
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:26:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694445996
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:26:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "逆袭与小兔击败梦魇，并在宝箱中搜刮出三倍奖励！真是幸运啊"
|  horse_race = 1
|  tag_type = 1
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_player = {
|  |  block = {
|  |  |  mask = "63e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 153
|  |  |  |  weapon = 2203
|  |  |  }
|  |  |  name = "逆袭与小兔"
|  |  |  show_id = 170020
|  |  |  title_info = {
|  |  |  |  [1] = {
|  |  |  |  |  create_time = 1694411872
|  |  |  |  |  left_time = 1695016672
|  |  |  |  |  name = "2段"
|  |  |  |  |  progress = 1200
|  |  |  |  |  tid = 1002
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  create_time = 1694275200
|  |  |  |  |  left_time = 1694445998
|  |  |  |  |  name = "梦魇之主"
|  |  |  |  |  tid = 1014
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  create_time = 1693757192
|  |  |  |  |  left_time = 1694445998
|  |  |  |  |  name = "阿巴阿巴会长"
|  |  |  |  |  tid = 1081
|  |  |  |  }
|  |  |  }
|  |  }
|  |  pid = 170020
|  }
|  eid = 36
|  scene_id = 12
|  type = 1
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 36
|  pos_info = {
|  |  face_y = 278866
|  |  x = 25061
|  |  y = 12734
|  }
|  scene_id = 12
|  type = 1
}
[core/table.lua:94]:CNetCtrl解析mask: PlayerAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 153
|  |  weapon = 2203
|  }
|  name = "逆袭与小兔"
|  show_id = 170020
|  title_info = {
|  |  [1] = {
|  |  |  create_time = 1694411872
|  |  |  left_time = 1695016672
|  |  |  name = "2段"
|  |  |  progress = 1200
|  |  |  tid = 1002
|  |  }
|  |  [2] = {
|  |  |  create_time = 1694275200
|  |  |  left_time = 1694445998
|  |  |  name = "梦魇之主"
|  |  |  tid = 1014
|  |  }
|  |  [3] = {
|  |  |  create_time = 1693757192
|  |  |  left_time = 1694445998
|  |  |  name = "阿巴阿巴会长"
|  |  |  tid = 1081
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446006
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:26:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 34
|  scene_id = 12
}
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10107</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10117
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [5] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10107</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  喵小布 1503 [32,1.3,0] -6 false</color>
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 36
|  scene_id = 12
}
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [34.2,9,0] 360</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446016
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:26:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 喵~喵呜喵~</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [35.1,13.8,0] 30</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446026
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:27:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446036
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:27:16
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446046
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:27:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446056
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:27:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446066
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:27:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446076
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:27:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446086
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:28:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: friend.GS2CFriendGrade = {
|  grade = 21
|  pid = 12616
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446096
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:28:16
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446106
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:28:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446116
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:28:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446126
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:28:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446136
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:28:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10107</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10117
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [5] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10107</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  喵小布 1503 [32,1.3,0] -6 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446146
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:29:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [34.2,9,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 喵~喵呜喵~</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [35.1,13.8,0] 30</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446156
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:29:16
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446166
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:29:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446176
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:29:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446186
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:29:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446196
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:29:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446206
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:30:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446216
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:30:16
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446226
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:30:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446236
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:30:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446246
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:30:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446256
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:30:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446266
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:31:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446276
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:31:16
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10107</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10117
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [5] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10107</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  喵小布 1503 [32,1.3,0] -6 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [34.2,9,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 喵~喵呜喵~</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446286
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:31:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [35.1,13.8,0] 30</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_player = {
|  |  block = {
|  |  |  mask = "63e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 140
|  |  |  |  weapon = 2000
|  |  |  }
|  |  |  name = "小可乐呀"
|  |  |  show_id = 12616
|  |  |  title_info = {
|  |  |  |  [1] = {
|  |  |  |  |  create_time = 1694445329
|  |  |  |  |  left_time = 1694446143
|  |  |  |  |  name = "阿巴阿巴新人"
|  |  |  |  |  tid = 1077
|  |  |  |  }
|  |  |  }
|  |  }
|  |  pid = 12616
|  }
|  eid = 34
|  scene_id = 12
|  type = 1
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 34
|  pos_info = {
|  |  face_y = 68316
|  |  x = 21783
|  |  y = 18504
|  }
|  scene_id = 12
|  type = 1
}
[core/table.lua:94]:CNetCtrl解析mask: PlayerAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 140
|  |  weapon = 2000
|  }
|  name = "小可乐呀"
|  show_id = 12616
|  title_info = {
|  |  [1] = {
|  |  |  create_time = 1694445329
|  |  |  left_time = 1694446143
|  |  |  name = "阿巴阿巴新人"
|  |  |  tid = 1077
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 34
|  scene_id = 12
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446296
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:31:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446306
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:31:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446316
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:31:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446326
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:32:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446336
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:32:16
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446346
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:32:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446356
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:32:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_player = {
|  |  block = {
|  |  |  mask = "63e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 140
|  |  |  |  weapon = 2000
|  |  |  }
|  |  |  name = "小可乐呀"
|  |  |  show_id = 12616
|  |  |  title_info = {
|  |  |  |  [1] = {
|  |  |  |  |  create_time = 1694445329
|  |  |  |  |  left_time = 1694446344
|  |  |  |  |  name = "阿巴阿巴新人"
|  |  |  |  |  tid = 1077
|  |  |  |  }
|  |  |  }
|  |  }
|  |  pid = 12616
|  }
|  eid = 36
|  scene_id = 12
|  type = 1
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 36
|  pos_info = {
|  |  x = 22638
|  |  y = 17993
|  }
|  scene_id = 12
|  type = 1
}
[core/table.lua:94]:CNetCtrl解析mask: PlayerAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 140
|  |  weapon = 2000
|  }
|  name = "小可乐呀"
|  show_id = 12616
|  title_info = {
|  |  [1] = {
|  |  |  create_time = 1694445329
|  |  |  left_time = 1694446344
|  |  |  name = "阿巴阿巴新人"
|  |  |  tid = 1077
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446366
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:32:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446376
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:32:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446386
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:33:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 32
|  scene_id = 12
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446396
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:33:16
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446406
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:33:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10107</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10117
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [5] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10107</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  喵小布 1503 [32,1.3,0] -6 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [34.2,9,0] 360</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446416
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:33:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 喵~喵呜喵~</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [35.1,13.8,0] 30</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446426
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:33:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446436
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:33:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446446
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:34:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446456
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:34:16
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446466
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:34:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446476
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:34:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446486
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:34:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446496
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:34:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CLockScreenView ShowView
[logic/ui/CViewBase.lua:125]:CLockScreenView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446506
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:35:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CLockScreenView     CloseView
[logic/ui/CViewCtrl.lua:104]:CWelfareView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[logic/ui/CViewCtrl.lua:104]:CNpcShopView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446517
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:35:17
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446527
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:35:27
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446537
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:35:37
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1694446547
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/11 23:35:47
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10107</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10117
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [5] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10107</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  喵小布 1503 [32,1.3,0] -6 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [34.2,9,0] 360</color>
[core/table.lua:94]:-->Net Receive: login.GS2CLoginError = {
|  errcode = 1006
|  pid = 160100
}
[core/global.lua:59]:<color=#ffeb04>重置游戏</color>
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "main"]:206: in function 'ResetGame'
	[string "net/netlogin"]:34: in function <[string "net/netlogin"]:12>
	[C]: in function 'xxpcall'
	[string "net/CNetCtrl"]:318: in function 'ProtoCall'
	[string "net/CNetCtrl"]:300: in function 'ProcessProto'
	[string "net/CNetCtrl"]:251: in function 'ProtoHandlerCheck'
	[string "net/CNetCtrl"]:262: in function 'Receive'
	[string "net/CNetCtrl"]:175: in function 'OnSocketEvent'
	[string "net/CNetCtrl"]:111: in function 'm_Callback'
	[string "net/CTcpClient"]:43: in function <[string "net/CTcpClient"]:37>
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10107</color>
[core/table.lua:94]:Table = {}
[core/global.lua:59]:<color=#ffeb04>CViewCtrl.CloseAll--> nil</color>
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/ui/CViewCtrl"]:280: in function 'CloseAll'
	[string "main"]:231: in function 'ResetGame'
	[string "net/netlogin"]:34: in function <[string "net/netlogin"]:12>
	[C]: in function 'xxpcall'
	[string "net/CNetCtrl"]:318: in function 'ProtoCall'
	[string "net/CNetCtrl"]:300: in function 'ProcessProto'
	[string "net/CNetCtrl"]:251: in function 'ProtoHandlerCheck'
	[string "net/CNetCtrl"]:262: in function 'Receive'
	[string "net/CNetCtrl"]:175: in function 'OnSocketEvent'
	[string "net/CNetCtrl"]:111: in function 'm_Callback'
	[string "net/CTcpClient"]:43: in function <[string "net/CTcpClient"]:37>
[core/global.lua:59]:<color=#ffeb04>CloseAll-->CloseView:  CMainMenuView</color>
[logic/ui/CViewCtrl.lua:104]:CMainMenuView     CloseView
[logic/base/CResCtrl.lua:599]:res gc step start!
[logic/ui/CViewCtrl.lua:94]:CItemTipsConfirmWindowView ShowView
[net/netlogin.lua:87]:Login err    160100    1006
[logic/base/CResCtrl.lua:626]:res gc finish!
[logic/ui/CViewBase.lua:125]:CItemTipsConfirmWindowView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemTipsConfirmWindowView     CloseView
