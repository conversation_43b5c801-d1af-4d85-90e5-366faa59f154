with:891  hight:439
[logic/misc/CShareCtrl.lua:20]:ShareCallback ctor
[logic/login/CLoginCtrl.lua:5]:公告初始化...
[logic/base/CHttpCtrl.lua:32]:http get ->    https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials&client_id=rlU84dprH6I7sMjBkqM0gizl&client_secret=c15395c2433c4a904d22aa27eab8321b&
[logic/ui/CViewCtrl.lua:94]:CNotifyView ShowView
[logic/ui/CViewCtrl.lua:94]:CBottomView ShowView
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x4d092520"
|  json_result = true
|  timer = 16
}
[logic/audio/CSpeechCtrl.lua:72]:OnTokenGet err
[logic/ui/CViewBase.lua:125]:CBottomView LoadDone!
[logic/base/CResCtrl.lua:199]:-->resource init done!!! 12
[logic/ui/CViewBase.lua:125]:CNotifyView LoadDone!
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {}
[logic/ui/CViewCtrl.lua:94]:CLoginView ShowView
[logic/base/CHttpCtrl.lua:32]:http get ->    http://************:88/Note64/note.json
[logic/login/CLoginAccountPage.lua:80]:SendToCenterServer:    http://************:88/Note64/note.json    
[logic/ui/CViewBase.lua:125]:CLoginView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x730bb250"
|  json_result = true
|  timer = 60
}
[core/table.lua:94]:cb tResult-> = {
|  info = {
|  |  role_list = {}
|  |  server_info = {
|  |  |  groups = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 1
|  |  |  |  |  name = "1-10区"
|  |  |  |  }
|  |  |  }
|  |  |  infoList = {
|  |  |  |  [1] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "新服7天限时-大狂欢活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [10] = {
|  |  |  |  |  |  |  |  text = "[ff0000]传说伙伴碎片*100、梦觉书·零*20、梦觉书·低*20、梦觉书·高*20[-]"
|  |  |  |  |  |  |  |  title = "新区累计充值10000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [11] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "单日累计充值活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [12] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [13] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.活动为单日累计充值，高档位可领取低档位所有物品。
2.活动为单日累计充值，仅计算单日内玩家累计充值总额，次日重新计算。[-]"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [14] = {
|  |  |  |  |  |  |  |  text = "[ff0000]向客服申请[-]"
|  |  |  |  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [15] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备材料*10、金币*5w、熊猫蛋糕*5[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值100元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [16] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*10、金币铜币*10w、体力药水*1、鲜肉包*30[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值500元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [17] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*20、金币*20w、体力药水*2、焦糖包*60[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值1000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [18] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [19] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值5000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]新服前7天[-]"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [20] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*80[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值10000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [21] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值20000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [22] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "单笔充值活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [23] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [24] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.活动为单笔活动，仅可领取充值对应档位物品。
2.活动单日可多次领取。[-]"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [25] = {
|  |  |  |  |  |  |  |  text = "[ff0000]充值玩家向客服申请，次日发送给玩家CDK（节假日顺延）[-]"
|  |  |  |  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [26] = {
|  |  |  |  |  |  |  |  text = "[ff0000]灵魂钥匙*6、淬灵云晶*20、原石礼包*4、武器原石*1[-]"
|  |  |  |  |  |  |  |  title = "单笔68元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [27] = {
|  |  |  |  |  |  |  |  text = "[ff0000]灵魂钥匙*12、淬灵云晶*40、原石礼包*8、武器原石*2[-]"
|  |  |  |  |  |  |  |  title = "单笔128元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [28] = {
|  |  |  |  |  |  |  |  text = "[ff0000]灵魂钥匙*32、淬灵云晶*100、原石礼包*20、武器原石*5[-]"
|  |  |  |  |  |  |  |  title = "单笔328元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [29] = {
|  |  |  |  |  |  |  |  text = "[ff0000]灵魂钥匙*64、淬灵云晶*200、原石礼包*40、武器原石*10[-]"
|  |  |  |  |  |  |  |  title = "单笔648元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "[ff0000]单日累计充值达到对应档位，不重复高领低档，每个档位仅可领取一次奖励、请及时参加并申请领取对应档位。[-]"
|  |  |  |  |  |  |  |  title = "活动规则"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [30] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "永久累计充值活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [31] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [32] = {
|  |  |  |  |  |  |  |  text = "[ff0000]活动为永久累计充值，高档位可领取低档位所有物品。
每个档位奖励仅可领取一次[-]"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [33] = {
|  |  |  |  |  |  |  |  text = "[ff0000]向客服申请[-]"
|  |  |  |  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [34] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备材料*50、伙伴觉醒材料*50、金币*1w[-]"
|  |  |  |  |  |  |  |  title = "永久累计充值500元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [35] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片5、2星精英伙伴*10、3级符石礼袋*5、附魔材料*5、金币*5w[-]"
|  |  |  |  |  |  |  |  title = "永久累计充值1000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [36] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*10、2星精英伙伴*20、3级符石礼袋*10、附魔材料*10、金币*10w[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [37] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*30、3星传说伙伴*1、3级符石礼袋*30、附魔材料*30、金币*50w[-]"
|  |  |  |  |  |  |  |  title = "永久累计充值5000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [38] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*50、万能碎片*100、金币*100w、橙色可选御灵*10[-]"
|  |  |  |  |  |  |  |  title = "永久累充10000元礼包"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [39] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*100、万能碎片*200、金币*200w、橙色可选御灵*20[-]"
|  |  |  |  |  |  |  |  title = "永久累充20000元礼包"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "[ff0000]向客服申请[-]"
|  |  |  |  |  |  |  |  title = "发放时间方式："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [40] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*200、金币*500w、橙色可选御灵*50[-]"
|  |  |  |  |  |  |  |  title = "永久累充40000元礼包"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [41] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "单日累计充值货币返利1:10"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [42] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [43] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.本活动为阶梯式活动，当日累计充值的金额满足返利阶梯后，按照当日充值金额满足的最高档次进行发放，次日自动返利，无需申请
2.本活动为长期活动，与其他返利活动叠加。
充值100-1999元，返利50%钻石
充值2000-4999元，返利100%钻石
充值5000-9999元，返利150%钻石
充值10000-19999元，返利200%钻石
充值20000+元，返利250%钻石
以上返利会在第二天自动已邮件形式发送至玩家[-]"
|  |  |  |  |  |  |  |  title = "活动说明:"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [44] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "服务器冠名活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [45] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [46] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [47] = {
|  |  |  |  |  |  |  |  text = "[ff0000]全体玩家[-]"
|  |  |  |  |  |  |  |  title = "【活动对象】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [48] = {
|  |  |  |  |  |  |  |  text = "[ff0000]我的服务器，我为自己代言。[-]"
|  |  |  |  |  |  |  |  title = "【活动宣言】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [49] = {
|  |  |  |  |  |  |  |  text = "[ff0000]区服冠名权*1[-]"
|  |  |  |  |  |  |  |  title = "【冠名奖励】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [5] = {
|  |  |  |  |  |  |  |  text = "[ff0000]传说伙伴碎片*5、3级宝石礼包*2、鲜肉包*50、2星精英伙伴*1[-]"
|  |  |  |  |  |  |  |  title = "新区累计充值500元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [50] = {
|  |  |  |  |  |  |  |  text = "  [ff0000]亲爱的玩家：
  也许你在游戏里已经称霸全区，但是出了服务器，出了游戏，还有谁认识你现在就给你一个机会，获得至高无上的荣誉，让更多的人知道你的强大。特此举行“我的服务器，我为自己代言”，服务器玩家冠名活动。
即日起，只要满足以下条件，即可获得服务器冠名权：
  1）开服前3天累计充值满10000元
  2）开服前7日累计充值满20000元，且区服处于未冠名状态，获得服务器冠名权
  备注：本活动遵循先到先得原则。如出现多个满足条件的，冠名资格由第一个满足条件的获得。[-]"
|  |  |  |  |  |  |  |  title = "【活动内容】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [51] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1、活动时间内有玩家满足条件后活动结束，活动期间没有玩家满足条件则保持原服务器名称。
2、服务器冠名长度为4个字。（必须遵守相关法律法规，不得使用负面或影响社会风气，不得出现敏感词的名称）
3、由于冠名操作需要时间，工作人员会在7个工作日内，完成冠名操作。
4，每个服，冠名只有一次。被冠名的服务器不予再次冠名[-]"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [52] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "单日大额充值活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [53] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [54] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.活动为单日累计充值，仅计算单日内玩家累计充值总额，次日重新计算。[-]"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [55] = {
|  |  |  |  |  |  |  |  text = "[ff0000]充值玩家向客服申请，次日发送邮件给玩家（节假日顺延）[-]"
|  |  |  |  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [56] = {
|  |  |  |  |  |  |  |  text = "[ff0000]首日确定大额道具内容后，在次日再次充值达到大额道具申请条件的用户，在次日申请时可额外获得前一天道具内容的30%，往后同理[-]"
|  |  |  |  |  |  |  |  title = ""
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [57] = {
|  |  |  |  |  |  |  |  text = "[ff0000]钱袋*10个[-]"
|  |  |  |  |  |  |  |  title = "第一天申请："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [58] = {
|  |  |  |  |  |  |  |  text = "[ff0000]钱袋（10+10*0.3）13个[-]"
|  |  |  |  |  |  |  |  title = "第二天申请："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [59] = {
|  |  |  |  |  |  |  |  text = "[ff0000]钱袋（13+13*0.3）17个[-]"
|  |  |  |  |  |  |  |  title = "第三天申请："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [6] = {
|  |  |  |  |  |  |  |  text = "[ff0000]传说伙伴碎片*10、3级宝石礼包*5、鲜肉包*100、2星精英伙伴*2[-]"
|  |  |  |  |  |  |  |  title = "新区累计充值1000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [60] = {
|  |  |  |  |  |  |  |  text = "[ff0000]钱袋（17+17*0.3）23个[-]"
|  |  |  |  |  |  |  |  title = "第四天申请："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [61] = {
|  |  |  |  |  |  |  |  text = "[ff0000]以次类推  所有小数点向上取整， 0.1也算1[-]"
|  |  |  |  |  |  |  |  title = ""
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [7] = {
|  |  |  |  |  |  |  |  text = "[ff0000]传说伙伴碎片*20、3级宝石礼包*10、鲜肉包*200、2星精英伙伴*3[-]"
|  |  |  |  |  |  |  |  title = "新区累计充值2000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [8] = {
|  |  |  |  |  |  |  |  text = "[ff0000]传说伙伴碎片*30、3级宝石礼包*20、鲜肉包*300、2星精英伙伴*5[-]"
|  |  |  |  |  |  |  |  title = "新区累计充值3000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [9] = {
|  |  |  |  |  |  |  |  text = "[ff0000]传说伙伴碎片*50、梦觉书·零*10、梦觉书·低*10、梦觉书·高*10[-]"
|  |  |  |  |  |  |  |  title = "新区累计充值5000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "限时大狂欢"
|  |  |  |  }
|  |  |  }
|  |  |  ports = {}
|  |  |  role_list = ""
|  |  |  servers = {
|  |  |  |  [1] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10031
|  |  |  |  |  ip = "************"
|  |  |  |  |  name = "双线1区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1684511880
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7511
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10005"
|  |  |  |  |  start_time = 1684511880
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10032
|  |  |  |  |  ip = "************"
|  |  |  |  |  name = "双线2区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1684555080
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7511
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10005"
|  |  |  |  |  start_time = 1684555080
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10033
|  |  |  |  |  ip = "************"
|  |  |  |  |  name = "老大不小(双线3区)"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1684634280
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7611
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10006"
|  |  |  |  |  start_time = 1684634280
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10034
|  |  |  |  |  ip = "************"
|  |  |  |  |  name = "双线4区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1684663080
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7611
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10006"
|  |  |  |  |  start_time = 1684663080
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10035
|  |  |  |  |  ip = "************"
|  |  |  |  |  name = "双线5区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1684720680
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7611
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10006"
|  |  |  |  |  start_time = 1684720680
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10036
|  |  |  |  |  ip = "************"
|  |  |  |  |  name = "双线6区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1684749480
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7611
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10006"
|  |  |  |  |  start_time = 1684749480
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  }
|  |  }
|  |  token = ""
|  }
}
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {
|  [1] = {
|  |  group_id = 1
|  |  id = 1
|  |  name = "1-10区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  id = 10036
|  |  |  |  ip = "************"
|  |  |  |  name = "双线6区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1684749480
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7611
|  |  |  |  }
|  |  |  |  server_id = 10036
|  |  |  |  start_time = 1684749480
|  |  |  |  state = 2
|  |  |  }
|  |  |  [2] = {
|  |  |  |  group = 1
|  |  |  |  id = 10035
|  |  |  |  ip = "************"
|  |  |  |  name = "双线5区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1684720680
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7611
|  |  |  |  }
|  |  |  |  server_id = 10035
|  |  |  |  start_time = 1684720680
|  |  |  |  state = 2
|  |  |  }
|  |  |  [3] = {
|  |  |  |  group = 1
|  |  |  |  id = 10032
|  |  |  |  ip = "************"
|  |  |  |  name = "双线2区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1684555080
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7511
|  |  |  |  }
|  |  |  |  server_id = 10032
|  |  |  |  start_time = 1684555080
|  |  |  |  state = 2
|  |  |  }
|  |  |  [4] = {
|  |  |  |  group = 1
|  |  |  |  id = 10031
|  |  |  |  ip = "************"
|  |  |  |  name = "双线1区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1684511880
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7511
|  |  |  |  }
|  |  |  |  server_id = 10031
|  |  |  |  start_time = 1684511880
|  |  |  |  state = 2
|  |  |  }
|  |  |  [5] = {
|  |  |  |  group = 1
|  |  |  |  id = 10034
|  |  |  |  ip = "************"
|  |  |  |  name = "双线4区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1684663080
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7611
|  |  |  |  }
|  |  |  |  server_id = 10034
|  |  |  |  start_time = 1684663080
|  |  |  |  state = 2
|  |  |  }
|  |  |  [6] = {
|  |  |  |  group = 1
|  |  |  |  id = 10033
|  |  |  |  ip = "************"
|  |  |  |  name = "老大不小(双线3区)"
|  |  |  |  new = 1
|  |  |  |  open_time = 1684634280
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7611
|  |  |  |  }
|  |  |  |  server_id = 10033
|  |  |  |  start_time = 1684634280
|  |  |  |  state = 2
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:设置公告: = {
|  [1] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "新服7天限时-大狂欢活动"
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  text = "[ff0000]传说伙伴碎片*100、梦觉书·零*20、梦觉书·低*20、梦觉书·高*20[-]"
|  |  |  |  |  title = "新区累计充值10000元"
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "单日累计充值活动"
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [13] = {
|  |  |  |  |  text = "[ff0000]1.活动为单日累计充值，高档位可领取低档位所有物品。
2.活动为单日累计充值，仅计算单日内玩家累计充值总额，次日重新计算。[-]"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [14] = {
|  |  |  |  |  text = "[ff0000]向客服申请[-]"
|  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  }
|  |  |  |  [15] = {
|  |  |  |  |  text = "[ff0000]装备材料*10、金币*5w、熊猫蛋糕*5[-]"
|  |  |  |  |  title = "单日累计充值100元"
|  |  |  |  }
|  |  |  |  [16] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*10、金币铜币*10w、体力药水*1、鲜肉包*30[-]"
|  |  |  |  |  title = "单日累计充值500元"
|  |  |  |  }
|  |  |  |  [17] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*20、金币*20w、体力药水*2、焦糖包*60[-]"
|  |  |  |  |  title = "单日累计充值1000元"
|  |  |  |  }
|  |  |  |  [18] = {
|  |  |  |  |  text = "[ff0000]装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1[-]"
|  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  }
|  |  |  |  [19] = {
|  |  |  |  |  text = "[ff0000]装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3[-]"
|  |  |  |  |  title = "单日累计充值5000元"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]新服前7天[-]"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [20] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*80[-]"
|  |  |  |  |  title = "单日累计充值10000元"
|  |  |  |  }
|  |  |  |  [21] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1[-]"
|  |  |  |  |  title = "单日累计充值20000元"
|  |  |  |  }
|  |  |  |  [22] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "单笔充值活动"
|  |  |  |  }
|  |  |  |  [23] = {
|  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [24] = {
|  |  |  |  |  text = "[ff0000]1.活动为单笔活动，仅可领取充值对应档位物品。
2.活动单日可多次领取。[-]"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [25] = {
|  |  |  |  |  text = "[ff0000]充值玩家向客服申请，次日发送给玩家CDK（节假日顺延）[-]"
|  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  }
|  |  |  |  [26] = {
|  |  |  |  |  text = "[ff0000]灵魂钥匙*6、淬灵云晶*20、原石礼包*4、武器原石*1[-]"
|  |  |  |  |  title = "单笔68元"
|  |  |  |  }
|  |  |  |  [27] = {
|  |  |  |  |  text = "[ff0000]灵魂钥匙*12、淬灵云晶*40、原石礼包*8、武器原石*2[-]"
|  |  |  |  |  title = "单笔128元"
|  |  |  |  }
|  |  |  |  [28] = {
|  |  |  |  |  text = "[ff0000]灵魂钥匙*32、淬灵云晶*100、原石礼包*20、武器原石*5[-]"
|  |  |  |  |  title = "单笔328元"
|  |  |  |  }
|  |  |  |  [29] = {
|  |  |  |  |  text = "[ff0000]灵魂钥匙*64、淬灵云晶*200、原石礼包*40、武器原石*10[-]"
|  |  |  |  |  title = "单笔648元"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "[ff0000]单日累计充值达到对应档位，不重复高领低档，每个档位仅可领取一次奖励、请及时参加并申请领取对应档位。[-]"
|  |  |  |  |  title = "活动规则"
|  |  |  |  }
|  |  |  |  [30] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "永久累计充值活动"
|  |  |  |  }
|  |  |  |  [31] = {
|  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [32] = {
|  |  |  |  |  text = "[ff0000]活动为永久累计充值，高档位可领取低档位所有物品。
每个档位奖励仅可领取一次[-]"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [33] = {
|  |  |  |  |  text = "[ff0000]向客服申请[-]"
|  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  }
|  |  |  |  [34] = {
|  |  |  |  |  text = "[ff0000]装备材料*50、伙伴觉醒材料*50、金币*1w[-]"
|  |  |  |  |  title = "永久累计充值500元"
|  |  |  |  }
|  |  |  |  [35] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片5、2星精英伙伴*10、3级符石礼袋*5、附魔材料*5、金币*5w[-]"
|  |  |  |  |  title = "永久累计充值1000元"
|  |  |  |  }
|  |  |  |  [36] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*10、2星精英伙伴*20、3级符石礼袋*10、附魔材料*10、金币*10w[-]"
|  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  }
|  |  |  |  [37] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*30、3星传说伙伴*1、3级符石礼袋*30、附魔材料*30、金币*50w[-]"
|  |  |  |  |  title = "永久累计充值5000元"
|  |  |  |  }
|  |  |  |  [38] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*50、万能碎片*100、金币*100w、橙色可选御灵*10[-]"
|  |  |  |  |  title = "永久累充10000元礼包"
|  |  |  |  }
|  |  |  |  [39] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*100、万能碎片*200、金币*200w、橙色可选御灵*20[-]"
|  |  |  |  |  title = "永久累充20000元礼包"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "[ff0000]向客服申请[-]"
|  |  |  |  |  title = "发放时间方式："
|  |  |  |  }
|  |  |  |  [40] = {
|  |  |  |  |  text = "[ff0000]万能碎片*200、金币*500w、橙色可选御灵*50[-]"
|  |  |  |  |  title = "永久累充40000元礼包"
|  |  |  |  }
|  |  |  |  [41] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "单日累计充值货币返利1:10"
|  |  |  |  }
|  |  |  |  [42] = {
|  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [43] = {
|  |  |  |  |  text = "[ff0000]1.本活动为阶梯式活动，当日累计充值的金额满足返利阶梯后，按照当日充值金额满足的最高档次进行发放，次日自动返利，无需申请
2.本活动为长期活动，与其他返利活动叠加。
充值100-1999元，返利50%钻石
充值2000-4999元，返利100%钻石
充值5000-9999元，返利150%钻石
充值10000-19999元，返利200%钻石
充值20000+元，返利250%钻石
以上返利会在第二天自动已邮件形式发送至玩家[-]"
|  |  |  |  |  title = "活动说明:"
|  |  |  |  }
|  |  |  |  [44] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "服务器冠名活动"
|  |  |  |  }
|  |  |  |  [45] = {
|  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [46] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [47] = {
|  |  |  |  |  text = "[ff0000]全体玩家[-]"
|  |  |  |  |  title = "【活动对象】："
|  |  |  |  }
|  |  |  |  [48] = {
|  |  |  |  |  text = "[ff0000]我的服务器，我为自己代言。[-]"
|  |  |  |  |  title = "【活动宣言】："
|  |  |  |  }
|  |  |  |  [49] = {
|  |  |  |  |  text = "[ff0000]区服冠名权*1[-]"
|  |  |  |  |  title = "【冠名奖励】："
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  text = "[ff0000]传说伙伴碎片*5、3级宝石礼包*2、鲜肉包*50、2星精英伙伴*1[-]"
|  |  |  |  |  title = "新区累计充值500元"
|  |  |  |  }
|  |  |  |  [50] = {
|  |  |  |  |  text = "  [ff0000]亲爱的玩家：
  也许你在游戏里已经称霸全区，但是出了服务器，出了游戏，还有谁认识你现在就给你一个机会，获得至高无上的荣誉，让更多的人知道你的强大。特此举行“我的服务器，我为自己代言”，服务器玩家冠名活动。
即日起，只要满足以下条件，即可获得服务器冠名权：
  1）开服前3天累计充值满10000元
  2）开服前7日累计充值满20000元，且区服处于未冠名状态，获得服务器冠名权
  备注：本活动遵循先到先得原则。如出现多个满足条件的，冠名资格由第一个满足条件的获得。[-]"
|  |  |  |  |  title = "【活动内容】："
|  |  |  |  }
|  |  |  |  [51] = {
|  |  |  |  |  text = "[ff0000]1、活动时间内有玩家满足条件后活动结束，活动期间没有玩家满足条件则保持原服务器名称。
2、服务器冠名长度为4个字。（必须遵守相关法律法规，不得使用负面或影响社会风气，不得出现敏感词的名称）
3、由于冠名操作需要时间，工作人员会在7个工作日内，完成冠名操作。
4，每个服，冠名只有一次。被冠名的服务器不予再次冠名[-]"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  |  [52] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "单日大额充值活动"
|  |  |  |  }
|  |  |  |  [53] = {
|  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [54] = {
|  |  |  |  |  text = "[ff0000]1.活动为单日累计充值，仅计算单日内玩家累计充值总额，次日重新计算。[-]"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [55] = {
|  |  |  |  |  text = "[ff0000]充值玩家向客服申请，次日发送邮件给玩家（节假日顺延）[-]"
|  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  }
|  |  |  |  [56] = {
|  |  |  |  |  text = "[ff0000]首日确定大额道具内容后，在次日再次充值达到大额道具申请条件的用户，在次日申请时可额外获得前一天道具内容的30%，往后同理[-]"
|  |  |  |  |  title = ""
|  |  |  |  }
|  |  |  |  [57] = {
|  |  |  |  |  text = "[ff0000]钱袋*10个[-]"
|  |  |  |  |  title = "第一天申请："
|  |  |  |  }
|  |  |  |  [58] = {
|  |  |  |  |  text = "[ff0000]钱袋（10+10*0.3）13个[-]"
|  |  |  |  |  title = "第二天申请："
|  |  |  |  }
|  |  |  |  [59] = {
|  |  |  |  |  text = "[ff0000]钱袋（13+13*0.3）17个[-]"
|  |  |  |  |  title = "第三天申请："
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  text = "[ff0000]传说伙伴碎片*10、3级宝石礼包*5、鲜肉包*100、2星精英伙伴*2[-]"
|  |  |  |  |  title = "新区累计充值1000元"
|  |  |  |  }
|  |  |  |  [60] = {
|  |  |  |  |  text = "[ff0000]钱袋（17+17*0.3）23个[-]"
|  |  |  |  |  title = "第四天申请："
|  |  |  |  }
|  |  |  |  [61] = {
|  |  |  |  |  text = "[ff0000]以次类推  所有小数点向上取整， 0.1也算1[-]"
|  |  |  |  |  title = ""
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  text = "[ff0000]传说伙伴碎片*20、3级宝石礼包*10、鲜肉包*200、2星精英伙伴*3[-]"
|  |  |  |  |  title = "新区累计充值2000元"
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  text = "[ff0000]传说伙伴碎片*30、3级宝石礼包*20、鲜肉包*300、2星精英伙伴*5[-]"
|  |  |  |  |  title = "新区累计充值3000元"
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  text = "[ff0000]传说伙伴碎片*50、梦觉书·零*10、梦觉书·低*10、梦觉书·高*10[-]"
|  |  |  |  |  title = "新区累计充值5000元"
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "限时大狂欢"
|  }
}
[core/global.lua:59]:<color=#ffeb04>CLoginServerPage.SetServer</color>
[logic/ui/CViewCtrl.lua:94]:CSelectServerView ShowView
[core/table.lua:94]:GroupServers------------------------> = {
|  [1] = {
|  |  group_id = 1
|  |  id = 1
|  |  name = "1-10区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  id = 10036
|  |  |  |  ip = "************"
|  |  |  |  name = "双线6区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1684749480
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7611
|  |  |  |  }
|  |  |  |  server_id = 10036
|  |  |  |  start_time = 1684749480
|  |  |  |  state = 2
|  |  |  }
|  |  |  [2] = {
|  |  |  |  group = 1
|  |  |  |  id = 10035
|  |  |  |  ip = "************"
|  |  |  |  name = "双线5区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1684720680
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7611
|  |  |  |  }
|  |  |  |  server_id = 10035
|  |  |  |  start_time = 1684720680
|  |  |  |  state = 2
|  |  |  }
|  |  |  [3] = {
|  |  |  |  group = 1
|  |  |  |  id = 10032
|  |  |  |  ip = "************"
|  |  |  |  name = "双线2区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1684555080
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7511
|  |  |  |  }
|  |  |  |  server_id = 10032
|  |  |  |  start_time = 1684555080
|  |  |  |  state = 2
|  |  |  }
|  |  |  [4] = {
|  |  |  |  group = 1
|  |  |  |  id = 10031
|  |  |  |  ip = "************"
|  |  |  |  name = "双线1区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1684511880
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7511
|  |  |  |  }
|  |  |  |  server_id = 10031
|  |  |  |  start_time = 1684511880
|  |  |  |  state = 2
|  |  |  }
|  |  |  [5] = {
|  |  |  |  group = 1
|  |  |  |  id = 10034
|  |  |  |  ip = "************"
|  |  |  |  name = "双线4区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1684663080
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7611
|  |  |  |  }
|  |  |  |  server_id = 10034
|  |  |  |  start_time = 1684663080
|  |  |  |  state = 2
|  |  |  }
|  |  |  [6] = {
|  |  |  |  group = 1
|  |  |  |  id = 10033
|  |  |  |  ip = "************"
|  |  |  |  name = "老大不小(双线3区)"
|  |  |  |  new = 1
|  |  |  |  open_time = 1684634280
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7611
|  |  |  |  }
|  |  |  |  server_id = 10033
|  |  |  |  start_time = 1684634280
|  |  |  |  state = 2
|  |  |  }
|  |  }
|  }
}
[logic/ui/CViewBase.lua:125]:CSelectServerView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[logic/ui/CViewCtrl.lua:94]:CSelectServerView ShowView
[core/table.lua:94]:GroupServers------------------------> = {
|  [1] = {
|  |  group_id = 1
|  |  id = 1
|  |  name = "1-10区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  id = 10036
|  |  |  |  ip = "************"
|  |  |  |  name = "双线6区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1684749480
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7611
|  |  |  |  }
|  |  |  |  server_id = 10036
|  |  |  |  start_time = 1684749480
|  |  |  |  state = 2
|  |  |  }
|  |  |  [2] = {
|  |  |  |  group = 1
|  |  |  |  id = 10035
|  |  |  |  ip = "************"
|  |  |  |  name = "双线5区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1684720680
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7611
|  |  |  |  }
|  |  |  |  server_id = 10035
|  |  |  |  start_time = 1684720680
|  |  |  |  state = 2
|  |  |  }
|  |  |  [3] = {
|  |  |  |  group = 1
|  |  |  |  id = 10034
|  |  |  |  ip = "************"
|  |  |  |  name = "双线4区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1684663080
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7611
|  |  |  |  }
|  |  |  |  server_id = 10034
|  |  |  |  start_time = 1684663080
|  |  |  |  state = 2
|  |  |  }
|  |  |  [4] = {
|  |  |  |  group = 1
|  |  |  |  id = 10033
|  |  |  |  ip = "************"
|  |  |  |  name = "老大不小(双线3区)"
|  |  |  |  new = 1
|  |  |  |  open_time = 1684634280
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7611
|  |  |  |  }
|  |  |  |  server_id = 10033
|  |  |  |  start_time = 1684634280
|  |  |  |  state = 2
|  |  |  }
|  |  |  [5] = {
|  |  |  |  group = 1
|  |  |  |  id = 10032
|  |  |  |  ip = "************"
|  |  |  |  name = "双线2区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1684555080
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7511
|  |  |  |  }
|  |  |  |  server_id = 10032
|  |  |  |  start_time = 1684555080
|  |  |  |  state = 2
|  |  |  }
|  |  |  [6] = {
|  |  |  |  group = 1
|  |  |  |  id = 10031
|  |  |  |  ip = "************"
|  |  |  |  name = "双线1区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1684511880
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7511
|  |  |  |  }
|  |  |  |  server_id = 10031
|  |  |  |  start_time = 1684511880
|  |  |  |  state = 2
|  |  |  }
|  |  }
|  }
}
[logic/ui/CViewBase.lua:125]:CSelectServerView LoadDone!
[core/global.lua:59]:<color=#ffeb04>CLoginServerPage.SetServer</color>
[logic/login/CLoginCtrl.lua:151]:ConnectServer =     table:0x730BE210    table:0x73225A10
[net/CNetCtrl.lua:114]:Test连接    ************    7511
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[core/table.lua:94]:-->Net Receive: login.GS2CHello = {
|  time = **********
}
[core/table.lua:94]:<--Net Send: login.C2GSQueryLogin = {
|  res_file_version = {}
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:11:33
[net/netlogin.lua:208]:netlogin.GS2CQueryLogin-->
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 1001
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginAccount = {
|  account = "dw1"
|  client_svn_version = 96
|  client_version = "1.15.0.0"
|  device = "B760M GAMING (JGINYUE)"
|  imei = ""
|  is_qrcode = 0
|  mac = "00-E0-1A-9A-48-AB"
|  os = "Windows 10  (10.0.0) 64bit"
|  platform = 4
|  udid = "552ae815ac1f51979f8a3ae596276f67ae3d10cf"
}
[core/global.lua:59]:<color=#ffeb04>更新代码: 1049</color>
[logic/base/Utils.lua:400]:local framever, dllver, resver = C_api.Utils.GetResVersion()
if resver >= 96 or Utils.IsEditor() then
	function CWelfareCtrl.IsOpenCostSave(self)
		local b = false
		local t = g_TimeCtrl:GetTimeS()	
		if self.m_CostSaveStartTime ~= 0 and self.m_CostSaveEndTime ~= 0 and t > self.m_CostSaveStartTime and t < self.m_CostSaveEndTime then
			b = true
		end
		return b
	end
	function CPartnerHireView.IsCanYFRH(self, iParID)
		local d = data.partnerhiredata.DATA
		if table.index(d[iParID]["recommand_list"], "一发入魂") then
			return self:IsCanSSRDraw()
		end
	end
	function CPartnerEquipPage.ChangePart(self)
		local oItem = g_ItemCtrl:GetItem(self.m_CurItemID)
		if oItem and oItem:GetValue("level") == define.Partner.ParEquip.MaxLevel and oItem:GetValue("star") < define.Partner.ParEquip.MaxStar then
			if self.m_CurPart ~= self.m_UpStarPart and self.m_CurPart ~= self.m_UpStonePart then
				self.m_UpStarBtn:SetSelected(true)
				self:SwitchPart()
			end
		end
	end
	data.netdata.BAN["proto"]["herobox"]["item"]["GS2CItemQuickUse"] = true
end

[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 1001
}
[core/table.lua:94]:-->Net Receive: login.GS2CCheckInviteCodeResult = {
|  msg = "邀请码验证已关闭"
|  result = 1
}
[core/table.lua:94]:-->Net Receive: login.GS2CLoginAccount = {
|  account = "dw1"
|  role_list = {
|  |  [1] = {
|  |  |  grade = 21
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 120
|  |  |  |  weapon = 2500
|  |  |  }
|  |  |  pid = 10001
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginRole = {
|  account = "dw1"
|  pid = 10001
}
[core/global.lua:59]:<color=#ffeb04>读取系统设置本地数据，帐号为  dw1</color>
[core/global.lua:59]:<color=#ffeb04>dw1 的系统设置本地数据为 nil，使用默认数据</color>
[core/table.lua:94]:-->Net Receive: login.GS2CLoginRole = {
|  account = "dw1"
|  pid = 10001
|  role = {
|  |  abnormal_attr_ratio = 590
|  |  attack = 359
|  |  bcmd = {
|  |  |  [1] = {
|  |  |  |  idx = 208
|  |  |  }
|  |  |  [10] = {
|  |  |  |  idx = 105
|  |  |  }
|  |  |  [11] = {
|  |  |  |  cmd = "封印"
|  |  |  |  idx = 202
|  |  |  }
|  |  |  [12] = {
|  |  |  |  idx = 107
|  |  |  }
|  |  |  [13] = {
|  |  |  |  idx = 108
|  |  |  }
|  |  |  [14] = {
|  |  |  |  idx = 205
|  |  |  }
|  |  |  [15] = {
|  |  |  |  idx = 206
|  |  |  }
|  |  |  [16] = {
|  |  |  |  idx = 207
|  |  |  }
|  |  |  [2] = {
|  |  |  |  idx = 106
|  |  |  }
|  |  |  [3] = {
|  |  |  |  cmd = "禁疗"
|  |  |  |  idx = 204
|  |  |  }
|  |  |  [4] = {
|  |  |  |  cmd = "集火"
|  |  |  |  idx = 201
|  |  |  }
|  |  |  [5] = {
|  |  |  |  cmd = "减速"
|  |  |  |  idx = 203
|  |  |  }
|  |  |  [6] = {
|  |  |  |  cmd = "治疗"
|  |  |  |  idx = 101
|  |  |  }
|  |  |  [7] = {
|  |  |  |  cmd = "复活"
|  |  |  |  idx = 102
|  |  |  }
|  |  |  [8] = {
|  |  |  |  cmd = "加速"
|  |  |  |  idx = 103
|  |  |  }
|  |  |  [9] = {
|  |  |  |  cmd = "解封"
|  |  |  |  idx = 104
|  |  |  }
|  |  }
|  |  coin = 3030
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 43
|  |  energy = 240
|  |  exp = 40960
|  |  grade = 21
|  |  kp_sdk_info = {
|  |  |  create_time = 1684507882
|  |  |  upgrade_time = 1684508706
|  |  }
|  |  mask = "7e6683d1ffffffe"
|  |  max_hp = 3759
|  |  model_info = {
|  |  |  color = {
|  |  |  |  [1] = 0
|  |  |  }
|  |  |  shape = 120
|  |  |  weapon = 2500
|  |  }
|  |  name = "相对之亚人"
|  |  open_day = 5
|  |  org_fuben_cnt = 2
|  |  power = 1163
|  |  res_abnormal_ratio = 590
|  |  res_critical_ratio = 500
|  |  school = 1
|  |  school_branch = 1
|  |  sex = 2
|  |  show_id = 10001
|  |  skill_point = 20
|  |  speed = 753
|  |  systemsetting = {}
|  }
|  role_token = "**************"
|  xg_account = "bus10001"
}
[logic/ui/CViewCtrl.lua:94]:CMainMenuView ShowView
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 590
|  active = 0
|  arenamedal = 0
|  attack = 359
|  bcmd = {
|  |  [1] = {
|  |  |  idx = 208
|  |  }
|  |  [10] = {
|  |  |  idx = 105
|  |  }
|  |  [11] = {
|  |  |  cmd = "封印"
|  |  |  idx = 202
|  |  }
|  |  [12] = {
|  |  |  idx = 107
|  |  }
|  |  [13] = {
|  |  |  idx = 108
|  |  }
|  |  [14] = {
|  |  |  idx = 205
|  |  }
|  |  [15] = {
|  |  |  idx = 206
|  |  }
|  |  [16] = {
|  |  |  idx = 207
|  |  }
|  |  [2] = {
|  |  |  idx = 106
|  |  }
|  |  [3] = {
|  |  |  cmd = "禁疗"
|  |  |  idx = 204
|  |  }
|  |  [4] = {
|  |  |  cmd = "集火"
|  |  |  idx = 201
|  |  }
|  |  [5] = {
|  |  |  cmd = "减速"
|  |  |  idx = 203
|  |  }
|  |  [6] = {
|  |  |  cmd = "治疗"
|  |  |  idx = 101
|  |  }
|  |  [7] = {
|  |  |  cmd = "复活"
|  |  |  idx = 102
|  |  }
|  |  [8] = {
|  |  |  cmd = "加速"
|  |  |  idx = 103
|  |  }
|  |  [9] = {
|  |  |  cmd = "解封"
|  |  |  idx = 104
|  |  }
|  }
|  camp = 0
|  chatself = false
|  chubeiexp = 0
|  coin = 3030
|  coin_over = 0
|  color_coin = 0
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 43
|  energy = 240
|  exp = 40960
|  followers = {}
|  goldcoin = 0
|  grade = 21
|  hp = 0
|  kp_sdk_info = {
|  |  create_time = 1684507882
|  |  upgrade_time = 1684508706
|  }
|  max_hp = 3759
|  medal = 0
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 120
|  |  weapon = 2500
|  }
|  name = "相对之亚人"
|  open_day = 5
|  org_fuben_cnt = 2
|  org_offer = 0
|  power = 1163
|  res_abnormal_ratio = 590
|  res_critical_ratio = 500
|  school = 1
|  school_branch = 1
|  sex = 2
|  show_id = 10001
|  skill_point = 20
|  skin = 0
|  speed = 753
|  systemsetting = {}
|  title_info = {}
|  trapmine_point = 0
|  travel_score = 0
|  upvote_amount = 0
}
[logic/misc/CTimeCtrl.lua:64]:HeartBeat-->开启心跳包检测
[core/table.lua:94]:<--Net Send: title.C2GSTitleInfoList = {}
[core/table.lua:94]:-->Net Receive: player.GS2CGamePushSetting = {}
[core/table.lua:94]:-->Net Receive: player.GS2CShapeList = {
|  shapes = {
|  |  [1] = 120
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CGuidanceInfo = {
|  guidanceinfo = {
|  |  key = {
|  |  |  [1] = 7000041
|  |  |  [10] = 1003099
|  |  |  [11] = 3006099
|  |  |  [12] = 3007099
|  |  |  [13] = 3008099
|  |  |  [14] = 3003099
|  |  |  [15] = 1004099
|  |  |  [16] = 3009099
|  |  |  [17] = 3010099
|  |  |  [18] = 3001101
|  |  |  [19] = 3001102
|  |  |  [2] = 7000051
|  |  |  [20] = 1006099
|  |  |  [21] = 3016099
|  |  |  [22] = 1010099
|  |  |  [23] = 1005099
|  |  |  [24] = 3001201
|  |  |  [25] = 3001202
|  |  |  [26] = 1007099
|  |  |  [27] = 3019099
|  |  |  [28] = 5000700
|  |  |  [29] = 1012099
|  |  |  [3] = 6010002
|  |  |  [30] = 1008099
|  |  |  [31] = 5000600
|  |  |  [32] = 1009099
|  |  |  [4] = 1027099
|  |  |  [5] = 3001099
|  |  |  [6] = 1028099
|  |  |  [7] = 5000300
|  |  |  [8] = 1001099
|  |  |  [9] = 3002099
|  |  }
|  |  version = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>>>>>>>>>>>>>>>>>>>>> GS2CGuidanceInfo </color>
[core/table.lua:94]:-->Net Receive: travel.GS2CLoginTravelPartner = {
|  travel_partner = {
|  |  server_time = 1684887094
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CServerGradeInfo = {
|  days = 3
|  server_grade = 60
}
[core/table.lua:94]:-->Net Receive: scene.GS2CShowScene = {
|  map_id = 101000
|  scene_id = 12
|  scene_name = "帝都"
}
[core/global.lua:59]:<color=#ffeb04>CResCtrl.MoveToSecondary</color>
[core/table.lua:94]:-->Net Receive: task.GS2CLoginTask = {
|  shimen_status = 1
|  taskdata = {
|  |  [1] = {
|  |  |  acceptnpc = 10007
|  |  |  clientnpc = {
|  |  |  |  [1] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 302
|  |  |  |  |  }
|  |  |  |  |  name = "重华"
|  |  |  |  |  npcid = 14318
|  |  |  |  |  npctype = 10007
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 12800
|  |  |  |  |  |  y = 9700
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  detaildesc = "摆脱少女重华的纠缠！"
|  |  |  name = "少女重华"
|  |  |  patrolinfo = {}
|  |  |  pickiteminfo = {}
|  |  |  placeinfo = {}
|  |  |  shapeinfo = {}
|  |  |  statusinfo = {
|  |  |  |  note = "CreateTask"
|  |  |  |  status = 2
|  |  |  }
|  |  |  submitnpc = 10007
|  |  |  target = 10007
|  |  |  targetdesc = "遭到质问"
|  |  |  taskid = 10003
|  |  |  taskitem = {}
|  |  |  tasktype = 1
|  |  |  traceinfo = {}
|  |  |  type = 2
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>login task</color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x4cfa21f8"
|  |  AssociatedPick = "function: 0x4cfa2258"
|  |  AssociatedSubmit = "function: 0x4cfa2228"
|  |  CreateDefalutData = "function: 0x4cfa01d8"
|  |  GetChaptetFubenData = "function: 0x4cfa1f18"
|  |  GetProgressThing = "function: 0x4cfa22b8"
|  |  GetRemainTime = "function: 0x4cfa0170"
|  |  GetStatus = "function: 0x4cfa2318"
|  |  GetTaskClientExtStrDic = "function: 0x4cfa2288"
|  |  GetTaskTypeSpriteteName = "function: 0x4cfa1ee8"
|  |  GetTraceInfo = "function: 0x4cfa2040"
|  |  GetTraceNpcType = "function: 0x4cfa1eb8"
|  |  GetValue = "function: 0x4cfa1f80"
|  |  IsAbandon = "function: 0x4cfa1fe0"
|  |  IsAddEscortDynamicNpc = "function: 0x4cfa2650"
|  |  IsMissMengTask = "function: 0x4cfa01a0"
|  |  IsPassChaterFuben = "function: 0x4cfa1f48"
|  |  IsTaskSpecityAction = "function: 0x4cfa2010"
|  |  IsTaskSpecityCategory = "function: 0x4cfa21c8"
|  |  New = "function: 0x4cfa6b48"
|  |  NewByData = "function: 0x4cf9f718"
|  |  RaiseProgressIdx = "function: 0x4cfa22e8"
|  |  RefreshTask = "function: 0x4cfa1fb0"
|  |  ResetEndTime = "function: 0x4cfa0240"
|  |  SetStatus = "function: 0x4cfa2680"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4cfa0140"
|  }
|  m_CData = {
|  |  ChapterFb = "1,1"
|  |  autoDoNextTask = 0
|  |  clientExtStr = ""
|  |  name = "少女重华"
|  |  submitNpcId = 10007
|  |  submitRewardStr = {
|  |  |  [1] = "R1003"
|  |  }
|  |  taskWalkingTips = "想不到帝都也有恶霸。,那个人是刚才的！;none;none"
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10007
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 302
|  |  |  |  }
|  |  |  |  name = "重华"
|  |  |  |  npcid = 14318
|  |  |  |  npctype = 10007
|  |  |  |  pos_info = {
|  |  |  |  |  x = 12800
|  |  |  |  |  y = 9700
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "摆脱少女重华的纠缠！"
|  |  isdone = 0
|  |  name = "少女重华"
|  |  patrolinfo = {}
|  |  pickiteminfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10007
|  |  target = 10007
|  |  targetdesc = "遭到质问"
|  |  taskid = 10003
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CTeachProgress = {}
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshPartnerTask = {
|  partnertask_progress = {
|  |  [1] = {
|  |  |  parid = 502
|  |  |  status = 1
|  |  |  taskid = 62090
|  |  }
|  |  [2] = {
|  |  |  parid = 313
|  |  |  status = 1
|  |  |  taskid = 62195
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  day_task = {
|  |  [1] = 1016
|  |  [2] = 3005
|  |  [3] = 1017
|  |  [4] = 3008
|  |  [5] = 3009
|  |  [6] = 1011
|  }
}
[core/table.lua:94]:-->Net Receive: state.GS2CLoginState = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginHuodongInfo = {
|  convoyinfo = {
|  |  convoy_partner = 1200
|  |  free_time = 1
|  |  pool_info = {
|  |  |  [1] = {
|  |  |  |  level = 1
|  |  |  |  partnerid = 1506
|  |  |  |  pos = 1
|  |  |  |  rewardid = 1001
|  |  |  }
|  |  |  [2] = {
|  |  |  |  level = 2
|  |  |  |  partnerid = 1200
|  |  |  |  pos = 2
|  |  |  |  rewardid = 1002
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [3] = {
|  |  |  |  level = 3
|  |  |  |  partnerid = 1750
|  |  |  |  pos = 3
|  |  |  |  rewardid = 1003
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [4] = {
|  |  |  |  level = 4
|  |  |  |  partnerid = 1510
|  |  |  |  pos = 4
|  |  |  |  rewardid = 1004
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [5] = {
|  |  |  |  level = 5
|  |  |  |  partnerid = 1020
|  |  |  |  pos = 5
|  |  |  |  rewardid = 1005
|  |  |  |  weight = 25
|  |  |  }
|  |  }
|  |  selected_pos = 2
|  |  target_npc = 5015
|  }
|  dailytrain = {
|  |  reward_times = 60
|  }
|  hireinfo = {
|  |  [1] = {
|  |  |  parid = 502
|  |  |  times = 1
|  |  }
|  }
|  huntinfo = {}
}
[logic/ui/CSprite.lua:159]:DynamicSprite dSprInfo    Item    10012
[logic/ui/CViewBase.lua:125]:CMainMenuView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginChapterInfo = {
|  finalchapter = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  level = 1
|  |  |  open = 1
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: handbook.GS2CLoginBookList = {
|  book_list = {
|  |  [1] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 313131
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 113131
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100313
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [2] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 313021
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = {
|  |  |  |  |  |  [1] = 113023
|  |  |  |  |  }
|  |  |  |  |  id = 313022
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100302
|  |  |  red_point = 2
|  |  |  repair = 1
|  |  |  show = 1
|  |  }
|  |  [3] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 315021
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115021
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100502
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  }
|  red_points = {
|  |  [1] = {
|  |  |  book_type = 1
|  |  |  red_point = 1
|  |  }
|  |  [2] = {
|  |  |  book_type = 2
|  |  }
|  |  [3] = {
|  |  |  book_type = 3
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: team.GS2CInviteInfo = {}
[core/table.lua:94]:-->Net Receive: mail.GS2CLoginMail = {}
[core/table.lua:94]:-->Net Receive: friend.GS2CLoginFriend = {}
[core/global.lua:59]:<color=#ffeb04>好友版本一致，无需更新</color>
[core/table.lua:94]:-->Net Receive: friend.GS2CApplyList = {
|  pidlist = {
|  |  [1] = 10007
|  |  [10] = 10039
|  |  [11] = 10042
|  |  [12] = 10050
|  |  [13] = 10052
|  |  [14] = 10055
|  |  [2] = 10010
|  |  [3] = 10011
|  |  [4] = 10012
|  |  [5] = 10014
|  |  [6] = 10017
|  |  [7] = 10018
|  |  [8] = 10026
|  |  [9] = 10028
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 21016
}
[core/table.lua:94]:<--Net Send: friend.C2GSQueryFriendApply = {
|  pid_list = {
|  |  [1] = 10007
|  |  [10] = 10039
|  |  [11] = 10042
|  |  [12] = 10050
|  |  [13] = 10052
|  |  [14] = 10055
|  |  [2] = 10010
|  |  [3] = 10011
|  |  [4] = 10012
|  |  [5] = 10014
|  |  [6] = 10017
|  |  [7] = 10018
|  |  [8] = 10026
|  |  [9] = 10028
|  }
}
[core/table.lua:94]:-->Net Receive: friend.GS2CFriendSetting = {
|  setting = {
|  |  notify = 1
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CDailySignInfo = {
|  sign_info = {
|  |  [1] = {
|  |  |  key = "week"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHeroBoxRecord = {}
[core/table.lua:94]:-->Net Receive: partner.GS2CDrawCardUI = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CRankBack = {
|  endtime = 1685116800
|  starttime = 1684512000
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTimeResumeInfo = {
|  end_time = 1685116800
|  plan_id = 1
|  start_time = 1684512000
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CRefreshTimeResume = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginTrapmine = {
|  rare_monster = {
|  |  [1] = {
|  |  |  map_id = 202000
|  |  |  partypes = {
|  |  |  |  [1] = 312
|  |  |  |  [2] = 401
|  |  |  |  [3] = 314
|  |  |  }
|  |  }
|  |  [2] = {
|  |  |  map_id = 204000
|  |  |  partypes = {
|  |  |  |  [1] = 409
|  |  |  |  [2] = 403
|  |  |  |  [3] = 407
|  |  |  }
|  |  }
|  |  [3] = {
|  |  |  map_id = 205000
|  |  |  partypes = {
|  |  |  |  [1] = 508
|  |  |  |  [2] = 509
|  |  |  |  [3] = 513
|  |  |  }
|  |  }
|  |  [4] = {
|  |  |  map_id = 206000
|  |  |  partypes = {
|  |  |  |  [1] = 412
|  |  |  |  [2] = 414
|  |  |  |  [3] = 415
|  |  |  }
|  |  }
|  |  [5] = {
|  |  |  map_id = 210400
|  |  |  partypes = {
|  |  |  |  [1] = 504
|  |  |  |  [2] = 505
|  |  |  |  [3] = 507
|  |  |  }
|  |  }
|  |  [6] = {
|  |  |  map_id = 200000
|  |  |  partypes = {
|  |  |  |  [1] = 416
|  |  |  |  [2] = 501
|  |  |  |  [3] = 502
|  |  |  }
|  |  }
|  |  [7] = {
|  |  |  map_id = 201000
|  |  |  partypes = {
|  |  |  |  [1] = 308
|  |  |  |  [2] = 302
|  |  |  |  [3] = 311
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 56906
|  |  |  type = "zsk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 56906
|  |  |  type = "yk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
|  mask = "c"
}
[core/table.lua:94]:CNetCtrl解析mask: GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 56906
|  |  |  type = "zsk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 56906
|  |  |  type = "yk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: store.GS2CPayForColorCoinInfo = {
|  colorcoin_list = {
|  |  [1] = {
|  |  |  key = "goldcoinstore_1005"
|  |  }
|  |  [10] = {
|  |  |  key = "goldcoinstore_2004"
|  |  }
|  |  [11] = {
|  |  |  key = "goldcoinstore_2005"
|  |  }
|  |  [12] = {
|  |  |  key = "goldcoinstore_2006"
|  |  }
|  |  [13] = {
|  |  |  key = "goldcoinstore_2007"
|  |  }
|  |  [14] = {
|  |  |  key = "goldcoinstore_1003"
|  |  }
|  |  [15] = {
|  |  |  key = "goldcoinstore_1004"
|  |  }
|  |  [2] = {
|  |  |  key = "goldcoinstore_1006"
|  |  }
|  |  [3] = {
|  |  |  key = "goldcoinstore_1007"
|  |  }
|  |  [4] = {
|  |  |  key = "goldcoinstore_1008"
|  |  }
|  |  [5] = {
|  |  |  key = "goldcoinstore_1001"
|  |  }
|  |  [6] = {
|  |  |  key = "goldcoinstore_1002"
|  |  }
|  |  [7] = {
|  |  |  key = "goldcoinstore_2001"
|  |  }
|  |  [8] = {
|  |  |  key = "goldcoinstore_2002"
|  |  }
|  |  [9] = {
|  |  |  key = "goldcoinstore_2003"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeRewrad = {
|  end_time = 1685116800
|  reward_info = {
|  |  [1] = {
|  |  |  left_amount = 2
|  |  |  rmb = 328
|  |  }
|  |  [2] = {
|  |  |  left_amount = 2
|  |  |  rmb = 648
|  |  }
|  }
|  schedule = 1
|  start_time = 1684512000
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraWarState = {
|  time = 1685203200
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraQueueStatus = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2COneRMBGift = {
|  endtime = 1685116800
|  gift = {
|  |  [1] = {
|  |  |  key = 1
|  |  }
|  |  [2] = {
|  |  |  key = 2
|  |  }
|  |  [3] = {
|  |  |  key = 3
|  |  }
|  }
|  starttime = 1684512000
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliReddot = {
|  cday = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHDAddChargeInfo = {
|  endtime = 1685116800
|  list = {
|  |  [1] = {
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  id = 2
|  |  }
|  |  [3] = {
|  |  |  id = 3
|  |  }
|  |  [4] = {
|  |  |  id = 4
|  |  }
|  |  [5] = {
|  |  |  id = 5
|  |  }
|  }
|  starttime = 1684512000
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CNewFieldBoss = {
|  bossid = {
|  |  [1] = 1
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2COnlineGift = {
|  onlinetime = 262
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CResumeRestore = {
|  end_time = 1685116800
|  plan_id = 1
|  start_time = 1684512000
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CRefreshResumeRestore = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CRushRankInfo = {
|  rush = {
|  |  [1] = {
|  |  |  endtime = 1685116800
|  |  |  idx = 117
|  |  |  show_endtime = 1685203200
|  |  }
|  |  [2] = {
|  |  |  endtime = 1685116800
|  |  |  idx = 115
|  |  |  show_endtime = 1685203200
|  |  }
|  |  [3] = {
|  |  |  endtime = 1685116800
|  |  |  idx = 118
|  |  |  show_endtime = 1685203200
|  |  }
|  |  [4] = {
|  |  |  endtime = 1685116800
|  |  |  idx = 105
|  |  |  show_endtime = 1685203200
|  |  }
|  |  [5] = {
|  |  |  endtime = 1685116800
|  |  |  idx = 106
|  |  |  show_endtime = 1685203200
|  |  }
|  |  [6] = {
|  |  |  endtime = 1685116800
|  |  |  idx = 113
|  |  |  show_endtime = 1685203200
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcFightInfoList = {
|  info_list = {
|  |  [1] = {
|  |  |  npc_type = 10024
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [10] = {
|  |  |  npc_type = 10007
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [11] = {
|  |  |  npc_type = 10008
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [12] = {
|  |  |  npc_type = 10009
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [13] = {
|  |  |  npc_type = 10011
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [14] = {
|  |  |  npc_type = 10012
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [15] = {
|  |  |  npc_type = 10013
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [16] = {
|  |  |  npc_type = 10000
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [17] = {
|  |  |  npc_type = 10023
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [18] = {
|  |  |  npc_type = 10014
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [19] = {
|  |  |  npc_type = 10015
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [2] = {
|  |  |  npc_type = 10002
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [20] = {
|  |  |  npc_type = 10016
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [21] = {
|  |  |  npc_type = 10017
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [22] = {
|  |  |  npc_type = 10018
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [23] = {
|  |  |  npc_type = 10019
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [24] = {
|  |  |  npc_type = 10020
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [25] = {
|  |  |  npc_type = 10021
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [26] = {
|  |  |  npc_type = 10022
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [3] = {
|  |  |  npc_type = 10010
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [4] = {
|  |  |  npc_type = 10001
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [5] = {
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [6] = {
|  |  |  npc_type = 10003
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [7] = {
|  |  |  npc_type = 10004
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [8] = {
|  |  |  npc_type = 10005
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [9] = {
|  |  |  npc_type = 10006
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CRefreshRewardBack = {
|  info = {
|  |  [1] = {
|  |  |  free = 1
|  |  |  left = 2
|  |  |  sid = 2006
|  |  |  vip = 100
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:94]:CWelfareView ShowView
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeScore = {
|  cur_id = 1
|  end_time = 1685116800
|  score_info = {}
|  start_time = 1684512000
|  status = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CGradeGiftInfo = {
|  buy_gift = {
|  |  items = {
|  |  |  [1] = {
|  |  |  |  amount = 3
|  |  |  |  sid = 10040
|  |  |  |  virtual = 10040
|  |  |  }
|  |  |  [2] = {
|  |  |  |  amount = 50
|  |  |  |  sid = 14011
|  |  |  |  virtual = 14011
|  |  |  }
|  |  |  [3] = {
|  |  |  |  amount = 66666
|  |  |  |  sid = 1002
|  |  |  |  virtual = 1002
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  discount = 2
|  endtime = 1684897894
|  free_gift = {
|  |  items = {
|  |  |  [1] = {
|  |  |  |  amount = 3
|  |  |  |  sid = 14011
|  |  |  |  virtual = 14011
|  |  |  }
|  |  |  [2] = {
|  |  |  |  amount = 6666
|  |  |  |  sid = 1002
|  |  |  |  virtual = 1002
|  |  |  }
|  |  }
|  }
|  grade = 30
|  ios_payid = "com.kaopu.ylq.appstore.lb.12"
|  now_price = 12
|  old_price = 600
|  payid = "com.kaopu.ylq.lb.12"
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayBuy = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CHistoryCharge = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFirstChargeUI = {
|  bOpen = true
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliPoint = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawCnt = {
|  cnt = 3
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliTime = {
|  endtime = 1685116800
|  starttime = 1684512000
}
[core/table.lua:94]:-->Net Receive: house.GS2CEnterHouse = {
|  buff_info = {}
|  furniture_info = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  lock_status = 1
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  type = 2
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  type = 3
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  type = 4
|  |  }
|  |  [5] = {
|  |  |  level = 1
|  |  |  type = 5
|  |  }
|  }
|  handle_type = 1
|  max_train = 1
|  max_warm_degree = 1000
|  owner_pid = 10001
|  partner_info = {
|  |  [1] = {
|  |  |  coin = 2780
|  |  |  type = 1001
|  |  }
|  }
|  warm_degree = 10
}
[core/table.lua:94]:-->Net Receive: house.GS2CPartnerExchangeUI = {
|  handle_type = 1
|  love_cnt = 10
|  max_gift_cnt = 10
|  max_love_cnt = 10
|  partner_gift_cnt = 10
}
[core/table.lua:94]:-->Net Receive: house.GS2COpenWorkDesk = {
|  desk_info = {
|  |  [1] = {
|  |  |  lock_status = 1
|  |  |  pos = 1
|  |  |  status = 1
|  |  }
|  |  [2] = {
|  |  |  pos = 2
|  |  |  status = 1
|  |  }
|  |  [3] = {
|  |  |  pos = 3
|  |  |  status = 1
|  |  }
|  |  [4] = {
|  |  |  lock_status = 1
|  |  |  pos = 4
|  |  |  status = 1
|  |  }
|  }
|  handle_type = 1
|  owner_pid = 10001
}
[core/table.lua:94]:-->Net Receive: player.GS2CTodayInfo = {
|  info = {
|  |  mask = "0"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: todyInfo = {}
[core/table.lua:94]:-->Net Receive: player.GS2CGameShare = {
|  game_share = {
|  |  [1] = {
|  |  |  type = "draw_card_share"
|  |  }
|  |  [2] = {
|  |  |  type = "skin_share"
|  |  }
|  |  [3] = {
|  |  |  type = "picture_share"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginRewardInfo = {
|  login_day = 3
|  rewarded_day = 2
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {
|  infolist = {
|  |  [1] = {
|  |  |  blist = {
|  |  |  |  [1] = 1
|  |  |  |  [2] = 3
|  |  |  }
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  blist = {
|  |  |  |  [1] = 8
|  |  |  }
|  |  |  id = 4
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayRedDot = {}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayMain = {
|  server_day = 5
}
[core/table.lua:94]:-->Net Receive: task.GS2CLoginAchieveTask = {
|  info = {
|  |  [1] = {
|  |  |  achievetype = 1
|  |  |  describe = "领取1次在线奖励"
|  |  |  name = "在线奖励"
|  |  |  target = 1
|  |  |  taskid = 31001
|  |  }
|  |  [2] = {
|  |  |  achievetype = 2
|  |  |  degree = 1
|  |  |  describe = "穿戴一件符文"
|  |  |  name = "穿戴符文（1）"
|  |  |  target = 1
|  |  |  taskid = 31524
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>LoginAchieveTask</color>
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2729
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  mask = "1997c2e0000000"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  give_org_equip = {}
|  give_org_wish = {}
|  is_equip_wish = 0
|  is_org_wish = 0
|  org_build_status = 0
|  org_build_time = 0
|  org_id = 0
|  org_leader = ""
|  org_level = 0
|  org_pos = 0
|  org_red_packet = 0
|  org_sign_reward = 0
|  org_status = 0
|  orgname = ""
}
[core/table.lua:94]:-->Net Receive: item.GS2CLoginItem = {
|  itemdata = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  create_time = 1684508716
|  |  |  id = 1
|  |  |  itemlevel = 4
|  |  |  name = "万能碎片"
|  |  |  sid = 14002
|  |  }
|  |  [2] = {
|  |  |  amount = 1
|  |  |  create_time = 1684886826
|  |  |  id = 8
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 1
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101001
|  |  }
|  |  [3] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1684507882
|  |  |  equip_info = {
|  |  |  |  pos = 1
|  |  |  |  stone_sid = 3100000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 2
|  |  |  itemlevel = 1
|  |  |  name = "练习红魔钺"
|  |  |  power = 57
|  |  |  sid = 2100000
|  |  }
|  |  [4] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 121
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 24
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1684507882
|  |  |  equip_info = {
|  |  |  |  pos = 2
|  |  |  |  stone_sid = 4000000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 3
|  |  |  itemlevel = 1
|  |  |  name = "练习飘逸巾"
|  |  |  power = 24
|  |  |  sid = 2200000
|  |  }
|  |  [5] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1684507882
|  |  |  equip_info = {
|  |  |  |  pos = 3
|  |  |  |  stone_sid = 4110000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 4
|  |  |  itemlevel = 1
|  |  |  name = "练习絮语衣"
|  |  |  power = 32
|  |  |  sid = 2320000
|  |  }
|  |  [6] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1684507882
|  |  |  equip_info = {
|  |  |  |  pos = 4
|  |  |  |  stone_sid = 4200000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 5
|  |  |  itemlevel = 1
|  |  |  name = "练习草织戒"
|  |  |  power = 19
|  |  |  sid = 2400000
|  |  }
|  |  [7] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 364
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 72
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1684507882
|  |  |  equip_info = {
|  |  |  |  pos = 5
|  |  |  |  stone_sid = 4310000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 6
|  |  |  itemlevel = 1
|  |  |  name = "练习离叶腰"
|  |  |  power = 72
|  |  |  sid = 2520000
|  |  }
|  |  [8] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 11
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "speed"
|  |  |  |  |  value = 33
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 77
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1684507882
|  |  |  equip_info = {
|  |  |  |  pos = 6
|  |  |  |  stone_sid = 4410000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 7
|  |  |  itemlevel = 1
|  |  |  name = "练习青藤鞋"
|  |  |  power = 77
|  |  |  sid = 2620000
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>loginitem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenPlanName = {}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartnerList = {
|  partner_list = {
|  |  [1] = {
|  |  |  abnormal_attr_ratio = 600
|  |  |  amount = 1
|  |  |  attack = 328
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 67
|  |  |  equip_list = {
|  |  |  |  [1] = 8
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  grade = 1
|  |  |  mask = "fffffefe"
|  |  |  max_hp = 1862
|  |  |  model_info = {
|  |  |  |  shape = 313
|  |  |  |  skin = 203130
|  |  |  }
|  |  |  name = "檀"
|  |  |  parid = 1
|  |  |  partner_type = 313
|  |  |  patahp = 1862
|  |  |  power = 934
|  |  |  power_rank = 7
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 700
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31301
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31302
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31303
|  |  |  |  }
|  |  |  }
|  |  |  speed = 665
|  |  |  star = 2
|  |  }
|  |  [2] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 283
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 59
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  grade = 1
|  |  |  mask = "fffffefe"
|  |  |  max_hp = 1792
|  |  |  model_info = {
|  |  |  |  shape = 502
|  |  |  |  skin = 205020
|  |  |  }
|  |  |  name = "马面面"
|  |  |  parid = 2
|  |  |  partner_type = 502
|  |  |  patahp = 1792
|  |  |  power = 632
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 500
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50201
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50202
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50203
|  |  |  |  }
|  |  |  }
|  |  |  speed = 315
|  |  |  star = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartner = {
|  fight_info = {
|  |  [1] = {
|  |  |  parid = 1
|  |  |  pos = 1
|  |  }
|  |  [2] = {
|  |  |  parid = 2
|  |  |  pos = 3
|  |  }
|  }
|  owned_equip_list = {
|  |  [1] = 6101001
|  }
|  owned_partner_list = {
|  |  [1] = 502
|  |  [2] = 313
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPicturePosList = {}
[core/table.lua:94]:-->Net Receive: skill.GS2CLoginSkill = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3001
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3002
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  needcost = 15
|  |  |  sk = 3004
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  needcost = 25
|  |  |  sk = 3005
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3006
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3007
|  |  |  type = 1
|  |  }
|  }
}
[logic/skill/CSkillCtrl.lua:112]:技能数据登陆协议返回:
[core/table.lua:94]:Table = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3001
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3002
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  needcost = 15
|  |  |  sk = 3004
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  needcost = 25
|  |  |  sk = 3005
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3006
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3007
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginParSoulPlan = {}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  abnormal_attr_ratio = 590
|  |  attack = 359
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 43
|  |  mask = "87ff00"
|  |  max_hp = 3759
|  |  power = 1163
|  |  res_abnormal_ratio = 590
|  |  res_critical_ratio = 500
|  |  speed = 753
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 590
|  attack = 359
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 43
|  hp = 0
|  max_hp = 3759
|  power = 1163
|  res_abnormal_ratio = 590
|  res_critical_ratio = 500
|  speed = 753
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterScene = {
|  eid = 32
|  pos_info = {
|  |  face_y = 98988
|  |  x = 18409
|  |  y = 17105
|  }
|  scene_id = 12
}
[core/global.lua:59]:<color=#ffeb04>result  DrawCardLineUp_Two_PartnerMain</color>
[core/table.lua:94]:<--Net Send: teach.C2GSClearGuidance = {
|  key = {
|  |  [1] = 3010099
|  |  [2] = 3001101
|  |  [3] = 3001102
|  }
}
[core/global.lua:59]:<color=#ffeb04>=00000=  userdata: 0x85a51bd8 nil</color>
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887094
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:11:34
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_MainMenu"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/mainmenu/CMainMenuExpandBox"]:280: in function <[string "logic/mainmenu/CMainMenuExpandBox"]:251>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_MainMenu"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>读取地图： 1020</color>
[core/global.lua:59]:<color=#ffeb04>Unity2017.4.30f1 localScale</color>
[logic/ui/CViewCtrl.lua:104]:CLoginView     CloseView
[logic/base/CResCtrl.lua:599]:res gc step start!
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[core/global.lua:59]:<color=#ffeb04>地图加载完成: 1020 ,当前地图: nil</color>
[logic/map/CMapCtrl.lua:1134]:<color=#00FF00> >>> .AddTaskChapterNpcNpc | 实例一个跟踪Npc | Table:AddTraceNpc </color>
[logic/map/CMapCtrl.lua:1134]:<color=#00FF00> >>> .AddTaskChapterNpcNpc | 实例一个跟踪Npc | Table:AddTraceNpc </color>
[logic/map/CMapCtrl.lua:1134]:<color=#00FF00> >>> .AddTaskChapterNpcNpc | 实例一个跟踪Npc | Table:AddTraceNpc </color>
[logic/map/CMapCtrl.lua:1134]:<color=#00FF00> >>> .AddTaskChapterNpcNpc | 实例一个跟踪Npc | Table:AddTraceNpc </color>
[logic/map/CMapCtrl.lua:1134]:<color=#00FF00> >>> .AddTaskChapterNpcNpc | 实例一个跟踪Npc | Table:AddTraceNpc </color>
[logic/map/CMapCtrl.lua:1134]:<color=#00FF00> >>> .AddTaskChapterNpcNpc | 实例一个跟踪Npc | Table:AddTraceNpc </color>
[core/table.lua:94]:Table = {}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1102
|  |  |  }
|  |  |  name = "峰馆长"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 2
|  |  npctype = 5040
|  }
|  eid = 1
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 1
|  pos_info = {
|  |  x = 31800
|  |  y = 17600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1102
|  }
|  name = "峰馆长"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1009
|  |  |  }
|  |  |  name = "神父"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 8
|  |  npctype = 5042
|  }
|  eid = 3
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 3
|  pos_info = {
|  |  x = 12200
|  |  y = 25000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1009
|  }
|  name = "神父"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1503
|  |  |  }
|  |  |  name = "喵小布"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 21
|  |  npctype = 5047
|  }
|  eid = 6
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 6
|  pos_info = {
|  |  x = 33000
|  |  y = 9500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1503
|  }
|  name = "喵小布"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
|  [2] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1012
|  |  |  }
|  |  |  name = "克瑞斯汀"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 24
|  |  npctype = 5048
|  }
|  eid = 7
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 7
|  pos_info = {
|  |  x = 20200
|  |  y = 21100
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1012
|  }
|  name = "克瑞斯汀"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1510
|  |  |  }
|  |  |  name = "小佳"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 57
|  |  npctype = 5064
|  }
|  eid = 8
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 8
|  pos_info = {
|  |  x = 13800
|  |  y = 20000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1510
|  }
|  name = "小佳"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
|  [2] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [3] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 306
|  |  |  }
|  |  |  name = "袁雀"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 60
|  |  npctype = 5001
|  }
|  eid = 9
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 9
|  pos_info = {
|  |  x = 6600
|  |  y = 26200
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 306
|  }
|  name = "袁雀"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1501
|  |  |  }
|  |  |  name = "扳尾"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 63
|  |  npctype = 5002
|  }
|  eid = 10
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 10
|  pos_info = {
|  |  x = 6000
|  |  y = 21200
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1501
|  }
|  name = "扳尾"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1014
|  |  |  }
|  |  |  name = "乔焱"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 66
|  |  npctype = 5003
|  }
|  eid = 11
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 11
|  pos_info = {
|  |  x = 13200
|  |  y = 24600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1014
|  }
|  name = "乔焱"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 414
|  |  |  }
|  |  |  name = "蓝堂昼"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 75
|  |  npctype = 5006
|  }
|  eid = 14
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 14
|  pos_info = {
|  |  x = 28000
|  |  y = 14500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 414
|  }
|  name = "蓝堂昼"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 311
|  |  |  }
|  |  |  name = "花常酒"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 78
|  |  npctype = 5007
|  }
|  eid = 15
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 15
|  pos_info = {
|  |  x = 35600
|  |  y = 13400
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 311
|  }
|  name = "花常酒"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 508
|  |  |  }
|  |  |  name = "魂夕"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 81
|  |  npctype = 5008
|  }
|  eid = 16
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 16
|  pos_info = {
|  |  x = 24000
|  |  y = 12900
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 508
|  }
|  name = "魂夕"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1502
|  |  |  }
|  |  |  name = "荆鸣"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 205
|  |  npctype = 5012
|  }
|  eid = 20
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 20
|  pos_info = {
|  |  x = 7000
|  |  y = 11300
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1502
|  }
|  name = "荆鸣"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1016
|  |  |  }
|  |  |  name = "水生"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 196
|  |  npctype = 5009
|  }
|  eid = 17
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 17
|  pos_info = {
|  |  x = 24000
|  |  y = 22000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1016
|  }
|  name = "水生"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1151
|  |  |  }
|  |  |  name = "邓酒爷"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 202
|  |  npctype = 5011
|  }
|  eid = 19
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 19
|  pos_info = {
|  |  x = 22300
|  |  y = 5100
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1151
|  }
|  name = "邓酒爷"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: title.GS2CTitleInfoList = {
|  infos = {
|  |  [1] = {
|  |  |  name = "8段"
|  |  |  tid = 1008
|  |  }
|  |  [10] = {
|  |  |  name = "应用宝贵族"
|  |  |  tid = 1017
|  |  }
|  |  [11] = {
|  |  |  name = "王者公会"
|  |  |  tid = 1018
|  |  }
|  |  [12] = {
|  |  |  name = "王者公会会长"
|  |  |  tid = 1019
|  |  }
|  |  [13] = {
|  |  |  name = "黄金公会"
|  |  |  tid = 1020
|  |  }
|  |  [14] = {
|  |  |  name = "黄金公会会长"
|  |  |  tid = 1021
|  |  }
|  |  [15] = {
|  |  |  name = "白银公会"
|  |  |  tid = 1022
|  |  }
|  |  [16] = {
|  |  |  name = "白银公会会长"
|  |  |  tid = 1023
|  |  }
|  |  [17] = {
|  |  |  name = "明日之星"
|  |  |  tid = 1024
|  |  }
|  |  [18] = {
|  |  |  name = "独孤求败"
|  |  |  tid = 1025
|  |  }
|  |  [19] = {
|  |  |  name = "谁与争锋"
|  |  |  tid = 1026
|  |  }
|  |  [2] = {
|  |  |  name = "测-贵族宝"
|  |  |  tid = 1009
|  |  }
|  |  [20] = {
|  |  |  name = "所向披靡"
|  |  |  tid = 1027
|  |  }
|  |  [21] = {
|  |  |  name = "地牢终结者"
|  |  |  tid = 1028
|  |  }
|  |  [22] = {
|  |  |  name = "地牢征服者"
|  |  |  tid = 1029
|  |  }
|  |  [23] = {
|  |  |  name = "地牢破坏者"
|  |  |  tid = 1030
|  |  }
|  |  [24] = {
|  |  |  name = "眯眯眼是变态"
|  |  |  tid = 1031
|  |  }
|  |  [25] = {
|  |  |  name = "邻家妹妹"
|  |  |  tid = 1032
|  |  }
|  |  [26] = {
|  |  |  name = "兄控"
|  |  |  tid = 1033
|  |  }
|  |  [27] = {
|  |  |  name = "农夫三拳"
|  |  |  tid = 1034
|  |  }
|  |  [28] = {
|  |  |  name = "赌神"
|  |  |  tid = 1035
|  |  }
|  |  [29] = {
|  |  |  name = "神之右手"
|  |  |  tid = 1036
|  |  }
|  |  [3] = {
|  |  |  name = "测-我就是托"
|  |  |  tid = 1010
|  |  }
|  |  [30] = {
|  |  |  name = "茶会噩梦"
|  |  |  tid = 1037
|  |  }
|  |  [31] = {
|  |  |  name = "我是傲娇"
|  |  |  tid = 1038
|  |  }
|  |  [32] = {
|  |  |  name = "闷骚美男子"
|  |  |  tid = 1039
|  |  }
|  |  [33] = {
|  |  |  name = "脸炮"
|  |  |  tid = 1040
|  |  }
|  |  [34] = {
|  |  |  name = "妹控"
|  |  |  tid = 1041
|  |  }
|  |  [35] = {
|  |  |  name = "守夜人"
|  |  |  tid = 1042
|  |  }
|  |  [36] = {
|  |  |  name = "泡泡娘"
|  |  |  tid = 1043
|  |  }
|  |  [37] = {
|  |  |  name = "姐控"
|  |  |  tid = 1044
|  |  }
|  |  [38] = {
|  |  |  name = "迷妹"
|  |  |  tid = 1045
|  |  }
|  |  [39] = {
|  |  |  name = "小天使"
|  |  |  tid = 1046
|  |  }
|  |  [4] = {
|  |  |  name = "测-粗壮大腿"
|  |  |  tid = 1011
|  |  }
|  |  [40] = {
|  |  |  name = "欺诈兔子"
|  |  |  tid = 1047
|  |  }
|  |  [41] = {
|  |  |  name = "天然呆"
|  |  |  tid = 1048
|  |  }
|  |  [42] = {
|  |  |  name = "开局一条狗"
|  |  |  tid = 1049
|  |  }
|  |  [43] = {
|  |  |  name = "种竹专业户"
|  |  |  tid = 1050
|  |  }
|  |  [44] = {
|  |  |  name = "顾家好男人"
|  |  |  tid = 1051
|  |  }
|  |  [45] = {
|  |  |  name = "无敌小钢炮"
|  |  |  tid = 1052
|  |  }
|  |  [46] = {
|  |  |  name = "小豆丁"
|  |  |  tid = 1053
|  |  }
|  |  [47] = {
|  |  |  name = "浪里白条"
|  |  |  tid = 1054
|  |  }
|  |  [48] = {
|  |  |  name = "话唠"
|  |  |  tid = 1055
|  |  }
|  |  [49] = {
|  |  |  name = "角落种蘑菇"
|  |  |  tid = 1056
|  |  }
|  |  [5] = {
|  |  |  name = "攻防战第一公会"
|  |  |  tid = 1012
|  |  }
|  |  [50] = {
|  |  |  name = "天然疯"
|  |  |  tid = 1057
|  |  }
|  |  [51] = {
|  |  |  name = "吃货"
|  |  |  tid = 1058
|  |  }
|  |  [52] = {
|  |  |  name = "控制狂"
|  |  |  tid = 1059
|  |  }
|  |  [53] = {
|  |  |  name = "女王大人"
|  |  |  tid = 1060
|  |  }
|  |  [54] = {
|  |  |  name = "八卦小王子"
|  |  |  tid = 1061
|  |  }
|  |  [55] = {
|  |  |  name = "三无"
|  |  |  tid = 1062
|  |  }
|  |  [56] = {
|  |  |  name = "养得累"
|  |  |  tid = 1063
|  |  }
|  |  [57] = {
|  |  |  name = "扑街之王"
|  |  |  tid = 1064
|  |  }
|  |  [58] = {
|  |  |  name = "抖S"
|  |  |  tid = 1065
|  |  }
|  |  [59] = {
|  |  |  name = "工作狂人"
|  |  |  tid = 1066
|  |  }
|  |  [6] = {
|  |  |  name = "攻防战第一司令"
|  |  |  tid = 1013
|  |  }
|  |  [60] = {
|  |  |  name = "精分中毒患者"
|  |  |  tid = 1067
|  |  }
|  |  [61] = {
|  |  |  name = "神神叨叨"
|  |  |  tid = 1068
|  |  }
|  |  [62] = {
|  |  |  name = "浮夸之神"
|  |  |  tid = 1069
|  |  }
|  |  [63] = {
|  |  |  name = "抖M"
|  |  |  tid = 1070
|  |  }
|  |  [64] = {
|  |  |  name = "自恋狂"
|  |  |  tid = 1071
|  |  }
|  |  [65] = {
|  |  |  name = "天然黑"
|  |  |  tid = 1072
|  |  }
|  |  [66] = {
|  |  |  name = "为所欲为"
|  |  |  tid = 1073
|  |  }
|  |  [67] = {
|  |  |  name = "氪金大佬"
|  |  |  tid = 1074
|  |  }
|  |  [68] = {
|  |  |  name = "应用宝帝王"
|  |  |  tid = 1075
|  |  }
|  |  [69] = {
|  |  |  name = "应用宝至尊"
|  |  |  tid = 1076
|  |  }
|  |  [7] = {
|  |  |  name = "梦魇之主"
|  |  |  tid = 1014
|  |  }
|  |  [70] = {
|  |  |  name = "公会新人"
|  |  |  tid = 1077
|  |  }
|  |  [71] = {
|  |  |  name = "公会成员"
|  |  |  tid = 1078
|  |  }
|  |  [72] = {
|  |  |  name = "公会精英"
|  |  |  tid = 1079
|  |  }
|  |  [73] = {
|  |  |  name = "公会副会"
|  |  |  tid = 1080
|  |  }
|  |  [74] = {
|  |  |  name = "公会会长"
|  |  |  tid = 1081
|  |  }
|  |  [75] = {
|  |  |  name = "情侣专属称谓"
|  |  |  tid = 1082
|  |  }
|  |  [76] = {
|  |  |  name = "命运之子"
|  |  |  tid = 1083
|  |  }
|  |  [77] = {
|  |  |  name = "不做大多数"
|  |  |  tid = 1084
|  |  }
|  |  [78] = {
|  |  |  name = "1段"
|  |  |  tid = 1001
|  |  }
|  |  [79] = {
|  |  |  name = "2段"
|  |  |  tid = 1002
|  |  }
|  |  [8] = {
|  |  |  name = "梦魇骑士"
|  |  |  tid = 1015
|  |  }
|  |  [80] = {
|  |  |  name = "3段"
|  |  |  tid = 1003
|  |  }
|  |  [81] = {
|  |  |  name = "4段"
|  |  |  tid = 1004
|  |  }
|  |  [82] = {
|  |  |  name = "5段"
|  |  |  tid = 1005
|  |  }
|  |  [83] = {
|  |  |  name = "6段"
|  |  |  tid = 1006
|  |  }
|  |  [84] = {
|  |  |  name = "7段"
|  |  |  tid = 1007
|  |  }
|  |  [9] = {
|  |  |  name = "梦魇猎人"
|  |  |  tid = 1016
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 21016
}
[core/table.lua:94]:-->Net Receive: friend.GS2CApplyProfile = {
|  profile_list = {
|  |  [1] = {
|  |  |  pro = {
|  |  |  |  grade = 15
|  |  |  |  mask = "fe"
|  |  |  |  name = "晃悠悠中华锅"
|  |  |  |  pid = 10007
|  |  |  |  school = 1
|  |  |  |  shape = 110
|  |  |  }
|  |  }
|  |  [10] = {
|  |  |  pro = {
|  |  |  |  grade = 25
|  |  |  |  mask = "fe"
|  |  |  |  name = "梦梦"
|  |  |  |  pid = 10039
|  |  |  |  school = 3
|  |  |  |  shape = 160
|  |  |  }
|  |  }
|  |  [11] = {
|  |  |  pro = {
|  |  |  |  grade = 18
|  |  |  |  mask = "fe"
|  |  |  |  name = "强盗"
|  |  |  |  pid = 10042
|  |  |  |  school = 1
|  |  |  |  shape = 110
|  |  |  }
|  |  }
|  |  [12] = {
|  |  |  pro = {
|  |  |  |  grade = 45
|  |  |  |  mask = "fe"
|  |  |  |  name = "G丶欧皇"
|  |  |  |  pid = 10050
|  |  |  |  school = 3
|  |  |  |  shape = 150
|  |  |  }
|  |  }
|  |  [13] = {
|  |  |  pro = {
|  |  |  |  grade = 25
|  |  |  |  mask = "fe"
|  |  |  |  name = "李狗蛋"
|  |  |  |  pid = 10052
|  |  |  |  school = 2
|  |  |  |  shape = 140
|  |  |  }
|  |  }
|  |  [14] = {
|  |  |  pro = {
|  |  |  |  grade = 14
|  |  |  |  mask = "fe"
|  |  |  |  name = "wink丶"
|  |  |  |  pid = 10055
|  |  |  |  school = 1
|  |  |  |  shape = 120
|  |  |  }
|  |  }
|  |  [2] = {
|  |  |  pro = {
|  |  |  |  grade = 12
|  |  |  |  mask = "fe"
|  |  |  |  name = "反常计数君"
|  |  |  |  pid = 10010
|  |  |  |  school = 1
|  |  |  |  shape = 110
|  |  |  }
|  |  }
|  |  [3] = {
|  |  |  pro = {
|  |  |  |  grade = 25
|  |  |  |  mask = "fe"
|  |  |  |  name = "尖刀"
|  |  |  |  pid = 10011
|  |  |  |  school = 3
|  |  |  |  shape = 150
|  |  |  }
|  |  }
|  |  [4] = {
|  |  |  pro = {
|  |  |  |  grade = 16
|  |  |  |  mask = "fe"
|  |  |  |  name = "痛痛飞"
|  |  |  |  pid = 10012
|  |  |  |  school = 2
|  |  |  |  shape = 140
|  |  |  }
|  |  }
|  |  [5] = {
|  |  |  pro = {
|  |  |  |  grade = 14
|  |  |  |  mask = "fe"
|  |  |  |  name = "悬疑v盾牌"
|  |  |  |  pid = 10014
|  |  |  |  school = 3
|  |  |  |  shape = 150
|  |  |  }
|  |  }
|  |  [6] = {
|  |  |  pro = {
|  |  |  |  grade = 15
|  |  |  |  mask = "fe"
|  |  |  |  name = "么么哒黑熊"
|  |  |  |  pid = 10017
|  |  |  |  school = 3
|  |  |  |  shape = 150
|  |  |  }
|  |  }
|  |  [7] = {
|  |  |  pro = {
|  |  |  |  grade = 36
|  |  |  |  mask = "fe"
|  |  |  |  name = "黄毛怪大婶"
|  |  |  |  pid = 10018
|  |  |  |  school = 1
|  |  |  |  shape = 120
|  |  |  }
|  |  }
|  |  [8] = {
|  |  |  pro = {
|  |  |  |  grade = 30
|  |  |  |  mask = "fe"
|  |  |  |  name = "老大艹不小"
|  |  |  |  pid = 10026
|  |  |  |  school = 2
|  |  |  |  shape = 140
|  |  |  }
|  |  }
|  |  [9] = {
|  |  |  pro = {
|  |  |  |  grade = 25
|  |  |  |  mask = "fe"
|  |  |  |  name = "笑了笑与杀手"
|  |  |  |  pid = 10028
|  |  |  |  school = 3
|  |  |  |  shape = 150
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: friend = {
|  friend_degree = 0
|  grade = 15
|  name = "晃悠悠中华锅"
|  pid = 10007
|  relation = 0
|  school = 1
|  shape = 110
}
[core/table.lua:94]:CNetCtrl解析mask: friend = {
|  friend_degree = 0
|  grade = 12
|  name = "反常计数君"
|  pid = 10010
|  relation = 0
|  school = 1
|  shape = 110
}
[core/table.lua:94]:CNetCtrl解析mask: friend = {
|  friend_degree = 0
|  grade = 25
|  name = "尖刀"
|  pid = 10011
|  relation = 0
|  school = 3
|  shape = 150
}
[core/table.lua:94]:CNetCtrl解析mask: friend = {
|  friend_degree = 0
|  grade = 16
|  name = "痛痛飞"
|  pid = 10012
|  relation = 0
|  school = 2
|  shape = 140
}
[core/table.lua:94]:CNetCtrl解析mask: friend = {
|  friend_degree = 0
|  grade = 14
|  name = "悬疑v盾牌"
|  pid = 10014
|  relation = 0
|  school = 3
|  shape = 150
}
[core/table.lua:94]:CNetCtrl解析mask: friend = {
|  friend_degree = 0
|  grade = 15
|  name = "么么哒黑熊"
|  pid = 10017
|  relation = 0
|  school = 3
|  shape = 150
}
[core/table.lua:94]:CNetCtrl解析mask: friend = {
|  friend_degree = 0
|  grade = 36
|  name = "黄毛怪大婶"
|  pid = 10018
|  relation = 0
|  school = 1
|  shape = 120
}
[core/table.lua:94]:CNetCtrl解析mask: friend = {
|  friend_degree = 0
|  grade = 30
|  name = "老大艹不小"
|  pid = 10026
|  relation = 0
|  school = 2
|  shape = 140
}
[core/table.lua:94]:CNetCtrl解析mask: friend = {
|  friend_degree = 0
|  grade = 25
|  name = "笑了笑与杀手"
|  pid = 10028
|  relation = 0
|  school = 3
|  shape = 150
}
[core/table.lua:94]:CNetCtrl解析mask: friend = {
|  friend_degree = 0
|  grade = 25
|  name = "梦梦"
|  pid = 10039
|  relation = 0
|  school = 3
|  shape = 160
}
[core/table.lua:94]:CNetCtrl解析mask: friend = {
|  friend_degree = 0
|  grade = 18
|  name = "强盗"
|  pid = 10042
|  relation = 0
|  school = 1
|  shape = 110
}
[core/table.lua:94]:CNetCtrl解析mask: friend = {
|  friend_degree = 0
|  grade = 45
|  name = "G丶欧皇"
|  pid = 10050
|  relation = 0
|  school = 3
|  shape = 150
}
[core/table.lua:94]:CNetCtrl解析mask: friend = {
|  friend_degree = 0
|  grade = 25
|  name = "李狗蛋"
|  pid = 10052
|  relation = 0
|  school = 2
|  shape = 140
}
[core/table.lua:94]:CNetCtrl解析mask: friend = {
|  friend_degree = 0
|  grade = 14
|  name = "wink丶"
|  pid = 10055
|  relation = 0
|  school = 1
|  shape = 120
}
[core/global.lua:59]:<color=#ffeb04>找到新的引导类型 DrawCardLineUp_Two_MainMenu</color>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_MainMenu"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/mainmenu/CMainMenuExpandBox"]:280: in function <[string "logic/mainmenu/CMainMenuExpandBox"]:251>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_MainMenu"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_MainMenu"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_MainMenu"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10122</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小叶 1509 [19.3,20.4,0] -58 false</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小佳 1510 [18.3,20,0] -22.7 false</color>
[logic/platform/CSdkCtrl.lua:380]:sdk upload: not init
[core/table.lua:94]:Table = {
|  [1] = "16"
|  [2] = "20"
}
[core/global.lua:59]:<color=#ffeb04>21 16 20</color>
[core/table.lua:94]:Table = {
|  [1] = "21"
|  [2] = "24"
}
[core/global.lua:59]:<color=#ffeb04>21 21 24</color>
[core/table.lua:94]:Table = {
|  [1] = "25"
|  [2] = "27"
}
[core/global.lua:59]:<color=#ffeb04>21 25 27</color>
[logic/ui/CViewCtrl.lua:94]:CGuideView ShowView
[logic/ui/CViewBase.lua:125]:CWelfareView LoadDone!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_MainMenu"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/ui/CViewBase"]:139: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:311: in function <[string "logic/base/CResCtrl"]:305>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[logic/ui/CViewBase.lua:125]:CGuideView LoadDone!
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 之前忙着做兼职，果然这次考试成绩不好。</color>
[logic/base/CResCtrl.lua:626]:res gc finish!
[logic/ui/CViewCtrl.lua:104]:CWelfareView     CloseView
[core/global.lua:59]:<color=#ffeb04>找到新的引导类型 DrawCardLineUp_Two_MainMenu</color>
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 DrawCardLineUp_Two_MainMenu_1 1</color>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_MainMenu"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_MainMenu"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 哇！你还是一如既往的厉害，年级前10耶！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  2 mihu true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887104
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:11:44
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 是吗？我还没有找到我排名…</color>
[core/global.lua:59]:<color=#ffeb04>引导结束 DrawCardLineUp_Two_MainMenu DrawCardLineUp_Two_MainMenu_1 1</color>
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 DrawCardLineUp_Two_MainMenu_2 2</color>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_MainMenu"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewBase.lua:131]:CGuideMaskView LoadDone, not in loadingview!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_MainMenu"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/ui/CViewBase"]:139: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:311: in function <[string "logic/base/CResCtrl"]:305>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[core/table.lua:94]:-->Net Receive: mail.GS2CAddMail = {
|  simpleinfo = {
|  |  createtime = 1684887107
|  |  hasattach = 1
|  |  keeptime = 7776000
|  |  mailid = 1
|  |  subject = "喵小萌的来信"
|  |  title = "删测福利"
|  }
}
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  1 -155</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 你看错了，那是倒数排名。</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  2 37</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 不对啊我还看到班长名字了。</color>
[logic/ui/CViewCtrl.lua:94]:CPartnerMainView ShowView
[core/global.lua:59]:<color=#ffeb04>引导结束 DrawCardLineUp_Two_MainMenu DrawCardLineUp_Two_MainMenu_2 2</color>
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewCtrl.lua:104]:CGuideView     CloseView
[logic/guide/CGuideCtrl.lua:333]:已完成引导DrawCardLineUp_Two_MainMenu
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_DrawCardLineUp_Two_MainMenu"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 3010099
|  }
}
[core/global.lua:59]:<color=#ffeb04>停止引导检查,并且下一帧再检测引导</color>
[logic/ui/CViewBase.lua:125]:CGuideMaskView LoadDone!
[logic/ui/CViewBase.lua:125]:CPartnerMainView LoadDone!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/ui/CViewBase"]:139: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:311: in function <[string "logic/base/CResCtrl"]:305>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:98: in function <[string "logic/ui/CViewBase"]:97>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>找到新的引导类型 DrawCardLineUp_Two_PartnerMain</color>
[logic/ui/CViewCtrl.lua:94]:CGuideView ShowView
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/task/CTaskCtrl"]:2434: in function 'StartAutoDoingShiMen'
	[string "logic/guide/CGuideCtrl"]:2383: in function 'StopActionWhenGuide'
	[string "logic/guide/CGuideView"]:31: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewBase.lua:125]:CGuideView LoadDone!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/ui/CViewBase"]:139: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 DrawCardLineUp_Two_PartnerMain_1 1</color>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>引导结束 DrawCardLineUp_Two_PartnerMain DrawCardLineUp_Two_PartnerMain_1 1</color>
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_DrawCardLineUp_Two_PartnerMain_1"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 3001101
|  }
}
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 DrawCardLineUp_Two_PartnerMain_2 2</color>
[logic/ui/CViewBase.lua:131]:CGuideMaskView LoadDone, not in loadingview!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/ui/CViewBase"]:139: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 wuyu2 true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887114
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:11:54
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CPartnerChooseView ShowView
[core/global.lua:59]:<color=#ffeb04>引导结束 DrawCardLineUp_Two_PartnerMain DrawCardLineUp_Two_PartnerMain_2 2</color>
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_DrawCardLineUp_Two_PartnerMain_2"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 3001102
|  }
}
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewBase.lua:125]:CGuideMaskView LoadDone!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/ui/CViewBase"]:139: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewBase.lua:125]:CPartnerChooseView LoadDone!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/ui/CViewBase"]:139: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:311: in function <[string "logic/base/CResCtrl"]:305>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:98: in function <[string "logic/ui/CViewBase"]:97>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 听说，班长在考试前被没收了漫画。</color>
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  2 zhenjing true</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 18001
}
[core/table.lua:94]:<--Net Send: partner.C2GSPartnerFight = {
|  fight_info = {
|  |  parid = 2
|  |  pos = 3
|  }
}
[logic/ui/CViewCtrl.lua:104]:CPartnerChooseView     CloseView
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 18001
}
[core/table.lua:94]:-->Net Receive: partner.GS2CRefreshFightPartner = {
|  fight_info = {
|  |  pos = 3
|  }
}
[logic/ui/CViewCtrl.lua:104]:CGuideView     CloseView
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "DrawCardLineUp_Two_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/table.lua:94]:-->Net Receive: mail.GS2CAddMail = {
|  simpleinfo = {
|  |  createtime = 1684887121
|  |  hasattach = 1
|  |  keeptime = 7776000
|  |  mailid = 2
|  |  subject = "喵小萌的来信"
|  |  title = "删测福利"
|  }
}
[logic/ui/CViewCtrl.lua:104]:CPartnerMainView     CloseView
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 那她一定因为伤心过度所以考砸了。</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887124
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:12:04
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  2 43</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 说起来考试前我看了一本小说，推荐哦！</color>
[logic/ui/CViewCtrl.lua:94]:CAttrMainView ShowView
[logic/ui/CViewBase.lua:125]:CAttrMainView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CAttrMainView     CloseView
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 6
|  scene_id = 12
}
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好看吗，借我啦！</color>
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 19
|  scene_id = 12
}
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  2 [12.3,25.5,0] 90</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [11.6,25,0] 90</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 不要啦，人家还没看完呢！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 别小气嘛！</color>
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1503
|  |  |  }
|  |  |  name = "喵小布"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 21
|  |  npctype = 5047
|  }
|  eid = 6
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 6
|  pos_info = {
|  |  x = 33000
|  |  y = 9500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1503
|  }
|  name = "喵小布"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1151
|  |  |  }
|  |  |  name = "邓酒爷"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 202
|  |  npctype = 5011
|  }
|  eid = 19
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 19
|  pos_info = {
|  |  x = 22300
|  |  y = 5100
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1151
|  }
|  name = "邓酒爷"
|  trapmine = {}
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887134
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:12:14
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CFriendMainView ShowView
[logic/ui/CViewBase.lua:125]:CFriendMainView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 17001
}
[core/table.lua:94]:<--Net Send: mail.C2GSOpenMail = {
|  mailid = 2
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 17001
}
[core/table.lua:94]:-->Net Receive: mail.GS2CMailInfo = {
|  attachs = {
|  |  [1] = {
|  |  |  sid = 13269
|  |  |  type = 1
|  |  |  val = 4
|  |  }
|  }
|  context = "亲爱的玩家：
        欢迎参加不删档测试，还记得终极测试时本喵答应给大家的福利吗？现在把礼物奉上，请亲们收好哦！如果亲们在游戏中遇到有BUG，或对游戏有改良建议，可以通过官网论坛等途径告诉小萌，最后祝亲们游戏愉快，记得常来八门村找我玩哦，喵~~

                                                            喵小萌致上"
|  hasattach = 1
|  keeptime = 7776000
|  mailid = 2
|  name = "喵小萌"
|  opened = 1
|  subject = "喵小萌的来信"
|  title = "删测福利"
|  validtime = 1692663121
}
[core/global.lua:59]:<color=#ffeb04>CMailCtrl.UpdateMailInfo, mailid = 2</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  2 false</color>
[core/table.lua:94]:-->Net Receive: mail.GS2CMailOpened = {
|  mailids = {
|  |  [1] = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>领取邮件附件, mailid = 2</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 17002
}
[core/table.lua:94]:<--Net Send: mail.C2GSAcceptAttach = {
|  mailid = 2
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 17002
}
[core/table.lua:94]:-->Net Receive: mail.GS2CDelAttach = {
|  mailid = 2
}
[core/table.lua:94]:-->Net Receive: mail.GS2CMailOpened = {
|  mailids = {
|  |  [1] = 2
|  }
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得 #P[紫色可选御灵] x 4#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 4
|  |  create_time = 1684887121
|  |  id = 9
|  |  itemlevel = 3
|  |  name = "紫色可选御灵"
|  |  sid = 13269
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CItemQuickUse = {
|  id = 9
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 4
|  |  |  sid = 13269
|  |  |  virtual = 13269
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 17001
}
[core/table.lua:94]:<--Net Send: mail.C2GSOpenMail = {
|  mailid = 1
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 17001
}
[core/table.lua:94]:-->Net Receive: mail.GS2CMailInfo = {
|  attachs = {
|  |  [1] = {
|  |  |  sid = 13281
|  |  |  type = 1
|  |  |  val = 4
|  |  }
|  }
|  context = "亲爱的玩家：
        欢迎参加不删档测试，还记得终极测试时本喵答应给大家的福利吗？现在把礼物奉上，请亲们收好哦！如果亲们在游戏中遇到有BUG，或对游戏有改良建议，可以通过官网论坛等途径告诉小萌，最后祝亲们游戏愉快，记得常来八门村找我玩哦，喵~~

                                                            喵小萌致上"
|  hasattach = 1
|  keeptime = 7776000
|  mailid = 1
|  name = "喵小萌"
|  opened = 1
|  subject = "喵小萌的来信"
|  title = "删测福利"
|  validtime = 1692663107
}
[core/global.lua:59]:<color=#ffeb04>CMailCtrl.UpdateMailInfo, mailid = 1</color>
[core/table.lua:94]:-->Net Receive: mail.GS2CMailOpened = {
|  mailids = {
|  |  [1] = 1
|  }
}
[core/global.lua:59]:<color=#ffeb04>领取邮件附件, mailid = 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 17002
}
[core/table.lua:94]:<--Net Send: mail.C2GSAcceptAttach = {
|  mailid = 1
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 17002
}
[core/table.lua:94]:-->Net Receive: mail.GS2CDelAttach = {
|  mailid = 1
}
[core/table.lua:94]:-->Net Receive: mail.GS2CMailOpened = {
|  mailids = {
|  |  [1] = 1
|  }
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得 #O[伙伴可选礼包] x 4#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 4
|  |  create_time = 1684887107
|  |  id = 10
|  |  itemlevel = 4
|  |  name = "伙伴可选礼包"
|  |  sid = 13281
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CItemQuickUse = {
|  id = 10
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 4
|  |  |  sid = 13281
|  |  |  virtual = 13281
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[logic/ui/CViewCtrl.lua:94]:CItemQuickUseView ShowView
[logic/ui/CViewBase.lua:125]:CItemQuickUseView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887144
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:12:24
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CItemQuickUseView     CloseView
[logic/ui/CViewCtrl.lua:104]:CFriendMainView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemBagMainView ShowView
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CViewBase.lua:125]:CItemBagMainView LoadDone!
[logic/ui/CViewCtrl.lua:94]:CItemTipsBaseInfoView ShowView
[logic/ui/CViewBase.lua:125]:CItemTipsBaseInfoView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemTipsBaseInfoView     CloseView
[logic/ui/CViewCtrl.lua:104]:CItemBagMainView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887154
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:12:34
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CGmView ShowView
[logic/ui/CViewBase.lua:125]:CGmView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887164
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:12:44
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSGMCmd = {
|  cmd = "rewardexp 10029 100000"
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "该玩家不在线，不能增加经验"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887174
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:12:54
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSGMCmd = {
|  cmd = "rewardexp 10001 100000"
}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  day_task = {
|  |  [1] = 1016
|  |  [2] = 3005
|  |  [3] = 1017
|  |  [4] = 3008
|  |  [5] = 3009
|  |  [6] = 1011
|  |  [7] = 1001
|  }
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得#exp#G100000#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#exp#G100000#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CRefreshTrainTimes = {
|  times = 120
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHuntInfo = {
|  npcinfo = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  status = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 22
|  |  id = 10101
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 22
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 22
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 22
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 22
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 22
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  day_task = {
|  |  [1] = 1016
|  |  [2] = 3005
|  |  [3] = 1017
|  |  [4] = 3008
|  |  [5] = 3009
|  |  [6] = 1011
|  |  [7] = 1001
|  |  [8] = 1024
|  |  [9] = 2003
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 22
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2749
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2749
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CGradeGiftInfo = {
|  buy_gift = {
|  |  items = {
|  |  |  [1] = {
|  |  |  |  amount = 3
|  |  |  |  sid = 10040
|  |  |  |  virtual = 10040
|  |  |  }
|  |  |  [2] = {
|  |  |  |  amount = 50
|  |  |  |  sid = 14011
|  |  |  |  virtual = 14011
|  |  |  }
|  |  |  [3] = {
|  |  |  |  amount = 66666
|  |  |  |  sid = 1002
|  |  |  |  virtual = 1002
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  discount = 2
|  endtime = 1684897981
|  free_gift = {
|  |  items = {
|  |  |  [1] = {
|  |  |  |  amount = 3
|  |  |  |  sid = 14011
|  |  |  |  virtual = 14011
|  |  |  }
|  |  |  [2] = {
|  |  |  |  amount = 6666
|  |  |  |  sid = 1002
|  |  |  |  virtual = 1002
|  |  |  }
|  |  }
|  }
|  grade = 30
|  ios_payid = "com.kaopu.ylq.appstore.lb.12"
|  now_price = 12
|  old_price = 600
|  open_ui = 1
|  payid = "com.kaopu.ylq.lb.12"
|  status = 1
}
[logic/ui/CViewCtrl.lua:94]:CGradeGiftView ShowView
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 23
|  |  id = 10101
|  }
}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  day_task = {
|  |  [1] = 1016
|  |  [10] = 1003
|  |  [2] = 3005
|  |  [3] = 1017
|  |  [4] = 3008
|  |  [5] = 3009
|  |  [6] = 1011
|  |  [7] = 1001
|  |  [8] = 1024
|  |  [9] = 2003
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 23
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 23
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 23
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 23
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 23
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 23
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2779
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2779
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 24
|  |  id = 10101
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 24
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 24
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 24
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 24
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  day_task = {
|  |  [1] = 1016
|  |  [10] = 1003
|  |  [11] = 1020
|  |  [2] = 3005
|  |  [3] = 1017
|  |  [4] = 3008
|  |  [5] = 3009
|  |  [6] = 1011
|  |  [7] = 1001
|  |  [8] = 1024
|  |  [9] = 2003
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 24
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 24
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2799
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2799
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 25
|  |  id = 10101
|  }
}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  day_task = {
|  |  [1] = 1016
|  |  [10] = 1003
|  |  [11] = 1020
|  |  [12] = 1006
|  |  [2] = 3005
|  |  [3] = 1017
|  |  [4] = 3008
|  |  [5] = 3009
|  |  [6] = 1011
|  |  [7] = 1001
|  |  [8] = 1024
|  |  [9] = 2003
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 25
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 25
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 25
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 25
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 25
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 25
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2829
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  day_task = {
|  |  [1] = 1016
|  |  [10] = 1003
|  |  [11] = 1020
|  |  [12] = 1006
|  |  [13] = 2001
|  |  [2] = 3005
|  |  [3] = 1017
|  |  [4] = 3008
|  |  [5] = 3009
|  |  [6] = 1011
|  |  [7] = 1001
|  |  [8] = 1024
|  |  [9] = 2003
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2829
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  day_task = {
|  |  [1] = 1016
|  |  [10] = 1003
|  |  [11] = 1020
|  |  [12] = 1006
|  |  [13] = 2001
|  |  [14] = 1007
|  |  [2] = 3005
|  |  [3] = 1017
|  |  [4] = 3008
|  |  [5] = 3009
|  |  [6] = 1011
|  |  [7] = 1001
|  |  [8] = 1024
|  |  [9] = 2003
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 26
|  |  id = 10101
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 26
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 26
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 26
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 26
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 26
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 26
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "指令执行成功"
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  abnormal_attr_ratio = 670
|  |  attack = 422
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 43
|  |  energy = 294
|  |  exp = 140960
|  |  grade = 39
|  |  hp = 5217
|  |  kp_sdk_info = {
|  |  |  create_time = 1684507882
|  |  |  upgrade_time = 1684887181
|  |  }
|  |  mask = "10028000287ff42"
|  |  max_hp = 5304
|  |  power = 1599
|  |  res_abnormal_ratio = 670
|  |  res_critical_ratio = 500
|  |  skill_point = 56
|  |  speed = 753
|  |  trapmine_point = 50
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 670
|  attack = 422
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 43
|  energy = 294
|  exp = 140960
|  grade = 39
|  hp = 5217
|  kp_sdk_info = {
|  |  create_time = 1684507882
|  |  upgrade_time = 1684887181
|  }
|  max_hp = 5304
|  power = 1599
|  res_abnormal_ratio = 670
|  res_critical_ratio = 500
|  skill_point = 56
|  speed = 753
|  trapmine_point = 50
}
[logic/platform/CSdkCtrl.lua:380]:sdk upload: not init
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Lilian"
|  |  [2] = "Open_Forge"
|  |  [3] = "Open_Equipfuben"
|  |  [4] = "Open_Arena"
|  |  [5] = "Open_MingLei"
|  |  [6] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/attr/CAttrCtrl"]:141: in function 'UpdateAttr'
	[string "net/netplayer"]:9: in function <[string "net/netplayer"]:5>
	[C]: in function 'xxpcall'
	[string "net/CNetCtrl"]:318: in function 'ProtoCall'
	[string "net/CNetCtrl"]:300: in function 'ProcessProto'
	[string "net/CNetCtrl"]:251: in function 'ProtoHandlerCheck'
	[string "net/CNetCtrl"]:262: in function 'Receive'
	[string "net/CNetCtrl"]:175: in function 'OnSocketEvent'
	[string "net/CNetCtrl"]:111: in function 'm_Callback'
	[string "net/CTcpClient"]:43: in function <[string "net/CTcpClient"]:37>
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#w8#G50#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2849
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2849
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 27
|  |  id = 10101
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 27
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 27
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 27
|  |  id = 10301
|  }
}
[core/global.lua:59]:<color=#ffeb04>找到新的引导类型 Open_Lilian</color>
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 27
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 27
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 27
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2879
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2879
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 28
|  |  id = 10101
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 28
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 28
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 28
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 28
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 28
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 28
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2899
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2899
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 29
|  |  id = 10101
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 29
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 29
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 29
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 29
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 29
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 29
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2928
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2928
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenInfo = {
|  cur_plan = 1
|  fuwen = {
|  |  [1] = {
|  |  |  fuwen_attr = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  quality = 1
|  |  |  |  |  value = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  quality = 1
|  |  |  |  |  value = 3
|  |  |  |  }
|  |  |  }
|  |  |  plan = 1
|  |  }
|  }
|  itemid = 2
}
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenPlanName = {
|  fuwen_name = {
|  |  [1] = {
|  |  |  name = "默认方案"
|  |  |  plan = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenInfo = {
|  cur_plan = 1
|  fuwen = {
|  |  [1] = {
|  |  |  fuwen_attr = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  quality = 1
|  |  |  |  |  value = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  quality = 1
|  |  |  |  |  value = 17
|  |  |  |  }
|  |  |  }
|  |  |  plan = 1
|  |  }
|  }
|  itemid = 3
}
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenPlanName = {
|  fuwen_name = {
|  |  [1] = {
|  |  |  name = "默认方案"
|  |  |  plan = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenInfo = {
|  cur_plan = 1
|  fuwen = {
|  |  [1] = {
|  |  |  fuwen_attr = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  quality = 1
|  |  |  |  |  value = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  quality = 1
|  |  |  |  |  value = 17
|  |  |  |  }
|  |  |  }
|  |  |  plan = 1
|  |  }
|  }
|  itemid = 4
}
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenPlanName = {
|  fuwen_name = {
|  |  [1] = {
|  |  |  name = "默认方案"
|  |  |  plan = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenInfo = {
|  cur_plan = 1
|  fuwen = {
|  |  [1] = {
|  |  |  fuwen_attr = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "speed"
|  |  |  |  |  quality = 1
|  |  |  |  |  value = 7
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  quality = 1
|  |  |  |  |  value = 3
|  |  |  |  }
|  |  |  }
|  |  |  plan = 1
|  |  }
|  }
|  itemid = 5
}
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenPlanName = {
|  fuwen_name = {
|  |  [1] = {
|  |  |  name = "默认方案"
|  |  |  plan = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenInfo = {
|  cur_plan = 1
|  fuwen = {
|  |  [1] = {
|  |  |  fuwen_attr = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  quality = 1
|  |  |  |  |  value = 17
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  quality = 1
|  |  |  |  |  value = 3
|  |  |  |  }
|  |  |  }
|  |  |  plan = 1
|  |  }
|  }
|  itemid = 6
}
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenPlanName = {
|  fuwen_name = {
|  |  [1] = {
|  |  |  name = "默认方案"
|  |  |  plan = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenInfo = {
|  cur_plan = 1
|  fuwen = {
|  |  [1] = {
|  |  |  fuwen_attr = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "speed"
|  |  |  |  |  quality = 1
|  |  |  |  |  value = 7
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  quality = 1
|  |  |  |  |  value = 3
|  |  |  |  }
|  |  |  }
|  |  |  plan = 1
|  |  }
|  }
|  itemid = 7
}
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenPlanName = {
|  fuwen_name = {
|  |  [1] = {
|  |  |  name = "默认方案"
|  |  |  plan = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveDone = {
|  id = 10103
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {
|  infolist = {
|  |  [1] = {
|  |  |  blist = {
|  |  |  |  [1] = 1
|  |  |  |  [2] = 3
|  |  |  }
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  blist = {
|  |  |  |  [1] = 8
|  |  |  }
|  |  |  id = 4
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayRedDot = {
|  days = {
|  |  [1] = 1
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 30
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 30
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 30
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 30
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 30
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 30
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2949
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2949
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 31
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 31
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 31
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 31
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 31
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 31
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2978
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2978
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 32
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 32
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 32
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 32
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 32
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 32
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2999
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2999
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 33
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 33
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 33
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 33
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 33
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 33
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3019
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3019
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 34
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 34
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 34
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 34
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 34
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 34
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3048
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3048
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 35
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 35
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 35
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 35
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 35
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 35
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3068
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3068
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 36
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 36
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 36
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 36
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 36
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 36
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3097
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3097
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 37
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 37
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 37
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 37
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 37
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 37
|  |  id = 10601
|  }
}
[logic/ui/CViewCtrl.lua:94]:CGuideView ShowView
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Lilian"
|  |  [2] = "Open_Forge"
|  |  [3] = "Open_Equipfuben"
|  |  [4] = "Open_Arena"
|  |  [5] = "Open_MingLei"
|  |  [6] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/mainmenu/CMainMenuExpandBox"]:280: in function <[string "logic/mainmenu/CMainMenuExpandBox"]:251>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3117
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3117
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 38
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 38
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 38
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 38
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 38
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 38
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3146
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3146
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 39
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 39
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 39
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 39
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 39
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 39
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3165
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3165
|  |  id = 10602
|  }
}
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Lilian"
|  |  [2] = "Open_Forge"
|  |  [3] = "Open_Equipfuben"
|  |  [4] = "Open_Arena"
|  |  [5] = "Open_MingLei"
|  |  [6] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/task/CTaskCtrl"]:2434: in function 'StartAutoDoingShiMen'
	[string "logic/guide/CGuideCtrl"]:2383: in function 'StopActionWhenGuide'
	[string "logic/guide/CGuideView"]:31: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewBase.lua:125]:CGuideView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 Open_Lilian_1 1</color>
[logic/model/CHero.lua:65]:same sync pos
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Lilian"
|  |  [2] = "Open_Forge"
|  |  [3] = "Open_Equipfuben"
|  |  [4] = "Open_Arena"
|  |  [5] = "Open_MingLei"
|  |  [6] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewBase.lua:125]:CGradeGiftView LoadDone!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Lilian"
|  |  [2] = "Open_Forge"
|  |  [3] = "Open_Equipfuben"
|  |  [4] = "Open_Arena"
|  |  [5] = "Open_MingLei"
|  |  [6] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Lilian"
|  |  [2] = "Open_Forge"
|  |  [3] = "Open_Equipfuben"
|  |  [4] = "Open_Arena"
|  |  [5] = "Open_MingLei"
|  |  [6] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewCtrl.lua:104]:CGuideView     CloseView
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewBase.lua:125]:CGuideMaskView LoadDone!
[core/table.lua:94]:Table = {
|  [1] = "16"
|  [2] = "20"
}
[core/global.lua:59]:<color=#ffeb04>39 16 20</color>
[core/table.lua:94]:Table = {
|  [1] = "21"
|  [2] = "24"
}
[core/global.lua:59]:<color=#ffeb04>39 21 24</color>
[core/table.lua:94]:Table = {
|  [1] = "25"
|  [2] = "27"
}
[core/global.lua:59]:<color=#ffeb04>39 25 27</color>
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[logic/ui/CViewCtrl.lua:104]:CGradeGiftView     CloseView
[core/global.lua:59]:<color=#ffeb04>找到新的引导类型 Open_Lilian</color>
[logic/ui/CViewCtrl.lua:94]:CGuideView ShowView
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Lilian"
|  |  [2] = "Open_Forge"
|  |  [3] = "Open_Equipfuben"
|  |  [4] = "Open_Arena"
|  |  [5] = "Open_MingLei"
|  |  [6] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Lilian"
|  |  [2] = "Open_Forge"
|  |  [3] = "Open_Equipfuben"
|  |  [4] = "Open_Arena"
|  |  [5] = "Open_MingLei"
|  |  [6] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/task/CTaskCtrl"]:2434: in function 'StartAutoDoingShiMen'
	[string "logic/guide/CGuideCtrl"]:2383: in function 'StopActionWhenGuide'
	[string "logic/guide/CGuideView"]:31: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewBase.lua:125]:CGuideView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 Open_Lilian_1 1</color>
[logic/model/CHero.lua:65]:same sync pos
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Lilian"
|  |  [2] = "Open_Forge"
|  |  [3] = "Open_Equipfuben"
|  |  [4] = "Open_Arena"
|  |  [5] = "Open_MingLei"
|  |  [6] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Lilian"
|  |  [2] = "Open_Forge"
|  |  [3] = "Open_Equipfuben"
|  |  [4] = "Open_Arena"
|  |  [5] = "Open_MingLei"
|  |  [6] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887184
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:13:04
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>引导结束 Open_Lilian Open_Lilian_1 1</color>
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_Lilian_1"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1001101
|  }
}
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 Open_Lilian_2 2</color>
[logic/ui/CViewBase.lua:131]:CGuideMaskView LoadDone, not in loadingview!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Lilian"
|  |  [2] = "Open_Forge"
|  |  [3] = "Open_Equipfuben"
|  |  [4] = "Open_Arena"
|  |  [5] = "Open_MingLei"
|  |  [6] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>引导结束 Open_Lilian Open_Lilian_2 2</color>
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_Lilian_2"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1001102
|  }
}
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Tips_Lilian_0"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 5001100
|  }
}
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewCtrl.lua:104]:CGuideView     CloseView
[logic/guide/CGuideCtrl.lua:333]:已完成引导Open_Lilian
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_Lilian"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1011099
|  }
}
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Forge"
|  |  [2] = "Open_Equipfuben"
|  |  [3] = "Open_Arena"
|  |  [4] = "Open_MingLei"
|  |  [5] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/guide/CGuideCtrl"]:338: in function 'Continue'
	[string "logic/guide/CGuideView"]:439: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[core/global.lua:59]:<color=#ffeb04>找到新的引导类型 Open_Forge</color>
[logic/ui/CViewCtrl.lua:94]:CGuideView ShowView
[logic/ui/CViewBase.lua:125]:CGuideMaskView LoadDone!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Forge"
|  |  [2] = "Open_Equipfuben"
|  |  [3] = "Open_Arena"
|  |  [4] = "Open_MingLei"
|  |  [5] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Forge"
|  |  [2] = "Open_Equipfuben"
|  |  [3] = "Open_Arena"
|  |  [4] = "Open_MingLei"
|  |  [5] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/task/CTaskCtrl"]:2434: in function 'StartAutoDoingShiMen'
	[string "logic/guide/CGuideCtrl"]:2383: in function 'StopActionWhenGuide'
	[string "logic/guide/CGuideView"]:31: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewBase.lua:125]:CGuideView LoadDone!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Forge"
|  |  [2] = "Open_Equipfuben"
|  |  [3] = "Open_Arena"
|  |  [4] = "Open_MingLei"
|  |  [5] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 Open_Forge_1 1</color>
[logic/model/CHero.lua:65]:same sync pos
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Forge"
|  |  [2] = "Open_Equipfuben"
|  |  [3] = "Open_Arena"
|  |  [4] = "Open_MingLei"
|  |  [5] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Forge"
|  |  [2] = "Open_Equipfuben"
|  |  [3] = "Open_Arena"
|  |  [4] = "Open_MingLei"
|  |  [5] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>引导结束 Open_Forge Open_Forge_1 1</color>
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewCtrl.lua:104]:CGuideView     CloseView
[logic/guide/CGuideCtrl.lua:333]:已完成引导Open_Forge
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_Forge"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1013099
|  }
}
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Equipfuben"
|  |  [2] = "Open_Arena"
|  |  [3] = "Open_MingLei"
|  |  [4] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/guide/CGuideCtrl"]:338: in function 'Continue'
	[string "logic/guide/CGuideOpenBox"]:45: in function <[string "logic/guide/CGuideOpenBox"]:38>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>找到新的引导类型 Open_Equipfuben</color>
[logic/ui/CViewBase.lua:125]:CGuideMaskView LoadDone!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Equipfuben"
|  |  [2] = "Open_Arena"
|  |  [3] = "Open_MingLei"
|  |  [4] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewCtrl.lua:94]:CGuideView ShowView
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Equipfuben"
|  |  [2] = "Open_Arena"
|  |  [3] = "Open_MingLei"
|  |  [4] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Equipfuben"
|  |  [2] = "Open_Arena"
|  |  [3] = "Open_MingLei"
|  |  [4] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/task/CTaskCtrl"]:2434: in function 'StartAutoDoingShiMen'
	[string "logic/guide/CGuideCtrl"]:2383: in function 'StopActionWhenGuide'
	[string "logic/guide/CGuideView"]:31: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewBase.lua:125]:CGuideView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 Open_Equipfuben_1 1</color>
[logic/model/CHero.lua:65]:same sync pos
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Equipfuben"
|  |  [2] = "Open_Arena"
|  |  [3] = "Open_MingLei"
|  |  [4] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Equipfuben"
|  |  [2] = "Open_Arena"
|  |  [3] = "Open_MingLei"
|  |  [4] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887194
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:13:14
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>引导结束 Open_Equipfuben Open_Equipfuben_1 1</color>
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_Equipfuben_1"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1001401
|  }
}
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewCtrl.lua:104]:CGuideView     CloseView
[logic/guide/CGuideCtrl.lua:333]:已完成引导Open_Equipfuben
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_Equipfuben"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1014099
|  }
}
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Arena"
|  |  [2] = "Open_MingLei"
|  |  [3] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/guide/CGuideCtrl"]:338: in function 'Continue'
	[string "logic/guide/CGuideOpenBox"]:45: in function <[string "logic/guide/CGuideOpenBox"]:38>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>找到新的引导类型 Open_Arena</color>
[logic/ui/CViewBase.lua:125]:CGuideMaskView LoadDone!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Arena"
|  |  [2] = "Open_MingLei"
|  |  [3] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewCtrl.lua:94]:CGuideView ShowView
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Arena"
|  |  [2] = "Open_MingLei"
|  |  [3] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Arena"
|  |  [2] = "Open_MingLei"
|  |  [3] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/task/CTaskCtrl"]:2434: in function 'StartAutoDoingShiMen'
	[string "logic/guide/CGuideCtrl"]:2383: in function 'StopActionWhenGuide'
	[string "logic/guide/CGuideView"]:31: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewBase.lua:125]:CGuideView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 Open_Arena_1 1</color>
[logic/model/CHero.lua:65]:same sync pos
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Arena"
|  |  [2] = "Open_MingLei"
|  |  [3] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Arena"
|  |  [2] = "Open_MingLei"
|  |  [3] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10122</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
|  [2] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [3] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10122</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小叶 1509 [19.3,20.4,0] -58 false</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小佳 1510 [18.3,20,0] -22.7 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 之前忙着做兼职，果然这次考试成绩不好。</color>
[core/global.lua:59]:<color=#ffeb04>引导结束 Open_Arena Open_Arena_1 1</color>
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_Arena_1"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1001501
|  }
}
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Tips_ArneaClub_0"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 5000900
|  }
}
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewCtrl.lua:104]:CGuideView     CloseView
[logic/guide/CGuideCtrl.lua:333]:已完成引导Open_Arena
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_Arena"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1015099
|  }
}
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_MingLei"
|  |  [2] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/guide/CGuideCtrl"]:338: in function 'Continue'
	[string "logic/guide/CGuideOpenBox"]:45: in function <[string "logic/guide/CGuideOpenBox"]:38>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>找到新的引导类型 Open_MingLei</color>
[logic/ui/CViewBase.lua:125]:CGuideMaskView LoadDone!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_MingLei"
|  |  [2] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewCtrl.lua:94]:CGuideView ShowView
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_MingLei"
|  |  [2] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_MingLei"
|  |  [2] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/task/CTaskCtrl"]:2434: in function 'StartAutoDoingShiMen'
	[string "logic/guide/CGuideCtrl"]:2383: in function 'StopActionWhenGuide'
	[string "logic/guide/CGuideView"]:31: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewBase.lua:125]:CGuideView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 Open_MingLei_1 1</color>
[logic/model/CHero.lua:65]:same sync pos
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_MingLei"
|  |  [2] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_MingLei"
|  |  [2] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 哇！你还是一如既往的厉害，年级前10耶！</color>
[core/global.lua:59]:<color=#ffeb04>引导结束 Open_MingLei Open_MingLei_1 1</color>
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_MingLei_1"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1001601
|  }
}
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewCtrl.lua:104]:CGuideView     CloseView
[logic/guide/CGuideCtrl.lua:333]:已完成引导Open_MingLei
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_MingLei"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1016099
|  }
}
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/guide/CGuideCtrl"]:338: in function 'Continue'
	[string "logic/guide/CGuideOpenBox"]:45: in function <[string "logic/guide/CGuideOpenBox"]:38>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>找到新的引导类型 Open_Trapmine</color>
[logic/ui/CViewBase.lua:125]:CGuideMaskView LoadDone!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewCtrl.lua:94]:CGuideView ShowView
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/task/CTaskCtrl"]:2434: in function 'StartAutoDoingShiMen'
	[string "logic/guide/CGuideCtrl"]:2383: in function 'StopActionWhenGuide'
	[string "logic/guide/CGuideView"]:31: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewBase.lua:125]:CGuideView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 Open_Trapmine_1 1</color>
[logic/model/CHero.lua:65]:same sync pos
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Trapmine"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887204
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:13:24
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  2 mihu true</color>
[core/global.lua:59]:<color=#ffeb04>引导结束 Open_Trapmine Open_Trapmine_1 1</color>
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_Trapmine_1"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1001701
|  }
}
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewCtrl.lua:104]:CGuideView     CloseView
[logic/guide/CGuideCtrl.lua:333]:已完成引导Open_Trapmine
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_Trapmine"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1017099
|  }
}
[core/global.lua:59]:<color=#ffeb04>停止引导检查,并且下一帧再检测引导</color>
[logic/ui/CViewBase.lua:125]:CGuideMaskView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 是吗？我还没有找到我排名…</color>
[logic/ui/CViewCtrl.lua:104]:CGmView     CloseView
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  1 -155</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 你看错了，那是倒数排名。</color>
[core/table.lua:94]:<--Net Send: other.C2GSGMCmd = {
|  cmd = "rewardexp 10001 100000"
}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  day_task = {
|  |  [1] = 1016
|  |  [10] = 1003
|  |  [11] = 1020
|  |  [12] = 1006
|  |  [13] = 2001
|  |  [14] = 1007
|  |  [15] = 1022
|  |  [2] = 3005
|  |  [3] = 1017
|  |  [4] = 3008
|  |  [5] = 3009
|  |  [6] = 1011
|  |  [7] = 1001
|  |  [8] = 1024
|  |  [9] = 2003
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  mask = "80000000"
|  |  soul_type = 9
|  }
|  partnerid = 1
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  soul_type = 9
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveDone = {
|  id = 10104
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  mask = "80000000"
|  |  soul_type = 2
|  }
|  partnerid = 2
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  soul_type = 2
}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  day_task = {
|  |  [1] = 1016
|  |  [10] = 1003
|  |  [11] = 1020
|  |  [12] = 1006
|  |  [13] = 2001
|  |  [14] = 1007
|  |  [15] = 1022
|  |  [16] = 1005
|  |  [2] = 3005
|  |  [3] = 1017
|  |  [4] = 3008
|  |  [5] = 3009
|  |  [6] = 1011
|  |  [7] = 1001
|  |  [8] = 1024
|  |  [9] = 2003
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {
|  infolist = {
|  |  [1] = {
|  |  |  blist = {
|  |  |  |  [1] = 1
|  |  |  |  [2] = 3
|  |  |  }
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  blist = {
|  |  |  |  [1] = 8
|  |  |  }
|  |  |  id = 4
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 40
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 40
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 40
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 40
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 40
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 40
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得#exp#G120000#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#exp#G120000#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  day_task = {
|  |  [1] = 1016
|  |  [10] = 1003
|  |  [11] = 1020
|  |  [12] = 1006
|  |  [13] = 2001
|  |  [14] = 1007
|  |  [15] = 1022
|  |  [16] = 1005
|  |  [17] = 1018
|  |  [2] = 3005
|  |  [3] = 1017
|  |  [4] = 3008
|  |  [5] = 3009
|  |  [6] = 1011
|  |  [7] = 1001
|  |  [8] = 1024
|  |  [9] = 2003
|  }
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3219
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3219
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "指令执行成功"
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  abnormal_attr_ratio = 710
|  |  attack = 452
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 43
|  |  energy = 306
|  |  exp = 260960
|  |  grade = 43
|  |  hp = 5874
|  |  kp_sdk_info = {
|  |  |  create_time = 1684507882
|  |  |  upgrade_time = 1684887213
|  |  }
|  |  mask = "10020000287ff42"
|  |  max_hp = 6069
|  |  power = 1814
|  |  res_abnormal_ratio = 710
|  |  res_critical_ratio = 500
|  |  skill_point = 66
|  |  speed = 753
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 710
|  attack = 452
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 43
|  energy = 306
|  exp = 260960
|  grade = 43
|  hp = 5874
|  kp_sdk_info = {
|  |  create_time = 1684507882
|  |  upgrade_time = 1684887213
|  }
|  max_hp = 6069
|  power = 1814
|  res_abnormal_ratio = 710
|  res_critical_ratio = 500
|  skill_point = 66
|  speed = 753
}
[logic/platform/CSdkCtrl.lua:380]:sdk upload: not init
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Pefuben"
|  |  [2] = "Open_Convoy"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/attr/CAttrCtrl"]:141: in function 'UpdateAttr'
	[string "net/netplayer"]:9: in function <[string "net/netplayer"]:5>
	[C]: in function 'xxpcall'
	[string "net/CNetCtrl"]:318: in function 'ProtoCall'
	[string "net/CNetCtrl"]:300: in function 'ProcessProto'
	[string "net/CNetCtrl"]:251: in function 'ProtoHandlerCheck'
	[string "net/CNetCtrl"]:262: in function 'Receive'
	[string "net/CNetCtrl"]:175: in function 'OnSocketEvent'
	[string "net/CNetCtrl"]:111: in function 'm_Callback'
	[string "net/CTcpClient"]:43: in function <[string "net/CTcpClient"]:37>
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 41
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 41
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 41
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 41
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 41
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 41
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3273
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3273
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 42
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 42
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 42
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 42
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 42
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 42
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3326
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3326
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 43
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 43
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 43
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 43
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 43
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 43
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3380
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3380
|  |  id = 10602
|  }
}
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Tips_HuntPartnerSoulView_0"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 5000800
|  }
}
[core/global.lua:59]:<color=#ffeb04>找到新的引导类型 Open_Pefuben</color>
[logic/ui/CViewCtrl.lua:94]:CGuideView ShowView
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Pefuben"
|  |  [2] = "Open_Convoy"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/task/CTaskCtrl"]:2434: in function 'StartAutoDoingShiMen'
	[string "logic/guide/CGuideCtrl"]:2383: in function 'StopActionWhenGuide'
	[string "logic/guide/CGuideView"]:31: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewBase.lua:125]:CGuideView LoadDone!
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  2 37</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 不对啊我还看到班长名字了。</color>
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 Open_Pefuben_1 1</color>
[logic/model/CHero.lua:65]:same sync pos
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Pefuben"
|  |  [2] = "Open_Convoy"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Pefuben"
|  |  [2] = "Open_Convoy"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3380
|  |  id = 10602
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:Table = {
|  [1] = "16"
|  [2] = "20"
}
[core/global.lua:59]:<color=#ffeb04>43 16 20</color>
[core/table.lua:94]:Table = {
|  [1] = "21"
|  [2] = "24"
}
[core/global.lua:59]:<color=#ffeb04>43 21 24</color>
[core/table.lua:94]:Table = {
|  [1] = "25"
|  [2] = "27"
}
[core/global.lua:59]:<color=#ffeb04>43 25 27</color>
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887214
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:13:34
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 wuyu2 true</color>
[core/global.lua:59]:<color=#ffeb04>引导结束 Open_Pefuben Open_Pefuben_1 1</color>
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_Pefuben_1"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1001801
|  }
}
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewCtrl.lua:104]:CGuideView     CloseView
[logic/guide/CGuideCtrl.lua:333]:已完成引导Open_Pefuben
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_Pefuben"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1018099
|  }
}
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Convoy"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/guide/CGuideCtrl"]:338: in function 'Continue'
	[string "logic/guide/CGuideOpenBox"]:45: in function <[string "logic/guide/CGuideOpenBox"]:38>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>找到新的引导类型 Open_Convoy</color>
[logic/ui/CViewBase.lua:125]:CGuideMaskView LoadDone!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Convoy"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewCtrl.lua:94]:CGuideView ShowView
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Convoy"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Convoy"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/task/CTaskCtrl"]:2434: in function 'StartAutoDoingShiMen'
	[string "logic/guide/CGuideCtrl"]:2383: in function 'StopActionWhenGuide'
	[string "logic/guide/CGuideView"]:31: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:300: in function <[string "logic/base/CResCtrl"]:297>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[logic/ui/CViewBase.lua:125]:CGuideView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 Open_Convoy_1 1</color>
[logic/model/CHero.lua:65]:same sync pos
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Convoy"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Convoy"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:27: in function <[string "main"]:24>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 听说，班长在考试前被没收了漫画。</color>
[core/global.lua:59]:<color=#ffeb04>引导结束 Open_Convoy Open_Convoy_1 1</color>
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_Convoy_1"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1001901
|  }
}
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewCtrl.lua:104]:CGuideView     CloseView
[logic/guide/CGuideCtrl.lua:333]:已完成引导Open_Convoy
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_Convoy"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1019099
|  }
}
[core/global.lua:59]:<color=#ffeb04>停止引导检查,并且下一帧再检测引导</color>
[logic/ui/CViewBase.lua:125]:CGuideMaskView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  2 zhenjing true</color>
[logic/ui/CViewCtrl.lua:104]:CGmView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887224
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:13:44
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 那她一定因为伤心过度所以考砸了。</color>
[logic/ui/CViewCtrl.lua:94]:CItemBagMainView ShowView
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CViewBase.lua:125]:CItemBagMainView LoadDone!
[logic/ui/CViewCtrl.lua:94]:CItemTipsBaseInfoView ShowView
[logic/ui/CViewBase.lua:125]:CItemTipsBaseInfoView LoadDone!
[logic/ui/CViewCtrl.lua:94]:CItemPartnerEquipSoulSelectView ShowView
[logic/ui/CViewBase.lua:125]:CItemPartnerEquipSoulSelectView LoadDone!
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  2 43</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 说起来考试前我看了一本小说，推荐哦！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好看吗，借我啦！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887234
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:13:54
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  2 [12.3,25.5,0] 90</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [11.6,25,0] 90</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 不要啦，人家还没看完呢！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 别小气嘛！</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  2 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887244
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:14:04
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887254
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:14:14
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887264
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:14:24
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CItemPartnerEquipSoulSelectView     CloseView
[logic/ui/CViewCtrl.lua:104]:CItemTipsBaseInfoView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemTipsBaseInfoView ShowView
[logic/ui/CViewBase.lua:125]:CItemTipsBaseInfoView LoadDone!
[logic/ui/CViewCtrl.lua:94]:CItemPartnertSelectPackageView ShowView
[logic/ui/CViewBase.lua:125]:CItemPartnertSelectPackageView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemTipsBaseInfoView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887274
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:14:34
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 5003
}
[core/table.lua:94]:<--Net Send: item.C2GSChooseItem = {
|  amount = 1
|  itemid = 10
|  itemsids = {
|  |  [1] = "1010(partner=316)"
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemPartnertSelectPackageView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 5003
}
[core/table.lua:94]:-->Net Receive: item.GS2CItemAmount = {
|  amount = 3
|  create_time = 1684887107
|  id = 10
}
[core/table.lua:94]:-->Net Receive: partner.GS2CAddPartnerList = {
|  partner_list = {
|  |  [1] = {
|  |  |  abnormal_attr_ratio = 600
|  |  |  amount = 1
|  |  |  attack = 281
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 1000
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 57
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  grade = 1
|  |  |  mask = "fffffefe"
|  |  |  max_hp = 2156
|  |  |  model_info = {
|  |  |  |  shape = 316
|  |  |  |  skin = 203160
|  |  |  }
|  |  |  name = "流褐"
|  |  |  parid = 3
|  |  |  partner_type = 316
|  |  |  patahp = 2156
|  |  |  power = 830
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 400
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31601
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31602
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31603
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 2
|  |  |  speed = 105
|  |  |  star = 2
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CShowNewPartnerUI = {
|  par_types = {
|  |  [1] = {
|  |  |  desc = "恭喜你获得伙伴：流褐"
|  |  |  par_type = 316
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:94]:CPartnerGainView2 ShowView
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  sid = 316
|  |  |  virtual = 1010
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshPartnerTask = {
|  partnertask_progress = {
|  |  [1] = {
|  |  |  parid = 316
|  |  |  status = 1
|  |  |  taskid = 62139
|  |  }
|  }
|  refresh_id = 1
}
[core/table.lua:94]:-->Net Receive: handbook.GS2CHandBookRedPoint = {
|  red_point = {
|  |  book_type = 1
|  |  red_point = 1
|  }
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得[#P流褐#n]"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: handbook.GS2CBookInfoChange = {
|  book_info = {
|  |  chapter = {
|  |  |  [1] = {
|  |  |  |  id = 313161
|  |  |  }
|  |  }
|  |  condition = {
|  |  |  [1] = 113161
|  |  }
|  |  entry_name = 1
|  |  id = 100316
|  |  red_point = 1
|  |  repair = 1
|  |  show = 1
|  |  unlock = 1
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CPictureDegree = {
|  info = {
|  |  cur = {
|  |  |  [1] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 316
|  |  |  |  value = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 313
|  |  |  |  value = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 313
|  |  |  |  value = 1
|  |  |  }
|  |  |  [4] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 313
|  |  |  |  value = 1
|  |  |  }
|  |  }
|  |  id = 3
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CPictureDegree = {
|  info = {
|  |  cur = {
|  |  |  [1] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 316
|  |  |  |  value = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 313
|  |  |  |  value = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 316
|  |  |  |  value = 1
|  |  |  }
|  |  |  [4] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 313
|  |  |  |  value = 1
|  |  |  }
|  |  |  [5] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 313
|  |  |  |  value = 1
|  |  |  }
|  |  }
|  |  id = 3
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 5
|  |  id = 10702
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CPictureDegree = {
|  info = {
|  |  cur = {
|  |  |  [1] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 316
|  |  |  |  value = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 313
|  |  |  |  value = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 316
|  |  |  |  value = 1
|  |  |  }
|  |  |  [4] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 313
|  |  |  |  value = 1
|  |  |  }
|  |  |  [5] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 316
|  |  |  |  value = 1
|  |  |  }
|  |  |  [6] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 313
|  |  |  |  value = 1
|  |  |  }
|  |  }
|  |  id = 3
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveDone = {
|  id = 40302
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {
|  infolist = {
|  |  [1] = {
|  |  |  blist = {
|  |  |  |  [1] = 1
|  |  |  |  [2] = 3
|  |  |  }
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  blist = {
|  |  |  |  [1] = 8
|  |  |  }
|  |  |  id = 4
|  |  }
|  }
}
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[logic/ui/CViewBase.lua:125]:CPartnerGainView2 LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887284
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:14:44
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CPartnerGainView2     CloseView
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887294
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:14:54
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10122</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
|  [2] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [3] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10122</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小叶 1509 [19.3,20.4,0] -58 false</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小佳 1510 [18.3,20,0] -22.7 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 之前忙着做兼职，果然这次考试成绩不好。</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887304
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:15:04
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 哇！你还是一如既往的厉害，年级前10耶！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  2 mihu true</color>
[logic/ui/CViewCtrl.lua:94]:CItemTipsBaseInfoView ShowView
[logic/ui/CViewBase.lua:125]:CItemTipsBaseInfoView LoadDone!
[logic/ui/CViewCtrl.lua:94]:CItemPartnerEquipSoulSelectView ShowView
[logic/ui/CViewBase.lua:125]:CItemPartnerEquipSoulSelectView LoadDone!
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 是吗？我还没有找到我排名…</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  1 -155</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 你看错了，那是倒数排名。</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887314
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:15:14
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  2 37</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 不对啊我还看到班长名字了。</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 wuyu2 true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 听说，班长在考试前被没收了漫画。</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887324
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:15:24
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  2 zhenjing true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 那她一定因为伤心过度所以考砸了。</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  2 43</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 说起来考试前我看了一本小说，推荐哦！</color>
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 4210
|  |  id = 10602
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887334
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:15:34
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好看吗，借我啦！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  2 [12.3,25.5,0] 90</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [11.6,25,0] 90</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 不要啦，人家还没看完呢！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 别小气嘛！</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  2 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887344
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:15:44
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887354
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:15:54
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887364
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:16:04
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887374
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:16:14
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887384
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:16:24
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887394
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:16:34
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10122</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
|  [2] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [3] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10122</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小叶 1509 [19.3,20.4,0] -58 false</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小佳 1510 [18.3,20,0] -22.7 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887404
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:16:44
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 之前忙着做兼职，果然这次考试成绩不好。</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 哇！你还是一如既往的厉害，年级前10耶！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  2 mihu true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 是吗？我还没有找到我排名…</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887414
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:16:54
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  1 -155</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 你看错了，那是倒数排名。</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  2 37</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 不对啊我还看到班长名字了。</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 wuyu2 true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887424
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:17:04
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 听说，班长在考试前被没收了漫画。</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  2 zhenjing true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 那她一定因为伤心过度所以考砸了。</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  2 43</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 说起来考试前我看了一本小说，推荐哦！</color>
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887434
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:17:14
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好看吗，借我啦！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  2 [12.3,25.5,0] 90</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [11.6,25,0] 90</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 不要啦，人家还没看完呢！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 别小气嘛！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887444
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:17:24
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  2 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887454
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:17:34
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887464
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:17:44
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887474
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:17:54
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887484
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:18:04
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887495
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:18:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887505
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:18:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10122</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
|  [2] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [3] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10122</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小叶 1509 [19.3,20.4,0] -58 false</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小佳 1510 [18.3,20,0] -22.7 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 之前忙着做兼职，果然这次考试成绩不好。</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 哇！你还是一如既往的厉害，年级前10耶！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  2 mihu true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887515
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:18:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 是吗？我还没有找到我排名…</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  1 -155</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 你看错了，那是倒数排名。</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  2 37</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 不对啊我还看到班长名字了。</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887525
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:18:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 wuyu2 true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 听说，班长在考试前被没收了漫画。</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  2 zhenjing true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 那她一定因为伤心过度所以考砸了。</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887535
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:18:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  2 43</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 说起来考试前我看了一本小说，推荐哦！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好看吗，借我啦！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  2 [12.3,25.5,0] 90</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [11.6,25,0] 90</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 不要啦，人家还没看完呢！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 别小气嘛！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887545
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:19:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  2 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887555
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:19:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887565
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:19:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887575
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:19:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887585
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:19:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887595
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:19:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887605
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:20:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10122</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
|  [2] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [3] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10122</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小叶 1509 [19.3,20.4,0] -58 false</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小佳 1510 [18.3,20,0] -22.7 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 之前忙着做兼职，果然这次考试成绩不好。</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 哇！你还是一如既往的厉害，年级前10耶！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887615
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:20:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  2 mihu true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 是吗？我还没有找到我排名…</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  1 -155</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 你看错了，那是倒数排名。</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887625
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:20:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  2 37</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 不对啊我还看到班长名字了。</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 wuyu2 true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 听说，班长在考试前被没收了漫画。</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  2 zhenjing true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1684887635
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/05/24 08:20:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
