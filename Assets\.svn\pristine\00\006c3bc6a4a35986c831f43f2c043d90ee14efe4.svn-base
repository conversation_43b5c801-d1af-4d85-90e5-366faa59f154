with:884  hight:432
[logic/misc/CShareCtrl.lua:28]:jit    false    SSE3    SSE4.1    BMI2    fold    cse    dce    narrow    loop
[logic/login/CLoginCtrl.lua:5]:公告初始化...
[logic/base/CHttpCtrl.lua:32]:http get ->    https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials&client_secret=c15395c2433c4a904d22aa27eab8321b&client_id=rlU84dprH6I7sMjBkqM0gizl&
[logic/ui/CViewCtrl.lua:94]:CNotifyView ShowView
[logic/ui/CViewCtrl.lua:94]:CBottomView ShowView
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x65177f20"
|  json_result = true
|  timer = 16
}
[logic/audio/CSpeechCtrl.lua:72]:OnTokenGet err
[logic/base/CResCtrl.lua:199]:-->resource init done!!! 12
[logic/ui/CViewBase.lua:125]:CNotifyView LoadDone!
[logic/ui/CViewBase.lua:125]:CBottomView LoadDone!
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {}
[logic/ui/CViewCtrl.lua:94]:CLoginView ShowView
[logic/base/CHttpCtrl.lua:32]:http get ->    http://42.51.32.254:88/Note640603/note.json
[logic/login/CLoginAccountPage.lua:80]:SendToCenterServer:    http://42.51.32.254:88/Note640603/note.json    
[logic/ui/CViewBase.lua:125]:CLoginView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x6d2ec4c0"
|  json_result = true
|  timer = 60
}
[core/table.lua:94]:cb tResult-> = {
|  info = {
|  |  role_list = {}
|  |  server_info = {
|  |  |  groups = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 1
|  |  |  |  |  name = "1-10区"
|  |  |  |  }
|  |  |  }
|  |  |  infoList = {
|  |  |  |  [1] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "新服7天限时-大狂欢活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [10] = {
|  |  |  |  |  |  |  |  text = "[ff0000]传说伙伴碎片*100、梦觉书·零*20、梦觉书·低*20、梦觉书·高*20[-]"
|  |  |  |  |  |  |  |  title = "新区累计充值10000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [11] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "单日累计充值活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [12] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [13] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.活动为单日累计充值，高档位可领取低档位所有物品。
2.活动为单日累计充值，仅计算单日内玩家累计充值总额，次日重新计算。[-]"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [14] = {
|  |  |  |  |  |  |  |  text = "[ff0000]向客服申请[-]"
|  |  |  |  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [15] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备材料*10、金币*5w、熊猫蛋糕*5[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值100元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [16] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*10、金币铜币*10w、体力药水*1、鲜肉包*30[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值500元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [17] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*20、金币*20w、体力药水*2、焦糖包*60[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值1000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [18] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [19] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值5000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]新服前7天[-]"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [20] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*80[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值10000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [21] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值20000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [22] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "单笔充值活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [23] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [24] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.活动为单笔活动，仅可领取充值对应档位物品。
2.活动单日可多次领取。[-]"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [25] = {
|  |  |  |  |  |  |  |  text = "[ff0000]充值玩家向客服申请，次日发送给玩家CDK（节假日顺延）[-]"
|  |  |  |  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [26] = {
|  |  |  |  |  |  |  |  text = "[ff0000]灵魂钥匙*6、淬灵云晶*20、原石礼包*4、武器原石*1[-]"
|  |  |  |  |  |  |  |  title = "单笔68元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [27] = {
|  |  |  |  |  |  |  |  text = "[ff0000]灵魂钥匙*12、淬灵云晶*40、原石礼包*8、武器原石*2[-]"
|  |  |  |  |  |  |  |  title = "单笔128元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [28] = {
|  |  |  |  |  |  |  |  text = "[ff0000]灵魂钥匙*32、淬灵云晶*100、原石礼包*20、武器原石*5[-]"
|  |  |  |  |  |  |  |  title = "单笔328元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [29] = {
|  |  |  |  |  |  |  |  text = "[ff0000]灵魂钥匙*64、淬灵云晶*200、原石礼包*40、武器原石*10[-]"
|  |  |  |  |  |  |  |  title = "单笔648元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "[ff0000]单日累计充值达到对应档位，不重复高领低档，每个档位仅可领取一次奖励、请及时参加并申请领取对应档位。[-]"
|  |  |  |  |  |  |  |  title = "活动规则"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [30] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "永久累计充值活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [31] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [32] = {
|  |  |  |  |  |  |  |  text = "[ff0000]活动为永久累计充值，高档位可领取低档位所有物品。
每个档位奖励仅可领取一次[-]"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [33] = {
|  |  |  |  |  |  |  |  text = "[ff0000]向客服申请[-]"
|  |  |  |  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [34] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备材料*50、伙伴觉醒材料*50、金币*1w[-]"
|  |  |  |  |  |  |  |  title = "永久累计充值500元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [35] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片5、2星精英伙伴*10、3级符石礼袋*5、附魔材料*5、金币*5w[-]"
|  |  |  |  |  |  |  |  title = "永久累计充值1000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [36] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*10、2星精英伙伴*20、3级符石礼袋*10、附魔材料*10、金币*10w[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [37] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*30、3星传说伙伴*1、3级符石礼袋*30、附魔材料*30、金币*50w[-]"
|  |  |  |  |  |  |  |  title = "永久累计充值5000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [38] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*50、万能碎片*100、金币*100w、橙色可选御灵*10[-]"
|  |  |  |  |  |  |  |  title = "永久累充10000元礼包"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [39] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*100、万能碎片*200、金币*200w、橙色可选御灵*20[-]"
|  |  |  |  |  |  |  |  title = "永久累充20000元礼包"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "[ff0000]向客服申请[-]"
|  |  |  |  |  |  |  |  title = "发放时间方式："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [40] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*200、金币*500w、橙色可选御灵*50[-]"
|  |  |  |  |  |  |  |  title = "永久累充40000元礼包"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [41] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "单日累计充值货币返利1:10"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [42] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [43] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.本活动为阶梯式活动，当日累计充值的金额满足返利阶梯后，按照当日充值金额满足的最高档次进行发放，次日自动返利，无需申请
2.本活动为长期活动，与其他返利活动叠加。
充值100-1999元，返利50%钻石
充值2000-4999元，返利100%钻石
充值5000-9999元，返利150%钻石
充值10000-19999元，返利200%钻石
充值20000+元，返利250%钻石
以上返利会在第二天自动已邮件形式发送至玩家[-]"
|  |  |  |  |  |  |  |  title = "活动说明:"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [44] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "服务器冠名活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [45] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [46] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [47] = {
|  |  |  |  |  |  |  |  text = "[ff0000]全体玩家[-]"
|  |  |  |  |  |  |  |  title = "【活动对象】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [48] = {
|  |  |  |  |  |  |  |  text = "[ff0000]我的服务器，我为自己代言。[-]"
|  |  |  |  |  |  |  |  title = "【活动宣言】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [49] = {
|  |  |  |  |  |  |  |  text = "[ff0000]区服冠名权*1[-]"
|  |  |  |  |  |  |  |  title = "【冠名奖励】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [5] = {
|  |  |  |  |  |  |  |  text = "[ff0000]传说伙伴碎片*5、3级宝石礼包*2、鲜肉包*50、2星精英伙伴*1[-]"
|  |  |  |  |  |  |  |  title = "新区累计充值500元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [50] = {
|  |  |  |  |  |  |  |  text = "  [ff0000]亲爱的玩家：
  也许你在游戏里已经称霸全区，但是出了服务器，出了游戏，还有谁认识你现在就给你一个机会，获得至高无上的荣誉，让更多的人知道你的强大。特此举行“我的服务器，我为自己代言”，服务器玩家冠名活动。
即日起，只要满足以下条件，即可获得服务器冠名权：
  1）开服前3天累计充值满10000元
  2）开服前7日累计充值满20000元，且区服处于未冠名状态，获得服务器冠名权
  备注：本活动遵循先到先得原则。如出现多个满足条件的，冠名资格由第一个满足条件的获得。[-]"
|  |  |  |  |  |  |  |  title = "【活动内容】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [51] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1、活动时间内有玩家满足条件后活动结束，活动期间没有玩家满足条件则保持原服务器名称。
2、服务器冠名长度为4个字。（必须遵守相关法律法规，不得使用负面或影响社会风气，不得出现敏感词的名称）
3、由于冠名操作需要时间，工作人员会在7个工作日内，完成冠名操作。
4，每个服，冠名只有一次。被冠名的服务器不予再次冠名[-]"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [52] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "单日大额充值活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [53] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [54] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.活动为单日累计充值，仅计算单日内玩家累计充值总额，次日重新计算。[-]"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [55] = {
|  |  |  |  |  |  |  |  text = "[ff0000]充值玩家向客服申请，次日发送邮件给玩家（节假日顺延）[-]"
|  |  |  |  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [56] = {
|  |  |  |  |  |  |  |  text = "[ff0000]首日确定大额道具内容后，在次日再次充值达到大额道具申请条件的用户，在次日申请时可额外获得前一天道具内容的30%，往后同理[-]"
|  |  |  |  |  |  |  |  title = ""
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [57] = {
|  |  |  |  |  |  |  |  text = "[ff0000]钱袋*10个[-]"
|  |  |  |  |  |  |  |  title = "第一天申请："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [58] = {
|  |  |  |  |  |  |  |  text = "[ff0000]钱袋（10+10*0.3）13个[-]"
|  |  |  |  |  |  |  |  title = "第二天申请："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [59] = {
|  |  |  |  |  |  |  |  text = "[ff0000]钱袋（13+13*0.3）17个[-]"
|  |  |  |  |  |  |  |  title = "第三天申请："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [6] = {
|  |  |  |  |  |  |  |  text = "[ff0000]传说伙伴碎片*10、3级宝石礼包*5、鲜肉包*100、2星精英伙伴*2[-]"
|  |  |  |  |  |  |  |  title = "新区累计充值1000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [60] = {
|  |  |  |  |  |  |  |  text = "[ff0000]钱袋（17+17*0.3）23个[-]"
|  |  |  |  |  |  |  |  title = "第四天申请："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [61] = {
|  |  |  |  |  |  |  |  text = "[ff0000]以次类推  所有小数点向上取整， 0.1也算1[-]"
|  |  |  |  |  |  |  |  title = ""
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [7] = {
|  |  |  |  |  |  |  |  text = "[ff0000]传说伙伴碎片*20、3级宝石礼包*10、鲜肉包*200、2星精英伙伴*3[-]"
|  |  |  |  |  |  |  |  title = "新区累计充值2000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [8] = {
|  |  |  |  |  |  |  |  text = "[ff0000]传说伙伴碎片*30、3级宝石礼包*20、鲜肉包*300、2星精英伙伴*5[-]"
|  |  |  |  |  |  |  |  title = "新区累计充值3000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [9] = {
|  |  |  |  |  |  |  |  text = "[ff0000]传说伙伴碎片*50、梦觉书·零*10、梦觉书·低*10、梦觉书·高*10[-]"
|  |  |  |  |  |  |  |  title = "新区累计充值5000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "限时大狂欢"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.活动为单日累计充值，仅计算活动期间内玩家累计充值总额，次日重新计算。
2.此活动时间为：6月2日 - 6月4日
3.此活动与游戏其他线下活动叠加，可同时参与。[-]"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [10] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值20000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]充值玩家向客服申请，次日发送邮件给玩家（节假日顺延）[-]"
|  |  |  |  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "[ff0000]均可双倍领取!!![-]"
|  |  |  |  |  |  |  |  title = "以下单日所有档位均可双倍领取"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备材料*10、金币*5w、熊猫蛋糕*5[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值100元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [5] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*10、金币铜币*10w、体力药水*1、鲜肉包*30[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值500元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [6] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*20、金币*20w、体力药水*2、焦糖包*60[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值1000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [7] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [8] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值5000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [9] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*80[-]"
|  |  |  |  |  |  |  |  title = "单日累计充值10000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "周末限时累充"
|  |  |  |  }
|  |  |  }
|  |  |  ports = {}
|  |  |  role_list = ""
|  |  |  servers = {
|  |  |  |  [1] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10041
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线1区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1686498900
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7811
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10008"
|  |  |  |  |  start_time = 1686498900
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10050
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线10区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1686725700
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7911
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10009"
|  |  |  |  |  start_time = 1686725700
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10051
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线11区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1686740100
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10010"
|  |  |  |  |  start_time = 1686740100
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10052
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线12区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1686794100
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10010"
|  |  |  |  |  start_time = 1686794100
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [13] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10053
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线13区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1686880500
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10010"
|  |  |  |  |  start_time = 1686880500
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [14] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10054
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "神王"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1686966900
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10010"
|  |  |  |  |  start_time = 1686966900
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [15] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10055
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线15区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1687053300
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10010"
|  |  |  |  |  start_time = 1687053300
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [16] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10056
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线16区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1687139700
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10010"
|  |  |  |  |  start_time = 1687139700
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [17] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10057
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线17区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1687226100
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10010"
|  |  |  |  |  start_time = 1687226100
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [18] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10058
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线18区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1687312500
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10010"
|  |  |  |  |  start_time = 1687312500
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [19] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10059
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线19区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1687398900
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10010"
|  |  |  |  |  start_time = 1687398900
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10042
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线2区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1686531300
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7811
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10008"
|  |  |  |  |  start_time = 1686531300
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [20] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10052
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线20区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1687485300
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10010"
|  |  |  |  |  start_time = 1687485300
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10043
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线3区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1686545700
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7811
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10008"
|  |  |  |  |  start_time = 1686545700
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10044
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线4区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1686560100
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7811
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10008"
|  |  |  |  |  start_time = 1686560100
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10045
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线5区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1686574500
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7811
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10008"
|  |  |  |  |  start_time = 1686574500
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10046
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线6区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1686621300
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7911
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10009"
|  |  |  |  |  start_time = 1686621300
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10047
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线7区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1686639300
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7911
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10009"
|  |  |  |  |  start_time = 1686639300
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10048
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线8区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1686653700
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7911
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10009"
|  |  |  |  |  start_time = 1686653700
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10049
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "双线9区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1686704100
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7911
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10009"
|  |  |  |  |  start_time = 1686704100
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  }
|  |  }
|  |  token = ""
|  }
}
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {
|  [1] = {
|  |  group_id = 1
|  |  id = 1
|  |  name = "1-10区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  id = 10054
|  |  |  |  ip = "**************"
|  |  |  |  name = "神王"
|  |  |  |  new = 1
|  |  |  |  open_time = 1686966900
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8011
|  |  |  |  }
|  |  |  |  server_id = 10054
|  |  |  |  start_time = 1686966900
|  |  |  |  state = 2
|  |  |  }
|  |  |  [10] = {
|  |  |  |  group = 1
|  |  |  |  id = 10044
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线4区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1686560100
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7811
|  |  |  |  }
|  |  |  |  server_id = 10044
|  |  |  |  start_time = 1686560100
|  |  |  |  state = 2
|  |  |  }
|  |  |  [11] = {
|  |  |  |  group = 1
|  |  |  |  id = 10041
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线1区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1686498900
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7811
|  |  |  |  }
|  |  |  |  server_id = 10041
|  |  |  |  start_time = 1686498900
|  |  |  |  state = 2
|  |  |  }
|  |  |  [12] = {
|  |  |  |  group = 1
|  |  |  |  id = 10049
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线9区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1686704100
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7911
|  |  |  |  }
|  |  |  |  server_id = 10049
|  |  |  |  start_time = 1686704100
|  |  |  |  state = 2
|  |  |  }
|  |  |  [13] = {
|  |  |  |  group = 1
|  |  |  |  id = 10042
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线2区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1686531300
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7811
|  |  |  |  }
|  |  |  |  server_id = 10042
|  |  |  |  start_time = 1686531300
|  |  |  |  state = 2
|  |  |  }
|  |  |  [14] = {
|  |  |  |  group = 1
|  |  |  |  id = 10048
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线8区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1686653700
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7911
|  |  |  |  }
|  |  |  |  server_id = 10048
|  |  |  |  start_time = 1686653700
|  |  |  |  state = 2
|  |  |  }
|  |  |  [15] = {
|  |  |  |  group = 1
|  |  |  |  id = 10050
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线10区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1686725700
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7911
|  |  |  |  }
|  |  |  |  server_id = 10050
|  |  |  |  start_time = 1686725700
|  |  |  |  state = 2
|  |  |  }
|  |  |  [16] = {
|  |  |  |  group = 1
|  |  |  |  id = 10051
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线11区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1686740100
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8011
|  |  |  |  }
|  |  |  |  server_id = 10051
|  |  |  |  start_time = 1686740100
|  |  |  |  state = 2
|  |  |  }
|  |  |  [17] = {
|  |  |  |  group = 1
|  |  |  |  id = 10053
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线13区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1686880500
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8011
|  |  |  |  }
|  |  |  |  server_id = 10053
|  |  |  |  start_time = 1686880500
|  |  |  |  state = 2
|  |  |  }
|  |  |  [18] = {
|  |  |  |  group = 1
|  |  |  |  id = 10045
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线5区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1686574500
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7811
|  |  |  |  }
|  |  |  |  server_id = 10045
|  |  |  |  start_time = 1686574500
|  |  |  |  state = 2
|  |  |  }
|  |  |  [19] = {
|  |  |  |  group = 1
|  |  |  |  id = 10052
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线20区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1687485300
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8011
|  |  |  |  }
|  |  |  |  server_id = 10052
|  |  |  |  start_time = 1687485300
|  |  |  |  state = 2
|  |  |  }
|  |  |  [2] = {
|  |  |  |  group = 1
|  |  |  |  id = 10046
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线6区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1686621300
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7911
|  |  |  |  }
|  |  |  |  server_id = 10046
|  |  |  |  start_time = 1686621300
|  |  |  |  state = 2
|  |  |  }
|  |  |  [3] = {
|  |  |  |  group = 1
|  |  |  |  id = 10055
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线15区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1687053300
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8011
|  |  |  |  }
|  |  |  |  server_id = 10055
|  |  |  |  start_time = 1687053300
|  |  |  |  state = 2
|  |  |  }
|  |  |  [4] = {
|  |  |  |  group = 1
|  |  |  |  id = 10043
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线3区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1686545700
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7811
|  |  |  |  }
|  |  |  |  server_id = 10043
|  |  |  |  start_time = 1686545700
|  |  |  |  state = 2
|  |  |  }
|  |  |  [5] = {
|  |  |  |  group = 1
|  |  |  |  id = 10059
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线19区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1687398900
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8011
|  |  |  |  }
|  |  |  |  server_id = 10059
|  |  |  |  start_time = 1687398900
|  |  |  |  state = 2
|  |  |  }
|  |  |  [6] = {
|  |  |  |  group = 1
|  |  |  |  id = 10058
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线18区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1687312500
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8011
|  |  |  |  }
|  |  |  |  server_id = 10058
|  |  |  |  start_time = 1687312500
|  |  |  |  state = 2
|  |  |  }
|  |  |  [7] = {
|  |  |  |  group = 1
|  |  |  |  id = 10057
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线17区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1687226100
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8011
|  |  |  |  }
|  |  |  |  server_id = 10057
|  |  |  |  start_time = 1687226100
|  |  |  |  state = 2
|  |  |  }
|  |  |  [8] = {
|  |  |  |  group = 1
|  |  |  |  id = 10047
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线7区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1686639300
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7911
|  |  |  |  }
|  |  |  |  server_id = 10047
|  |  |  |  start_time = 1686639300
|  |  |  |  state = 2
|  |  |  }
|  |  |  [9] = {
|  |  |  |  group = 1
|  |  |  |  id = 10056
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线16区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1687139700
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8011
|  |  |  |  }
|  |  |  |  server_id = 10056
|  |  |  |  start_time = 1687139700
|  |  |  |  state = 2
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:设置公告: = {
|  [1] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "新服7天限时-大狂欢活动"
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  text = "[ff0000]传说伙伴碎片*100、梦觉书·零*20、梦觉书·低*20、梦觉书·高*20[-]"
|  |  |  |  |  title = "新区累计充值10000元"
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "单日累计充值活动"
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [13] = {
|  |  |  |  |  text = "[ff0000]1.活动为单日累计充值，高档位可领取低档位所有物品。
2.活动为单日累计充值，仅计算单日内玩家累计充值总额，次日重新计算。[-]"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [14] = {
|  |  |  |  |  text = "[ff0000]向客服申请[-]"
|  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  }
|  |  |  |  [15] = {
|  |  |  |  |  text = "[ff0000]装备材料*10、金币*5w、熊猫蛋糕*5[-]"
|  |  |  |  |  title = "单日累计充值100元"
|  |  |  |  }
|  |  |  |  [16] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*10、金币铜币*10w、体力药水*1、鲜肉包*30[-]"
|  |  |  |  |  title = "单日累计充值500元"
|  |  |  |  }
|  |  |  |  [17] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*20、金币*20w、体力药水*2、焦糖包*60[-]"
|  |  |  |  |  title = "单日累计充值1000元"
|  |  |  |  }
|  |  |  |  [18] = {
|  |  |  |  |  text = "[ff0000]装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1[-]"
|  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  }
|  |  |  |  [19] = {
|  |  |  |  |  text = "[ff0000]装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3[-]"
|  |  |  |  |  title = "单日累计充值5000元"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]新服前7天[-]"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [20] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*80[-]"
|  |  |  |  |  title = "单日累计充值10000元"
|  |  |  |  }
|  |  |  |  [21] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1[-]"
|  |  |  |  |  title = "单日累计充值20000元"
|  |  |  |  }
|  |  |  |  [22] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "单笔充值活动"
|  |  |  |  }
|  |  |  |  [23] = {
|  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [24] = {
|  |  |  |  |  text = "[ff0000]1.活动为单笔活动，仅可领取充值对应档位物品。
2.活动单日可多次领取。[-]"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [25] = {
|  |  |  |  |  text = "[ff0000]充值玩家向客服申请，次日发送给玩家CDK（节假日顺延）[-]"
|  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  }
|  |  |  |  [26] = {
|  |  |  |  |  text = "[ff0000]灵魂钥匙*6、淬灵云晶*20、原石礼包*4、武器原石*1[-]"
|  |  |  |  |  title = "单笔68元"
|  |  |  |  }
|  |  |  |  [27] = {
|  |  |  |  |  text = "[ff0000]灵魂钥匙*12、淬灵云晶*40、原石礼包*8、武器原石*2[-]"
|  |  |  |  |  title = "单笔128元"
|  |  |  |  }
|  |  |  |  [28] = {
|  |  |  |  |  text = "[ff0000]灵魂钥匙*32、淬灵云晶*100、原石礼包*20、武器原石*5[-]"
|  |  |  |  |  title = "单笔328元"
|  |  |  |  }
|  |  |  |  [29] = {
|  |  |  |  |  text = "[ff0000]灵魂钥匙*64、淬灵云晶*200、原石礼包*40、武器原石*10[-]"
|  |  |  |  |  title = "单笔648元"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "[ff0000]单日累计充值达到对应档位，不重复高领低档，每个档位仅可领取一次奖励、请及时参加并申请领取对应档位。[-]"
|  |  |  |  |  title = "活动规则"
|  |  |  |  }
|  |  |  |  [30] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "永久累计充值活动"
|  |  |  |  }
|  |  |  |  [31] = {
|  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [32] = {
|  |  |  |  |  text = "[ff0000]活动为永久累计充值，高档位可领取低档位所有物品。
每个档位奖励仅可领取一次[-]"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [33] = {
|  |  |  |  |  text = "[ff0000]向客服申请[-]"
|  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  }
|  |  |  |  [34] = {
|  |  |  |  |  text = "[ff0000]装备材料*50、伙伴觉醒材料*50、金币*1w[-]"
|  |  |  |  |  title = "永久累计充值500元"
|  |  |  |  }
|  |  |  |  [35] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片5、2星精英伙伴*10、3级符石礼袋*5、附魔材料*5、金币*5w[-]"
|  |  |  |  |  title = "永久累计充值1000元"
|  |  |  |  }
|  |  |  |  [36] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*10、2星精英伙伴*20、3级符石礼袋*10、附魔材料*10、金币*10w[-]"
|  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  }
|  |  |  |  [37] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*30、3星传说伙伴*1、3级符石礼袋*30、附魔材料*30、金币*50w[-]"
|  |  |  |  |  title = "永久累计充值5000元"
|  |  |  |  }
|  |  |  |  [38] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*50、万能碎片*100、金币*100w、橙色可选御灵*10[-]"
|  |  |  |  |  title = "永久累充10000元礼包"
|  |  |  |  }
|  |  |  |  [39] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*100、万能碎片*200、金币*200w、橙色可选御灵*20[-]"
|  |  |  |  |  title = "永久累充20000元礼包"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "[ff0000]向客服申请[-]"
|  |  |  |  |  title = "发放时间方式："
|  |  |  |  }
|  |  |  |  [40] = {
|  |  |  |  |  text = "[ff0000]万能碎片*200、金币*500w、橙色可选御灵*50[-]"
|  |  |  |  |  title = "永久累充40000元礼包"
|  |  |  |  }
|  |  |  |  [41] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "单日累计充值货币返利1:10"
|  |  |  |  }
|  |  |  |  [42] = {
|  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [43] = {
|  |  |  |  |  text = "[ff0000]1.本活动为阶梯式活动，当日累计充值的金额满足返利阶梯后，按照当日充值金额满足的最高档次进行发放，次日自动返利，无需申请
2.本活动为长期活动，与其他返利活动叠加。
充值100-1999元，返利50%钻石
充值2000-4999元，返利100%钻石
充值5000-9999元，返利150%钻石
充值10000-19999元，返利200%钻石
充值20000+元，返利250%钻石
以上返利会在第二天自动已邮件形式发送至玩家[-]"
|  |  |  |  |  title = "活动说明:"
|  |  |  |  }
|  |  |  |  [44] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "服务器冠名活动"
|  |  |  |  }
|  |  |  |  [45] = {
|  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [46] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [47] = {
|  |  |  |  |  text = "[ff0000]全体玩家[-]"
|  |  |  |  |  title = "【活动对象】："
|  |  |  |  }
|  |  |  |  [48] = {
|  |  |  |  |  text = "[ff0000]我的服务器，我为自己代言。[-]"
|  |  |  |  |  title = "【活动宣言】："
|  |  |  |  }
|  |  |  |  [49] = {
|  |  |  |  |  text = "[ff0000]区服冠名权*1[-]"
|  |  |  |  |  title = "【冠名奖励】："
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  text = "[ff0000]传说伙伴碎片*5、3级宝石礼包*2、鲜肉包*50、2星精英伙伴*1[-]"
|  |  |  |  |  title = "新区累计充值500元"
|  |  |  |  }
|  |  |  |  [50] = {
|  |  |  |  |  text = "  [ff0000]亲爱的玩家：
  也许你在游戏里已经称霸全区，但是出了服务器，出了游戏，还有谁认识你现在就给你一个机会，获得至高无上的荣誉，让更多的人知道你的强大。特此举行“我的服务器，我为自己代言”，服务器玩家冠名活动。
即日起，只要满足以下条件，即可获得服务器冠名权：
  1）开服前3天累计充值满10000元
  2）开服前7日累计充值满20000元，且区服处于未冠名状态，获得服务器冠名权
  备注：本活动遵循先到先得原则。如出现多个满足条件的，冠名资格由第一个满足条件的获得。[-]"
|  |  |  |  |  title = "【活动内容】："
|  |  |  |  }
|  |  |  |  [51] = {
|  |  |  |  |  text = "[ff0000]1、活动时间内有玩家满足条件后活动结束，活动期间没有玩家满足条件则保持原服务器名称。
2、服务器冠名长度为4个字。（必须遵守相关法律法规，不得使用负面或影响社会风气，不得出现敏感词的名称）
3、由于冠名操作需要时间，工作人员会在7个工作日内，完成冠名操作。
4，每个服，冠名只有一次。被冠名的服务器不予再次冠名[-]"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  |  [52] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "单日大额充值活动"
|  |  |  |  }
|  |  |  |  [53] = {
|  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [54] = {
|  |  |  |  |  text = "[ff0000]1.活动为单日累计充值，仅计算单日内玩家累计充值总额，次日重新计算。[-]"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [55] = {
|  |  |  |  |  text = "[ff0000]充值玩家向客服申请，次日发送邮件给玩家（节假日顺延）[-]"
|  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  }
|  |  |  |  [56] = {
|  |  |  |  |  text = "[ff0000]首日确定大额道具内容后，在次日再次充值达到大额道具申请条件的用户，在次日申请时可额外获得前一天道具内容的30%，往后同理[-]"
|  |  |  |  |  title = ""
|  |  |  |  }
|  |  |  |  [57] = {
|  |  |  |  |  text = "[ff0000]钱袋*10个[-]"
|  |  |  |  |  title = "第一天申请："
|  |  |  |  }
|  |  |  |  [58] = {
|  |  |  |  |  text = "[ff0000]钱袋（10+10*0.3）13个[-]"
|  |  |  |  |  title = "第二天申请："
|  |  |  |  }
|  |  |  |  [59] = {
|  |  |  |  |  text = "[ff0000]钱袋（13+13*0.3）17个[-]"
|  |  |  |  |  title = "第三天申请："
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  text = "[ff0000]传说伙伴碎片*10、3级宝石礼包*5、鲜肉包*100、2星精英伙伴*2[-]"
|  |  |  |  |  title = "新区累计充值1000元"
|  |  |  |  }
|  |  |  |  [60] = {
|  |  |  |  |  text = "[ff0000]钱袋（17+17*0.3）23个[-]"
|  |  |  |  |  title = "第四天申请："
|  |  |  |  }
|  |  |  |  [61] = {
|  |  |  |  |  text = "[ff0000]以次类推  所有小数点向上取整， 0.1也算1[-]"
|  |  |  |  |  title = ""
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  text = "[ff0000]传说伙伴碎片*20、3级宝石礼包*10、鲜肉包*200、2星精英伙伴*3[-]"
|  |  |  |  |  title = "新区累计充值2000元"
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  text = "[ff0000]传说伙伴碎片*30、3级宝石礼包*20、鲜肉包*300、2星精英伙伴*5[-]"
|  |  |  |  |  title = "新区累计充值3000元"
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  text = "[ff0000]传说伙伴碎片*50、梦觉书·零*10、梦觉书·低*10、梦觉书·高*10[-]"
|  |  |  |  |  title = "新区累计充值5000元"
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "限时大狂欢"
|  }
|  [2] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]1.活动为单日累计充值，仅计算活动期间内玩家累计充值总额，次日重新计算。
2.此活动时间为：6月2日 - 6月4日
3.此活动与游戏其他线下活动叠加，可同时参与。[-]"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1[-]"
|  |  |  |  |  title = "单日累计充值20000元"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]充值玩家向客服申请，次日发送邮件给玩家（节假日顺延）[-]"
|  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "[ff0000]均可双倍领取!!![-]"
|  |  |  |  |  title = "以下单日所有档位均可双倍领取"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "[ff0000]装备材料*10、金币*5w、熊猫蛋糕*5[-]"
|  |  |  |  |  title = "单日累计充值100元"
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*10、金币铜币*10w、体力药水*1、鲜肉包*30[-]"
|  |  |  |  |  title = "单日累计充值500元"
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*20、金币*20w、体力药水*2、焦糖包*60[-]"
|  |  |  |  |  title = "单日累计充值1000元"
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  text = "[ff0000]装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1[-]"
|  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  text = "[ff0000]装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3[-]"
|  |  |  |  |  title = "单日累计充值5000元"
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*80[-]"
|  |  |  |  |  title = "单日累计充值10000元"
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "周末限时累充"
|  }
}
[core/global.lua:59]:<color=#ffeb04>CLoginServerPage.SetServer</color>
[logic/login/CLoginCtrl.lua:151]:ConnectServer =     table:0x6D2F5668    table:0x6D398CE8
[net/CNetCtrl.lua:114]:Test连接    **************    8011
[core/table.lua:94]:-->Net Receive: login.GS2CHello = {
|  time = **********
}
[core/table.lua:94]:<--Net Send: login.C2GSQueryLogin = {
|  res_file_version = {}
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/07/05 22:07:26
[net/netlogin.lua:208]:netlogin.GS2CQueryLogin-->
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 1001
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginAccount = {
|  account = "dw1"
|  client_svn_version = 96
|  client_version = "1.15.0.0"
|  device = "B760M GAMING (JGINYUE)"
|  imei = ""
|  is_qrcode = 0
|  mac = "00-E0-1A-9A-48-AB"
|  os = "Windows 10  (10.0.0) 64bit"
|  platform = 4
|  udid = "6370fc50ee13b3bf1fd91cc507270f729c390721"
}
[core/global.lua:59]:<color=#ffeb04>更新代码: 1049</color>
[logic/base/Utils.lua:400]:local framever, dllver, resver = C_api.Utils.GetResVersion()
if resver >= 96 or Utils.IsEditor() then
	function CWelfareCtrl.IsOpenCostSave(self)
		local b = false
		local t = g_TimeCtrl:GetTimeS()	
		if self.m_CostSaveStartTime ~= 0 and self.m_CostSaveEndTime ~= 0 and t > self.m_CostSaveStartTime and t < self.m_CostSaveEndTime then
			b = true
		end
		return b
	end
	function CPartnerHireView.IsCanYFRH(self, iParID)
		local d = data.partnerhiredata.DATA
		if table.index(d[iParID]["recommand_list"], "一发入魂") then
			return self:IsCanSSRDraw()
		end
	end
	function CPartnerEquipPage.ChangePart(self)
		local oItem = g_ItemCtrl:GetItem(self.m_CurItemID)
		if oItem and oItem:GetValue("level") == define.Partner.ParEquip.MaxLevel and oItem:GetValue("star") < define.Partner.ParEquip.MaxStar then
			if self.m_CurPart ~= self.m_UpStarPart and self.m_CurPart ~= self.m_UpStonePart then
				self.m_UpStarBtn:SetSelected(true)
				self:SwitchPart()
			end
		end
	end
	data.netdata.BAN["proto"]["herobox"]["item"]["GS2CItemQuickUse"] = true
end

[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 1001
}
[core/table.lua:94]:-->Net Receive: login.GS2CCheckInviteCodeResult = {
|  msg = "邀请码验证已关闭"
|  result = 1
}
[core/table.lua:94]:-->Net Receive: login.GS2CLoginAccount = {
|  account = "dw1"
|  role_list = {
|  |  [1] = {
|  |  |  grade = 14
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 110
|  |  |  |  weapon = 2500
|  |  |  }
|  |  |  pid = 10001
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginRole = {
|  account = "dw1"
|  pid = 10001
}
[core/global.lua:59]:<color=#ffeb04>读取系统设置本地数据，帐号为  dw1</color>
[core/global.lua:59]:<color=#ffeb04>dw1 的系统设置本地数据为 nil，使用默认数据</color>
[core/table.lua:94]:-->Net Receive: login.GS2CLoginRole = {
|  account = "dw1"
|  pid = 10001
|  role = {
|  |  abnormal_attr_ratio = 560
|  |  active = 1
|  |  attack = 328
|  |  bcmd = {
|  |  |  [1] = {
|  |  |  |  idx = 208
|  |  |  }
|  |  |  [10] = {
|  |  |  |  idx = 105
|  |  |  }
|  |  |  [11] = {
|  |  |  |  idx = 106
|  |  |  }
|  |  |  [12] = {
|  |  |  |  idx = 107
|  |  |  }
|  |  |  [13] = {
|  |  |  |  idx = 108
|  |  |  }
|  |  |  [14] = {
|  |  |  |  idx = 205
|  |  |  }
|  |  |  [15] = {
|  |  |  |  idx = 206
|  |  |  }
|  |  |  [16] = {
|  |  |  |  idx = 207
|  |  |  }
|  |  |  [2] = {
|  |  |  |  cmd = "集火"
|  |  |  |  idx = 201
|  |  |  }
|  |  |  [3] = {
|  |  |  |  cmd = "禁疗"
|  |  |  |  idx = 204
|  |  |  }
|  |  |  [4] = {
|  |  |  |  cmd = "封印"
|  |  |  |  idx = 202
|  |  |  }
|  |  |  [5] = {
|  |  |  |  cmd = "减速"
|  |  |  |  idx = 203
|  |  |  }
|  |  |  [6] = {
|  |  |  |  cmd = "治疗"
|  |  |  |  idx = 101
|  |  |  }
|  |  |  [7] = {
|  |  |  |  cmd = "复活"
|  |  |  |  idx = 102
|  |  |  }
|  |  |  [8] = {
|  |  |  |  cmd = "加速"
|  |  |  |  idx = 103
|  |  |  }
|  |  |  [9] = {
|  |  |  |  cmd = "解封"
|  |  |  |  idx = 104
|  |  |  }
|  |  }
|  |  coin = 492490
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 43
|  |  energy = 213
|  |  exp = 22660
|  |  grade = 14
|  |  kp_sdk_info = {
|  |  |  create_time = 1686746520
|  |  |  upgrade_time = 1688556158
|  |  }
|  |  mask = "7e6683d1ffffffe"
|  |  max_hp = 3173
|  |  model_info = {
|  |  |  color = {
|  |  |  |  [1] = 0
|  |  |  }
|  |  |  shape = 110
|  |  |  weapon = 2500
|  |  }
|  |  name = "风流o空气"
|  |  open_day = 20
|  |  org_fuben_cnt = 2
|  |  power = 991
|  |  res_abnormal_ratio = 560
|  |  res_critical_ratio = 500
|  |  school = 1
|  |  school_branch = 1
|  |  sex = 1
|  |  show_id = 10001
|  |  skill_point = 13
|  |  speed = 753
|  |  systemsetting = {}
|  }
|  role_token = "**************"
|  xg_account = "bus10001"
}
[logic/ui/CViewCtrl.lua:94]:CMainMenuView ShowView
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 560
|  active = 1
|  arenamedal = 0
|  attack = 328
|  bcmd = {
|  |  [1] = {
|  |  |  idx = 208
|  |  }
|  |  [10] = {
|  |  |  idx = 105
|  |  }
|  |  [11] = {
|  |  |  idx = 106
|  |  }
|  |  [12] = {
|  |  |  idx = 107
|  |  }
|  |  [13] = {
|  |  |  idx = 108
|  |  }
|  |  [14] = {
|  |  |  idx = 205
|  |  }
|  |  [15] = {
|  |  |  idx = 206
|  |  }
|  |  [16] = {
|  |  |  idx = 207
|  |  }
|  |  [2] = {
|  |  |  cmd = "集火"
|  |  |  idx = 201
|  |  }
|  |  [3] = {
|  |  |  cmd = "禁疗"
|  |  |  idx = 204
|  |  }
|  |  [4] = {
|  |  |  cmd = "封印"
|  |  |  idx = 202
|  |  }
|  |  [5] = {
|  |  |  cmd = "减速"
|  |  |  idx = 203
|  |  }
|  |  [6] = {
|  |  |  cmd = "治疗"
|  |  |  idx = 101
|  |  }
|  |  [7] = {
|  |  |  cmd = "复活"
|  |  |  idx = 102
|  |  }
|  |  [8] = {
|  |  |  cmd = "加速"
|  |  |  idx = 103
|  |  }
|  |  [9] = {
|  |  |  cmd = "解封"
|  |  |  idx = 104
|  |  }
|  }
|  camp = 0
|  chatself = false
|  chubeiexp = 0
|  coin = 492490
|  coin_over = 0
|  color_coin = 0
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 43
|  energy = 213
|  exp = 22660
|  followers = {}
|  goldcoin = 0
|  grade = 14
|  hp = 0
|  kp_sdk_info = {
|  |  create_time = 1686746520
|  |  upgrade_time = 1688556158
|  }
|  max_hp = 3173
|  medal = 0
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 110
|  |  weapon = 2500
|  }
|  name = "风流o空气"
|  open_day = 20
|  org_fuben_cnt = 2
|  org_offer = 0
|  power = 991
|  res_abnormal_ratio = 560
|  res_critical_ratio = 500
|  school = 1
|  school_branch = 1
|  sex = 1
|  show_id = 10001
|  skill_point = 13
|  skin = 0
|  speed = 753
|  systemsetting = {}
|  title_info = {}
|  trapmine_point = 0
|  travel_score = 0
|  upvote_amount = 0
}
[logic/misc/CTimeCtrl.lua:64]:HeartBeat-->开启心跳包检测
[core/table.lua:94]:<--Net Send: title.C2GSTitleInfoList = {}
[core/table.lua:94]:-->Net Receive: player.GS2CGamePushSetting = {}
[core/table.lua:94]:-->Net Receive: player.GS2CShapeList = {
|  shapes = {
|  |  [1] = 110
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CGuidanceInfo = {
|  guidanceinfo = {
|  |  key = {
|  |  |  [1] = 7000041
|  |  |  [10] = 3002099
|  |  |  [11] = 1003099
|  |  |  [12] = 3006099
|  |  |  [13] = 3007099
|  |  |  [14] = 3008099
|  |  |  [15] = 3003099
|  |  |  [16] = 1004099
|  |  |  [17] = 3009099
|  |  |  [18] = 3010099
|  |  |  [19] = 3011099
|  |  |  [2] = 7000051
|  |  |  [20] = 1006099
|  |  |  [21] = 3016099
|  |  |  [22] = 1010099
|  |  |  [23] = 1005099
|  |  |  [24] = 3012099
|  |  |  [25] = 7000071
|  |  |  [26] = 3014099
|  |  |  [27] = 3015099
|  |  |  [28] = 3013099
|  |  |  [29] = 1007099
|  |  |  [3] = 6010002
|  |  |  [30] = 3019099
|  |  |  [31] = 5000700
|  |  |  [32] = 1012099
|  |  |  [4] = 1027099
|  |  |  [5] = 3001099
|  |  |  [6] = 1028099
|  |  |  [7] = 5000300
|  |  |  [8] = 7000061
|  |  |  [9] = 1001099
|  |  }
|  |  version = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>>>>>>>>>>>>>>>>>>>>> GS2CGuidanceInfo </color>
[core/table.lua:94]:-->Net Receive: travel.GS2CLoginTravelPartner = {
|  travel_partner = {
|  |  server_time = **********
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CServerGradeInfo = {
|  days = 4
|  server_grade = 75
}
[core/table.lua:94]:-->Net Receive: scene.GS2CShowScene = {
|  map_id = 101000
|  scene_id = 12
|  scene_name = "帝都"
}
[core/global.lua:59]:<color=#ffeb04>CResCtrl.MoveToSecondary</color>
[core/table.lua:94]:-->Net Receive: task.GS2CLoginTask = {
|  shimen_status = 1
|  taskdata = {
|  |  [1] = {
|  |  |  acceptnpc = 10012
|  |  |  clientnpc = {
|  |  |  |  [1] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 513
|  |  |  |  |  }
|  |  |  |  |  name = "夜叉"
|  |  |  |  |  npcid = 3916
|  |  |  |  |  npctype = 10012
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 25800
|  |  |  |  |  |  y = 11700
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 404
|  |  |  |  |  }
|  |  |  |  |  name = "群众"
|  |  |  |  |  npcid = 3917
|  |  |  |  |  npctype = 10013
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 25000
|  |  |  |  |  |  y = 12600
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  detaildesc = "与袭击者对话，试图获取有用信息。"
|  |  |  name = "质问"
|  |  |  patrolinfo = {}
|  |  |  pickiteminfo = {}
|  |  |  placeinfo = {}
|  |  |  shapeinfo = {}
|  |  |  statusinfo = {
|  |  |  |  note = "CreateTask"
|  |  |  |  status = 2
|  |  |  }
|  |  |  submitnpc = 10012
|  |  |  target = 10012
|  |  |  targetdesc = "一头雾水"
|  |  |  taskid = 10008
|  |  |  taskitem = {}
|  |  |  tasktype = 1
|  |  |  traceinfo = {}
|  |  |  type = 2
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>login task</color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x64aa6f50"
|  |  AssociatedPick = "function: 0x64aa6fb0"
|  |  AssociatedSubmit = "function: 0x64aa6f80"
|  |  CreateDefalutData = "function: 0x64aa4f30"
|  |  GetChaptetFubenData = "function: 0x64aa6c70"
|  |  GetProgressThing = "function: 0x64aa7010"
|  |  GetRemainTime = "function: 0x64aa4ec8"
|  |  GetStatus = "function: 0x64aa7070"
|  |  GetTaskClientExtStrDic = "function: 0x64aa6fe0"
|  |  GetTaskTypeSpriteteName = "function: 0x64aa6c40"
|  |  GetTraceInfo = "function: 0x64aa6d98"
|  |  GetTraceNpcType = "function: 0x64aa6c10"
|  |  GetValue = "function: 0x64aa6cd8"
|  |  IsAbandon = "function: 0x64aa6d38"
|  |  IsAddEscortDynamicNpc = "function: 0x64aa73a8"
|  |  IsMissMengTask = "function: 0x64aa4ef8"
|  |  IsPassChaterFuben = "function: 0x64aa6ca0"
|  |  IsTaskSpecityAction = "function: 0x64aa6d68"
|  |  IsTaskSpecityCategory = "function: 0x64aa6f20"
|  |  New = "function: 0x64aab8a0"
|  |  NewByData = "function: 0x64aa4470"
|  |  RaiseProgressIdx = "function: 0x64aa7040"
|  |  RefreshTask = "function: 0x64aa6d08"
|  |  ResetEndTime = "function: 0x64aa4f98"
|  |  SetStatus = "function: 0x64aa73d8"
|  |  classname = "CTask"
|  |  ctor = "function: 0x64aa4e98"
|  }
|  m_CData = {
|  |  ChapterFb = "1,2"
|  |  autoDoNextTask = 10009
|  |  clientExtStr = ""
|  |  name = "质问"
|  |  submitNpcId = 10012
|  |  submitRewardStr = {
|  |  |  [1] = "R1008"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10012
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 513
|  |  |  |  }
|  |  |  |  name = "夜叉"
|  |  |  |  npcid = 3916
|  |  |  |  npctype = 10012
|  |  |  |  pos_info = {
|  |  |  |  |  x = 25800
|  |  |  |  |  y = 11700
|  |  |  |  }
|  |  |  }
|  |  |  [2] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 404
|  |  |  |  }
|  |  |  |  name = "群众"
|  |  |  |  npcid = 3917
|  |  |  |  npctype = 10013
|  |  |  |  pos_info = {
|  |  |  |  |  x = 25000
|  |  |  |  |  y = 12600
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "与袭击者对话，试图获取有用信息。"
|  |  isdone = 0
|  |  name = "质问"
|  |  patrolinfo = {}
|  |  pickiteminfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10012
|  |  target = 10012
|  |  targetdesc = "一头雾水"
|  |  taskid = 10008
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[logic/ui/CSprite.lua:159]:DynamicSprite dSprInfo    Item    10012
[logic/ui/CViewBase.lua:125]:CMainMenuView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: teach.GS2CTeachProgress = {}
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshPartnerTask = {
|  partnertask_progress = {
|  |  [1] = {
|  |  |  parid = 313
|  |  |  status = 1
|  |  |  taskid = 62195
|  |  }
|  |  [2] = {
|  |  |  parid = 403
|  |  |  status = 1
|  |  |  taskid = 62169
|  |  }
|  |  [3] = {
|  |  |  parid = 501
|  |  |  status = 1
|  |  |  taskid = 62085
|  |  }
|  |  [4] = {
|  |  |  parid = 502
|  |  |  status = 1
|  |  |  taskid = 62090
|  |  }
|  |  [5] = {
|  |  |  parid = 302
|  |  |  status = 1
|  |  |  taskid = 62080
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  activepoint = 1
|  day_task = {
|  |  [1] = 1016
|  |  [2] = 3005
|  }
}
[core/table.lua:94]:-->Net Receive: state.GS2CLoginState = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginHuodongInfo = {
|  convoyinfo = {
|  |  convoy_partner = 1504
|  |  free_time = 1
|  |  pool_info = {
|  |  |  [1] = {
|  |  |  |  level = 1
|  |  |  |  partnerid = 1504
|  |  |  |  pos = 1
|  |  |  |  rewardid = 1001
|  |  |  }
|  |  |  [2] = {
|  |  |  |  level = 2
|  |  |  |  partnerid = 1201
|  |  |  |  pos = 2
|  |  |  |  rewardid = 1002
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [3] = {
|  |  |  |  level = 3
|  |  |  |  partnerid = 1018
|  |  |  |  pos = 3
|  |  |  |  rewardid = 1003
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [4] = {
|  |  |  |  level = 4
|  |  |  |  partnerid = 1510
|  |  |  |  pos = 4
|  |  |  |  rewardid = 1004
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [5] = {
|  |  |  |  level = 5
|  |  |  |  partnerid = 1752
|  |  |  |  pos = 5
|  |  |  |  rewardid = 1005
|  |  |  |  weight = 25
|  |  |  }
|  |  }
|  |  selected_pos = 1
|  |  target_npc = 5014
|  }
|  dailytrain = {
|  |  reward_times = 60
|  }
|  hireinfo = {
|  |  [1] = {
|  |  |  parid = 501
|  |  |  times = 1
|  |  }
|  |  [2] = {
|  |  |  parid = 502
|  |  |  times = 1
|  |  }
|  |  [3] = {
|  |  |  parid = 403
|  |  |  times = 1
|  |  }
|  }
|  huntinfo = {}
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginChapterInfo = {
|  finalchapter = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  level = 2
|  |  |  open = 1
|  |  |  type = 1
|  |  }
|  }
|  totalstar_info = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  star = 3
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: handbook.GS2CLoginBookList = {
|  book_list = {
|  |  [1] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 313131
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 113131
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100313
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [2] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 314031
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 114031
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100403
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [3] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 315011
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  id = 315012
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115011
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100501
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [4] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 313021
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = {
|  |  |  |  |  |  [1] = 113023
|  |  |  |  |  }
|  |  |  |  |  id = 313022
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 113021
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100302
|  |  |  red_point = 3
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [5] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 315021
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115021
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100502
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  }
|  red_points = {
|  |  [1] = {
|  |  |  book_type = 1
|  |  |  red_point = 1
|  |  }
|  |  [2] = {
|  |  |  book_type = 2
|  |  }
|  |  [3] = {
|  |  |  book_type = 3
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: team.GS2CInviteInfo = {}
[core/table.lua:94]:-->Net Receive: mail.GS2CLoginMail = {}
[core/table.lua:94]:-->Net Receive: friend.GS2CLoginFriend = {}
[core/global.lua:59]:<color=#ffeb04>好友版本一致，无需更新</color>
[core/table.lua:94]:-->Net Receive: friend.GS2CApplyList = {
|  pidlist = {
|  |  [1] = 10002
|  |  [2] = 10003
|  |  [3] = 10005
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 21016
}
[core/table.lua:94]:<--Net Send: friend.C2GSQueryFriendApply = {
|  pid_list = {
|  |  [1] = 10002
|  |  [2] = 10003
|  |  [3] = 10005
|  }
}
[core/table.lua:94]:-->Net Receive: friend.GS2CFriendSetting = {
|  setting = {
|  |  notify = 1
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CDrawCardUI = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliReddot = {
|  cday = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraWarState = {
|  time = 1688682668
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraQueueStatus = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CNewFieldBoss = {
|  bossid = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  }
}
[core/table.lua:94]:-->Net Receive: item.GS2CLoginItem = {
|  itemdata = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  create_time = 1686746870
|  |  |  id = 1
|  |  |  itemlevel = 4
|  |  |  name = "万能碎片"
|  |  |  sid = 14002
|  |  }
|  |  [10] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 364
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 72
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1686746521
|  |  |  equip_info = {
|  |  |  |  pos = 5
|  |  |  |  stone_sid = 4300000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 8
|  |  |  itemlevel = 1
|  |  |  name = "练习折桂腰"
|  |  |  power = 72
|  |  |  sid = 2510000
|  |  }
|  |  [11] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "speed"
|  |  |  |  |  value = 33
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 11
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 77
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1686746521
|  |  |  equip_info = {
|  |  |  |  pos = 6
|  |  |  |  stone_sid = 4400000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 11
|  |  |  itemlevel = 1
|  |  |  name = "练习虬草鞋"
|  |  |  power = 77
|  |  |  sid = 2610000
|  |  }
|  |  [2] = {
|  |  |  amount = 1
|  |  |  create_time = 1688538782
|  |  |  id = 2
|  |  |  itemlevel = 4
|  |  |  name = "50级橙武礼包"
|  |  |  sid = 12083
|  |  }
|  |  [3] = {
|  |  |  amount = 2
|  |  |  create_time = 1688538812
|  |  |  id = 3
|  |  |  itemlevel = 2
|  |  |  name = "深蓝琥珀"
|  |  |  sid = 14021
|  |  }
|  |  [4] = {
|  |  |  amount = 1
|  |  |  create_time = 1688538909
|  |  |  id = 4
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 1
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101001
|  |  }
|  |  [5] = {
|  |  |  amount = 1
|  |  |  create_time = 1688538934
|  |  |  id = 5
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 3
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101001
|  |  }
|  |  [6] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1686746521
|  |  |  equip_info = {
|  |  |  |  pos = 1
|  |  |  |  stone_sid = 3100000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 6
|  |  |  itemlevel = 1
|  |  |  name = "练习红魔钺"
|  |  |  power = 57
|  |  |  sid = 2100000
|  |  }
|  |  [7] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 121
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 24
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1686746521
|  |  |  equip_info = {
|  |  |  |  pos = 2
|  |  |  |  stone_sid = 4000000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 10
|  |  |  itemlevel = 1
|  |  |  name = "练习飘逸巾"
|  |  |  power = 24
|  |  |  sid = 2200000
|  |  }
|  |  [8] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1686746521
|  |  |  equip_info = {
|  |  |  |  pos = 3
|  |  |  |  stone_sid = 4100000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 7
|  |  |  itemlevel = 1
|  |  |  name = "练习剑麻甲"
|  |  |  power = 32
|  |  |  sid = 2310000
|  |  }
|  |  [9] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1686746521
|  |  |  equip_info = {
|  |  |  |  pos = 4
|  |  |  |  stone_sid = 4200000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 9
|  |  |  itemlevel = 1
|  |  |  name = "练习草织戒"
|  |  |  power = 19
|  |  |  sid = 2400000
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>loginitem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenPlanName = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginTrapmine = {
|  rare_monster = {
|  |  [1] = {
|  |  |  map_id = 202000
|  |  |  partypes = {
|  |  |  |  [1] = 312
|  |  |  |  [2] = 401
|  |  |  |  [3] = 314
|  |  |  }
|  |  }
|  |  [2] = {
|  |  |  map_id = 204000
|  |  |  partypes = {
|  |  |  |  [1] = 404
|  |  |  |  [2] = 402
|  |  |  |  [3] = 403
|  |  |  }
|  |  }
|  |  [3] = {
|  |  |  map_id = 205000
|  |  |  partypes = {
|  |  |  |  [1] = 508
|  |  |  |  [2] = 509
|  |  |  |  [3] = 514
|  |  |  }
|  |  }
|  |  [4] = {
|  |  |  map_id = 206000
|  |  |  partypes = {
|  |  |  |  [1] = 413
|  |  |  |  [2] = 414
|  |  |  |  [3] = 415
|  |  |  }
|  |  }
|  |  [5] = {
|  |  |  map_id = 210400
|  |  |  partypes = {
|  |  |  |  [1] = 504
|  |  |  |  [2] = 506
|  |  |  |  [3] = 507
|  |  |  }
|  |  }
|  |  [6] = {
|  |  |  map_id = 200000
|  |  |  partypes = {
|  |  |  |  [1] = 501
|  |  |  |  [2] = 502
|  |  |  |  [3] = 417
|  |  |  }
|  |  }
|  |  [7] = {
|  |  |  map_id = 201000
|  |  |  partypes = {
|  |  |  |  [1] = 308
|  |  |  |  [2] = 301
|  |  |  |  [3] = 303
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHeroBoxRecord = {}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcFightInfoList = {
|  info_list = {
|  |  [1] = {
|  |  |  npc_type = 10024
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [10] = {
|  |  |  npc_type = 10007
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [11] = {
|  |  |  npc_type = 10008
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [12] = {
|  |  |  npc_type = 10009
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [13] = {
|  |  |  npc_type = 10011
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [14] = {
|  |  |  npc_type = 10012
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [15] = {
|  |  |  npc_type = 10013
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [16] = {
|  |  |  npc_type = 10000
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [17] = {
|  |  |  npc_type = 10023
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [18] = {
|  |  |  npc_type = 10014
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [19] = {
|  |  |  npc_type = 10015
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [2] = {
|  |  |  npc_type = 10002
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [20] = {
|  |  |  npc_type = 10016
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [21] = {
|  |  |  npc_type = 10017
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [22] = {
|  |  |  npc_type = 10018
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [23] = {
|  |  |  npc_type = 10019
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [24] = {
|  |  |  npc_type = 10020
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [25] = {
|  |  |  npc_type = 10021
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [26] = {
|  |  |  npc_type = 10022
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [3] = {
|  |  |  npc_type = 10010
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [4] = {
|  |  |  npc_type = 10001
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [5] = {
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [6] = {
|  |  |  npc_type = 10003
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [7] = {
|  |  |  npc_type = 10004
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [8] = {
|  |  |  npc_type = 10005
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [9] = {
|  |  |  npc_type = 10006
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2COnlineGift = {
|  onlinetime = 16573
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterScene = {
|  eid = 25
|  pos_info = {
|  |  face_y = 186114
|  |  x = 37442
|  |  y = 7051
|  }
|  scene_id = 12
}
[core/global.lua:59]:<color=#ffeb04>result  nil</color>
[core/global.lua:59]:<color=#ffeb04>=00000=  userdata: 0x82259cb0 nil</color>
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = **********
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/07/05 22:07:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>读取地图： 1020</color>
[core/global.lua:59]:<color=#ffeb04>Unity2017.4.30f1 localScale</color>
[logic/ui/CViewCtrl.lua:104]:CLoginView     CloseView
[logic/base/CResCtrl.lua:599]:res gc step start!
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[core/global.lua:59]:<color=#ffeb04>地图加载完成: 1020 ,当前地图: nil</color>
[logic/map/CMapCtrl.lua:1134]:<color=#00FF00> >>> .AddTaskChapterNpcNpc | 实例一个跟踪Npc | Table:AddTraceNpc </color>
[core/table.lua:94]:Table = {}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartnerList = {
|  partner_list = {
|  |  [1] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 341
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 65
|  |  |  equip_list = {
|  |  |  |  [1] = 4
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 1050
|  |  |  grade = 2
|  |  |  hp = 1992
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 1992
|  |  |  model_info = {
|  |  |  |  shape = 302
|  |  |  |  skin = 203020
|  |  |  }
|  |  |  name = "重华"
|  |  |  parid = 1
|  |  |  partner_type = 302
|  |  |  patahp = 1992
|  |  |  power = 730
|  |  |  power_rank = 97
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 600
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30201
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30202
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30203
|  |  |  |  }
|  |  |  }
|  |  |  speed = 420
|  |  |  star = 1
|  |  }
|  |  [2] = {
|  |  |  abnormal_attr_ratio = 600
|  |  |  amount = 1
|  |  |  attack = 273
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 67
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  grade = 1
|  |  |  mask = "fffffefe"
|  |  |  max_hp = 1862
|  |  |  model_info = {
|  |  |  |  shape = 313
|  |  |  |  skin = 203130
|  |  |  }
|  |  |  name = "檀"
|  |  |  parid = 2
|  |  |  partner_type = 313
|  |  |  patahp = 1862
|  |  |  power = 879
|  |  |  power_rank = 21
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 700
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31301
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31302
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31303
|  |  |  |  }
|  |  |  }
|  |  |  speed = 665
|  |  |  star = 2
|  |  }
|  |  [3] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 349
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 62
|  |  |  equip_list = {
|  |  |  |  [1] = 5
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 1050
|  |  |  grade = 2
|  |  |  hp = 1929
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 1929
|  |  |  model_info = {
|  |  |  |  shape = 502
|  |  |  |  skin = 205020
|  |  |  }
|  |  |  name = "马面面"
|  |  |  parid = 3
|  |  |  partner_type = 502
|  |  |  patahp = 1929
|  |  |  power = 728
|  |  |  power_rank = 58
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 500
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50201
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50202
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50203
|  |  |  |  }
|  |  |  }
|  |  |  speed = 315
|  |  |  star = 1
|  |  }
|  |  [4] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 294
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 600
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 63
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 1050
|  |  |  grade = 2
|  |  |  hp = 2138
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 2138
|  |  |  model_info = {
|  |  |  |  shape = 403
|  |  |  |  skin = 204030
|  |  |  }
|  |  |  name = "蛇姬"
|  |  |  parid = 4
|  |  |  partner_type = 403
|  |  |  patahp = 2138
|  |  |  power = 708
|  |  |  power_rank = 59
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 600
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40301
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40302
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40303
|  |  |  |  }
|  |  |  }
|  |  |  speed = 735
|  |  |  star = 1
|  |  }
|  |  [5] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 374
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 1000
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 70
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 7050
|  |  |  grade = 8
|  |  |  hp = 2688
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 2688
|  |  |  model_info = {
|  |  |  |  shape = 501
|  |  |  |  skin = 205010
|  |  |  }
|  |  |  name = "阿坊"
|  |  |  parid = 5
|  |  |  partner_type = 501
|  |  |  patahp = 2688
|  |  |  power = 938
|  |  |  power_rank = 39
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 400
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50101
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50102
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50103
|  |  |  |  }
|  |  |  }
|  |  |  speed = 70
|  |  |  star = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartner = {
|  fight_info = {
|  |  [1] = {
|  |  |  parid = 1
|  |  |  pos = 1
|  |  }
|  |  [2] = {
|  |  |  parid = 3
|  |  |  pos = 2
|  |  }
|  |  [3] = {
|  |  |  parid = 4
|  |  |  pos = 3
|  |  }
|  |  [4] = {
|  |  |  parid = 5
|  |  |  pos = 4
|  |  }
|  }
|  owned_equip_list = {
|  |  [1] = 6101001
|  }
|  owned_partner_list = {
|  |  [1] = 313
|  |  [2] = 403
|  |  [3] = 501
|  |  [4] = 302
|  |  [5] = 502
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPicturePosList = {}
[core/table.lua:94]:-->Net Receive: skill.GS2CLoginSkill = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3001
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3002
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  needcost = 15
|  |  |  sk = 3004
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  needcost = 25
|  |  |  sk = 3005
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3006
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3007
|  |  |  type = 1
|  |  }
|  }
}
[logic/skill/CSkillCtrl.lua:112]:技能数据登陆协议返回:
[core/table.lua:94]:Table = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3001
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3002
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  needcost = 15
|  |  |  sk = 3004
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  needcost = 25
|  |  |  sk = 3005
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3006
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3007
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginParSoulPlan = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 6754
|  |  |  type = "yk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 6754
|  |  |  type = "zsk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
|  mask = "c"
}
[core/table.lua:94]:CNetCtrl解析mask: GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 6754
|  |  |  type = "yk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 6754
|  |  |  type = "zsk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: store.GS2CPayForColorCoinInfo = {
|  colorcoin_list = {
|  |  [1] = {
|  |  |  key = "goldcoinstore_1005"
|  |  }
|  |  [10] = {
|  |  |  key = "goldcoinstore_2004"
|  |  }
|  |  [11] = {
|  |  |  key = "goldcoinstore_2005"
|  |  }
|  |  [12] = {
|  |  |  key = "goldcoinstore_2006"
|  |  }
|  |  [13] = {
|  |  |  key = "goldcoinstore_2007"
|  |  }
|  |  [14] = {
|  |  |  key = "goldcoinstore_1003"
|  |  }
|  |  [15] = {
|  |  |  key = "goldcoinstore_1004"
|  |  }
|  |  [2] = {
|  |  |  key = "goldcoinstore_1006"
|  |  }
|  |  [3] = {
|  |  |  key = "goldcoinstore_1007"
|  |  }
|  |  [4] = {
|  |  |  key = "goldcoinstore_1008"
|  |  }
|  |  [5] = {
|  |  |  key = "goldcoinstore_1001"
|  |  }
|  |  [6] = {
|  |  |  key = "goldcoinstore_1002"
|  |  }
|  |  [7] = {
|  |  |  key = "goldcoinstore_2001"
|  |  }
|  |  [8] = {
|  |  |  key = "goldcoinstore_2002"
|  |  }
|  |  [9] = {
|  |  |  key = "goldcoinstore_2003"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeRewrad = {
|  end_time = 1687276800
|  start_time = 1686672000
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CRefreshRewardBack = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeScore = {
|  cur_id = 1
|  score_info = {}
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CDailySignInfo = {
|  sign_info = {
|  |  [1] = {
|  |  |  key = "week"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayBuy = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CHistoryCharge = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFirstChargeUI = {
|  bOpen = true
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliPoint = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawCnt = {
|  cnt = 3
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliTime = {
|  endtime = 1687276800
|  starttime = 1686672000
}
[core/table.lua:94]:-->Net Receive: house.GS2CEnterHouse = {
|  buff_info = {}
|  furniture_info = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  lock_status = 1
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  type = 2
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  type = 3
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  type = 4
|  |  }
|  |  [5] = {
|  |  |  level = 1
|  |  |  type = 5
|  |  }
|  }
|  handle_type = 1
|  max_train = 1
|  max_warm_degree = 1000
|  owner_pid = 10001
|  partner_info = {
|  |  [1] = {
|  |  |  coin = 2007
|  |  |  type = 1001
|  |  }
|  }
|  warm_degree = 98
}
[core/table.lua:94]:-->Net Receive: house.GS2CPartnerExchangeUI = {
|  handle_type = 1
|  love_cnt = 10
|  max_gift_cnt = 10
|  max_love_cnt = 10
|  partner_gift_cnt = 10
}
[core/table.lua:94]:-->Net Receive: house.GS2COpenWorkDesk = {
|  desk_info = {
|  |  [1] = {
|  |  |  lock_status = 1
|  |  |  pos = 1
|  |  |  status = 1
|  |  }
|  |  [2] = {
|  |  |  pos = 2
|  |  |  status = 1
|  |  }
|  |  [3] = {
|  |  |  pos = 3
|  |  |  status = 1
|  |  }
|  |  [4] = {
|  |  |  lock_status = 1
|  |  |  pos = 4
|  |  |  status = 1
|  |  }
|  }
|  handle_type = 1
|  owner_pid = 10001
}
[core/table.lua:94]:-->Net Receive: player.GS2CTodayInfo = {
|  info = {
|  |  mask = "0"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: todyInfo = {}
[core/table.lua:94]:-->Net Receive: player.GS2CGameShare = {
|  game_share = {
|  |  [1] = {
|  |  |  type = "picture_share"
|  |  }
|  |  [2] = {
|  |  |  type = "skin_share"
|  |  }
|  |  [3] = {
|  |  |  type = "draw_card_share"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginRewardInfo = {
|  breed_val = 1
|  login_day = 3
|  rewarded_day = 7
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  mask = "1997c2e0000000"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  give_org_equip = {}
|  give_org_wish = {}
|  is_equip_wish = 0
|  is_org_wish = 0
|  org_build_status = 0
|  org_build_time = 0
|  org_id = 0
|  org_leader = ""
|  org_level = 0
|  org_pos = 0
|  org_red_packet = 0
|  org_sign_reward = 0
|  org_status = 0
|  orgname = ""
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {
|  infolist = {
|  |  [1] = {
|  |  |  blist = {
|  |  |  |  [1] = 1
|  |  |  |  [2] = 2
|  |  |  |  [3] = 3
|  |  |  }
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  blist = {
|  |  |  |  [1] = 8
|  |  |  }
|  |  |  id = 4
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayMain = {
|  server_day = 21
}
[core/table.lua:94]:-->Net Receive: task.GS2CLoginAchieveTask = {
|  info = {
|  |  [1] = {
|  |  |  achievetype = 2
|  |  |  degree = 1
|  |  |  describe = "穿戴一件符文"
|  |  |  name = "穿戴符文（1）"
|  |  |  target = 1
|  |  |  taskid = 31524
|  |  }
|  |  [2] = {
|  |  |  achievetype = 1
|  |  |  describe = "领取1次在线奖励"
|  |  |  name = "在线奖励"
|  |  |  target = 1
|  |  |  taskid = 31001
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>LoginAchieveTask</color>
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  abnormal_attr_ratio = 560
|  |  attack = 328
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 43
|  |  mask = "87ff00"
|  |  max_hp = 3173
|  |  power = 991
|  |  res_abnormal_ratio = 560
|  |  res_critical_ratio = 500
|  |  speed = 753
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 560
|  attack = 328
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 43
|  hp = 0
|  max_hp = 3173
|  power = 991
|  res_abnormal_ratio = 560
|  res_critical_ratio = 500
|  speed = 753
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1102
|  |  |  }
|  |  |  name = "峰馆长"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 2
|  |  npctype = 5040
|  }
|  eid = 1
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 1
|  pos_info = {
|  |  x = 31800
|  |  y = 17600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1102
|  }
|  name = "峰馆长"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1508
|  |  |  }
|  |  |  name = "小叶"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 5
|  |  npctype = 5041
|  }
|  eid = 2
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 2
|  pos_info = {
|  |  x = 40000
|  |  y = 14600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1508
|  }
|  name = "小叶"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1510
|  |  |  }
|  |  |  name = "小佳"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 11
|  |  npctype = 5043
|  }
|  eid = 4
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 4
|  pos_info = {
|  |  x = 27000
|  |  y = 4000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1510
|  }
|  name = "小佳"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10117
|  |  triggerNpc = 5043
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1510
|  |  |  }
|  |  |  name = "小兰"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 14
|  |  npctype = 5044
|  }
|  eid = 5
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 5
|  pos_info = {
|  |  x = 19000
|  |  y = 4000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1510
|  }
|  name = "小兰"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10117
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1503
|  |  |  }
|  |  |  name = "喵小布"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 21
|  |  npctype = 5047
|  }
|  eid = 6
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 6
|  pos_info = {
|  |  x = 33000
|  |  y = 9500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1503
|  }
|  name = "喵小布"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10117
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 414
|  |  |  }
|  |  |  name = "蓝堂昼"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 75
|  |  npctype = 5006
|  }
|  eid = 14
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 14
|  pos_info = {
|  |  x = 28000
|  |  y = 14500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 414
|  }
|  name = "蓝堂昼"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 311
|  |  |  }
|  |  |  name = "花常酒"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 78
|  |  npctype = 5007
|  }
|  eid = 15
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 15
|  pos_info = {
|  |  x = 35600
|  |  y = 13400
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 311
|  }
|  name = "花常酒"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 508
|  |  |  }
|  |  |  name = "魂夕"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 81
|  |  npctype = 5008
|  }
|  eid = 16
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 16
|  pos_info = {
|  |  x = 24000
|  |  y = 12900
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 508
|  }
|  name = "魂夕"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1150
|  |  |  }
|  |  |  name = "飞龙哥"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 199
|  |  npctype = 5010
|  }
|  eid = 18
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 18
|  pos_info = {
|  |  x = 29000
|  |  y = 3500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1150
|  }
|  name = "飞龙哥"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1151
|  |  |  }
|  |  |  name = "邓酒爷"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 202
|  |  npctype = 5011
|  }
|  eid = 19
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 19
|  pos_info = {
|  |  x = 22300
|  |  y = 5100
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1151
|  }
|  name = "邓酒爷"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: title.GS2CTitleInfoList = {
|  infos = {
|  |  [1] = {
|  |  |  name = "8段"
|  |  |  tid = 1008
|  |  }
|  |  [10] = {
|  |  |  name = "应用宝贵族"
|  |  |  tid = 1017
|  |  }
|  |  [11] = {
|  |  |  name = "王者公会"
|  |  |  tid = 1018
|  |  }
|  |  [12] = {
|  |  |  name = "王者公会会长"
|  |  |  tid = 1019
|  |  }
|  |  [13] = {
|  |  |  name = "黄金公会"
|  |  |  tid = 1020
|  |  }
|  |  [14] = {
|  |  |  name = "黄金公会会长"
|  |  |  tid = 1021
|  |  }
|  |  [15] = {
|  |  |  name = "白银公会"
|  |  |  tid = 1022
|  |  }
|  |  [16] = {
|  |  |  name = "白银公会会长"
|  |  |  tid = 1023
|  |  }
|  |  [17] = {
|  |  |  name = "明日之星"
|  |  |  tid = 1024
|  |  }
|  |  [18] = {
|  |  |  name = "独孤求败"
|  |  |  tid = 1025
|  |  }
|  |  [19] = {
|  |  |  name = "谁与争锋"
|  |  |  tid = 1026
|  |  }
|  |  [2] = {
|  |  |  name = "测-贵族宝"
|  |  |  tid = 1009
|  |  }
|  |  [20] = {
|  |  |  name = "所向披靡"
|  |  |  tid = 1027
|  |  }
|  |  [21] = {
|  |  |  name = "地牢终结者"
|  |  |  tid = 1028
|  |  }
|  |  [22] = {
|  |  |  name = "地牢征服者"
|  |  |  tid = 1029
|  |  }
|  |  [23] = {
|  |  |  name = "地牢破坏者"
|  |  |  tid = 1030
|  |  }
|  |  [24] = {
|  |  |  name = "眯眯眼是变态"
|  |  |  tid = 1031
|  |  }
|  |  [25] = {
|  |  |  name = "邻家妹妹"
|  |  |  tid = 1032
|  |  }
|  |  [26] = {
|  |  |  name = "兄控"
|  |  |  tid = 1033
|  |  }
|  |  [27] = {
|  |  |  name = "农夫三拳"
|  |  |  tid = 1034
|  |  }
|  |  [28] = {
|  |  |  name = "赌神"
|  |  |  tid = 1035
|  |  }
|  |  [29] = {
|  |  |  name = "神之右手"
|  |  |  tid = 1036
|  |  }
|  |  [3] = {
|  |  |  name = "测-我就是托"
|  |  |  tid = 1010
|  |  }
|  |  [30] = {
|  |  |  name = "茶会噩梦"
|  |  |  tid = 1037
|  |  }
|  |  [31] = {
|  |  |  name = "我是傲娇"
|  |  |  tid = 1038
|  |  }
|  |  [32] = {
|  |  |  name = "闷骚美男子"
|  |  |  tid = 1039
|  |  }
|  |  [33] = {
|  |  |  name = "脸炮"
|  |  |  tid = 1040
|  |  }
|  |  [34] = {
|  |  |  name = "妹控"
|  |  |  tid = 1041
|  |  }
|  |  [35] = {
|  |  |  name = "守夜人"
|  |  |  tid = 1042
|  |  }
|  |  [36] = {
|  |  |  name = "泡泡娘"
|  |  |  tid = 1043
|  |  }
|  |  [37] = {
|  |  |  name = "姐控"
|  |  |  tid = 1044
|  |  }
|  |  [38] = {
|  |  |  name = "迷妹"
|  |  |  tid = 1045
|  |  }
|  |  [39] = {
|  |  |  name = "小天使"
|  |  |  tid = 1046
|  |  }
|  |  [4] = {
|  |  |  name = "测-粗壮大腿"
|  |  |  tid = 1011
|  |  }
|  |  [40] = {
|  |  |  name = "欺诈兔子"
|  |  |  tid = 1047
|  |  }
|  |  [41] = {
|  |  |  name = "天然呆"
|  |  |  tid = 1048
|  |  }
|  |  [42] = {
|  |  |  name = "开局一条狗"
|  |  |  tid = 1049
|  |  }
|  |  [43] = {
|  |  |  name = "种竹专业户"
|  |  |  tid = 1050
|  |  }
|  |  [44] = {
|  |  |  name = "顾家好男人"
|  |  |  tid = 1051
|  |  }
|  |  [45] = {
|  |  |  name = "无敌小钢炮"
|  |  |  tid = 1052
|  |  }
|  |  [46] = {
|  |  |  name = "小豆丁"
|  |  |  tid = 1053
|  |  }
|  |  [47] = {
|  |  |  name = "浪里白条"
|  |  |  tid = 1054
|  |  }
|  |  [48] = {
|  |  |  name = "话唠"
|  |  |  tid = 1055
|  |  }
|  |  [49] = {
|  |  |  name = "角落种蘑菇"
|  |  |  tid = 1056
|  |  }
|  |  [5] = {
|  |  |  name = "攻防战第一公会"
|  |  |  tid = 1012
|  |  }
|  |  [50] = {
|  |  |  name = "天然疯"
|  |  |  tid = 1057
|  |  }
|  |  [51] = {
|  |  |  name = "吃货"
|  |  |  tid = 1058
|  |  }
|  |  [52] = {
|  |  |  name = "控制狂"
|  |  |  tid = 1059
|  |  }
|  |  [53] = {
|  |  |  name = "女王大人"
|  |  |  tid = 1060
|  |  }
|  |  [54] = {
|  |  |  name = "八卦小王子"
|  |  |  tid = 1061
|  |  }
|  |  [55] = {
|  |  |  name = "三无"
|  |  |  tid = 1062
|  |  }
|  |  [56] = {
|  |  |  name = "养得累"
|  |  |  tid = 1063
|  |  }
|  |  [57] = {
|  |  |  name = "扑街之王"
|  |  |  tid = 1064
|  |  }
|  |  [58] = {
|  |  |  name = "抖S"
|  |  |  tid = 1065
|  |  }
|  |  [59] = {
|  |  |  name = "工作狂人"
|  |  |  tid = 1066
|  |  }
|  |  [6] = {
|  |  |  name = "攻防战第一司令"
|  |  |  tid = 1013
|  |  }
|  |  [60] = {
|  |  |  name = "精分中毒患者"
|  |  |  tid = 1067
|  |  }
|  |  [61] = {
|  |  |  name = "神神叨叨"
|  |  |  tid = 1068
|  |  }
|  |  [62] = {
|  |  |  name = "浮夸之神"
|  |  |  tid = 1069
|  |  }
|  |  [63] = {
|  |  |  name = "抖M"
|  |  |  tid = 1070
|  |  }
|  |  [64] = {
|  |  |  name = "自恋狂"
|  |  |  tid = 1071
|  |  }
|  |  [65] = {
|  |  |  name = "天然黑"
|  |  |  tid = 1072
|  |  }
|  |  [66] = {
|  |  |  name = "为所欲为"
|  |  |  tid = 1073
|  |  }
|  |  [67] = {
|  |  |  name = "氪金大佬"
|  |  |  tid = 1074
|  |  }
|  |  [68] = {
|  |  |  name = "应用宝帝王"
|  |  |  tid = 1075
|  |  }
|  |  [69] = {
|  |  |  name = "应用宝至尊"
|  |  |  tid = 1076
|  |  }
|  |  [7] = {
|  |  |  name = "梦魇之主"
|  |  |  tid = 1014
|  |  }
|  |  [70] = {
|  |  |  name = "公会新人"
|  |  |  tid = 1077
|  |  }
|  |  [71] = {
|  |  |  name = "公会成员"
|  |  |  tid = 1078
|  |  }
|  |  [72] = {
|  |  |  name = "公会精英"
|  |  |  tid = 1079
|  |  }
|  |  [73] = {
|  |  |  name = "公会副会"
|  |  |  tid = 1080
|  |  }
|  |  [74] = {
|  |  |  name = "公会会长"
|  |  |  tid = 1081
|  |  }
|  |  [75] = {
|  |  |  name = "情侣专属称谓"
|  |  |  tid = 1082
|  |  }
|  |  [76] = {
|  |  |  name = "命运之子"
|  |  |  tid = 1083
|  |  }
|  |  [77] = {
|  |  |  name = "不做大多数"
|  |  |  tid = 1084
|  |  }
|  |  [78] = {
|  |  |  name = "1段"
|  |  |  tid = 1001
|  |  }
|  |  [79] = {
|  |  |  name = "2段"
|  |  |  tid = 1002
|  |  }
|  |  [8] = {
|  |  |  name = "梦魇骑士"
|  |  |  tid = 1015
|  |  }
|  |  [80] = {
|  |  |  name = "3段"
|  |  |  tid = 1003
|  |  }
|  |  [81] = {
|  |  |  name = "4段"
|  |  |  tid = 1004
|  |  }
|  |  [82] = {
|  |  |  name = "5段"
|  |  |  tid = 1005
|  |  }
|  |  [83] = {
|  |  |  name = "6段"
|  |  |  tid = 1006
|  |  }
|  |  [84] = {
|  |  |  name = "7段"
|  |  |  tid = 1007
|  |  }
|  |  [9] = {
|  |  |  name = "梦魇猎人"
|  |  |  tid = 1016
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 21016
}
[core/table.lua:94]:-->Net Receive: friend.GS2CApplyProfile = {
|  profile_list = {
|  |  [1] = {
|  |  |  pro = {
|  |  |  |  grade = 10
|  |  |  |  mask = "fe"
|  |  |  |  name = "毛蛋"
|  |  |  |  pid = 10002
|  |  |  |  school = 1
|  |  |  |  shape = 120
|  |  |  }
|  |  }
|  |  [2] = {
|  |  |  pro = {
|  |  |  |  grade = 33
|  |  |  |  mask = "fe"
|  |  |  |  name = "迷途"
|  |  |  |  pid = 10003
|  |  |  |  school = 1
|  |  |  |  shape = 120
|  |  |  }
|  |  }
|  |  [3] = {
|  |  |  pro = {
|  |  |  |  grade = 25
|  |  |  |  mask = "fe"
|  |  |  |  name = "昂首凤梨"
|  |  |  |  pid = 10005
|  |  |  |  school = 3
|  |  |  |  shape = 150
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: friend = {
|  friend_degree = 0
|  grade = 10
|  name = "毛蛋"
|  pid = 10002
|  relation = 0
|  school = 1
|  shape = 120
}
[core/table.lua:94]:CNetCtrl解析mask: friend = {
|  friend_degree = 0
|  grade = 33
|  name = "迷途"
|  pid = 10003
|  relation = 0
|  school = 1
|  shape = 120
}
[core/table.lua:94]:CNetCtrl解析mask: friend = {
|  friend_degree = 0
|  grade = 25
|  name = "昂首凤梨"
|  pid = 10005
|  relation = 0
|  school = 3
|  shape = 150
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10107</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  喵小布 1503 [32,1.3,0] -6 false</color>
[logic/platform/CSdkCtrl.lua:380]:sdk upload: not init
[core/table.lua:94]:Table = {
|  [1] = "16"
|  [2] = "20"
}
[core/global.lua:59]:<color=#ffeb04>14 16 20</color>
[core/table.lua:94]:Table = {
|  [1] = "21"
|  [2] = "24"
}
[core/global.lua:59]:<color=#ffeb04>14 21 24</color>
[core/table.lua:94]:Table = {
|  [1] = "25"
|  [2] = "27"
}
[core/global.lua:59]:<color=#ffeb04>14 25 27</color>
[logic/base/CResCtrl.lua:626]:res gc finish!
[logic/ui/CViewCtrl.lua:94]:CPartnerMainView ShowView
[logic/ui/CViewBase.lua:125]:CPartnerMainView LoadDone!
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [34.2,9,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 喵~喵呜喵~</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1688566056
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/07/05 22:07:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [35.1,13.8,0] 30</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[logic/ui/CViewCtrl.lua:104]:CPartnerMainView     CloseView
[logic/ui/CViewCtrl.lua:94]:CPartnerMainView ShowView
[logic/ui/CViewBase.lua:125]:CPartnerMainView LoadDone!
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[logic/ui/CViewCtrl.lua:104]:CPartnerMainView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1688566066
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/07/05 22:07:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CChat = {
|  cmd = "
官网：ａｓｙ３３.ｃｏ
官网：ａｓｙ３３.ｃｏｍ，下载万梦手游，铳自动３浙ｍ，下载万梦手游，铳自动３"
|  role_info = {
|  |  grade = 26
|  |  name = "凉爽的乌贼"
|  |  pid = 10008
|  |  shape = 150
|  }
|  type = 1
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1688566076
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/07/05 22:07:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1688566086
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/07/05 22:08:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x64aa6f50"
|  |  AssociatedPick = "function: 0x64aa6fb0"
|  |  AssociatedSubmit = "function: 0x64aa6f80"
|  |  CreateDefalutData = "function: 0x64aa4f30"
|  |  GetChaptetFubenData = "function: 0x64aa6c70"
|  |  GetProgressThing = "function: 0x64aa7010"
|  |  GetRemainTime = "function: 0x64aa4ec8"
|  |  GetStatus = "function: 0x64aa7070"
|  |  GetTaskClientExtStrDic = "function: 0x64aa6fe0"
|  |  GetTaskTypeSpriteteName = "function: 0x64aa6c40"
|  |  GetTraceInfo = "function: 0x64aa6d98"
|  |  GetTraceNpcType = "function: 0x64aa6c10"
|  |  GetValue = "function: 0x64aa6cd8"
|  |  IsAbandon = "function: 0x64aa6d38"
|  |  IsAddEscortDynamicNpc = "function: 0x64aa73a8"
|  |  IsMissMengTask = "function: 0x64aa4ef8"
|  |  IsPassChaterFuben = "function: 0x64aa6ca0"
|  |  IsTaskSpecityAction = "function: 0x64aa6d68"
|  |  IsTaskSpecityCategory = "function: 0x64aa6f20"
|  |  New = "function: 0x64aab8a0"
|  |  NewByData = "function: 0x64aa4470"
|  |  RaiseProgressIdx = "function: 0x64aa7040"
|  |  RefreshTask = "function: 0x64aa6d08"
|  |  ResetEndTime = "function: 0x64aa4f98"
|  |  SetStatus = "function: 0x64aa73d8"
|  |  classname = "CTask"
|  |  ctor = "function: 0x64aa4e98"
|  }
|  m_CData = {
|  |  ChapterFb = "1,2"
|  |  autoDoNextTask = 10009
|  |  clientExtStr = ""
|  |  name = "质问"
|  |  submitNpcId = 10012
|  |  submitRewardStr = {
|  |  |  [1] = "R1008"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_ChapterFb = "1,2"
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10012
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 513
|  |  |  |  }
|  |  |  |  name = "夜叉"
|  |  |  |  npcid = 3916
|  |  |  |  npctype = 10012
|  |  |  |  pos_info = {
|  |  |  |  |  x = 25800
|  |  |  |  |  y = 11700
|  |  |  |  }
|  |  |  }
|  |  |  [2] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 404
|  |  |  |  }
|  |  |  |  name = "群众"
|  |  |  |  npcid = 3917
|  |  |  |  npctype = 10013
|  |  |  |  pos_info = {
|  |  |  |  |  x = 25000
|  |  |  |  |  y = 12600
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "与袭击者对话，试图获取有用信息。"
|  |  isdone = 0
|  |  name = "质问"
|  |  patrolinfo = {}
|  |  pickiteminfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10012
|  |  target = 10012
|  |  targetdesc = "一头雾水"
|  |  taskid = 10008
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1688566096
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/07/05 22:08:16
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1688566106
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/07/05 22:08:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1688566116
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/07/05 22:08:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CChat = {
|  cmd = "
官网：ａｓｙ３３.ｃｏ

官网：ａｓｙ３３.ｃｏｍ，下载万梦手游，铳自动３浙官网：ａｓｙ３３.ｃｏｍ"
|  role_info = {
|  |  grade = 26
|  |  name = "凉爽的乌贼"
|  |  pid = 10008
|  |  shape = 150
|  }
|  type = 1
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1688566126
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/07/05 22:08:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1688566136
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/07/05 22:08:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1688566146
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/07/05 22:09:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1688566156
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/07/05 22:09:16
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1688566166
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/07/05 22:09:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CChat = {
|  cmd = "
官网：ａｓｙ３３.ｃｏ


官网：ａｓｙ３３.ｃｏｍ，下载万梦手游，铳自动３浙官网：ａｓｙ３３.ｃｏ"
|  role_info = {
|  |  grade = 26
|  |  name = "凉爽的乌贼"
|  |  pid = 10008
|  |  shape = 150
|  }
|  type = 1
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1688566176
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/07/05 22:09:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10107</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10117
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10107</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  喵小布 1503 [32,1.3,0] -6 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [34.2,9,0] 360</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1688566186
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/07/05 22:09:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 喵~喵呜喵~</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [35.1,13.8,0] 30</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1688566196
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/07/05 22:09:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1688566206
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/07/05 22:10:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[logic/ui/CViewCtrl.lua:94]:CPartnerMainView ShowView
[logic/ui/CViewBase.lua:125]:CPartnerMainView LoadDone!
