with:849  hight:522
[logic/misc/CShareCtrl.lua:28]:jit    false    SSE2    SSE3    SSE4.1    BMI2    fold    cse    dce    fwd    dse    narrow    loop    abc    sink    fuse
[logic/login/CLoginCtrl.lua:5]:公告初始化...
[logic/base/CHttpCtrl.lua:32]:http get ->    https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials&client_id=rlU84dprH6I7sMjBkqM0gizl&client_secret=c15395c2433c4a904d22aa27eab8321b&
[logic/ui/CViewCtrl.lua:94]:CNotifyView ShowView
[logic/ui/CViewCtrl.lua:94]:CBottomView ShowView
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x534ceb60"
|  json_result = true
|  timer = 16
}
[logic/audio/CSpeechCtrl.lua:72]:OnTokenGet err
[logic/ui/CViewBase.lua:125]:CBottomView LoadDone!
[logic/base/CResCtrl.lua:199]:-->resource init done!!! 12
[logic/ui/CViewBase.lua:125]:CNotifyView LoadDone!
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {}
[logic/ui/CViewCtrl.lua:94]:CLoginView ShowView
[logic/base/CHttpCtrl.lua:32]:http get ->    http://*************:88/Note/note2311.json
[logic/ui/CViewBase.lua:125]:CLoginView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x829fde08"
|  json_result = true
|  timer = 58
}
[core/table.lua:94]:cb tResult-> = {
|  info = {
|  |  role_list = {}
|  |  server_info = {
|  |  |  groups = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 1
|  |  |  |  |  name = "1-10区"
|  |  |  |  }
|  |  |  }
|  |  |  infoList = {
|  |  |  |  [1] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]测试[-]"
|  |  |  |  |  |  |  |  title = "测试"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "测试"
|  |  |  |  }
|  |  |  }
|  |  |  ports = {}
|  |  |  role_list = ""
|  |  |  servers = {
|  |  |  |  [1] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 1
|  |  |  |  |  ip = "*************"
|  |  |  |  |  name = "测试区"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1692806400
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7011
|  |  |  |  |  |  [2] = 7012
|  |  |  |  |  |  [3] = 7013
|  |  |  |  |  |  [4] = 7014
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10001"
|  |  |  |  |  start_time = 1692806400
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  }
|  |  }
|  |  token = ""
|  }
}
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {
|  [1] = {
|  |  group_id = 1
|  |  id = 1
|  |  name = "1-10区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  id = 1
|  |  |  |  ip = "*************"
|  |  |  |  name = "测试区"
|  |  |  |  new = 0
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = 1692806400
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  |  [2] = 7012
|  |  |  |  |  [3] = 7013
|  |  |  |  |  [4] = 7014
|  |  |  |  }
|  |  |  |  server_id = 1
|  |  |  |  start_time = 1692806400
|  |  |  |  state = 1
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:设置公告: = {
|  [1] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]测试[-]"
|  |  |  |  |  title = "测试"
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "测试"
|  }
}
