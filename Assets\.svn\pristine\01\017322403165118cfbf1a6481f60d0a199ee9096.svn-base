module(...)
--dialogueani editor build
DATA={
	[1]={
		cmdList={
			[1]={
				args={
					[1]={[1]=2,},
					[2]={[1]=[[我是谁我在哪？]],},
					[3]={[1]=100,},
					[4]={[1]=[[空]],},
					[5]={[1]=0,},
					[6]={[1]=[[空]],},
					[7]={[1]=0,},
					[8]={[1]=[[空]],},
					[9]={[1]=0,},
				},
				cmdType=[[globalnpcani]],
				func=[[GNpcSay]],
				name=[[冒泡说话]],
			},
		},
		delay=2,
		idx=1,
		startTime=0,
		type=[[player]],
	},
}

CONFIG={
	isLoop=1,
	isStroy=2,
	isTrigger=0,
	loopTime=60,
	mapInfo=[[]],
	minTriggerLevel=1,
	name=[[剧场动画名_10324]],
}
