AssetManager.Instance already exist
0x0000000140E5253D (Unity) StackWalker::GetCurrentCallstack
0x0000000140E54231 (Unity) StackWalker::ShowCallstack
0x00000001406065B3 (Unity) GetStacktrace
0x0000000140603ABD (Unity) DebugStringToFile
0x0000000140603F1C (Unity) DebugStringToFile
0x0000000140E9239C (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000000E5CE2EB (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x000000000E5CE1D5 (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x000000002574D908 (Mono JIT Code) [Logger.cs:42] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000005265A519 (Mono JIT Code) [UnityEngineDebugBindings.gen.cs:121] UnityEngine.Debug:LogError (object) 
0x000000005265A2E6 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x000000000E5FB970 (Mono JIT Code) [AssetManager.cs:232] AssetPipeline.AssetManager:CreateInstance () 
0x000000005265A169 (Mono JIT Code) [AssetBundleBuilder.cs:894] AssetPipeline.AssetBundleBuilder:GenDynamicAtlas () 
0x0000000052659D9D (Mono JIT Code) [AssetBundleBuilder.cs:858] AssetPipeline.AssetBundleBuilder:<UpdateVersion>m__18 () 
0x0000000052A8CFEF (Mono JIT Code) [EditorApplicationBindings.gen.cs:232] UnityEditor.EditorApplication:Internal_CallDelayFunctions () 
0x00000000007B0A7E (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007FF8C6BE539F (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FF8C6B38385 (mono) [object.c:2623] mono_runtime_invoke 
0x000000014033E455 (Unity) mono_runtime_invoke_profiled
0x00000001404CD261 (Unity) CallStaticMonoMethod
0x00000001404CD507 (Unity) CallStaticMonoMethod
0x0000000140BE9E78 (Unity) Application::TickTimer
0x0000000140E4CC6E (Unity) FindMonoBinaryToUse
0x0000000140E4E2A1 (Unity) WinMain
0x00000001415B2F10 (Unity) strnlen
0x00007FF91E307034 (KERNEL32) BaseThreadInitThunk


AssetManager.Instance already exist
   at GameDebug.LogError(System.String msg, System.String stackTrace)
   at AssetPipeline.AssetManager.CreateInstance()
   at AssetPipeline.AssetBundleBuilder.GenDynamicAtlas()
   at AssetPipeline.AssetBundleBuilder.<UpdateVersion>m__18()
   at UnityEditor.EditorApplication.Internal_CallDelayFunctions()

