//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class CLASSTAG_QiniuCloudWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(QiniuCloud), typeof(System.Object));
		<PERSON><PERSON>RegFunction("SetUploadCallback",FUNCTAG_SetUploadCallback);
		<PERSON><PERSON>unction("SetDownloadCallback",FUNCTAG_SetDownloadCallback);
		L<PERSON>RegFunction("UploadFile",FUNCTAG_UploadFile);
		L.RegFunction("DownloadFile",FUNCTAG_DownloadFile);
        L.RegFunction("SetAppID",FUNCTAG_SetAppID);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_SetUploadCallback(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			LuaFunction arg0 = ToLua.CheckLuaFunction(L, 1);
			QiniuCloud.SetUploadCallback(arg0);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_SetDownloadCallback(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			LuaFunction arg0 = ToLua.CheckLuaFunction(L, 1);
			QiniuCloud.SetDownloadCallback(arg0);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_UploadFile(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			string arg0 = ToLua.CheckString(L, 1);
			string arg1 = ToLua.CheckString(L, 2);
			QiniuCloud.UploadFile(arg0, arg1);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_SetAppID(IntPtr L)
	{
		try
		{
            ToLua.CheckArgsCount(L, 2);
            string arg0 = ToLua.CheckString(L, 1);
            string arg1 = ToLua.CheckString(L, 2);
            QiniuCloud.FUNCTAG_SetAppID(arg0, arg1);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_DownloadFile(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			QiniuCloud.DownloadFile(arg0);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

