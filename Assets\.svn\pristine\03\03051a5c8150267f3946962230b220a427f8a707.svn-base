with:857  hight:420
[logic/misc/CShareCtrl.lua:18]:ShareCallback ctor
[logic/login/CLoginCtrl.lua:5]:公告初始化...
[logic/base/CHttpCtrl.lua:32]:http get ->    https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials&client_id=rlU84dprH6I7sMjBkqM0gizl&client_secret=c15395c2433c4a904d22aa27eab8321b&
[logic/ui/CViewCtrl.lua:94]:CNotifyView ShowView
[logic/ui/CViewCtrl.lua:94]:CBottomView ShowView
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x285bbc80"
|  json_result = true
|  timer = 16
}
[logic/audio/CSpeechCtrl.lua:72]:OnTokenGet err
[logic/ui/CViewBase.lua:125]:CBottomView LoadDone!
[logic/base/CResCtrl.lua:198]:-->resource init done!!! 12
[logic/ui/CViewBase.lua:125]:CNotifyView LoadDone!
[core/table.lua:94]:CServerCtrl.SetServerData: = {
|  [1] = {
|  |  group_id = 1
|  |  name = "1-10区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线1区"
|  |  |  |  new = 0
|  |  |  |  server_id = 1001
|  |  |  |  state = 2
|  |  |  }
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:94]:CLoginView ShowView
[logic/base/CHttpCtrl.lua:32]:http get ->    http://************:88/Note/note.json
[logic/login/CLoginAccountPage.lua:78]:SendToCenterServer:    http://************:88/Note/note.json    
[logic/ui/CViewBase.lua:125]:CLoginView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x4fb19af8"
|  json_result = true
|  timer = 51
}
[core/table.lua:94]:cb tResult-> = {
|  info = {
|  |  role_list = {}
|  |  server_info = {
|  |  |  infoList = {
|  |  |  |  content = {
|  |  |  |  |  contents = {
|  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  text = "1、转出条件：上款游戏充值超过100元的玩家
2、转入条件：新游戏充值超过100元"
|  |  |  |  |  |  |  title = "一、参与转游条件"
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  pic = ""
|  |  |  |  |  text = "ceshi"
|  |  |  |  }
|  |  |  |  hot = 1
|  |  |  |  title = "系统公告"
|  |  |  }
|  |  |  ports = ""
|  |  |  role_list = ""
|  |  }
|  |  token = ""
|  }
}
[logic/login/CLoginAccountPage.lua:84]:info    table:0x56F74208
[logic/login/CLoginCtrl.lua:401]:dInfo.server_info.groups    nil
[core/table.lua:94]:CServerCtrl.SetServerData: = {}
[logic/login/CLoginCtrl.lua:98]:dNotice.content    循环开始喽...    table:0x56F742D0
[core/table.lua:94]:设置公告: = {
|  content = {
|  |  contents = {
|  |  |  [1] = {
|  |  |  |  text = "1、转出条件：上款游戏充值超过100元的玩家
2、转入条件：新游戏充值超过100元"
|  |  |  |  title = "一、参与转游条件"
|  |  |  }
|  |  }
|  |  pic = ""
|  |  text = "ceshi"
|  }
|  hot = 1
|  title = "系统公告"
}
[logic/login/CLoginCtrl.lua:106]:table.print ===== 设置公告    1584c87338ad8f7dad6a98254980266b
[logic/ui/CViewCtrl.lua:94]:CLoginNoticeView ShowView
[logic/ui/CViewBase.lua:125]:CLoginNoticeView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CLoginNoticeView     CloseView
