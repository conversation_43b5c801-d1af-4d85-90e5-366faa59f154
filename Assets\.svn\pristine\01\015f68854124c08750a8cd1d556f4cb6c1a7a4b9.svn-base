module(...)
--magic editor build
DATA={
	atk_stophit=true,
	cmds={
		[1]={
			args={action_name=[[run]],action_time=0.6,excutor=[[atkobj]],},
			func_name=[[PlayAction]],
			start_time=0,
		},
		[2]={
			args={
				begin_type=[[current]],
				calc_face=true,
				ease_type=[[Linear]],
				end_relative={base_pos=[[vic]],depth=0,relative_angle=0,relative_dis=1.65,},
				end_type=[[end_relative]],
				excutor=[[atkobj]],
				look_at_pos=true,
				move_time=0.6,
				move_type=[[line]],
			},
			func_name=[[Move]],
			start_time=0,
		},
		[3]={args={alive_time=0.5,},func_name=[[Name]],start_time=0,},
		[4]={
			args={sound_path=[[Magic/sound_magic_160002_1.wav]],sound_rate=1,},
			func_name=[[PlaySound]],
			start_time=0,
		},
		[5]={args={},func_name=[[MagcAnimStart]],start_time=0.6,},
		[6]={
			args={action_name=[[attack2]],excutor=[[atkobj]],},
			func_name=[[PlayAction]],
			start_time=0.6,
		},
		[7]={
			args={
				alive_time=2,
				effect={
					is_cached=true,
					magic_layer=[[center]],
					path=[[Effect/Magic/magic_eff_1600/Prefabs/magic_eff_160002_att.prefab]],
					preload=true,
				},
				effect_dir_type=[[relative]],
				effect_pos={base_pos=[[atk]],depth=0,relative_angle=0,relative_dis=0,},
				excutor=[[vicobj]],
				relative_dir={base_pos=[[vic]],depth=0,relative_angle=0,relative_dis=-1,},
			},
			func_name=[[StandEffect]],
			start_time=0.6,
		},
		[8]={args={},func_name=[[MagcAnimEnd]],start_time=1.3,},
		[9]={
			args={
				consider_hight=false,
				damage_follow=true,
				face_atk=true,
				hurt_delta=0,
				play_anim=true,
			},
			func_name=[[VicHitInfo]],
			start_time=1.3,
		},
		[10]={args={},func_name=[[End]],start_time=2,},
	},
	group_cmds={},
	magic_anim_end_time=1.3,
	magic_anim_start_time=0.6,
	pre_load_res={[1]=[[Effect/Magic/magic_eff_1600/Prefabs/magic_eff_160002_att.prefab]],},
	run_env=[[war]],
	type=1,
	wait_goback=true,
}
