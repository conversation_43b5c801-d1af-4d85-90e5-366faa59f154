%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Noise_317_12
  m_Shader: {fileID: 4800000, guid: 87b101305105b8c41ab3705e4f01b82f, type: 3}
  m_ShaderKeywords: _EMISSION _POSTERIZE_SWITCH_ON
  m_LightmapFlags: 1
  m_CustomRenderQueue: 3001
  stringTagMap: {}
  m_SavedProperties:
    serializedVersion: 2
    m_TexEnvs:
    - first:
        name: _BumpMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailAlbedoMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailMask
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailNormalMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _EmissionMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _Liuguang01
      second:
        m_Texture: {fileID: 2800000, guid: 887d45145f23c924a94ff8fca910e1ad, type: 3}
        m_Scale: {x: 2, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _Liuguang02
      second:
        m_Texture: {fileID: 2800000, guid: 6475569e57de23843b4c52854998a6af, type: 3}
        m_Scale: {x: 1, y: 0.5}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MASk
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MainTex
      second:
        m_Texture: {fileID: 2800000, guid: 73dea759706fcdf4ab79cced1196c230, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: -0.03, y: 0}
    - first:
        name: _Mask
      second:
        m_Texture: {fileID: 2800000, guid: 844153d09fd554147a4aa6c09bc92887, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MetallicGlossMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _OcclusionMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _ParallaxMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _TEX1
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _TEX2
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - first:
        name: _ALPHA
      second: 2
    - first:
        name: _Alpha
      second: 0.7
    - first:
        name: _BumpScale
      second: 1
    - first:
        name: _Cutoff
      second: 0.5
    - first:
        name: _DetailNormalMapScale
      second: 1
    - first:
        name: _DstBlend
      second: 0
    - first:
        name: _GlossMapScale
      second: 1
    - first:
        name: _Glossiness
      second: 0.5
    - first:
        name: _GlossyReflections
      second: 1
    - first:
        name: _Liuguang_ZT
      second: 10
    - first:
        name: _Liuguang_power
      second: 1
    - first:
        name: _Metallic
      second: 0
    - first:
        name: _Mode
      second: 0
    - first:
        name: _NiuQu
      second: 0
    - first:
        name: _OcclusionStrength
      second: 1
    - first:
        name: _Parallax
      second: 0.02
    - first:
        name: _Posterize
      second: 3
    - first:
        name: _Posterize_Switch
      second: 1
    - first:
        name: _SmoothnessTextureChannel
      second: 0
    - first:
        name: _SpecularHighlights
      second: 1
    - first:
        name: _SrcBlend
      second: 1
    - first:
        name: _TEX1_SUDU
      second: -0.2
    - first:
        name: _TEX2_SUDU
      second: 0.1
    - first:
        name: _UVSec
      second: 0
    - first:
        name: _U_Speed01
      second: -10
    - first:
        name: _U_Speed02
      second: -20
    - first:
        name: _V_Speed01
      second: 0
    - first:
        name: _V_Speed02
      second: 0
    - first:
        name: _ZT
      second: 2
    - first:
        name: _ZWrite
      second: 1
    - first:
        name: _node_4581
      second: 0
    - first:
        name: _node_6626
      second: 2
    m_Colors:
    - first:
        name: _Color
      second: {r: 1, g: 1, b: 1, a: 1}
    - first:
        name: _Color2
      second: {r: 1, g: 0, b: 0.5, a: 1}
    - first:
        name: _EmissionColor
      second: {r: 0, g: 0, b: 0, a: 1}
    - first:
        name: _TintColor
      second: {r: 0.007352948, g: 0, b: 0, a: 1}
