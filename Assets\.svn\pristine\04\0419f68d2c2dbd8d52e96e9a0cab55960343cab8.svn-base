with:870  hight:416
[logic/misc/CShareCtrl.lua:18]:ShareCallback ctor
[logic/login/CLoginCtrl.lua:5]:公告初始化...
[logic/base/CHttpCtrl.lua:32]:http get ->    https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials&client_id=rlU84dprH6I7sMjBkqM0gizl&client_secret=c15395c2433c4a904d22aa27eab8321b&
[logic/ui/CViewCtrl.lua:94]:CNotifyView ShowView
[logic/ui/CViewCtrl.lua:94]:CBottomView ShowView
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x800d51d8"
|  json_result = true
|  timer = 16
}
[logic/audio/CSpeechCtrl.lua:72]:OnTokenGet err
[logic/ui/CViewBase.lua:125]:CNotifyView LoadDone!
[logic/base/CResCtrl.lua:198]:-->resource init done!!! 12
[logic/ui/CViewBase.lua:125]:CBottomView LoadDone!
[core/table.lua:94]:CServerCtrl.SetServerData: = {
|  [1001] = {
|  |  group_id = 1001
|  |  name = "1-10区"
|  |  servers = {}
|  }
|  [1002] = {
|  |  group_id = 1003
|  |  name = "默认"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线1区"
|  |  |  |  new = 0
|  |  |  |  server_id = 1001
|  |  |  |  state = 2
|  |  |  }
|  |  |  [2] = {
|  |  |  |  group = 2
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线11区"
|  |  |  |  new = 1
|  |  |  |  server_id = 1101
|  |  |  |  state = 2
|  |  |  }
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:94]:CLoginView ShowView
[logic/base/CHttpCtrl.lua:32]:http get ->    http://************:88/Note/note.json
[logic/login/CLoginAccountPage.lua:79]:SendToCenterServer:    http://************:88/Note/note.json    
[logic/ui/CViewBase.lua:125]:CLoginView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x952553c0"
|  json_result = true
|  timer = 60
}
[core/table.lua:94]:cb tResult-> = {
|  info = {
|  |  role_list = {}
|  |  server_info = {
|  |  |  groups = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 1
|  |  |  |  |  name = "1-10区"
|  |  |  |  }
|  |  |  }
|  |  |  infoList = {
|  |  |  |  [1] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "新服7天限时-大狂欢活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [10] = {
|  |  |  |  |  |  |  |  text = "传说伙伴碎片*100、梦觉书·零*20、梦觉书·低*20、梦觉书·高*20"
|  |  |  |  |  |  |  |  title = "新区累计充值10000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [11] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "单日累计充值活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [12] = {
|  |  |  |  |  |  |  |  text = "永久"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [13] = {
|  |  |  |  |  |  |  |  text = "1.活动为单日累计充值，高档位可领取低档位所有物品。
2.活动为单日累计充值，仅计算单日内玩家累计充值总额，次日重新计算。"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [14] = {
|  |  |  |  |  |  |  |  text = "向客服申请"
|  |  |  |  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [15] = {
|  |  |  |  |  |  |  |  text = "装备材料*10、金币*5w、熊猫蛋糕*5"
|  |  |  |  |  |  |  |  title = "单日累计充值100元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [16] = {
|  |  |  |  |  |  |  |  text = "一发入魂碎片*10、金币铜币*10w、体力药水*1、鲜肉包*30"
|  |  |  |  |  |  |  |  title = "单日累计充值500元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [17] = {
|  |  |  |  |  |  |  |  text = "一发入魂碎片*20、金币*20w、体力药水*2、焦糖包*60"
|  |  |  |  |  |  |  |  title = "单日累计充值1000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [18] = {
|  |  |  |  |  |  |  |  text = "装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1"
|  |  |  |  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [19] = {
|  |  |  |  |  |  |  |  text = "装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3"
|  |  |  |  |  |  |  |  title = "单日累计充值5000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "新服前7天"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [20] = {
|  |  |  |  |  |  |  |  text = "一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*80"
|  |  |  |  |  |  |  |  title = "单日累计充值10000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [21] = {
|  |  |  |  |  |  |  |  text = "一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1"
|  |  |  |  |  |  |  |  title = "单日累计充值20000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [22] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "永久累计充值活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [23] = {
|  |  |  |  |  |  |  |  text = "永久"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [24] = {
|  |  |  |  |  |  |  |  text = "活动为永久累计充值，高档位可领取低档位所有物品。、r
每个档位奖励仅可领取一次"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [25] = {
|  |  |  |  |  |  |  |  text = "向客服申请"
|  |  |  |  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [26] = {
|  |  |  |  |  |  |  |  text = "装备材料*50、伙伴觉醒材料*50、金币*1w"
|  |  |  |  |  |  |  |  title = "永久累计充值500元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [27] = {
|  |  |  |  |  |  |  |  text = "喇叭100、2星精英伙伴*10、装备原石*5、附魔材料*5、金币*5w"
|  |  |  |  |  |  |  |  title = "永久累计充值1000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [28] = {
|  |  |  |  |  |  |  |  text = "喇叭300、2星精英伙伴*20、装备原石*10、附魔材料*10、金币*10w"
|  |  |  |  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [29] = {
|  |  |  |  |  |  |  |  text = "喇叭500、3星传说伙伴*1、装备原石*30、附魔材料*30、金币*50w"
|  |  |  |  |  |  |  |  title = "永久累计充值5000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "单日累计充值达到对应档位，不重复高领低档，每个档位仅可领取一次奖励、请及时参加并申请领取对应档位。"
|  |  |  |  |  |  |  |  title = "活动规则"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [30] = {
|  |  |  |  |  |  |  |  text = "一发入魂碎片*100、万能碎片*100、金币*100w"
|  |  |  |  |  |  |  |  title = "永久累充10000元礼包"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [31] = {
|  |  |  |  |  |  |  |  text = "一发入魂碎片*200、万能碎片*200、金币*200w"
|  |  |  |  |  |  |  |  title = "永久累充20000元礼包"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [32] = {
|  |  |  |  |  |  |  |  text = "万能碎片*500、金币*500w"
|  |  |  |  |  |  |  |  title = "永久累充40000元礼包"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [33] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "单日累计充值货币返利1:10"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [34] = {
|  |  |  |  |  |  |  |  text = "永久"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [35] = {
|  |  |  |  |  |  |  |  text = "1.本活动为阶梯式活动，当日累计充值的金额满足返利阶梯后，按照当日充值金额满足的最高档次进行发放，次日自动返利，无需申请
2.本活动为长期活动，与其他返利活动叠加。
充值100-1999元，返利50%钻石
充值2000-4999元，返利100%钻石
充值5000-9999元，返利150%钻石
充值10000-19999元，返利200%钻石
充值20000+元，返利250%钻石
以上返利会在第二天自动已邮件形式发送至玩家"
|  |  |  |  |  |  |  |  title = "活动说明:"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [36] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "服务器冠名活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [37] = {
|  |  |  |  |  |  |  |  text = "永久"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [38] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [39] = {
|  |  |  |  |  |  |  |  text = "全体玩家"
|  |  |  |  |  |  |  |  title = "【活动对象】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "向客服申请"
|  |  |  |  |  |  |  |  title = "发放时间方式："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [40] = {
|  |  |  |  |  |  |  |  text = "我的服务器，我为自己代言。"
|  |  |  |  |  |  |  |  title = "【活动宣言】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [41] = {
|  |  |  |  |  |  |  |  text = "区服冠名权*1"
|  |  |  |  |  |  |  |  title = "【冠名奖励】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [42] = {
|  |  |  |  |  |  |  |  text = "  亲爱的玩家：
  也许你在游戏里已经称霸全区，但是出了服务器，出了游戏，还有谁认识你现在就给你一个机会，获得至高无上的荣誉，让更多的人知道你的强大。特此举行“我的服务器，我为自己代言”，服务器玩家冠名活动。
即日起，只要满足以下条件，即可获得服务器冠名权：
  1）开服前3天累计充值满10000元
  2）开服前7日累计充值满20000元，且区服处于未冠名状态，获得服务器冠名权
  备注：本活动遵循先到先得原则。如出现多个满足条件的，冠名资格由第一个满足条件的获得。"
|  |  |  |  |  |  |  |  title = "【活动内容】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [43] = {
|  |  |  |  |  |  |  |  text = "1、活动时间内有玩家满足条件后活动结束，活动期间没有玩家满足条件则保持原服务器名称。
2、服务器冠名长度为4个字。（必须遵守相关法律法规，不得使用负面或影响社会风气，不得出现敏感词的名称）
3、由于冠名操作需要时间，工作人员会在7个工作日内，完成冠名操作。
4，每个服，冠名只有一次。被冠名的服务器不予再次冠名"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [5] = {
|  |  |  |  |  |  |  |  text = "精英伙伴碎片*50、攻击宝石礼包*1、防御宝石礼包*1、2星精英伙伴*1"
|  |  |  |  |  |  |  |  title = "新区累计充值500元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [6] = {
|  |  |  |  |  |  |  |  text = "精英伙伴碎片*100、攻击宝石礼包*3、防御宝石礼包*3、2星精英伙伴*2"
|  |  |  |  |  |  |  |  title = "新区累计充值1000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [7] = {
|  |  |  |  |  |  |  |  text = "传说伙伴碎片*20、攻击宝石礼包*5、防御宝石礼包*5、2星精英伙伴*3"
|  |  |  |  |  |  |  |  title = "新区累计充值2000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [8] = {
|  |  |  |  |  |  |  |  text = "传说伙伴碎片*30、攻击宝石礼包*10、防御宝石礼包*10"
|  |  |  |  |  |  |  |  title = "新区累计充值3000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [9] = {
|  |  |  |  |  |  |  |  text = "传说伙伴碎片*50、梦觉书·零*10、梦觉书·低*10、梦觉书·高*10"
|  |  |  |  |  |  |  |  title = "新区累计充值5000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = "ceshi"
|  |  |  |  |  }
|  |  |  |  |  hot = 1
|  |  |  |  |  title = "限时大狂欢"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "1、转出条件：上款游戏充值超过100元的玩家
2、转入条件：新游戏充值超过100元"
|  |  |  |  |  |  |  |  title = "一、参与转游条件"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "★新游单日充值100-999元
老游戏充值金额20%转入新游（转入金额上限为5000元）
★新游单日充值1000-2999元
老游戏充值金额30%转入新游（转入金额上限为10000元）
★新游单日充值3000-4999元
老游戏充值金额40%转入新游（转入金额上限为10000元）
★新游单日充值5000-9999元
老游戏充值金额60%转入新游（转入金额上限为20000元）
★新游单日充值10000-19999元
老游戏充值金额80%转入新游（转入金额上限为30000元）
★新游单日充值20000以上
老游戏充值金额100%转入新游（转入金额上限为40000元）"
|  |  |  |  |  |  |  |  title = "二、阶梯转游返利"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "A，玩家新游戏充值200元，老游戏充值2200元，则转入货币为：2200*30%*新游货币比例
B，玩家新游戏充值200元，老游戏充值10000元，则转入货币为：5000*30%*新游货币比例"
|  |  |  |  |  |  |  |  title = "【举例】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "1，每个账号仅支持一次转游！
2，转游玩家在老游戏A和B都有过充值，但只能选择其中一个游戏进行转出到新游戏，不接受多个游戏同时转入一个新游戏
3，转游福利仅限玩家新角色创建的7天内申请，创建时间超过游戏7天则无法无法享受。
4，以上返利仅为游戏货币奖励，不计入vip经验、累充活动和线下返利
5，封停账号：玩家申请转游成功之后封停上款游戏角色"
|  |  |  |  |  |  |  |  title = "【温馨提示】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = "ceshi"
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "转游/转区"
|  |  |  |  }
|  |  |  }
|  |  |  ports = {
|  |  |  |  [1] = 7011
|  |  |  |  [2] = 7012
|  |  |  |  [3] = 27011
|  |  |  |  [4] = 27012
|  |  |  |  [5] = 27013
|  |  |  }
|  |  |  role_list = ""
|  |  |  servers = {
|  |  |  |  [1] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 1001
|  |  |  |  |  ip = "************"
|  |  |  |  |  name = "双线1区"
|  |  |  |  |  new = 0
|  |  |  |  |  server_id = 1001
|  |  |  |  |  start_time = 1506844350
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  }
|  |  }
|  |  token = ""
|  }
}
[core/table.lua:94]:CServerCtrl.SetServerData: = {
|  [1] = {
|  |  group_id = 1
|  |  id = 1
|  |  name = "1-10区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  id = 1001
|  |  |  |  ip = "************"
|  |  |  |  name = "双线1区"
|  |  |  |  new = 0
|  |  |  |  server_id = 1001
|  |  |  |  start_time = 1506844350
|  |  |  |  state = 2
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:设置公告: = {
|  [1] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "新服7天限时-大狂欢活动"
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  text = "传说伙伴碎片*100、梦觉书·零*20、梦觉书·低*20、梦觉书·高*20"
|  |  |  |  |  title = "新区累计充值10000元"
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "单日累计充值活动"
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  text = "永久"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [13] = {
|  |  |  |  |  text = "1.活动为单日累计充值，高档位可领取低档位所有物品。
2.活动为单日累计充值，仅计算单日内玩家累计充值总额，次日重新计算。"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [14] = {
|  |  |  |  |  text = "向客服申请"
|  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  }
|  |  |  |  [15] = {
|  |  |  |  |  text = "装备材料*10、金币*5w、熊猫蛋糕*5"
|  |  |  |  |  title = "单日累计充值100元"
|  |  |  |  }
|  |  |  |  [16] = {
|  |  |  |  |  text = "一发入魂碎片*10、金币铜币*10w、体力药水*1、鲜肉包*30"
|  |  |  |  |  title = "单日累计充值500元"
|  |  |  |  }
|  |  |  |  [17] = {
|  |  |  |  |  text = "一发入魂碎片*20、金币*20w、体力药水*2、焦糖包*60"
|  |  |  |  |  title = "单日累计充值1000元"
|  |  |  |  }
|  |  |  |  [18] = {
|  |  |  |  |  text = "装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1"
|  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  }
|  |  |  |  [19] = {
|  |  |  |  |  text = "装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3"
|  |  |  |  |  title = "单日累计充值5000元"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "新服前7天"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [20] = {
|  |  |  |  |  text = "一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*80"
|  |  |  |  |  title = "单日累计充值10000元"
|  |  |  |  }
|  |  |  |  [21] = {
|  |  |  |  |  text = "一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1"
|  |  |  |  |  title = "单日累计充值20000元"
|  |  |  |  }
|  |  |  |  [22] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "永久累计充值活动"
|  |  |  |  }
|  |  |  |  [23] = {
|  |  |  |  |  text = "永久"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [24] = {
|  |  |  |  |  text = "活动为永久累计充值，高档位可领取低档位所有物品。、r
每个档位奖励仅可领取一次"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [25] = {
|  |  |  |  |  text = "向客服申请"
|  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  }
|  |  |  |  [26] = {
|  |  |  |  |  text = "装备材料*50、伙伴觉醒材料*50、金币*1w"
|  |  |  |  |  title = "永久累计充值500元"
|  |  |  |  }
|  |  |  |  [27] = {
|  |  |  |  |  text = "喇叭100、2星精英伙伴*10、装备原石*5、附魔材料*5、金币*5w"
|  |  |  |  |  title = "永久累计充值1000元"
|  |  |  |  }
|  |  |  |  [28] = {
|  |  |  |  |  text = "喇叭300、2星精英伙伴*20、装备原石*10、附魔材料*10、金币*10w"
|  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  }
|  |  |  |  [29] = {
|  |  |  |  |  text = "喇叭500、3星传说伙伴*1、装备原石*30、附魔材料*30、金币*50w"
|  |  |  |  |  title = "永久累计充值5000元"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "单日累计充值达到对应档位，不重复高领低档，每个档位仅可领取一次奖励、请及时参加并申请领取对应档位。"
|  |  |  |  |  title = "活动规则"
|  |  |  |  }
|  |  |  |  [30] = {
|  |  |  |  |  text = "一发入魂碎片*100、万能碎片*100、金币*100w"
|  |  |  |  |  title = "永久累充10000元礼包"
|  |  |  |  }
|  |  |  |  [31] = {
|  |  |  |  |  text = "一发入魂碎片*200、万能碎片*200、金币*200w"
|  |  |  |  |  title = "永久累充20000元礼包"
|  |  |  |  }
|  |  |  |  [32] = {
|  |  |  |  |  text = "万能碎片*500、金币*500w"
|  |  |  |  |  title = "永久累充40000元礼包"
|  |  |  |  }
|  |  |  |  [33] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "单日累计充值货币返利1:10"
|  |  |  |  }
|  |  |  |  [34] = {
|  |  |  |  |  text = "永久"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [35] = {
|  |  |  |  |  text = "1.本活动为阶梯式活动，当日累计充值的金额满足返利阶梯后，按照当日充值金额满足的最高档次进行发放，次日自动返利，无需申请
2.本活动为长期活动，与其他返利活动叠加。
充值100-1999元，返利50%钻石
充值2000-4999元，返利100%钻石
充值5000-9999元，返利150%钻石
充值10000-19999元，返利200%钻石
充值20000+元，返利250%钻石
以上返利会在第二天自动已邮件形式发送至玩家"
|  |  |  |  |  title = "活动说明:"
|  |  |  |  }
|  |  |  |  [36] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "服务器冠名活动"
|  |  |  |  }
|  |  |  |  [37] = {
|  |  |  |  |  text = "永久"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [38] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [39] = {
|  |  |  |  |  text = "全体玩家"
|  |  |  |  |  title = "【活动对象】："
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "向客服申请"
|  |  |  |  |  title = "发放时间方式："
|  |  |  |  }
|  |  |  |  [40] = {
|  |  |  |  |  text = "我的服务器，我为自己代言。"
|  |  |  |  |  title = "【活动宣言】："
|  |  |  |  }
|  |  |  |  [41] = {
|  |  |  |  |  text = "区服冠名权*1"
|  |  |  |  |  title = "【冠名奖励】："
|  |  |  |  }
|  |  |  |  [42] = {
|  |  |  |  |  text = "  亲爱的玩家：
  也许你在游戏里已经称霸全区，但是出了服务器，出了游戏，还有谁认识你现在就给你一个机会，获得至高无上的荣誉，让更多的人知道你的强大。特此举行“我的服务器，我为自己代言”，服务器玩家冠名活动。
即日起，只要满足以下条件，即可获得服务器冠名权：
  1）开服前3天累计充值满10000元
  2）开服前7日累计充值满20000元，且区服处于未冠名状态，获得服务器冠名权
  备注：本活动遵循先到先得原则。如出现多个满足条件的，冠名资格由第一个满足条件的获得。"
|  |  |  |  |  title = "【活动内容】："
|  |  |  |  }
|  |  |  |  [43] = {
|  |  |  |  |  text = "1、活动时间内有玩家满足条件后活动结束，活动期间没有玩家满足条件则保持原服务器名称。
2、服务器冠名长度为4个字。（必须遵守相关法律法规，不得使用负面或影响社会风气，不得出现敏感词的名称）
3、由于冠名操作需要时间，工作人员会在7个工作日内，完成冠名操作。
4，每个服，冠名只有一次。被冠名的服务器不予再次冠名"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  text = "精英伙伴碎片*50、攻击宝石礼包*1、防御宝石礼包*1、2星精英伙伴*1"
|  |  |  |  |  title = "新区累计充值500元"
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  text = "精英伙伴碎片*100、攻击宝石礼包*3、防御宝石礼包*3、2星精英伙伴*2"
|  |  |  |  |  title = "新区累计充值1000元"
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  text = "传说伙伴碎片*20、攻击宝石礼包*5、防御宝石礼包*5、2星精英伙伴*3"
|  |  |  |  |  title = "新区累计充值2000元"
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  text = "传说伙伴碎片*30、攻击宝石礼包*10、防御宝石礼包*10"
|  |  |  |  |  title = "新区累计充值3000元"
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  text = "传说伙伴碎片*50、梦觉书·零*10、梦觉书·低*10、梦觉书·高*10"
|  |  |  |  |  title = "新区累计充值5000元"
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = "ceshi"
|  |  }
|  |  hot = 1
|  |  title = "限时大狂欢"
|  }
|  [2] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "1、转出条件：上款游戏充值超过100元的玩家
2、转入条件：新游戏充值超过100元"
|  |  |  |  |  title = "一、参与转游条件"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "★新游单日充值100-999元
老游戏充值金额20%转入新游（转入金额上限为5000元）
★新游单日充值1000-2999元
老游戏充值金额30%转入新游（转入金额上限为10000元）
★新游单日充值3000-4999元
老游戏充值金额40%转入新游（转入金额上限为10000元）
★新游单日充值5000-9999元
老游戏充值金额60%转入新游（转入金额上限为20000元）
★新游单日充值10000-19999元
老游戏充值金额80%转入新游（转入金额上限为30000元）
★新游单日充值20000以上
老游戏充值金额100%转入新游（转入金额上限为40000元）"
|  |  |  |  |  title = "二、阶梯转游返利"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "A，玩家新游戏充值200元，老游戏充值2200元，则转入货币为：2200*30%*新游货币比例
B，玩家新游戏充值200元，老游戏充值10000元，则转入货币为：5000*30%*新游货币比例"
|  |  |  |  |  title = "【举例】："
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "1，每个账号仅支持一次转游！
2，转游玩家在老游戏A和B都有过充值，但只能选择其中一个游戏进行转出到新游戏，不接受多个游戏同时转入一个新游戏
3，转游福利仅限玩家新角色创建的7天内申请，创建时间超过游戏7天则无法无法享受。
4，以上返利仅为游戏货币奖励，不计入vip经验、累充活动和线下返利
5，封停账号：玩家申请转游成功之后封停上款游戏角色"
|  |  |  |  |  title = "【温馨提示】："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = "ceshi"
|  |  }
|  |  hot = 2
|  |  title = "转游/转区"
|  }
}
[logic/login/CLoginCtrl.lua:151]:ConnectServer =     table:0x122120B8    table:0x122122D0
[net/CNetCtrl.lua:114]:Test连接    ************    27012
[core/table.lua:94]:-->Net Receive: login.GS2CHello = {
|  time = **********
}
[core/table.lua:94]:<--Net Send: login.C2GSQueryLogin = {
|  res_file_version = {}
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:54:00
[net/netlogin.lua:208]:netlogin.GS2CQueryLogin-->
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 1001
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginAccount = {
|  account = "test1212232"
|  client_svn_version = 96
|  client_version = "********"
|  device = "B760M-VDH (JGINYUE)"
|  imei = ""
|  is_qrcode = 0
|  mac = "00-E0-1B-74-72-10"
|  os = "Windows 10  (10.0.0) 64bit"
|  platform = 4
|  udid = "552ae815ac1f51979f8a3ae596276f67ae3d10cf"
}
[core/global.lua:59]:<color=#ffeb04>更新代码: 1049</color>
[logic/base/Utils.lua:398]:local framever, dllver, resver = C_api.Utils.GetResVersion()
if resver >= 96 or Utils.IsEditor() then
	function CWelfareCtrl.IsOpenCostSave(self)
		local b = false
		local t = g_TimeCtrl:GetTimeS()	
		if self.m_CostSaveStartTime ~= 0 and self.m_CostSaveEndTime ~= 0 and t > self.m_CostSaveStartTime and t < self.m_CostSaveEndTime then
			b = true
		end
		return b
	end
	function CPartnerHireView.IsCanYFRH(self, iParID)
		local d = data.partnerhiredata.DATA
		if table.index(d[iParID]["recommand_list"], "一发入魂") then
			return self:IsCanSSRDraw()
		end
	end
	function CPartnerEquipPage.ChangePart(self)
		local oItem = g_ItemCtrl:GetItem(self.m_CurItemID)
		if oItem and oItem:GetValue("level") == define.Partner.ParEquip.MaxLevel and oItem:GetValue("star") < define.Partner.ParEquip.MaxStar then
			if self.m_CurPart ~= self.m_UpStarPart and self.m_CurPart ~= self.m_UpStonePart then
				self.m_UpStarBtn:SetSelected(true)
				self:SwitchPart()
			end
		end
	end
	data.netdata.BAN["proto"]["herobox"]["item"]["GS2CItemQuickUse"] = true
end

[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 1001
}
[core/table.lua:94]:-->Net Receive: login.GS2CCheckInviteCodeResult = {
|  msg = "邀请码验证已关闭"
|  result = 1
}
[core/table.lua:94]:-->Net Receive: login.GS2CLoginAccount = {
|  account = "test1212232"
|  role_list = {
|  |  [1] = {
|  |  |  grade = 2
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 130
|  |  |  |  weapon = 2000
|  |  |  }
|  |  |  pid = 10046
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginRole = {
|  account = "test1212232"
|  pid = 10046
}
[core/global.lua:59]:<color=#ffeb04>读取系统设置本地数据，帐号为  test1212232</color>
[core/global.lua:59]:<color=#ffeb04>test1212232 的系统设置本地数据为 nil，使用默认数据</color>
[core/table.lua:94]:-->Net Receive: login.GS2CLoginRole = {
|  account = "test1212232"
|  pid = 10046
|  role = {
|  |  active = 1
|  |  bcmd = {
|  |  |  [1] = {
|  |  |  |  idx = 208
|  |  |  }
|  |  |  [10] = {
|  |  |  |  idx = 105
|  |  |  }
|  |  |  [11] = {
|  |  |  |  idx = 106
|  |  |  }
|  |  |  [12] = {
|  |  |  |  idx = 107
|  |  |  }
|  |  |  [13] = {
|  |  |  |  idx = 108
|  |  |  }
|  |  |  [14] = {
|  |  |  |  idx = 205
|  |  |  }
|  |  |  [15] = {
|  |  |  |  idx = 206
|  |  |  }
|  |  |  [16] = {
|  |  |  |  idx = 207
|  |  |  }
|  |  |  [2] = {
|  |  |  |  cmd = "封印"
|  |  |  |  idx = 202
|  |  |  }
|  |  |  [3] = {
|  |  |  |  cmd = "集火"
|  |  |  |  idx = 201
|  |  |  }
|  |  |  [4] = {
|  |  |  |  cmd = "减速"
|  |  |  |  idx = 203
|  |  |  }
|  |  |  [5] = {
|  |  |  |  cmd = "禁疗"
|  |  |  |  idx = 204
|  |  |  }
|  |  |  [6] = {
|  |  |  |  cmd = "治疗"
|  |  |  |  idx = 101
|  |  |  }
|  |  |  [7] = {
|  |  |  |  cmd = "复活"
|  |  |  |  idx = 102
|  |  |  }
|  |  |  [8] = {
|  |  |  |  cmd = "加速"
|  |  |  |  idx = 103
|  |  |  }
|  |  |  [9] = {
|  |  |  |  cmd = "解封"
|  |  |  |  idx = 104
|  |  |  }
|  |  }
|  |  coin = 12190
|  |  energy = 120
|  |  exp = 1610
|  |  grade = 2
|  |  hp = 1835
|  |  kp_sdk_info = {
|  |  |  create_time = **********
|  |  |  upgrade_time = **********
|  |  }
|  |  mask = "7e6683d1ffffffe"
|  |  model_info = {
|  |  |  color = {
|  |  |  |  [1] = 0
|  |  |  }
|  |  |  shape = 130
|  |  |  weapon = 2000
|  |  }
|  |  name = "爽朗d枪机"
|  |  open_day = 29
|  |  org_fuben_cnt = 2
|  |  power = **********
|  |  school = 2
|  |  school_branch = 1
|  |  sex = 1
|  |  show_id = 10046
|  |  skill_point = 1
|  |  systemsetting = {}
|  }
|  role_token = "**********0392"
|  xg_account = "bus10046"
}
[logic/ui/CViewCtrl.lua:94]:CMainMenuView ShowView
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 0
|  active = 1
|  arenamedal = 0
|  attack = 0
|  bcmd = {
|  |  [1] = {
|  |  |  idx = 208
|  |  }
|  |  [10] = {
|  |  |  idx = 105
|  |  }
|  |  [11] = {
|  |  |  idx = 106
|  |  }
|  |  [12] = {
|  |  |  idx = 107
|  |  }
|  |  [13] = {
|  |  |  idx = 108
|  |  }
|  |  [14] = {
|  |  |  idx = 205
|  |  }
|  |  [15] = {
|  |  |  idx = 206
|  |  }
|  |  [16] = {
|  |  |  idx = 207
|  |  }
|  |  [2] = {
|  |  |  cmd = "封印"
|  |  |  idx = 202
|  |  }
|  |  [3] = {
|  |  |  cmd = "集火"
|  |  |  idx = 201
|  |  }
|  |  [4] = {
|  |  |  cmd = "减速"
|  |  |  idx = 203
|  |  }
|  |  [5] = {
|  |  |  cmd = "禁疗"
|  |  |  idx = 204
|  |  }
|  |  [6] = {
|  |  |  cmd = "治疗"
|  |  |  idx = 101
|  |  }
|  |  [7] = {
|  |  |  cmd = "复活"
|  |  |  idx = 102
|  |  }
|  |  [8] = {
|  |  |  cmd = "加速"
|  |  |  idx = 103
|  |  }
|  |  [9] = {
|  |  |  cmd = "解封"
|  |  |  idx = 104
|  |  }
|  }
|  camp = 0
|  chatself = false
|  chubeiexp = 0
|  coin = 12190
|  coin_over = 0
|  color_coin = 0
|  critical_damage = 0
|  critical_ratio = 0
|  cure_critical_ratio = 0
|  defense = 0
|  energy = 120
|  exp = 1610
|  followers = {}
|  goldcoin = 0
|  grade = 2
|  hp = 1835
|  kp_sdk_info = {
|  |  create_time = **********
|  |  upgrade_time = **********
|  }
|  max_hp = 0
|  medal = 0
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 130
|  |  weapon = 2000
|  }
|  name = "爽朗d枪机"
|  open_day = 29
|  org_fuben_cnt = 2
|  org_offer = 0
|  power = **********
|  res_abnormal_ratio = 0
|  res_critical_ratio = 0
|  school = 2
|  school_branch = 1
|  sex = 1
|  show_id = 10046
|  skill_point = 1
|  skin = 0
|  speed = 0
|  systemsetting = {}
|  title_info = {}
|  trapmine_point = 0
|  travel_score = 0
|  upvote_amount = 0
}
[net/netlogin.lua:152]:create_time:    1680314274    **********    nil
[logic/misc/CTimeCtrl.lua:64]:HeartBeat-->开启心跳包检测
[core/table.lua:94]:<--Net Send: title.C2GSTitleInfoList = {}
[core/table.lua:94]:-->Net Receive: player.GS2CGamePushSetting = {}
[core/table.lua:94]:-->Net Receive: player.GS2CShapeList = {
|  shapes = {
|  |  [1] = 130
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CGuidanceInfo = {
|  guidanceinfo = {
|  |  key = {
|  |  |  [1] = 7000041
|  |  |  [2] = 7000051
|  |  |  [3] = 6010002
|  |  |  [4] = 1027099
|  |  |  [5] = 1028099
|  |  |  [6] = 7000061
|  |  }
|  |  version = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>>>>>>>>>>>>>>>>>>>>> GS2CGuidanceInfo </color>
[logic/ui/CSprite.lua:159]:DynamicSprite dSprInfo    Item    10012
[logic/ui/CViewBase.lua:125]:CMainMenuView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: travel.GS2CLoginTravelPartner = {
|  travel_partner = {
|  |  server_time = **********
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CServerGradeInfo = {
|  days = 4
|  server_grade = 80
}
[core/table.lua:94]:-->Net Receive: scene.GS2CShowScene = {
|  map_id = 101000
|  scene_id = 12
|  scene_name = "帝都"
}
[core/table.lua:94]:-->Net Receive: task.GS2CLoginTask = {
|  shimen_status = 1
|  taskdata = {
|  |  [1] = {
|  |  |  acceptnpc = 10007
|  |  |  clientnpc = {
|  |  |  |  [1] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 302
|  |  |  |  |  }
|  |  |  |  |  name = "重华"
|  |  |  |  |  npcid = 8205
|  |  |  |  |  npctype = 10007
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 12800
|  |  |  |  |  |  y = 9700
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  detaildesc = "摆脱少女重华的纠缠！"
|  |  |  name = "少女重华"
|  |  |  patrolinfo = {}
|  |  |  pickiteminfo = {}
|  |  |  placeinfo = {}
|  |  |  shapeinfo = {}
|  |  |  statusinfo = {
|  |  |  |  note = "CreateTask"
|  |  |  |  status = 2
|  |  |  }
|  |  |  submitnpc = 10007
|  |  |  target = 10007
|  |  |  targetdesc = "遭到质问"
|  |  |  taskid = 10003
|  |  |  taskitem = {}
|  |  |  tasktype = 1
|  |  |  traceinfo = {}
|  |  |  type = 2
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>login task</color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x53f21b10"
|  |  AssociatedPick = "function: 0x53f21b70"
|  |  AssociatedSubmit = "function: 0x53f21b40"
|  |  CreateDefalutData = "function: 0x53f1faf0"
|  |  GetChaptetFubenData = "function: 0x53f21830"
|  |  GetProgressThing = "function: 0x53f21bd0"
|  |  GetRemainTime = "function: 0x53f1fa88"
|  |  GetStatus = "function: 0x53f21c30"
|  |  GetTaskClientExtStrDic = "function: 0x53f21ba0"
|  |  GetTaskTypeSpriteteName = "function: 0x53f21800"
|  |  GetTraceInfo = "function: 0x53f21958"
|  |  GetTraceNpcType = "function: 0x53f217d0"
|  |  GetValue = "function: 0x53f21898"
|  |  IsAbandon = "function: 0x53f218f8"
|  |  IsAddEscortDynamicNpc = "function: 0x53f21f68"
|  |  IsMissMengTask = "function: 0x53f1fab8"
|  |  IsPassChaterFuben = "function: 0x53f21860"
|  |  IsTaskSpecityAction = "function: 0x53f21928"
|  |  IsTaskSpecityCategory = "function: 0x53f21ae0"
|  |  New = "function: 0x53f26460"
|  |  NewByData = "function: 0x53f1f030"
|  |  RaiseProgressIdx = "function: 0x53f21c00"
|  |  RefreshTask = "function: 0x53f218c8"
|  |  ResetEndTime = "function: 0x53f1fb58"
|  |  SetStatus = "function: 0x53f21f98"
|  |  classname = "CTask"
|  |  ctor = "function: 0x53f1fa58"
|  }
|  m_CData = {
|  |  ChapterFb = "1,1"
|  |  autoDoNextTask = 0
|  |  clientExtStr = ""
|  |  name = "少女重华"
|  |  submitNpcId = 10007
|  |  submitRewardStr = {
|  |  |  [1] = "R1003"
|  |  }
|  |  taskWalkingTips = "想不到帝都也有恶霸。,那个人是刚才的！;none;none"
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10007
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 302
|  |  |  |  }
|  |  |  |  name = "重华"
|  |  |  |  npcid = 8205
|  |  |  |  npctype = 10007
|  |  |  |  pos_info = {
|  |  |  |  |  x = 12800
|  |  |  |  |  y = 9700
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "摆脱少女重华的纠缠！"
|  |  isdone = 0
|  |  name = "少女重华"
|  |  patrolinfo = {}
|  |  pickiteminfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10007
|  |  target = 10007
|  |  targetdesc = "遭到质问"
|  |  taskid = 10003
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CTeachProgress = {}
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshPartnerTask = {
|  partnertask_progress = {
|  |  [1] = {
|  |  |  parid = 302
|  |  |  status = 1
|  |  |  taskid = 62080
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  activepoint = 1
}
[core/table.lua:94]:-->Net Receive: state.GS2CLoginState = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginHuodongInfo = {
|  convoyinfo = {
|  |  convoy_partner = 1201
|  |  free_time = 1
|  |  pool_info = {
|  |  |  [1] = {
|  |  |  |  level = 1
|  |  |  |  partnerid = 1506
|  |  |  |  pos = 1
|  |  |  |  rewardid = 1001
|  |  |  }
|  |  |  [2] = {
|  |  |  |  level = 2
|  |  |  |  partnerid = 1201
|  |  |  |  pos = 2
|  |  |  |  rewardid = 1002
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [3] = {
|  |  |  |  level = 3
|  |  |  |  partnerid = 1750
|  |  |  |  pos = 3
|  |  |  |  rewardid = 1003
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [4] = {
|  |  |  |  level = 4
|  |  |  |  partnerid = 1010
|  |  |  |  pos = 4
|  |  |  |  rewardid = 1004
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [5] = {
|  |  |  |  level = 5
|  |  |  |  partnerid = 1752
|  |  |  |  pos = 5
|  |  |  |  rewardid = 1005
|  |  |  |  weight = 25
|  |  |  }
|  |  }
|  |  selected_pos = 2
|  |  target_npc = 5032
|  }
|  dailytrain = {}
|  huntinfo = {}
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginChapterInfo = {
|  finalchapter = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  level = 1
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  type = 1
|  |  }
|  }
|  totalstar_info = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  star = 3
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: handbook.GS2CLoginBookList = {
|  book_list = {
|  |  [1] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 313021
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  id = 313022
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 113021
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100302
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  }
|  red_points = {
|  |  [1] = {
|  |  |  book_type = 1
|  |  |  red_point = 1
|  |  }
|  |  [2] = {
|  |  |  book_type = 2
|  |  }
|  |  [3] = {
|  |  |  book_type = 3
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: team.GS2CInviteInfo = {}
[core/table.lua:94]:-->Net Receive: mail.GS2CLoginMail = {}
[core/table.lua:94]:-->Net Receive: friend.GS2CLoginFriend = {}
[core/global.lua:59]:<color=#ffeb04>好友版本一致，无需更新</color>
[core/table.lua:94]:-->Net Receive: friend.GS2CApplyList = {}
[core/table.lua:94]:-->Net Receive: friend.GS2CFriendSetting = {
|  setting = {
|  |  notify = 1
|  }
}
[core/table.lua:94]:-->Net Receive: item.GS2CLoginItem = {
|  itemdata = {
|  |  [1] = {
|  |  |  amount = 2
|  |  |  create_time = 1680282668
|  |  |  id = 1
|  |  |  itemlevel = 2
|  |  |  name = "深蓝琥珀"
|  |  |  sid = 14021
|  |  }
|  |  [2] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  }
|  |  |  create_time = **********
|  |  |  equip_info = {
|  |  |  |  pos = 1
|  |  |  |  stone_sid = 3200000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 4
|  |  |  itemlevel = 1
|  |  |  name = "练习疾流弓"
|  |  |  power = 57
|  |  |  sid = 2110000
|  |  }
|  |  [3] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 121
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 24
|  |  |  |  }
|  |  |  }
|  |  |  create_time = **********
|  |  |  equip_info = {
|  |  |  |  pos = 2
|  |  |  |  stone_sid = 4000000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 5
|  |  |  itemlevel = 1
|  |  |  name = "练习飘逸巾"
|  |  |  power = 24
|  |  |  sid = 2200000
|  |  }
|  |  [4] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  }
|  |  |  create_time = **********
|  |  |  equip_info = {
|  |  |  |  pos = 3
|  |  |  |  stone_sid = 4100000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 3
|  |  |  itemlevel = 1
|  |  |  name = "练习剑麻甲"
|  |  |  power = 32
|  |  |  sid = 2310000
|  |  }
|  |  [5] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  }
|  |  |  create_time = **********
|  |  |  equip_info = {
|  |  |  |  pos = 4
|  |  |  |  stone_sid = 4200000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 6
|  |  |  itemlevel = 1
|  |  |  name = "练习草织戒"
|  |  |  power = 19
|  |  |  sid = 2400000
|  |  }
|  |  [6] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 364
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 72
|  |  |  |  }
|  |  |  }
|  |  |  create_time = **********
|  |  |  equip_info = {
|  |  |  |  pos = 5
|  |  |  |  stone_sid = 4300000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 2
|  |  |  itemlevel = 1
|  |  |  name = "练习折桂腰"
|  |  |  power = 72
|  |  |  sid = 2510000
|  |  }
|  |  [7] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 11
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "speed"
|  |  |  |  |  value = 33
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 77
|  |  |  |  }
|  |  |  }
|  |  |  create_time = **********
|  |  |  equip_info = {
|  |  |  |  pos = 6
|  |  |  |  stone_sid = 4400000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 7
|  |  |  itemlevel = 1
|  |  |  name = "练习虬草鞋"
|  |  |  power = 77
|  |  |  sid = 2610000
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>loginitem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenPlanName = {}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcFightInfoList = {
|  info_list = {
|  |  [1] = {
|  |  |  npc_type = 10024
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [10] = {
|  |  |  npc_type = 10007
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [11] = {
|  |  |  npc_type = 10008
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [12] = {
|  |  |  npc_type = 10009
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [13] = {
|  |  |  npc_type = 10011
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [14] = {
|  |  |  npc_type = 10012
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [15] = {
|  |  |  npc_type = 10013
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [16] = {
|  |  |  npc_type = 10000
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [17] = {
|  |  |  npc_type = 10023
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [18] = {
|  |  |  npc_type = 10014
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [19] = {
|  |  |  npc_type = 10015
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [2] = {
|  |  |  npc_type = 10002
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [20] = {
|  |  |  npc_type = 10016
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [21] = {
|  |  |  npc_type = 10017
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [22] = {
|  |  |  npc_type = 10018
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [23] = {
|  |  |  npc_type = 10019
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [24] = {
|  |  |  npc_type = 10020
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [25] = {
|  |  |  npc_type = 10021
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [26] = {
|  |  |  npc_type = 10022
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [3] = {
|  |  |  npc_type = 10010
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [4] = {
|  |  |  npc_type = 10001
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [5] = {
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [6] = {
|  |  |  npc_type = 10003
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [7] = {
|  |  |  npc_type = 10004
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [8] = {
|  |  |  npc_type = 10005
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [9] = {
|  |  |  npc_type = 10006
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2COnlineGift = {
|  onlinetime = 1411
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartnerList = {
|  partner_list = {
|  |  [1] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 275
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 62
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  grade = 1
|  |  |  mask = "fffffefe"
|  |  |  max_hp = 1851
|  |  |  model_info = {
|  |  |  |  shape = 302
|  |  |  |  skin = 203020
|  |  |  }
|  |  |  name = "重华"
|  |  |  parid = 1
|  |  |  partner_type = 302
|  |  |  patahp = 1851
|  |  |  power = 633
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 600
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30201
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30202
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30203
|  |  |  |  }
|  |  |  }
|  |  |  speed = 420
|  |  |  star = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartner = {
|  fight_info = {
|  |  [1] = {
|  |  |  parid = 1
|  |  |  pos = 1
|  |  }
|  }
|  owned_partner_list = {
|  |  [1] = 302
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterScene = {
|  eid = 32
|  pos_info = {
|  |  face_y = 169796
|  |  x = 19892
|  |  y = 16256
|  }
|  scene_id = 12
}
[core/global.lua:59]:<color=#ffeb04>result  nil</color>
[core/global.lua:59]:<color=#ffeb04>=00000=  userdata: 0x891c2a18 nil</color>
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314041
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:54:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>读取地图： 1020</color>
[core/global.lua:59]:<color=#ffeb04>Unity2017.4.30f1 localScale</color>
[logic/ui/CViewCtrl.lua:104]:CLoginView     CloseView
[logic/base/CResCtrl.lua:596]:res gc step start!
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[core/global.lua:59]:<color=#ffeb04>地图加载完成: 1020 ,当前地图: nil</color>
[core/table.lua:94]:Table = {}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPicturePosList = {}
[core/table.lua:94]:-->Net Receive: skill.GS2CLoginSkill = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3201
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3202
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  needcost = 15
|  |  |  sk = 3204
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  needcost = 25
|  |  |  sk = 3205
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3206
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3207
|  |  |  type = 1
|  |  }
|  }
}
[logic/skill/CSkillCtrl.lua:112]:技能数据登陆协议返回:
[core/table.lua:94]:Table = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3201
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3202
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  needcost = 15
|  |  |  sk = 3204
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  needcost = 25
|  |  |  sk = 3205
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3206
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3207
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginParSoulPlan = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeScore = {
|  cur_id = 1
|  end_time = 1682388000
|  score_info = {}
|  start_time = 1679709600
|  status = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CNewFieldBoss = {
|  bossid = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginTrapmine = {
|  rare_monster = {
|  |  [1] = {
|  |  |  map_id = 202000
|  |  |  partypes = {
|  |  |  |  [1] = 312
|  |  |  |  [2] = 401
|  |  |  |  [3] = 313
|  |  |  }
|  |  }
|  |  [2] = {
|  |  |  map_id = 204000
|  |  |  partypes = {
|  |  |  |  [1] = 404
|  |  |  |  [2] = 409
|  |  |  |  [3] = 403
|  |  |  }
|  |  }
|  |  [3] = {
|  |  |  map_id = 205000
|  |  |  partypes = {
|  |  |  |  [1] = 508
|  |  |  |  [2] = 509
|  |  |  |  [3] = 513
|  |  |  }
|  |  }
|  |  [4] = {
|  |  |  map_id = 206000
|  |  |  partypes = {
|  |  |  |  [1] = 412
|  |  |  |  [2] = 414
|  |  |  |  [3] = 415
|  |  |  }
|  |  }
|  |  [5] = {
|  |  |  map_id = 210400
|  |  |  partypes = {
|  |  |  |  [1] = 505
|  |  |  |  [2] = 506
|  |  |  |  [3] = 507
|  |  |  }
|  |  }
|  |  [6] = {
|  |  |  map_id = 200000
|  |  |  partypes = {
|  |  |  |  [1] = 416
|  |  |  |  [2] = 417
|  |  |  |  [3] = 501
|  |  |  }
|  |  }
|  |  [7] = {
|  |  |  map_id = 201000
|  |  |  partypes = {
|  |  |  |  [1] = 308
|  |  |  |  [2] = 302
|  |  |  |  [3] = 311
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 50760
|  |  |  type = "zsk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 50760
|  |  |  type = "yk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
|  mask = "c"
}
[core/table.lua:94]:CNetCtrl解析mask: GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 50760
|  |  |  type = "zsk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 50760
|  |  |  type = "yk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: store.GS2CPayForColorCoinInfo = {
|  colorcoin_list = {
|  |  [1] = {
|  |  |  key = "goldcoinstore_1005"
|  |  }
|  |  [10] = {
|  |  |  key = "goldcoinstore_2004"
|  |  }
|  |  [11] = {
|  |  |  key = "goldcoinstore_2005"
|  |  }
|  |  [12] = {
|  |  |  key = "goldcoinstore_2006"
|  |  }
|  |  [13] = {
|  |  |  key = "goldcoinstore_2007"
|  |  }
|  |  [14] = {
|  |  |  key = "goldcoinstore_1003"
|  |  }
|  |  [15] = {
|  |  |  key = "goldcoinstore_1004"
|  |  }
|  |  [2] = {
|  |  |  key = "goldcoinstore_1006"
|  |  }
|  |  [3] = {
|  |  |  key = "goldcoinstore_1007"
|  |  }
|  |  [4] = {
|  |  |  key = "goldcoinstore_1008"
|  |  }
|  |  [5] = {
|  |  |  key = "goldcoinstore_1001"
|  |  }
|  |  [6] = {
|  |  |  key = "goldcoinstore_1002"
|  |  }
|  |  [7] = {
|  |  |  key = "goldcoinstore_2001"
|  |  }
|  |  [8] = {
|  |  |  key = "goldcoinstore_2002"
|  |  }
|  |  [9] = {
|  |  |  key = "goldcoinstore_2003"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeRewrad = {
|  end_time = 1682388000
|  reward_info = {
|  |  [1] = {
|  |  |  left_amount = 2
|  |  |  rmb = 328
|  |  }
|  |  [2] = {
|  |  |  left_amount = 2
|  |  |  rmb = 648
|  |  }
|  }
|  schedule = 1
|  start_time = 1679709600
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraWarState = {
|  state = 2
|  time = 1680364800
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraQueueStatus = {}
[core/table.lua:94]:-->Net Receive: title.GS2CRemoveTitles = {
|  tidlist = {
|  |  [1] = 1001
|  |  [2] = 1002
|  |  [3] = 1003
|  |  [4] = 1004
|  |  [5] = 1005
|  |  [6] = 1006
|  |  [7] = 1007
|  |  [8] = 1008
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "8段"
|  |  tid = 1008
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "2段"
|  |  tid = 1002
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "3段"
|  |  tid = 1003
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "4段"
|  |  tid = 1004
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "5段"
|  |  tid = 1005
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "6段"
|  |  tid = 1006
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "7段"
|  |  tid = 1007
|  }
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliReddot = {
|  cday = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTimeResumeInfo = {
|  end_time = 1682388000
|  plan_id = 1
|  start_time = 1679709600
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CRefreshTimeResume = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHDDayChargeInfo = {
|  code = 2
|  endtime = 1682388000
|  starttime = 1679709600
}
[core/table.lua:94]:-->Net Receive: huodong.GS2COneRMBGift = {
|  endtime = 1682388000
|  gift = {
|  |  [1] = {
|  |  |  key = 1
|  |  }
|  |  [2] = {
|  |  |  key = 2
|  |  }
|  |  [3] = {
|  |  |  key = 3
|  |  }
|  }
|  starttime = 1679709600
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHDAddChargeInfo = {
|  endtime = 1682388000
|  list = {
|  |  [1] = {
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  id = 2
|  |  }
|  |  [3] = {
|  |  |  id = 3
|  |  }
|  |  [4] = {
|  |  |  id = 4
|  |  }
|  |  [5] = {
|  |  |  id = 5
|  |  }
|  }
|  starttime = 1679709600
}
[core/table.lua:94]:-->Net Receive: partner.GS2CDrawCardUI = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHeroBoxRecord = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CRankBack = {
|  endtime = 1682388000
|  starttime = 1679709600
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CDailySignInfo = {
|  sign_info = {
|  |  [1] = {
|  |  |  key = "week"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CRefreshRewardBack = {}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayBuy = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CHistoryCharge = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFirstChargeUI = {
|  bOpen = true
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliPoint = {
|  point = 1
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawCnt = {
|  cnt = 2
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliTime = {
|  endtime = 1682388000
|  starttime = 1679709600
}
[core/table.lua:94]:-->Net Receive: player.GS2CTodayInfo = {
|  info = {
|  |  mask = "0"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: todyInfo = {}
[core/table.lua:94]:-->Net Receive: player.GS2CGameShare = {
|  game_share = {
|  |  [1] = {
|  |  |  type = "draw_card_share"
|  |  }
|  |  [2] = {
|  |  |  type = "skin_share"
|  |  }
|  |  [3] = {
|  |  |  type = "picture_share"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginRewardInfo = {
|  breed_val = 1
|  login_day = 2
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  mask = "1997c2e0000000"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  give_org_equip = {}
|  give_org_wish = {}
|  is_equip_wish = 0
|  is_org_wish = 0
|  org_build_status = 0
|  org_build_time = 0
|  org_id = 0
|  org_leader = ""
|  org_level = 0
|  org_pos = 0
|  org_red_packet = 0
|  org_sign_reward = 0
|  org_status = 0
|  orgname = ""
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  abnormal_attr_ratio = 500
|  |  attack = 273
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 43
|  |  mask = "87ff00"
|  |  max_hp = 1887
|  |  power = 891
|  |  res_abnormal_ratio = 500
|  |  res_critical_ratio = 500
|  |  speed = 808
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 500
|  attack = 273
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 43
|  hp = 0
|  max_hp = 1887
|  power = 891
|  res_abnormal_ratio = 500
|  res_critical_ratio = 500
|  speed = 808
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  mask = "10000000"
|  |  power_rank = 56
|  }
|  partnerid = 1
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  power_rank = 56
}
[core/table.lua:94]:-->Net Receive: house.GS2CEnterHouse = {
|  buff_info = {}
|  furniture_info = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  lock_status = 1
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  type = 2
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  type = 3
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  type = 4
|  |  }
|  |  [5] = {
|  |  |  level = 1
|  |  |  type = 5
|  |  }
|  }
|  handle_type = 1
|  max_train = 1
|  max_warm_degree = 1000
|  owner_pid = 10046
|  partner_info = {
|  |  [1] = {
|  |  |  coin = 2205
|  |  |  type = 1001
|  |  }
|  }
|  warm_degree = 12
}
[core/table.lua:94]:-->Net Receive: house.GS2CPartnerExchangeUI = {
|  handle_type = 1
|  love_cnt = 10
|  max_gift_cnt = 10
|  max_love_cnt = 10
|  partner_gift_cnt = 10
}
[core/table.lua:94]:-->Net Receive: house.GS2COpenWorkDesk = {
|  desk_info = {
|  |  [1] = {
|  |  |  lock_status = 1
|  |  |  pos = 1
|  |  |  status = 1
|  |  }
|  |  [2] = {
|  |  |  pos = 2
|  |  |  status = 1
|  |  }
|  |  [3] = {
|  |  |  pos = 3
|  |  |  status = 1
|  |  }
|  |  [4] = {
|  |  |  lock_status = 1
|  |  |  pos = 4
|  |  |  status = 1
|  |  }
|  }
|  handle_type = 1
|  owner_pid = 10046
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {
|  infolist = {
|  |  [1] = {
|  |  |  blist = {
|  |  |  |  [1] = 8
|  |  |  }
|  |  |  id = 4
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayRedDot = {}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayMain = {
|  server_day = 3
}
[core/table.lua:94]:-->Net Receive: task.GS2CLoginAchieveTask = {}
[core/global.lua:59]:<color=#ffeb04>LoginAchieveTask</color>
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1102
|  |  |  }
|  |  |  name = "峰馆长"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 2
|  |  npctype = 5040
|  }
|  eid = 1
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 1
|  pos_info = {
|  |  x = 31800
|  |  y = 17600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1102
|  }
|  name = "峰馆长"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1009
|  |  |  }
|  |  |  name = "神父"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 8
|  |  npctype = 5042
|  }
|  eid = 3
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 3
|  pos_info = {
|  |  x = 12200
|  |  y = 25000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1009
|  }
|  name = "神父"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1503
|  |  |  }
|  |  |  name = "喵小布"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 21
|  |  npctype = 5047
|  }
|  eid = 6
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 6
|  pos_info = {
|  |  x = 33000
|  |  y = 9500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1503
|  }
|  name = "喵小布"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1012
|  |  |  }
|  |  |  name = "克瑞斯汀"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 24
|  |  npctype = 5048
|  }
|  eid = 7
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 7
|  pos_info = {
|  |  x = 20200
|  |  y = 21100
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1012
|  }
|  name = "克瑞斯汀"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1510
|  |  |  }
|  |  |  name = "小佳"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 57
|  |  npctype = 5064
|  }
|  eid = 8
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 8
|  pos_info = {
|  |  x = 13800
|  |  y = 20000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1510
|  }
|  name = "小佳"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 306
|  |  |  }
|  |  |  name = "袁雀"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 60
|  |  npctype = 5001
|  }
|  eid = 9
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 9
|  pos_info = {
|  |  x = 6600
|  |  y = 26200
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 306
|  }
|  name = "袁雀"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1501
|  |  |  }
|  |  |  name = "扳尾"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 63
|  |  npctype = 5002
|  }
|  eid = 10
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 10
|  pos_info = {
|  |  x = 6000
|  |  y = 21200
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1501
|  }
|  name = "扳尾"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1014
|  |  |  }
|  |  |  name = "乔焱"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 66
|  |  npctype = 5003
|  }
|  eid = 11
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 11
|  pos_info = {
|  |  x = 13200
|  |  y = 24600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1014
|  }
|  name = "乔焱"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 414
|  |  |  }
|  |  |  name = "蓝堂昼"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 75
|  |  npctype = 5006
|  }
|  eid = 14
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 14
|  pos_info = {
|  |  x = 28000
|  |  y = 14500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 414
|  }
|  name = "蓝堂昼"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 311
|  |  |  }
|  |  |  name = "花常酒"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 78
|  |  npctype = 5007
|  }
|  eid = 15
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 15
|  pos_info = {
|  |  x = 35600
|  |  y = 13400
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 311
|  }
|  name = "花常酒"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 508
|  |  |  }
|  |  |  name = "魂夕"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 81
|  |  npctype = 5008
|  }
|  eid = 16
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 16
|  pos_info = {
|  |  x = 24000
|  |  y = 12900
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 508
|  }
|  name = "魂夕"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1502
|  |  |  }
|  |  |  name = "荆鸣"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 205
|  |  npctype = 5012
|  }
|  eid = 20
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 20
|  pos_info = {
|  |  x = 7000
|  |  y = 11300
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1502
|  }
|  name = "荆鸣"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1016
|  |  |  }
|  |  |  name = "水生"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 196
|  |  npctype = 5009
|  }
|  eid = 17
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 17
|  pos_info = {
|  |  x = 24000
|  |  y = 22000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1016
|  }
|  name = "水生"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1151
|  |  |  }
|  |  |  name = "邓酒爷"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 202
|  |  npctype = 5011
|  }
|  eid = 19
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 19
|  pos_info = {
|  |  x = 22300
|  |  y = 5100
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1151
|  }
|  name = "邓酒爷"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: title.GS2CTitleInfoList = {
|  infos = {
|  |  [1] = {
|  |  |  name = "8段"
|  |  |  tid = 1008
|  |  }
|  |  [10] = {
|  |  |  name = "应用宝贵族"
|  |  |  tid = 1017
|  |  }
|  |  [11] = {
|  |  |  name = "王者公会"
|  |  |  tid = 1018
|  |  }
|  |  [12] = {
|  |  |  name = "王者公会会长"
|  |  |  tid = 1019
|  |  }
|  |  [13] = {
|  |  |  name = "黄金公会"
|  |  |  tid = 1020
|  |  }
|  |  [14] = {
|  |  |  name = "黄金公会会长"
|  |  |  tid = 1021
|  |  }
|  |  [15] = {
|  |  |  name = "白银公会"
|  |  |  tid = 1022
|  |  }
|  |  [16] = {
|  |  |  name = "白银公会会长"
|  |  |  tid = 1023
|  |  }
|  |  [17] = {
|  |  |  name = "明日之星"
|  |  |  tid = 1024
|  |  }
|  |  [18] = {
|  |  |  name = "独孤求败"
|  |  |  tid = 1025
|  |  }
|  |  [19] = {
|  |  |  name = "谁与争锋"
|  |  |  tid = 1026
|  |  }
|  |  [2] = {
|  |  |  name = "测-贵族宝"
|  |  |  tid = 1009
|  |  }
|  |  [20] = {
|  |  |  name = "所向披靡"
|  |  |  tid = 1027
|  |  }
|  |  [21] = {
|  |  |  name = "地牢终结者"
|  |  |  tid = 1028
|  |  }
|  |  [22] = {
|  |  |  name = "地牢征服者"
|  |  |  tid = 1029
|  |  }
|  |  [23] = {
|  |  |  name = "地牢破坏者"
|  |  |  tid = 1030
|  |  }
|  |  [24] = {
|  |  |  name = "眯眯眼是变态"
|  |  |  tid = 1031
|  |  }
|  |  [25] = {
|  |  |  name = "邻家妹妹"
|  |  |  tid = 1032
|  |  }
|  |  [26] = {
|  |  |  name = "兄控"
|  |  |  tid = 1033
|  |  }
|  |  [27] = {
|  |  |  name = "农夫三拳"
|  |  |  tid = 1034
|  |  }
|  |  [28] = {
|  |  |  name = "赌神"
|  |  |  tid = 1035
|  |  }
|  |  [29] = {
|  |  |  name = "神之右手"
|  |  |  tid = 1036
|  |  }
|  |  [3] = {
|  |  |  name = "测-我就是托"
|  |  |  tid = 1010
|  |  }
|  |  [30] = {
|  |  |  name = "茶会噩梦"
|  |  |  tid = 1037
|  |  }
|  |  [31] = {
|  |  |  name = "我是傲娇"
|  |  |  tid = 1038
|  |  }
|  |  [32] = {
|  |  |  name = "闷骚美男子"
|  |  |  tid = 1039
|  |  }
|  |  [33] = {
|  |  |  name = "脸炮"
|  |  |  tid = 1040
|  |  }
|  |  [34] = {
|  |  |  name = "妹控"
|  |  |  tid = 1041
|  |  }
|  |  [35] = {
|  |  |  name = "守夜人"
|  |  |  tid = 1042
|  |  }
|  |  [36] = {
|  |  |  name = "泡泡娘"
|  |  |  tid = 1043
|  |  }
|  |  [37] = {
|  |  |  name = "姐控"
|  |  |  tid = 1044
|  |  }
|  |  [38] = {
|  |  |  name = "迷妹"
|  |  |  tid = 1045
|  |  }
|  |  [39] = {
|  |  |  name = "小天使"
|  |  |  tid = 1046
|  |  }
|  |  [4] = {
|  |  |  name = "测-粗壮大腿"
|  |  |  tid = 1011
|  |  }
|  |  [40] = {
|  |  |  name = "欺诈兔子"
|  |  |  tid = 1047
|  |  }
|  |  [41] = {
|  |  |  name = "天然呆"
|  |  |  tid = 1048
|  |  }
|  |  [42] = {
|  |  |  name = "开局一条狗"
|  |  |  tid = 1049
|  |  }
|  |  [43] = {
|  |  |  name = "种竹专业户"
|  |  |  tid = 1050
|  |  }
|  |  [44] = {
|  |  |  name = "顾家好男人"
|  |  |  tid = 1051
|  |  }
|  |  [45] = {
|  |  |  name = "无敌小钢炮"
|  |  |  tid = 1052
|  |  }
|  |  [46] = {
|  |  |  name = "小豆丁"
|  |  |  tid = 1053
|  |  }
|  |  [47] = {
|  |  |  name = "浪里白条"
|  |  |  tid = 1054
|  |  }
|  |  [48] = {
|  |  |  name = "话唠"
|  |  |  tid = 1055
|  |  }
|  |  [49] = {
|  |  |  name = "角落种蘑菇"
|  |  |  tid = 1056
|  |  }
|  |  [5] = {
|  |  |  name = "攻防战第一公会"
|  |  |  tid = 1012
|  |  }
|  |  [50] = {
|  |  |  name = "天然疯"
|  |  |  tid = 1057
|  |  }
|  |  [51] = {
|  |  |  name = "吃货"
|  |  |  tid = 1058
|  |  }
|  |  [52] = {
|  |  |  name = "控制狂"
|  |  |  tid = 1059
|  |  }
|  |  [53] = {
|  |  |  name = "女王大人"
|  |  |  tid = 1060
|  |  }
|  |  [54] = {
|  |  |  name = "八卦小王子"
|  |  |  tid = 1061
|  |  }
|  |  [55] = {
|  |  |  name = "三无"
|  |  |  tid = 1062
|  |  }
|  |  [56] = {
|  |  |  name = "养得累"
|  |  |  tid = 1063
|  |  }
|  |  [57] = {
|  |  |  name = "扑街之王"
|  |  |  tid = 1064
|  |  }
|  |  [58] = {
|  |  |  name = "抖S"
|  |  |  tid = 1065
|  |  }
|  |  [59] = {
|  |  |  name = "工作狂人"
|  |  |  tid = 1066
|  |  }
|  |  [6] = {
|  |  |  name = "攻防战第一司令"
|  |  |  tid = 1013
|  |  }
|  |  [60] = {
|  |  |  name = "精分中毒患者"
|  |  |  tid = 1067
|  |  }
|  |  [61] = {
|  |  |  name = "神神叨叨"
|  |  |  tid = 1068
|  |  }
|  |  [62] = {
|  |  |  name = "浮夸之神"
|  |  |  tid = 1069
|  |  }
|  |  [63] = {
|  |  |  name = "抖M"
|  |  |  tid = 1070
|  |  }
|  |  [64] = {
|  |  |  name = "自恋狂"
|  |  |  tid = 1071
|  |  }
|  |  [65] = {
|  |  |  name = "天然黑"
|  |  |  tid = 1072
|  |  }
|  |  [66] = {
|  |  |  name = "为所欲为"
|  |  |  tid = 1073
|  |  }
|  |  [67] = {
|  |  |  name = "氪金大佬"
|  |  |  tid = 1074
|  |  }
|  |  [68] = {
|  |  |  name = "应用宝帝王"
|  |  |  tid = 1075
|  |  }
|  |  [69] = {
|  |  |  name = "应用宝至尊"
|  |  |  tid = 1076
|  |  }
|  |  [7] = {
|  |  |  name = "梦魇之主"
|  |  |  tid = 1014
|  |  }
|  |  [70] = {
|  |  |  name = "公会新人"
|  |  |  tid = 1077
|  |  }
|  |  [71] = {
|  |  |  name = "公会成员"
|  |  |  tid = 1078
|  |  }
|  |  [72] = {
|  |  |  name = "公会精英"
|  |  |  tid = 1079
|  |  }
|  |  [73] = {
|  |  |  name = "公会副会"
|  |  |  tid = 1080
|  |  }
|  |  [74] = {
|  |  |  name = "公会会长"
|  |  |  tid = 1081
|  |  }
|  |  [75] = {
|  |  |  name = "情侣专属称谓"
|  |  |  tid = 1082
|  |  }
|  |  [76] = {
|  |  |  name = "命运之子"
|  |  |  tid = 1083
|  |  }
|  |  [77] = {
|  |  |  name = "不做大多数"
|  |  |  tid = 1084
|  |  }
|  |  [78] = {
|  |  |  name = "1段"
|  |  |  tid = 1001
|  |  }
|  |  [79] = {
|  |  |  name = "2段"
|  |  |  tid = 1002
|  |  }
|  |  [8] = {
|  |  |  name = "梦魇骑士"
|  |  |  tid = 1015
|  |  }
|  |  [80] = {
|  |  |  name = "3段"
|  |  |  tid = 1003
|  |  }
|  |  [81] = {
|  |  |  name = "4段"
|  |  |  tid = 1004
|  |  }
|  |  [82] = {
|  |  |  name = "5段"
|  |  |  tid = 1005
|  |  }
|  |  [83] = {
|  |  |  name = "6段"
|  |  |  tid = 1006
|  |  }
|  |  [84] = {
|  |  |  name = "7段"
|  |  |  tid = 1007
|  |  }
|  |  [9] = {
|  |  |  name = "梦魇猎人"
|  |  |  tid = 1016
|  |  }
|  }
}
[logic/platform/CSdkCtrl.lua:380]:sdk upload: not init
[core/table.lua:94]:Table = {
|  [1] = "16"
|  [2] = "20"
}
[core/global.lua:59]:<color=#ffeb04>2 16 20</color>
[core/table.lua:94]:Table = {
|  [1] = "21"
|  [2] = "24"
}
[core/global.lua:59]:<color=#ffeb04>2 21 24</color>
[core/table.lua:94]:Table = {
|  [1] = "25"
|  [2] = "27"
}
[core/global.lua:59]:<color=#ffeb04>2 25 27</color>
[logic/base/CResCtrl.lua:623]:res gc finish!
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10122</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小叶 1509 [19.3,20.4,0] -58 false</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小佳 1510 [18.3,20,0] -22.7 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 之前忙着做兼职，果然这次考试成绩不好。</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 哇！你还是一如既往的厉害，年级前10耶！</color>
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1508
|  |  |  }
|  |  |  name = "小叶"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 5
|  |  npctype = 5041
|  }
|  eid = 2
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 2
|  pos_info = {
|  |  x = 40000
|  |  y = 14600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1508
|  }
|  name = "小叶"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1010
|  |  |  }
|  |  |  name = "丽丝"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 69
|  |  npctype = 5004
|  }
|  eid = 12
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 12
|  pos_info = {
|  |  x = 45600
|  |  y = 26900
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1010
|  }
|  name = "丽丝"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1011
|  |  |  }
|  |  |  name = "遥"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 72
|  |  npctype = 5005
|  }
|  eid = 13
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 13
|  pos_info = {
|  |  x = 45500
|  |  y = 20000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1011
|  }
|  name = "遥"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 417
|  |  |  }
|  |  |  name = "松姑子"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 220
|  |  npctype = 5019
|  }
|  eid = 21
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 21
|  pos_info = {
|  |  x = 42300
|  |  y = 24600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 417
|  }
|  name = "松姑子"
|  trapmine = {}
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 2
|  scene_id = 12
}
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  2 mihu true</color>
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 12
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 13
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 21
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314050
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:54:10
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 是吗？我还没有找到我排名…</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  1 -155</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 你看错了，那是倒数排名。</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  2 37</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 不对啊我还看到班长名字了。</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314060
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:54:20
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 wuyu2 true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 听说，班长在考试前被没收了漫画。</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  2 zhenjing true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314070
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:54:30
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 那她一定因为伤心过度所以考砸了。</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  2 43</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 说起来考试前我看了一本小说，推荐哦！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好看吗，借我啦！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  2 [12.3,25.5,0] 90</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [11.6,25,0] 90</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 不要啦，人家还没看完呢！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 别小气嘛！</color>
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314080
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:54:40
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  2 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314090
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:54:50
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CFirstChargeView ShowView
[logic/ui/CViewBase.lua:125]:CFirstChargeView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314101
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:55:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CFirstChargeView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314111
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:55:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314121
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:55:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314131
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:55:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x53f21b10"
|  |  AssociatedPick = "function: 0x53f21b70"
|  |  AssociatedSubmit = "function: 0x53f21b40"
|  |  CreateDefalutData = "function: 0x53f1faf0"
|  |  GetChaptetFubenData = "function: 0x53f21830"
|  |  GetProgressThing = "function: 0x53f21bd0"
|  |  GetRemainTime = "function: 0x53f1fa88"
|  |  GetStatus = "function: 0x53f21c30"
|  |  GetTaskClientExtStrDic = "function: 0x53f21ba0"
|  |  GetTaskTypeSpriteteName = "function: 0x53f21800"
|  |  GetTraceInfo = "function: 0x53f21958"
|  |  GetTraceNpcType = "function: 0x53f217d0"
|  |  GetValue = "function: 0x53f21898"
|  |  IsAbandon = "function: 0x53f218f8"
|  |  IsAddEscortDynamicNpc = "function: 0x53f21f68"
|  |  IsMissMengTask = "function: 0x53f1fab8"
|  |  IsPassChaterFuben = "function: 0x53f21860"
|  |  IsTaskSpecityAction = "function: 0x53f21928"
|  |  IsTaskSpecityCategory = "function: 0x53f21ae0"
|  |  New = "function: 0x53f26460"
|  |  NewByData = "function: 0x53f1f030"
|  |  RaiseProgressIdx = "function: 0x53f21c00"
|  |  RefreshTask = "function: 0x53f218c8"
|  |  ResetEndTime = "function: 0x53f1fb58"
|  |  SetStatus = "function: 0x53f21f98"
|  |  classname = "CTask"
|  |  ctor = "function: 0x53f1fa58"
|  }
|  m_CData = {
|  |  ChapterFb = "1,1"
|  |  autoDoNextTask = 0
|  |  clientExtStr = ""
|  |  name = "少女重华"
|  |  submitNpcId = 10007
|  |  submitRewardStr = {
|  |  |  [1] = "R1003"
|  |  }
|  |  taskWalkingTips = "想不到帝都也有恶霸。,那个人是刚才的！;none;none"
|  |  tips = 0
|  |  type = 2
|  }
|  m_ChapterFb = "1,1"
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10007
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 302
|  |  |  |  }
|  |  |  |  name = "重华"
|  |  |  |  npcid = 8205
|  |  |  |  npctype = 10007
|  |  |  |  pos_info = {
|  |  |  |  |  x = 12800
|  |  |  |  |  y = 9700
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "摆脱少女重华的纠缠！"
|  |  isdone = 0
|  |  name = "少女重华"
|  |  patrolinfo = {}
|  |  pickiteminfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10007
|  |  target = 10007
|  |  targetdesc = "遭到质问"
|  |  taskid = 10003
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 3
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1510
|  |  |  }
|  |  |  name = "小佳"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 11
|  |  npctype = 5043
|  }
|  eid = 4
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 4
|  pos_info = {
|  |  x = 27000
|  |  y = 4000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1510
|  }
|  name = "小佳"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1510
|  |  |  }
|  |  |  name = "小兰"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 14
|  |  npctype = 5044
|  }
|  eid = 5
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 5
|  pos_info = {
|  |  x = 19000
|  |  y = 4000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1510
|  }
|  name = "小兰"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 9
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1150
|  |  |  }
|  |  |  name = "飞龙哥"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 199
|  |  npctype = 5010
|  }
|  eid = 18
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 18
|  pos_info = {
|  |  x = 29000
|  |  y = 3500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1150
|  }
|  name = "飞龙哥"
|  trapmine = {}
}
[core/global.lua:59]:<color=#ffeb04> Trigger............  8205</color>
[core/table.lua:94]:Table = {
|  map_id = 101000
|  model_info = {
|  |  scale = 1
|  |  shape = 302
|  }
|  name = "重华"
|  npcid = 8205
|  npctype = 10007
|  pos_info = {
|  |  x = 12.8
|  |  y = 9.7
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8001
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickNpc = {
|  npcid = 8205
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8001
}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcSay = {
|  name = "重华"
|  npcid = 8205
|  shape = 302
|  text = "那个……请问……"
}
[core/global.lua:59]:<color=#ffeb04> fight  false</color>
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 7005
}
[core/table.lua:94]:<--Net Send: task.C2GSAcceptTask = {
|  taskid = 10003
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 7005
}
[core/table.lua:94]:-->Net Receive: task.GS2CDialog = {
|  dialog = {
|  |  [1] = {
|  |  |  content = "刚才很感谢你。打扰一下……你也是来参加武道大会的武者吗？"
|  |  |  next = "2"
|  |  |  pre_id_list = "10007,0"
|  |  |  status = 2
|  |  |  subid = 1
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [2] = {
|  |  |  content = "嗯。"
|  |  |  next = "3"
|  |  |  pre_id_list = "10007,0"
|  |  |  status = 2
|  |  |  subid = 2
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  |  [3] = {
|  |  |  content = "我是重华，也是来参加武道大会的。看到你的招式，跟我听过的某个地方的武技很像，你是不是…… "
|  |  |  next = "4"
|  |  |  pre_id_list = "10007,0"
|  |  |  status = 2
|  |  |  subid = 3
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [4] = {
|  |  |  content = "无可奉告。"
|  |  |  next = "5"
|  |  |  pre_id_list = "10007,0"
|  |  |  status = 2
|  |  |  subid = 4
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  |  [5] = {
|  |  |  content = "等等！那至少告诉重华你的名字……"
|  |  |  next = "6"
|  |  |  pre_id_list = "10007,0"
|  |  |  status = 2
|  |  |  subid = 5
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [6] = {
|  |  |  content = "以后再说吧。"
|  |  |  finish_event = "DONE"
|  |  |  next = "0"
|  |  |  pre_id_list = "10007,0"
|  |  |  status = 2
|  |  |  subid = 6
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  |  [7] = {
|  |  |  content = "横啊！就不信这么多人还撂不倒你！都给我上，让他看看商业街到底是谁说了算！"
|  |  |  next = "0"
|  |  |  pre_id_list = "10001"
|  |  |  status = 2
|  |  |  subid = 7
|  |  |  type = 3
|  |  |  ui_mode = 2
|  |  }
|  }
|  dialog_id = 10003
|  npc_id = 8205
|  npc_name = "重华"
|  sessionidx = "2165042"
|  shape = 302
|  task_big_type = 2
|  task_small_type = 1
}
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshTask = {
|  accepttime = 1680314135
|  name = "少女重华"
|  statusinfo = {
|  |  note = "Accept Task"
|  |  status = 1
|  }
|  target = 10007
|  taskid = 10003
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 7
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 8
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 10
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 11
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 17
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314141
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:55:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10122</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314151
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:55:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 1524
|  |  id = 10602
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314161
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:56:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314171
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:56:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314181
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:56:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314191
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:56:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314201
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:56:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314211
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:56:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314221
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:57:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 3</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 3003
}
[core/table.lua:94]:<--Net Send: other.C2GSCallback = {
|  sessionidx = "2165042"
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 3003
}
[core/table.lua:94]:-->Net Receive: task.GS2CDelTask = {
|  done = 1
|  taskid = 10003
}
[core/table.lua:94]:-->Net Receive: task.GS2CAddTask = {
|  taskdata = {
|  |  acceptnpc = 10008
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 1102
|  |  |  |  }
|  |  |  |  name = "店老板"
|  |  |  |  npcid = 8206
|  |  |  |  npctype = 10008
|  |  |  |  pos_info = {
|  |  |  |  |  x = 13000
|  |  |  |  |  y = 1600
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "向面店老板询问武道大会的比赛信息！"
|  |  name = "询问"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 10002
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10008
|  |  target = 10008
|  |  targetdesc = "大会信息"
|  |  taskid = 10004
|  |  taskitem = {}
|  |  tasktype = 15
|  |  traceinfo = {}
|  |  type = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>add task </color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x53f21b10"
|  |  AssociatedPick = "function: 0x53f21b70"
|  |  AssociatedSubmit = "function: 0x53f21b40"
|  |  CreateDefalutData = "function: 0x53f1faf0"
|  |  GetChaptetFubenData = "function: 0x53f21830"
|  |  GetProgressThing = "function: 0x53f21bd0"
|  |  GetRemainTime = "function: 0x53f1fa88"
|  |  GetStatus = "function: 0x53f21c30"
|  |  GetTaskClientExtStrDic = "function: 0x53f21ba0"
|  |  GetTaskTypeSpriteteName = "function: 0x53f21800"
|  |  GetTraceInfo = "function: 0x53f21958"
|  |  GetTraceNpcType = "function: 0x53f217d0"
|  |  GetValue = "function: 0x53f21898"
|  |  IsAbandon = "function: 0x53f218f8"
|  |  IsAddEscortDynamicNpc = "function: 0x53f21f68"
|  |  IsMissMengTask = "function: 0x53f1fab8"
|  |  IsPassChaterFuben = "function: 0x53f21860"
|  |  IsTaskSpecityAction = "function: 0x53f21928"
|  |  IsTaskSpecityCategory = "function: 0x53f21ae0"
|  |  New = "function: 0x53f26460"
|  |  NewByData = "function: 0x53f1f030"
|  |  RaiseProgressIdx = "function: 0x53f21c00"
|  |  RefreshTask = "function: 0x53f218c8"
|  |  ResetEndTime = "function: 0x53f1fb58"
|  |  SetStatus = "function: 0x53f21f98"
|  |  classname = "CTask"
|  |  ctor = "function: 0x53f1fa58"
|  }
|  m_CData = {
|  |  ChapterFb = "1,1"
|  |  autoDoNextTask = 10005
|  |  clientExtStr = ""
|  |  name = "询问"
|  |  submitNpcId = 10008
|  |  submitRewardStr = {
|  |  |  [1] = "R1004"
|  |  }
|  |  taskWalkingTips = "前面卖什么好香啊！;none;none"
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10008
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 1102
|  |  |  |  }
|  |  |  |  name = "店老板"
|  |  |  |  npcid = 8206
|  |  |  |  npctype = 10008
|  |  |  |  pos_info = {
|  |  |  |  |  x = 13000
|  |  |  |  |  y = 1600
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "向面店老板询问武道大会的比赛信息！"
|  |  isdone = 0
|  |  name = "询问"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 10002
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10008
|  |  target = 10008
|  |  targetdesc = "大会信息"
|  |  taskid = 10004
|  |  taskitem = {}
|  |  tasktype = 15
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  coin = 14250
|  |  exp = 1810
|  |  mask = "60"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  coin = 14250
|  exp = 1810
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得#exp#G200#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#exp#G200#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#w1#G2060#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#w1#G2060#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 200
|  |  mask = "80"
|  }
|  partnerid = 1
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 200
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B重华#n获得#hexp#G200#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B重华#n获得#hexp#G200#n"
|  type = 6
}
[logic/ui/CViewCtrl.lua:94]:CDialogueStoryStartView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueStoryStartView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314231
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:57:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CDialogueStoryStartView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314241
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:57:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314251
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:57:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314261
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:57:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x53f21b10"
|  |  AssociatedPick = "function: 0x53f21b70"
|  |  AssociatedSubmit = "function: 0x53f21b40"
|  |  CreateDefalutData = "function: 0x53f1faf0"
|  |  GetChaptetFubenData = "function: 0x53f21830"
|  |  GetProgressThing = "function: 0x53f21bd0"
|  |  GetRemainTime = "function: 0x53f1fa88"
|  |  GetStatus = "function: 0x53f21c30"
|  |  GetTaskClientExtStrDic = "function: 0x53f21ba0"
|  |  GetTaskTypeSpriteteName = "function: 0x53f21800"
|  |  GetTraceInfo = "function: 0x53f21958"
|  |  GetTraceNpcType = "function: 0x53f217d0"
|  |  GetValue = "function: 0x53f21898"
|  |  IsAbandon = "function: 0x53f218f8"
|  |  IsAddEscortDynamicNpc = "function: 0x53f21f68"
|  |  IsMissMengTask = "function: 0x53f1fab8"
|  |  IsPassChaterFuben = "function: 0x53f21860"
|  |  IsTaskSpecityAction = "function: 0x53f21928"
|  |  IsTaskSpecityCategory = "function: 0x53f21ae0"
|  |  New = "function: 0x53f26460"
|  |  NewByData = "function: 0x53f1f030"
|  |  RaiseProgressIdx = "function: 0x53f21c00"
|  |  RefreshTask = "function: 0x53f218c8"
|  |  ResetEndTime = "function: 0x53f1fb58"
|  |  SetStatus = "function: 0x53f21f98"
|  |  classname = "CTask"
|  |  ctor = "function: 0x53f1fa58"
|  }
|  m_CData = {
|  |  ChapterFb = "1,1"
|  |  autoDoNextTask = 10005
|  |  clientExtStr = ""
|  |  name = "询问"
|  |  submitNpcId = 10008
|  |  submitRewardStr = {
|  |  |  [1] = "R1004"
|  |  }
|  |  taskWalkingTips = "前面卖什么好香啊！;none;none"
|  |  tips = 0
|  |  type = 2
|  }
|  m_ChapterFb = "1,1"
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10008
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 1102
|  |  |  |  }
|  |  |  |  name = "店老板"
|  |  |  |  npcid = 8206
|  |  |  |  npctype = 10008
|  |  |  |  pos_info = {
|  |  |  |  |  x = 13000
|  |  |  |  |  y = 1600
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "向面店老板询问武道大会的比赛信息！"
|  |  isdone = 0
|  |  name = "询问"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 10002
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10008
|  |  target = 10008
|  |  targetdesc = "大会信息"
|  |  taskid = 10004
|  |  taskitem = {}
|  |  tasktype = 15
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 1
|  scene_id = 12
}
[core/global.lua:59]:<color=#ffeb04> Trigger............  8206</color>
[core/table.lua:94]:Table = {
|  map_id = 101000
|  model_info = {
|  |  scale = 1
|  |  shape = 1102
|  }
|  name = "店老板"
|  npcid = 8206
|  npctype = 10008
|  pos_info = {
|  |  x = 13
|  |  y = 1.6
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8001
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickNpc = {
|  npcid = 8206
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8001
}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcSay = {
|  name = "店老板"
|  npcid = 8206
|  shape = 1102
|  text = "欢迎光临！请问要吃点什么？"
}
[core/global.lua:59]:<color=#ffeb04> fight  false</color>
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 7005
}
[core/table.lua:94]:<--Net Send: task.C2GSAcceptTask = {
|  taskid = 10004
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 7005
}
[core/table.lua:94]:-->Net Receive: task.GS2CDialog = {
|  dialog = {
|  |  [1] = {
|  |  |  content = "吃点热的吧，小萌给的包子都冷了。"
|  |  |  next = "2"
|  |  |  pre_id_list = "10008,0"
|  |  |  status = 2
|  |  |  subid = 1
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  |  [2] = {
|  |  |  content = "欢迎光临！招牌叉烧面要来一碗吗？"
|  |  |  next = "3"
|  |  |  pre_id_list = "10008,0"
|  |  |  status = 2
|  |  |  subid = 2
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [3] = {
|  |  |  content = "好！哎，老板想问个问题。武道大会相关资讯可以在哪里获得呢？"
|  |  |  next = "4"
|  |  |  pre_id_list = "10008,0"
|  |  |  status = 2
|  |  |  subid = 3
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  |  [4] = {
|  |  |  content = "你肯定第一次来吧！我在这里开店好久，每年都会接待好多选手，规则都记熟咯。你替我的面店做个宣传，我就告诉你想知道的。"
|  |  |  finish_event = "DONE"
|  |  |  last_action = {
|  |  |  |  [1] = {
|  |  |  |  |  content = "搞怪姿势"
|  |  |  |  |  event = "social;10002;2"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  content = "跳舞助兴"
|  |  |  |  |  event = "social;10003;14"
|  |  |  |  }
|  |  |  }
|  |  |  next = "0"
|  |  |  pre_id_list = "10008,0"
|  |  |  status = 2
|  |  |  subid = 4
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  }
|  dialog_id = 10004
|  npc_id = 8206
|  npc_name = "店老板"
|  sessionidx = "2166042"
|  shape = 1102
|  task_big_type = 2
|  task_small_type = 15
}
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshTask = {
|  accepttime = 1680314266
|  name = "询问"
|  statusinfo = {
|  |  note = "Accept Task"
|  |  status = 1
|  }
|  target = 10008
|  taskid = 10004
}
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314271
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:57:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 1524
|  |  id = 10602
|  }
}
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314281
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:58:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314291
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:58:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314301
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:58:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314311
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:58:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314321
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:58:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 23052
}
[core/table.lua:94]:<--Net Send: huodong.C2GSSocailDisplay = {
|  id = 10002
|  target_pid = 0
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 23052
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CSocialDisplayInfo = {
|  social_display = {
|  |  display_id = 10002
|  |  start_time = 1680314326
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 3003
}
[core/table.lua:94]:<--Net Send: other.C2GSCallback = {
|  sessionidx = "2166042"
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 3003
}
[core/table.lua:94]:-->Net Receive: task.GS2CDelTask = {
|  done = 1
|  taskid = 10004
}
[core/table.lua:94]:-->Net Receive: task.GS2CAddTask = {
|  taskdata = {
|  |  acceptnpc = 10009
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 1102
|  |  |  |  }
|  |  |  |  name = "店老板"
|  |  |  |  npcid = 8207
|  |  |  |  npctype = 10009
|  |  |  |  pos_info = {
|  |  |  |  |  x = 20500
|  |  |  |  |  y = 3200
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "想面店老板询问武道大会的比赛信息！"
|  |  name = "盛会"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10009
|  |  target = 10009
|  |  targetdesc = "过去的历史"
|  |  taskid = 10005
|  |  taskitem = {}
|  |  tasktype = 1
|  |  traceinfo = {}
|  |  type = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>add task </color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x53f21b10"
|  |  AssociatedPick = "function: 0x53f21b70"
|  |  AssociatedSubmit = "function: 0x53f21b40"
|  |  CreateDefalutData = "function: 0x53f1faf0"
|  |  GetChaptetFubenData = "function: 0x53f21830"
|  |  GetProgressThing = "function: 0x53f21bd0"
|  |  GetRemainTime = "function: 0x53f1fa88"
|  |  GetStatus = "function: 0x53f21c30"
|  |  GetTaskClientExtStrDic = "function: 0x53f21ba0"
|  |  GetTaskTypeSpriteteName = "function: 0x53f21800"
|  |  GetTraceInfo = "function: 0x53f21958"
|  |  GetTraceNpcType = "function: 0x53f217d0"
|  |  GetValue = "function: 0x53f21898"
|  |  IsAbandon = "function: 0x53f218f8"
|  |  IsAddEscortDynamicNpc = "function: 0x53f21f68"
|  |  IsMissMengTask = "function: 0x53f1fab8"
|  |  IsPassChaterFuben = "function: 0x53f21860"
|  |  IsTaskSpecityAction = "function: 0x53f21928"
|  |  IsTaskSpecityCategory = "function: 0x53f21ae0"
|  |  New = "function: 0x53f26460"
|  |  NewByData = "function: 0x53f1f030"
|  |  RaiseProgressIdx = "function: 0x53f21c00"
|  |  RefreshTask = "function: 0x53f218c8"
|  |  ResetEndTime = "function: 0x53f1fb58"
|  |  SetStatus = "function: 0x53f21f98"
|  |  classname = "CTask"
|  |  ctor = "function: 0x53f1fa58"
|  }
|  m_CData = {
|  |  ChapterFb = "1,1"
|  |  autoDoNextTask = 10007
|  |  clientExtStr = ""
|  |  name = "盛会"
|  |  submitNpcId = 10009
|  |  submitRewardStr = {
|  |  |  [1] = "R1005"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10009
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 1102
|  |  |  |  }
|  |  |  |  name = "店老板"
|  |  |  |  npcid = 8207
|  |  |  |  npctype = 10009
|  |  |  |  pos_info = {
|  |  |  |  |  x = 20500
|  |  |  |  |  y = 3200
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "想面店老板询问武道大会的比赛信息！"
|  |  isdone = 0
|  |  name = "盛会"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10009
|  |  target = 10009
|  |  targetdesc = "过去的历史"
|  |  taskid = 10005
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  coin = 24250
|  |  exp = 2010
|  |  mask = "60"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  coin = 24250
|  exp = 2010
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得#exp#G200#n"
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 400
|  |  mask = "80"
|  }
|  partnerid = 1
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 400
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#exp#G200#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#w1#G10000#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#w1#G10000#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B重华#n获得#hexp#G200#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B重华#n获得#hexp#G200#n"
|  type = 6
}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x53f21b10"
|  |  AssociatedPick = "function: 0x53f21b70"
|  |  AssociatedSubmit = "function: 0x53f21b40"
|  |  CreateDefalutData = "function: 0x53f1faf0"
|  |  GetChaptetFubenData = "function: 0x53f21830"
|  |  GetProgressThing = "function: 0x53f21bd0"
|  |  GetRemainTime = "function: 0x53f1fa88"
|  |  GetStatus = "function: 0x53f21c30"
|  |  GetTaskClientExtStrDic = "function: 0x53f21ba0"
|  |  GetTaskTypeSpriteteName = "function: 0x53f21800"
|  |  GetTraceInfo = "function: 0x53f21958"
|  |  GetTraceNpcType = "function: 0x53f217d0"
|  |  GetValue = "function: 0x53f21898"
|  |  IsAbandon = "function: 0x53f218f8"
|  |  IsAddEscortDynamicNpc = "function: 0x53f21f68"
|  |  IsMissMengTask = "function: 0x53f1fab8"
|  |  IsPassChaterFuben = "function: 0x53f21860"
|  |  IsTaskSpecityAction = "function: 0x53f21928"
|  |  IsTaskSpecityCategory = "function: 0x53f21ae0"
|  |  New = "function: 0x53f26460"
|  |  NewByData = "function: 0x53f1f030"
|  |  RaiseProgressIdx = "function: 0x53f21c00"
|  |  RefreshTask = "function: 0x53f218c8"
|  |  ResetEndTime = "function: 0x53f1fb58"
|  |  SetStatus = "function: 0x53f21f98"
|  |  classname = "CTask"
|  |  ctor = "function: 0x53f1fa58"
|  }
|  m_CData = {
|  |  ChapterFb = "1,1"
|  |  autoDoNextTask = 10007
|  |  clientExtStr = ""
|  |  name = "盛会"
|  |  submitNpcId = 10009
|  |  submitRewardStr = {
|  |  |  [1] = "R1005"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_ChapterFb = "1,1"
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10009
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 1102
|  |  |  |  }
|  |  |  |  name = "店老板"
|  |  |  |  npcid = 8207
|  |  |  |  npctype = 10009
|  |  |  |  pos_info = {
|  |  |  |  |  x = 20500
|  |  |  |  |  y = 3200
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "想面店老板询问武道大会的比赛信息！"
|  |  isdone = 0
|  |  name = "盛会"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10009
|  |  target = 10009
|  |  targetdesc = "过去的历史"
|  |  taskid = 10005
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CSocialDisplayInfo = {}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314331
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:58:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> Trigger............  8207</color>
[core/table.lua:94]:Table = {
|  map_id = 101000
|  model_info = {
|  |  scale = 1
|  |  shape = 1102
|  }
|  name = "店老板"
|  npcid = 8207
|  npctype = 10009
|  pos_info = {
|  |  x = 20.5
|  |  y = 3.2
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8001
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickNpc = {
|  npcid = 8207
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8001
}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcSay = {
|  name = "店老板"
|  npcid = 8207
|  shape = 1102
|  text = "欢迎光临！请问要吃点什么？"
}
[core/global.lua:59]:<color=#ffeb04> fight  false</color>
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 7005
}
[core/table.lua:94]:<--Net Send: task.C2GSAcceptTask = {
|  taskid = 10005
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 7005
}
[core/table.lua:94]:-->Net Receive: task.GS2CDialog = {
|  dialog = {
|  |  [1] = {
|  |  |  content = "说起武道大会，那就得先谈一段历史。760年人族胜利帝国成立，为了强大国人体开始倡导全民习武。到统帅部成立依靠武道大会挑选人才，后来变成了这一年一次的盛会呐。"
|  |  |  next = "2"
|  |  |  pre_id_list = "10009,0"
|  |  |  status = 2
|  |  |  subid = 1
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [2] = {
|  |  |  content = "我只想知道武道大会的信息一般在哪里公布之类的，谢谢。"
|  |  |  next = "3"
|  |  |  pre_id_list = "10009,0"
|  |  |  status = 2
|  |  |  subid = 2
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  |  [3] = {
|  |  |  content = "传说，国家成立后，将原本为祸的大妖封印在月见岛，从那以后月见岛才成为了极夜的诅咒之岛。虽说没有参与战争的妖族得到了与人族相同的待遇，但从不会被统帅部录用。"
|  |  |  next = "4"
|  |  |  pre_id_list = "10009,0"
|  |  |  status = 2
|  |  |  subid = 3
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [4] = {
|  |  |  content = "哦，你们可以在码头边的公告栏看武道大会最新的资讯。"
|  |  |  next = "5"
|  |  |  pre_id_list = "10009,0"
|  |  |  status = 2
|  |  |  subid = 4
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [5] = {
|  |  |  content = "谢谢，我现在就去看看，面钱给你。"
|  |  |  next = "6"
|  |  |  pre_id_list = "10009,0"
|  |  |  status = 2
|  |  |  subid = 5
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  |  [6] = {
|  |  |  content = "哎等会儿，差点忘记跟你说，要参赛记得去统帅部报名，今天最后一天截止啦！"
|  |  |  next = "7"
|  |  |  pre_id_list = "10010,0"
|  |  |  status = 2
|  |  |  subid = 6
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [7] = {
|  |  |  content = "那么重要的信息为什么不早点说！"
|  |  |  finish_event = "DONE"
|  |  |  next = "0"
|  |  |  pre_id_list = "10010,0"
|  |  |  status = 2
|  |  |  subid = 7
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  }
|  dialog_id = 10005
|  npc_id = 8207
|  npc_name = "店老板"
|  sessionidx = "2167042"
|  shape = 1102
|  task_big_type = 2
|  task_small_type = 1
}
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshTask = {
|  accepttime = 1680314332
|  name = "盛会"
|  statusinfo = {
|  |  note = "Accept Task"
|  |  status = 1
|  }
|  target = 10009
|  taskid = 10005
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314341
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:59:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314351
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:59:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314361
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:59:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314371
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:59:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314381
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:59:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314391
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 09:59:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:-->Net Receive: huodong.GS2CResumeRestore = {
|  end_time = 1682388000
|  plan_id = 1
|  start_time = 1679709600
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CRefreshResumeRestore = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTimeResumeInfo = {
|  end_time = 1682388000
|  plan_id = 1
|  start_time = 1679709600
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CRefreshTimeResume = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHeroBoxRecord = {}
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "天降宝物，世界各地出现了大量的灵魂宝箱，欢迎冒险者们前去寻宝"
|  grade = 28
|  horse_race = 1
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 1524
|  |  id = 10602
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314401
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:00:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314411
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:00:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314421
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:00:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314431
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:00:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/global.lua:59]:<color=#ffeb04>OnContinue 3</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 3003
}
[core/table.lua:94]:<--Net Send: other.C2GSCallback = {
|  sessionidx = "2167042"
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 3003
}
[core/table.lua:94]:-->Net Receive: task.GS2CDelTask = {
|  done = 1
|  taskid = 10005
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 600
|  |  mask = "80"
|  }
|  partnerid = 1
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 600
}
[core/table.lua:94]:-->Net Receive: task.GS2CAddTask = {
|  taskdata = {
|  |  acceptnpc = 10011
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 513
|  |  |  |  }
|  |  |  |  name = "夜叉"
|  |  |  |  npcid = 8264
|  |  |  |  npctype = 10011
|  |  |  |  pos_info = {
|  |  |  |  |  x = 29400
|  |  |  |  |  y = 12500
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "眼前的人竟然知道残页的存在，她是谁？  "
|  |  name = "问路"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10011
|  |  target = 10011
|  |  targetdesc = "恶人"
|  |  taskid = 10007
|  |  taskitem = {}
|  |  tasktype = 1
|  |  traceinfo = {}
|  |  type = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>add task </color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x53f21b10"
|  |  AssociatedPick = "function: 0x53f21b70"
|  |  AssociatedSubmit = "function: 0x53f21b40"
|  |  CreateDefalutData = "function: 0x53f1faf0"
|  |  GetChaptetFubenData = "function: 0x53f21830"
|  |  GetProgressThing = "function: 0x53f21bd0"
|  |  GetRemainTime = "function: 0x53f1fa88"
|  |  GetStatus = "function: 0x53f21c30"
|  |  GetTaskClientExtStrDic = "function: 0x53f21ba0"
|  |  GetTaskTypeSpriteteName = "function: 0x53f21800"
|  |  GetTraceInfo = "function: 0x53f21958"
|  |  GetTraceNpcType = "function: 0x53f217d0"
|  |  GetValue = "function: 0x53f21898"
|  |  IsAbandon = "function: 0x53f218f8"
|  |  IsAddEscortDynamicNpc = "function: 0x53f21f68"
|  |  IsMissMengTask = "function: 0x53f1fab8"
|  |  IsPassChaterFuben = "function: 0x53f21860"
|  |  IsTaskSpecityAction = "function: 0x53f21928"
|  |  IsTaskSpecityCategory = "function: 0x53f21ae0"
|  |  New = "function: 0x53f26460"
|  |  NewByData = "function: 0x53f1f030"
|  |  RaiseProgressIdx = "function: 0x53f21c00"
|  |  RefreshTask = "function: 0x53f218c8"
|  |  ResetEndTime = "function: 0x53f1fb58"
|  |  SetStatus = "function: 0x53f21f98"
|  |  classname = "CTask"
|  |  ctor = "function: 0x53f1fa58"
|  }
|  m_CData = {
|  |  ChapterFb = "1,1"
|  |  autoDoNextTask = 0
|  |  clientExtStr = ""
|  |  name = "问路"
|  |  submitNpcId = 10011
|  |  submitRewardStr = {
|  |  |  [1] = "R1007"
|  |  }
|  |  taskWalkingTips = "不对统帅部在哪？;none;none"
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10011
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 513
|  |  |  |  }
|  |  |  |  name = "夜叉"
|  |  |  |  npcid = 8264
|  |  |  |  npctype = 10011
|  |  |  |  pos_info = {
|  |  |  |  |  x = 29400
|  |  |  |  |  y = 12500
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "眼前的人竟然知道残页的存在，她是谁？  "
|  |  isdone = 0
|  |  name = "问路"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10011
|  |  target = 10011
|  |  targetdesc = "恶人"
|  |  taskid = 10007
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  abnormal_attr_ratio = 510
|  |  attack = 279
|  |  coin = 26340
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 43
|  |  energy = 123
|  |  exp = 2210
|  |  grade = 3
|  |  hp = 1887
|  |  kp_sdk_info = {
|  |  |  create_time = **********
|  |  |  upgrade_time = 1680314437
|  |  }
|  |  mask = "10020000287ff62"
|  |  max_hp = 1939
|  |  power = 920
|  |  res_abnormal_ratio = 510
|  |  res_critical_ratio = 500
|  |  skill_point = 2
|  |  speed = 809
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 510
|  attack = 279
|  coin = 26340
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 43
|  energy = 123
|  exp = 2210
|  grade = 3
|  hp = 1887
|  kp_sdk_info = {
|  |  create_time = **********
|  |  upgrade_time = 1680314437
|  }
|  max_hp = 1939
|  power = 920
|  res_abnormal_ratio = 510
|  res_critical_ratio = 500
|  skill_point = 2
|  speed = 809
}
[logic/platform/CSdkCtrl.lua:380]:sdk upload: not init
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Partner_FWCD_One_MainMenu"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/attr/CAttrCtrl"]:141: in function 'UpdateAttr'
	[string "net/netplayer"]:9: in function <[string "net/netplayer"]:5>
	[C]: in function 'xxpcall'
	[string "net/CNetCtrl"]:318: in function 'ProtoCall'
	[string "net/CNetCtrl"]:300: in function 'ProcessProto'
	[string "net/CNetCtrl"]:251: in function 'ProtoHandlerCheck'
	[string "net/CNetCtrl"]:262: in function 'Receive'
	[string "net/CNetCtrl"]:175: in function 'OnSocketEvent'
	[string "net/CNetCtrl"]:111: in function 'm_Callback'
	[string "net/CTcpClient"]:43: in function <[string "net/CTcpClient"]:37>
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3
|  |  id = 10101
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3
|  |  id = 10501
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3
|  |  id = 10401
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3
|  |  id = 10301
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3
|  |  id = 10701
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3
|  |  id = 10201
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 3
|  |  id = 10601
|  }
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得#exp#G200#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#exp#G200#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B重华#n获得#hexp#G200#n"
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 1553
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B重华#n获得#hexp#G200#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#w1#G2090#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#w1#G2090#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 1553
|  |  id = 10602
|  }
}
[core/global.lua:59]:<color=#ffeb04>找到新的引导类型 Partner_FWCD_One_MainMenu</color>
[logic/ui/CViewCtrl.lua:94]:CGuideView ShowView
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Partner_FWCD_One_MainMenu"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/task/CTaskCtrl"]:2434: in function 'StartAutoDoingShiMen'
	[string "logic/guide/CGuideCtrl"]:2383: in function 'StopActionWhenGuide'
	[string "logic/guide/CGuideView"]:31: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:258: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:245: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:224: in function <[string "logic/base/CResCtrl"]:223>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:265: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:308: in function <[string "logic/base/CResCtrl"]:302>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[logic/ui/CViewBase.lua:125]:CGuideView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 Partner_FWCD_One_MainMenu_1 1</color>
[logic/model/CHero.lua:65]:same sync pos
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Partner_FWCD_One_MainMenu"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Partner_FWCD_One_MainMenu"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/mainmenu/CMainMenuExpandBox"]:280: in function <[string "logic/mainmenu/CMainMenuExpandBox"]:251>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Partner_FWCD_One_MainMenu"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/table.lua:94]:Table = {
|  [1] = "16"
|  [2] = "20"
}
[core/global.lua:59]:<color=#ffeb04>3 16 20</color>
[core/table.lua:94]:Table = {
|  [1] = "21"
|  [2] = "24"
}
[core/global.lua:59]:<color=#ffeb04>3 21 24</color>
[core/table.lua:94]:Table = {
|  [1] = "25"
|  [2] = "27"
}
[core/global.lua:59]:<color=#ffeb04>3 25 27</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314441
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:00:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314451
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:00:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314461
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:01:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314471
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:01:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314481
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:01:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314491
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:01:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314501
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:01:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314511
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:01:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 1553
|  |  id = 10602
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314521
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:02:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314531
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:02:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314541
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:02:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314551
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:02:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314561
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:02:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314571
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:02:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314581
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:03:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314591
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:03:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314601
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:03:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314611
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:03:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314621
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:03:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314631
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:03:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314641
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:04:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314651
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:04:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314661
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:04:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314671
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:04:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314681
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:04:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314691
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:04:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314701
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:05:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CLockScreenView ShowView
[logic/ui/CViewBase.lua:125]:CLockScreenView LoadDone!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Partner_FWCD_One_MainMenu"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314711
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:05:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314721
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:05:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314731
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:05:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314741
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:05:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314751
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:05:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314761
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:06:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314771
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:06:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314781
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:06:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314791
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:06:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314801
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:06:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314811
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:06:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314821
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:07:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314831
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:07:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314841
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:07:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314851
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:07:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314861
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:07:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314871
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:07:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314881
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:08:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314891
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:08:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314901
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:08:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314911
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:08:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314921
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:08:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314931
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:08:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314941
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:09:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314951
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:09:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314961
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:09:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314971
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:09:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314981
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:09:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680314991
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:09:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315001
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:10:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315011
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:10:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315021
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:10:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315031
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:10:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315041
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:10:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315051
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:10:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315061
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:11:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315071
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:11:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315081
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:11:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315091
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:11:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315101
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:11:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315111
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:11:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315121
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:12:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315131
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:12:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315141
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:12:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315151
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:12:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315161
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:12:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315171
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:12:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315181
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:13:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315191
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:13:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315201
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:13:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315211
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:13:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315221
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:13:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315231
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:13:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315241
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:14:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315251
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:14:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315261
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:14:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315271
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:14:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315281
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:14:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315291
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:14:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315301
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:15:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315311
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:15:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315321
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:15:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315331
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:15:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315341
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:15:41
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315351
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:15:51
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315361
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:16:01
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315371
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:16:11
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315381
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:16:21
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315391
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:16:31
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315402
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:16:42
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315412
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:16:52
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315422
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:17:02
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315432
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:17:12
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315442
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:17:22
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315452
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:17:32
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315462
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:17:42
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315472
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:17:52
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315482
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:18:02
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315492
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:18:12
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315502
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:18:22
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315512
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:18:32
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315522
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:18:42
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315532
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:18:52
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315542
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:19:02
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315552
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:19:12
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315562
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:19:22
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315572
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:19:32
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315582
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:19:42
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315592
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:19:52
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315602
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:20:02
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315612
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:20:12
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315622
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:20:22
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315632
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:20:32
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315642
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:20:42
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315652
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:20:52
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315662
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:21:02
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315672
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:21:12
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315682
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:21:22
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315692
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:21:32
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315702
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:21:42
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315712
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:21:52
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315722
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:22:02
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315732
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:22:12
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315742
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:22:22
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315752
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:22:32
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315762
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:22:42
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315772
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:22:52
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315782
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:23:02
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315792
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:23:12
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315802
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:23:22
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315812
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:23:32
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315822
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:23:42
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315832
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:23:52
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315842
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:24:02
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315852
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:24:12
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315862
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:24:22
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315872
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:24:32
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315882
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:24:42
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315892
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:24:52
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315902
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:25:02
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315912
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:25:12
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315922
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:25:22
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315932
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:25:32
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315942
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:25:42
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315952
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:25:52
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315962
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:26:02
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315972
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:26:12
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315982
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:26:22
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680315992
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:26:32
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316002
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:26:42
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316012
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:26:52
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316022
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:27:02
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316032
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:27:12
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316042
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:27:22
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316052
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:27:32
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316062
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:27:42
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CLockScreenView     CloseView
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Partner_FWCD_One_MainMenu"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316072
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:27:52
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316082
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:28:02
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316092
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:28:12
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>引导结束 Partner_FWCD_One_MainMenu Partner_FWCD_One_MainMenu_1 1</color>
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Partner_FWCD_One_MainMenu_1"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1000101
|  }
}
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 Partner_FWCD_One_MainMenu_2 2</color>
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Partner_FWCD_One_MainMenu_2"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1000102
|  }
}
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Partner_FWCD_One_MainMenu"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1001099
|  }
}
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[logic/ui/CViewBase.lua:131]:CGuideMaskView LoadDone, not in loadingview!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/ui/CViewBase"]:139: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:258: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:245: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:224: in function <[string "logic/base/CResCtrl"]:223>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:265: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:308: in function <[string "logic/base/CResCtrl"]:302>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316102
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:28:22
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316112
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:28:32
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316122
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:28:42
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316132
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:28:52
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316142
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:29:02
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316152
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:29:12
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316162
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:29:22
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316172
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:29:32
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316182
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:29:42
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316192
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:29:52
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316202
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:30:02
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316212
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:30:12
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10103</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10103</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小兰 1509 [19,4,0] 150 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 周年庆美味大酬宾！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316222
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:30:22
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看好吗，谢谢！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [28,3,0] 40</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316232
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:30:32
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 请看看，超级优惠哟~</color>
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [30,13,0] -80</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680316242
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 10:30:42
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
