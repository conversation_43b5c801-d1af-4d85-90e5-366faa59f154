%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: magic_eff_41702_att_ani
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0.33333334
        value: {x: -0, y: 0, z: 0}
        inSlope: {x: 0, y: -209.7471, z: 0}
        outSlope: {x: 0, y: -209.7471, z: 0}
        tangentMode: 0
      - time: 2
        value: {x: -0, y: -349.5785, z: 0}
        inSlope: {x: 0, y: -209.7471, z: 0}
        outSlope: {x: 0, y: -209.7471, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: rotate
  m_PositionCurves: []
  m_ScaleCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: 0.89171576, y: 0.68813044, z: 0.8917157}
        inSlope: {x: 3.2640982, y: 13.281838, z: 3.2640986}
        outSlope: {x: 3.2640982, y: 13.281838, z: 3.2640986}
        tangentMode: 0
      - time: 0.1
        value: {x: 1.2181256, y: 2.0163143, z: 1.2181256}
        inSlope: {x: 3.5990272, y: 2.5380926, z: 3.5990272}
        outSlope: {x: 3.5990272, y: 2.5380926, z: 3.5990272}
        tangentMode: 0
      - time: 0.25
        value: {x: 1.8082191, y: 0.7854662, z: 1.808219}
        inSlope: {x: -0.19817078, y: -3.1997168, z: -0.1981709}
        outSlope: {x: -0.19817078, y: -3.1997168, z: -0.1981709}
        tangentMode: 0
      - time: 0.41666666
        value: {x: 1.0865028, y: 1.0865028, z: 1.0865028}
        inSlope: {x: -2.4246573, y: 0.6436014, z: -2.424657}
        outSlope: {x: -2.4246573, y: 0.6436014, z: -2.424657}
        tangentMode: 0
      - time: 0.5833333
        value: {x: 1, y: 1, z: 1}
        inSlope: {x: -0.2595084, y: -0.2595084, z: -0.2595084}
        outSlope: {x: -0.2595084, y: -0.2595084, z: -0.2595084}
        tangentMode: 0
      - time: 0.8333333
        value: {x: 1, y: 1, z: 1}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: rotate/scaler
  m_FloatCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 1.75
        value: 1
        inSlope: -4
        outSlope: -4
        tangentMode: 10
      - time: 2
        value: 0
        inSlope: -4
        outSlope: -4
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: material._Alpha
    path: rotate/scaler/magic_eff_31202_mogu/magic_eff_31202_mogu
    classID: 137
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 1.75
        value: 1
        inSlope: -4
        outSlope: -4
        tangentMode: 10
      - time: 2
        value: 0
        inSlope: -4
        outSlope: -4
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: material._Alpha
    path: rotate/scaler/magic_eff_31202_mogu/Bone001/Bone002/magic_eff_41702_dun
    classID: 23
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 1.75
        value: 1
        inSlope: -4
        outSlope: -4
        tangentMode: 10
      - time: 2
        value: 0
        inSlope: -4
        outSlope: -4
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: material._Alpha
    path: rotate/scaler/magic_eff_31202_mogu/Bone001/Bone002/magic_eff_41702_jian
    classID: 23
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 31
      - time: 2
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 31
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: rotate/scaler/magic_eff_31202_mogu
    classID: 1
    script: {fileID: 0}
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - path: 931746316
      attribute: 4
      script: {fileID: 0}
      classID: 4
      customType: 14
      isPPtrCurve: 0
    - path: 3297187930
      attribute: 3
      script: {fileID: 0}
      classID: 4
      customType: 0
      isPPtrCurve: 0
    - path: 3856942741
      attribute: 2333735666
      script: {fileID: 0}
      classID: 137
      customType: 22
      isPPtrCurve: 0
    - path: 2589039916
      attribute: 2333735666
      script: {fileID: 0}
      classID: 23
      customType: 22
      isPPtrCurve: 0
    - path: 942780228
      attribute: 2333735666
      script: {fileID: 0}
      classID: 23
      customType: 22
      isPPtrCurve: 0
    - path: 2166524272
      attribute: 2086281974
      script: {fileID: 0}
      classID: 1
      customType: 0
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 2
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0.89171576
        inSlope: 3.2640982
        outSlope: 3.2640982
        tangentMode: 10
      - time: 0.1
        value: 1.2181256
        inSlope: 3.5990272
        outSlope: 3.5990272
        tangentMode: 10
      - time: 0.25
        value: 1.8082191
        inSlope: -0.19817078
        outSlope: -0.19817078
        tangentMode: 10
      - time: 0.41666666
        value: 1.0865028
        inSlope: -2.4246573
        outSlope: -2.4246573
        tangentMode: 10
      - time: 0.5833333
        value: 1
        inSlope: -0.2595084
        outSlope: -0.2595084
        tangentMode: 10
      - time: 0.8333333
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.x
    path: rotate/scaler
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0.68813044
        inSlope: 13.281838
        outSlope: 13.281838
        tangentMode: 10
      - time: 0.1
        value: 2.0163143
        inSlope: 2.5380926
        outSlope: 2.5380926
        tangentMode: 10
      - time: 0.25
        value: 0.7854662
        inSlope: -3.1997168
        outSlope: -3.1997168
        tangentMode: 10
      - time: 0.41666666
        value: 1.0865028
        inSlope: 0.6436014
        outSlope: 0.6436014
        tangentMode: 10
      - time: 0.5833333
        value: 1
        inSlope: -0.2595084
        outSlope: -0.2595084
        tangentMode: 10
      - time: 0.8333333
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.y
    path: rotate/scaler
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0.8917157
        inSlope: 3.2640986
        outSlope: 3.2640986
        tangentMode: 10
      - time: 0.1
        value: 1.2181256
        inSlope: 3.5990272
        outSlope: 3.5990272
        tangentMode: 10
      - time: 0.25
        value: 1.808219
        inSlope: -0.1981709
        outSlope: -0.1981709
        tangentMode: 10
      - time: 0.41666666
        value: 1.0865028
        inSlope: -2.424657
        outSlope: -2.424657
        tangentMode: 10
      - time: 0.5833333
        value: 1
        inSlope: -0.2595084
        outSlope: -0.2595084
        tangentMode: 10
      - time: 0.8333333
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.z
    path: rotate/scaler
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0.33333334
        value: -0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 2
        value: -0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: rotate
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0.33333334
        value: 0
        inSlope: -209.7471
        outSlope: -209.7471
        tangentMode: 10
      - time: 2
        value: -349.5785
        inSlope: -209.7471
        outSlope: -209.7471
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: rotate
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0.33333334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 2
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: rotate
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 1.75
        value: 1
        inSlope: -4
        outSlope: -4
        tangentMode: 10
      - time: 2
        value: 0
        inSlope: -4
        outSlope: -4
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: material._Alpha
    path: rotate/scaler/magic_eff_31202_mogu/magic_eff_31202_mogu
    classID: 137
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 1.75
        value: 1
        inSlope: -4
        outSlope: -4
        tangentMode: 10
      - time: 2
        value: 0
        inSlope: -4
        outSlope: -4
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: material._Alpha
    path: rotate/scaler/magic_eff_31202_mogu/Bone001/Bone002/magic_eff_41702_dun
    classID: 23
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 1.75
        value: 1
        inSlope: -4
        outSlope: -4
        tangentMode: 10
      - time: 2
        value: 0
        inSlope: -4
        outSlope: -4
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: material._Alpha
    path: rotate/scaler/magic_eff_31202_mogu/Bone001/Bone002/magic_eff_41702_jian
    classID: 23
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 31
      - time: 2
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 31
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: rotate/scaler/magic_eff_31202_mogu
    classID: 1
    script: {fileID: 0}
  m_EulerEditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: rotate
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: rotate
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: rotate
    classID: 4
    script: {fileID: 0}
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_GenerateMotionCurves: 0
  m_Events: []
