with:743  hight:454
[logic/misc/CShareCtrl.lua:18]:ShareCallback ctor
[logic/login/CLoginCtrl.lua:5]:公告初始化...
[logic/base/CHttpCtrl.lua:32]:http get ->    https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials&client_id=rlU84dprH6I7sMjBkqM0gizl&client_secret=c15395c2433c4a904d22aa27eab8321b&
[logic/ui/CViewCtrl.lua:94]:CNotifyView ShowView
[logic/ui/CViewCtrl.lua:94]:CBottomView ShowView
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x83d270f8"
|  json_result = true
|  timer = 16
}
[logic/audio/CSpeechCtrl.lua:72]:OnTokenGet err
[logic/ui/CViewBase.lua:125]:CNotifyView LoadDone!
[logic/ui/CViewBase.lua:125]:CBottomView LoadDone!
[logic/base/CResCtrl.lua:198]:-->resource init done!!! 12
[core/table.lua:94]:CServerCtrl.SetServerData: = {
|  [1001] = {
|  |  group_id = 1001
|  |  name = "1-10区"
|  |  servers = {}
|  }
|  [1002] = {
|  |  group_id = 1003
|  |  name = "默认"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线1区"
|  |  |  |  new = 0
|  |  |  |  server_id = 1001
|  |  |  |  state = 2
|  |  |  }
|  |  |  [2] = {
|  |  |  |  group = 2
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线11区"
|  |  |  |  new = 1
|  |  |  |  server_id = 1101
|  |  |  |  state = 2
|  |  |  }
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:94]:CLoginView ShowView
[logic/base/CHttpCtrl.lua:32]:http get ->    http://************:88/Note/note.json
[logic/login/CLoginAccountPage.lua:80]:SendToCenterServer:    http://************:88/Note/note.json    
[logic/ui/CViewBase.lua:125]:CLoginView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x987788f0"
|  json_result = true
|  timer = 60
}
[core/table.lua:94]:cb tResult-> = {
|  info = {
|  |  role_list = {}
|  |  server_info = {
|  |  |  groups = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 1
|  |  |  |  |  name = "1-10区"
|  |  |  |  }
|  |  |  }
|  |  |  infoList = {
|  |  |  |  [1] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "新服7天限时-大狂欢活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [10] = {
|  |  |  |  |  |  |  |  text = "传说伙伴碎片*100、梦觉书·零*20、梦觉书·低*20、梦觉书·高*20"
|  |  |  |  |  |  |  |  title = "新区累计充值10000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [11] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "单日累计充值活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [12] = {
|  |  |  |  |  |  |  |  text = "永久"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [13] = {
|  |  |  |  |  |  |  |  text = "1.活动为单日累计充值，高档位可领取低档位所有物品。
2.活动为单日累计充值，仅计算单日内玩家累计充值总额，次日重新计算。"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [14] = {
|  |  |  |  |  |  |  |  text = "向客服申请"
|  |  |  |  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [15] = {
|  |  |  |  |  |  |  |  text = "装备材料*10、金币*5w、熊猫蛋糕*5"
|  |  |  |  |  |  |  |  title = "单日累计充值100元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [16] = {
|  |  |  |  |  |  |  |  text = "一发入魂碎片*10、金币铜币*10w、体力药水*1、鲜肉包*30"
|  |  |  |  |  |  |  |  title = "单日累计充值500元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [17] = {
|  |  |  |  |  |  |  |  text = "一发入魂碎片*20、金币*20w、体力药水*2、焦糖包*60"
|  |  |  |  |  |  |  |  title = "单日累计充值1000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [18] = {
|  |  |  |  |  |  |  |  text = "装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1"
|  |  |  |  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [19] = {
|  |  |  |  |  |  |  |  text = "装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3"
|  |  |  |  |  |  |  |  title = "单日累计充值5000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "新服前7天"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [20] = {
|  |  |  |  |  |  |  |  text = "一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*80"
|  |  |  |  |  |  |  |  title = "单日累计充值10000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [21] = {
|  |  |  |  |  |  |  |  text = "一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1"
|  |  |  |  |  |  |  |  title = "单日累计充值20000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [22] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "永久累计充值活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [23] = {
|  |  |  |  |  |  |  |  text = "永久"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [24] = {
|  |  |  |  |  |  |  |  text = "活动为永久累计充值，高档位可领取低档位所有物品。、r
每个档位奖励仅可领取一次"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [25] = {
|  |  |  |  |  |  |  |  text = "向客服申请"
|  |  |  |  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [26] = {
|  |  |  |  |  |  |  |  text = "装备材料*50、伙伴觉醒材料*50、金币*1w"
|  |  |  |  |  |  |  |  title = "永久累计充值500元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [27] = {
|  |  |  |  |  |  |  |  text = "喇叭100、2星精英伙伴*10、装备原石*5、附魔材料*5、金币*5w"
|  |  |  |  |  |  |  |  title = "永久累计充值1000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [28] = {
|  |  |  |  |  |  |  |  text = "喇叭300、2星精英伙伴*20、装备原石*10、附魔材料*10、金币*10w"
|  |  |  |  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [29] = {
|  |  |  |  |  |  |  |  text = "喇叭500、3星传说伙伴*1、装备原石*30、附魔材料*30、金币*50w"
|  |  |  |  |  |  |  |  title = "永久累计充值5000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "单日累计充值达到对应档位，不重复高领低档，每个档位仅可领取一次奖励、请及时参加并申请领取对应档位。"
|  |  |  |  |  |  |  |  title = "活动规则"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [30] = {
|  |  |  |  |  |  |  |  text = "一发入魂碎片*100、万能碎片*100、金币*100w"
|  |  |  |  |  |  |  |  title = "永久累充10000元礼包"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [31] = {
|  |  |  |  |  |  |  |  text = "一发入魂碎片*200、万能碎片*200、金币*200w"
|  |  |  |  |  |  |  |  title = "永久累充20000元礼包"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [32] = {
|  |  |  |  |  |  |  |  text = "万能碎片*500、金币*500w"
|  |  |  |  |  |  |  |  title = "永久累充40000元礼包"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [33] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "单日累计充值货币返利1:10"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [34] = {
|  |  |  |  |  |  |  |  text = "永久"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [35] = {
|  |  |  |  |  |  |  |  text = "1.本活动为阶梯式活动，当日累计充值的金额满足返利阶梯后，按照当日充值金额满足的最高档次进行发放，次日自动返利，无需申请
2.本活动为长期活动，与其他返利活动叠加。
充值100-1999元，返利50%钻石
充值2000-4999元，返利100%钻石
充值5000-9999元，返利150%钻石
充值10000-19999元，返利200%钻石
充值20000+元，返利250%钻石
以上返利会在第二天自动已邮件形式发送至玩家"
|  |  |  |  |  |  |  |  title = "活动说明:"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [36] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "服务器冠名活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [37] = {
|  |  |  |  |  |  |  |  text = "永久"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [38] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [39] = {
|  |  |  |  |  |  |  |  text = "全体玩家"
|  |  |  |  |  |  |  |  title = "【活动对象】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "向客服申请"
|  |  |  |  |  |  |  |  title = "发放时间方式："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [40] = {
|  |  |  |  |  |  |  |  text = "我的服务器，我为自己代言。"
|  |  |  |  |  |  |  |  title = "【活动宣言】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [41] = {
|  |  |  |  |  |  |  |  text = "区服冠名权*1"
|  |  |  |  |  |  |  |  title = "【冠名奖励】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [42] = {
|  |  |  |  |  |  |  |  text = "  亲爱的玩家：
  也许你在游戏里已经称霸全区，但是出了服务器，出了游戏，还有谁认识你现在就给你一个机会，获得至高无上的荣誉，让更多的人知道你的强大。特此举行“我的服务器，我为自己代言”，服务器玩家冠名活动。
即日起，只要满足以下条件，即可获得服务器冠名权：
  1）开服前3天累计充值满10000元
  2）开服前7日累计充值满20000元，且区服处于未冠名状态，获得服务器冠名权
  备注：本活动遵循先到先得原则。如出现多个满足条件的，冠名资格由第一个满足条件的获得。"
|  |  |  |  |  |  |  |  title = "【活动内容】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [43] = {
|  |  |  |  |  |  |  |  text = "1、活动时间内有玩家满足条件后活动结束，活动期间没有玩家满足条件则保持原服务器名称。
2、服务器冠名长度为4个字。（必须遵守相关法律法规，不得使用负面或影响社会风气，不得出现敏感词的名称）
3、由于冠名操作需要时间，工作人员会在7个工作日内，完成冠名操作。
4，每个服，冠名只有一次。被冠名的服务器不予再次冠名"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [5] = {
|  |  |  |  |  |  |  |  text = "精英伙伴碎片*50、攻击宝石礼包*1、防御宝石礼包*1、2星精英伙伴*1"
|  |  |  |  |  |  |  |  title = "新区累计充值500元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [6] = {
|  |  |  |  |  |  |  |  text = "精英伙伴碎片*100、攻击宝石礼包*3、防御宝石礼包*3、2星精英伙伴*2"
|  |  |  |  |  |  |  |  title = "新区累计充值1000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [7] = {
|  |  |  |  |  |  |  |  text = "传说伙伴碎片*20、攻击宝石礼包*5、防御宝石礼包*5、2星精英伙伴*3"
|  |  |  |  |  |  |  |  title = "新区累计充值2000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [8] = {
|  |  |  |  |  |  |  |  text = "传说伙伴碎片*30、攻击宝石礼包*10、防御宝石礼包*10"
|  |  |  |  |  |  |  |  title = "新区累计充值3000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [9] = {
|  |  |  |  |  |  |  |  text = "传说伙伴碎片*50、梦觉书·零*10、梦觉书·低*10、梦觉书·高*10"
|  |  |  |  |  |  |  |  title = "新区累计充值5000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = "ceshi"
|  |  |  |  |  }
|  |  |  |  |  hot = 1
|  |  |  |  |  title = "限时大狂欢"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "1、转出条件：上款游戏充值超过100元的玩家
2、转入条件：新游戏充值超过100元"
|  |  |  |  |  |  |  |  title = "一、参与转游条件"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "★新游单日充值100-999元
老游戏充值金额20%转入新游（转入金额上限为5000元）
★新游单日充值1000-2999元
老游戏充值金额30%转入新游（转入金额上限为10000元）
★新游单日充值3000-4999元
老游戏充值金额40%转入新游（转入金额上限为10000元）
★新游单日充值5000-9999元
老游戏充值金额60%转入新游（转入金额上限为20000元）
★新游单日充值10000-19999元
老游戏充值金额80%转入新游（转入金额上限为30000元）
★新游单日充值20000以上
老游戏充值金额100%转入新游（转入金额上限为40000元）"
|  |  |  |  |  |  |  |  title = "二、阶梯转游返利"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "A，玩家新游戏充值200元，老游戏充值2200元，则转入货币为：2200*30%*新游货币比例
B，玩家新游戏充值200元，老游戏充值10000元，则转入货币为：5000*30%*新游货币比例"
|  |  |  |  |  |  |  |  title = "【举例】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "1，每个账号仅支持一次转游！
2，转游玩家在老游戏A和B都有过充值，但只能选择其中一个游戏进行转出到新游戏，不接受多个游戏同时转入一个新游戏
3，转游福利仅限玩家新角色创建的7天内申请，创建时间超过游戏7天则无法无法享受。
4，以上返利仅为游戏货币奖励，不计入vip经验、累充活动和线下返利
5，封停账号：玩家申请转游成功之后封停上款游戏角色"
|  |  |  |  |  |  |  |  title = "【温馨提示】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = "ceshi"
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "转游/转区"
|  |  |  |  }
|  |  |  }
|  |  |  ports = {
|  |  |  |  [1] = 7011
|  |  |  |  [2] = 7012
|  |  |  |  [3] = 27011
|  |  |  |  [4] = 27012
|  |  |  |  [5] = 27013
|  |  |  }
|  |  |  role_list = ""
|  |  |  servers = {
|  |  |  |  [1] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 1001
|  |  |  |  |  ip = "************"
|  |  |  |  |  name = "双线1区"
|  |  |  |  |  new = 0
|  |  |  |  |  server_id = 1001
|  |  |  |  |  start_time = 1506844350
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  }
|  |  }
|  |  token = ""
|  }
}
[core/table.lua:94]:CServerCtrl.SetServerData: = {
|  [1] = {
|  |  group_id = 1
|  |  id = 1
|  |  name = "1-10区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  id = 1001
|  |  |  |  ip = "************"
|  |  |  |  name = "双线1区"
|  |  |  |  new = 0
|  |  |  |  server_id = 1001
|  |  |  |  start_time = 1506844350
|  |  |  |  state = 2
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:设置公告: = {
|  [1] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "新服7天限时-大狂欢活动"
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  text = "传说伙伴碎片*100、梦觉书·零*20、梦觉书·低*20、梦觉书·高*20"
|  |  |  |  |  title = "新区累计充值10000元"
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "单日累计充值活动"
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  text = "永久"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [13] = {
|  |  |  |  |  text = "1.活动为单日累计充值，高档位可领取低档位所有物品。
2.活动为单日累计充值，仅计算单日内玩家累计充值总额，次日重新计算。"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [14] = {
|  |  |  |  |  text = "向客服申请"
|  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  }
|  |  |  |  [15] = {
|  |  |  |  |  text = "装备材料*10、金币*5w、熊猫蛋糕*5"
|  |  |  |  |  title = "单日累计充值100元"
|  |  |  |  }
|  |  |  |  [16] = {
|  |  |  |  |  text = "一发入魂碎片*10、金币铜币*10w、体力药水*1、鲜肉包*30"
|  |  |  |  |  title = "单日累计充值500元"
|  |  |  |  }
|  |  |  |  [17] = {
|  |  |  |  |  text = "一发入魂碎片*20、金币*20w、体力药水*2、焦糖包*60"
|  |  |  |  |  title = "单日累计充值1000元"
|  |  |  |  }
|  |  |  |  [18] = {
|  |  |  |  |  text = "装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1"
|  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  }
|  |  |  |  [19] = {
|  |  |  |  |  text = "装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3"
|  |  |  |  |  title = "单日累计充值5000元"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "新服前7天"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [20] = {
|  |  |  |  |  text = "一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*80"
|  |  |  |  |  title = "单日累计充值10000元"
|  |  |  |  }
|  |  |  |  [21] = {
|  |  |  |  |  text = "一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1"
|  |  |  |  |  title = "单日累计充值20000元"
|  |  |  |  }
|  |  |  |  [22] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "永久累计充值活动"
|  |  |  |  }
|  |  |  |  [23] = {
|  |  |  |  |  text = "永久"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [24] = {
|  |  |  |  |  text = "活动为永久累计充值，高档位可领取低档位所有物品。、r
每个档位奖励仅可领取一次"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [25] = {
|  |  |  |  |  text = "向客服申请"
|  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  }
|  |  |  |  [26] = {
|  |  |  |  |  text = "装备材料*50、伙伴觉醒材料*50、金币*1w"
|  |  |  |  |  title = "永久累计充值500元"
|  |  |  |  }
|  |  |  |  [27] = {
|  |  |  |  |  text = "喇叭100、2星精英伙伴*10、装备原石*5、附魔材料*5、金币*5w"
|  |  |  |  |  title = "永久累计充值1000元"
|  |  |  |  }
|  |  |  |  [28] = {
|  |  |  |  |  text = "喇叭300、2星精英伙伴*20、装备原石*10、附魔材料*10、金币*10w"
|  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  }
|  |  |  |  [29] = {
|  |  |  |  |  text = "喇叭500、3星传说伙伴*1、装备原石*30、附魔材料*30、金币*50w"
|  |  |  |  |  title = "永久累计充值5000元"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "单日累计充值达到对应档位，不重复高领低档，每个档位仅可领取一次奖励、请及时参加并申请领取对应档位。"
|  |  |  |  |  title = "活动规则"
|  |  |  |  }
|  |  |  |  [30] = {
|  |  |  |  |  text = "一发入魂碎片*100、万能碎片*100、金币*100w"
|  |  |  |  |  title = "永久累充10000元礼包"
|  |  |  |  }
|  |  |  |  [31] = {
|  |  |  |  |  text = "一发入魂碎片*200、万能碎片*200、金币*200w"
|  |  |  |  |  title = "永久累充20000元礼包"
|  |  |  |  }
|  |  |  |  [32] = {
|  |  |  |  |  text = "万能碎片*500、金币*500w"
|  |  |  |  |  title = "永久累充40000元礼包"
|  |  |  |  }
|  |  |  |  [33] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "单日累计充值货币返利1:10"
|  |  |  |  }
|  |  |  |  [34] = {
|  |  |  |  |  text = "永久"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [35] = {
|  |  |  |  |  text = "1.本活动为阶梯式活动，当日累计充值的金额满足返利阶梯后，按照当日充值金额满足的最高档次进行发放，次日自动返利，无需申请
2.本活动为长期活动，与其他返利活动叠加。
充值100-1999元，返利50%钻石
充值2000-4999元，返利100%钻石
充值5000-9999元，返利150%钻石
充值10000-19999元，返利200%钻石
充值20000+元，返利250%钻石
以上返利会在第二天自动已邮件形式发送至玩家"
|  |  |  |  |  title = "活动说明:"
|  |  |  |  }
|  |  |  |  [36] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "服务器冠名活动"
|  |  |  |  }
|  |  |  |  [37] = {
|  |  |  |  |  text = "永久"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [38] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [39] = {
|  |  |  |  |  text = "全体玩家"
|  |  |  |  |  title = "【活动对象】："
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "向客服申请"
|  |  |  |  |  title = "发放时间方式："
|  |  |  |  }
|  |  |  |  [40] = {
|  |  |  |  |  text = "我的服务器，我为自己代言。"
|  |  |  |  |  title = "【活动宣言】："
|  |  |  |  }
|  |  |  |  [41] = {
|  |  |  |  |  text = "区服冠名权*1"
|  |  |  |  |  title = "【冠名奖励】："
|  |  |  |  }
|  |  |  |  [42] = {
|  |  |  |  |  text = "  亲爱的玩家：
  也许你在游戏里已经称霸全区，但是出了服务器，出了游戏，还有谁认识你现在就给你一个机会，获得至高无上的荣誉，让更多的人知道你的强大。特此举行“我的服务器，我为自己代言”，服务器玩家冠名活动。
即日起，只要满足以下条件，即可获得服务器冠名权：
  1）开服前3天累计充值满10000元
  2）开服前7日累计充值满20000元，且区服处于未冠名状态，获得服务器冠名权
  备注：本活动遵循先到先得原则。如出现多个满足条件的，冠名资格由第一个满足条件的获得。"
|  |  |  |  |  title = "【活动内容】："
|  |  |  |  }
|  |  |  |  [43] = {
|  |  |  |  |  text = "1、活动时间内有玩家满足条件后活动结束，活动期间没有玩家满足条件则保持原服务器名称。
2、服务器冠名长度为4个字。（必须遵守相关法律法规，不得使用负面或影响社会风气，不得出现敏感词的名称）
3、由于冠名操作需要时间，工作人员会在7个工作日内，完成冠名操作。
4，每个服，冠名只有一次。被冠名的服务器不予再次冠名"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  text = "精英伙伴碎片*50、攻击宝石礼包*1、防御宝石礼包*1、2星精英伙伴*1"
|  |  |  |  |  title = "新区累计充值500元"
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  text = "精英伙伴碎片*100、攻击宝石礼包*3、防御宝石礼包*3、2星精英伙伴*2"
|  |  |  |  |  title = "新区累计充值1000元"
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  text = "传说伙伴碎片*20、攻击宝石礼包*5、防御宝石礼包*5、2星精英伙伴*3"
|  |  |  |  |  title = "新区累计充值2000元"
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  text = "传说伙伴碎片*30、攻击宝石礼包*10、防御宝石礼包*10"
|  |  |  |  |  title = "新区累计充值3000元"
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  text = "传说伙伴碎片*50、梦觉书·零*10、梦觉书·低*10、梦觉书·高*10"
|  |  |  |  |  title = "新区累计充值5000元"
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = "ceshi"
|  |  }
|  |  hot = 1
|  |  title = "限时大狂欢"
|  }
|  [2] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "1、转出条件：上款游戏充值超过100元的玩家
2、转入条件：新游戏充值超过100元"
|  |  |  |  |  title = "一、参与转游条件"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "★新游单日充值100-999元
老游戏充值金额20%转入新游（转入金额上限为5000元）
★新游单日充值1000-2999元
老游戏充值金额30%转入新游（转入金额上限为10000元）
★新游单日充值3000-4999元
老游戏充值金额40%转入新游（转入金额上限为10000元）
★新游单日充值5000-9999元
老游戏充值金额60%转入新游（转入金额上限为20000元）
★新游单日充值10000-19999元
老游戏充值金额80%转入新游（转入金额上限为30000元）
★新游单日充值20000以上
老游戏充值金额100%转入新游（转入金额上限为40000元）"
|  |  |  |  |  title = "二、阶梯转游返利"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "A，玩家新游戏充值200元，老游戏充值2200元，则转入货币为：2200*30%*新游货币比例
B，玩家新游戏充值200元，老游戏充值10000元，则转入货币为：5000*30%*新游货币比例"
|  |  |  |  |  title = "【举例】："
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "1，每个账号仅支持一次转游！
2，转游玩家在老游戏A和B都有过充值，但只能选择其中一个游戏进行转出到新游戏，不接受多个游戏同时转入一个新游戏
3，转游福利仅限玩家新角色创建的7天内申请，创建时间超过游戏7天则无法无法享受。
4，以上返利仅为游戏货币奖励，不计入vip经验、累充活动和线下返利
5，封停账号：玩家申请转游成功之后封停上款游戏角色"
|  |  |  |  |  title = "【温馨提示】："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = "ceshi"
|  |  }
|  |  hot = 2
|  |  title = "转游/转区"
|  }
}
[logic/login/CLoginCtrl.lua:151]:ConnectServer =     table:0x7FEBEBC0    table:0x7FEBEED0
[net/CNetCtrl.lua:114]:Test连接    ************    27011
[core/table.lua:94]:-->Net Receive: login.GS2CHello = {
|  time = **********
}
[core/table.lua:94]:<--Net Send: login.C2GSQueryLogin = {
|  res_file_version = {}
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/03/23 14:32:38
[net/netlogin.lua:207]:netlogin.GS2CQueryLogin-->
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 1001
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginAccount = {
|  account = "test0331"
|  client_svn_version = 96
|  client_version = "********"
|  device = "MS-7B48 (Micro-Star International Co., Ltd.)"
|  imei = ""
|  is_qrcode = 0
|  mac = "00-FF-AE-41-56-E4"
|  os = "Windows 10  (10.0.0) 64bit"
|  platform = 4
|  udid = "3293c42eeeb3f70c20aee7c52384c1871ecf8399"
}
[core/global.lua:59]:<color=#ffeb04>更新代码: 1049</color>
[logic/base/Utils.lua:398]:local framever, dllver, resver = C_api.Utils.GetResVersion()
if resver >= 96 or Utils.IsEditor() then
	function CWelfareCtrl.IsOpenCostSave(self)
		local b = false
		local t = g_TimeCtrl:GetTimeS()	
		if self.m_CostSaveStartTime ~= 0 and self.m_CostSaveEndTime ~= 0 and t > self.m_CostSaveStartTime and t < self.m_CostSaveEndTime then
			b = true
		end
		return b
	end
	function CPartnerHireView.IsCanYFRH(self, iParID)
		local d = data.partnerhiredata.DATA
		if table.index(d[iParID]["recommand_list"], "一发入魂") then
			return self:IsCanSSRDraw()
		end
	end
	function CPartnerEquipPage.ChangePart(self)
		local oItem = g_ItemCtrl:GetItem(self.m_CurItemID)
		if oItem and oItem:GetValue("level") == define.Partner.ParEquip.MaxLevel and oItem:GetValue("star") < define.Partner.ParEquip.MaxStar then
			if self.m_CurPart ~= self.m_UpStarPart and self.m_CurPart ~= self.m_UpStonePart then
				self.m_UpStarBtn:SetSelected(true)
				self:SwitchPart()
			end
		end
	end
	data.netdata.BAN["proto"]["herobox"]["item"]["GS2CItemQuickUse"] = true
end

[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 1001
}
[core/table.lua:94]:-->Net Receive: login.GS2CCheckInviteCodeResult = {
|  msg = "邀请码验证已关闭"
|  result = 1
}
[core/table.lua:94]:-->Net Receive: login.GS2CLoginAccount = {
|  account = "test0331"
|  role_list = {
|  |  [1] = {
|  |  |  grade = 2
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 110
|  |  |  |  weapon = 2500
|  |  |  }
|  |  |  pid = 10018
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginRole = {
|  account = "test0331"
|  pid = 10018
}
[core/global.lua:59]:<color=#ffeb04>读取系统设置本地数据，帐号为  test0331</color>
[core/global.lua:59]:<color=#ffeb04>test0331 的系统设置本地数据为 nil，使用默认数据</color>
[core/table.lua:94]:-->Net Receive: login.GS2CLoginRole = {
|  account = "test0331"
|  pid = 10018
|  role = {
|  |  abnormal_attr_ratio = 500
|  |  attack = 273
|  |  bcmd = {
|  |  |  [1] = {
|  |  |  |  idx = 208
|  |  |  }
|  |  |  [10] = {
|  |  |  |  idx = 105
|  |  |  }
|  |  |  [11] = {
|  |  |  |  idx = 106
|  |  |  }
|  |  |  [12] = {
|  |  |  |  idx = 107
|  |  |  }
|  |  |  [13] = {
|  |  |  |  idx = 108
|  |  |  }
|  |  |  [14] = {
|  |  |  |  idx = 205
|  |  |  }
|  |  |  [15] = {
|  |  |  |  idx = 206
|  |  |  }
|  |  |  [16] = {
|  |  |  |  idx = 207
|  |  |  }
|  |  |  [2] = {
|  |  |  |  cmd = "集火"
|  |  |  |  idx = 201
|  |  |  }
|  |  |  [3] = {
|  |  |  |  cmd = "封印"
|  |  |  |  idx = 202
|  |  |  }
|  |  |  [4] = {
|  |  |  |  cmd = "减速"
|  |  |  |  idx = 203
|  |  |  }
|  |  |  [5] = {
|  |  |  |  cmd = "禁疗"
|  |  |  |  idx = 204
|  |  |  }
|  |  |  [6] = {
|  |  |  |  cmd = "治疗"
|  |  |  |  idx = 101
|  |  |  }
|  |  |  [7] = {
|  |  |  |  cmd = "复活"
|  |  |  |  idx = 102
|  |  |  }
|  |  |  [8] = {
|  |  |  |  cmd = "加速"
|  |  |  |  idx = 103
|  |  |  }
|  |  |  [9] = {
|  |  |  |  cmd = "解封"
|  |  |  |  idx = 104
|  |  |  }
|  |  }
|  |  coin = 12030
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 43
|  |  energy = 107
|  |  exp = 960
|  |  grade = 2
|  |  hp = 2135
|  |  kp_sdk_info = {
|  |  |  create_time = 1679550688
|  |  |  upgrade_time = 1679550749
|  |  }
|  |  mask = "7e6683d1ffffffe"
|  |  max_hp = 2212
|  |  model_info = {
|  |  |  color = {
|  |  |  |  [1] = 0
|  |  |  }
|  |  |  shape = 110
|  |  |  weapon = 2500
|  |  }
|  |  name = "掏出杨桃"
|  |  open_day = 20
|  |  org_fuben_cnt = 2
|  |  power = 696
|  |  res_abnormal_ratio = 500
|  |  res_critical_ratio = 500
|  |  school = 1
|  |  school_branch = 1
|  |  sex = 1
|  |  show_id = 10018
|  |  skill_point = 1
|  |  speed = 753
|  |  systemsetting = {}
|  }
|  role_token = "**********0123"
|  xg_account = "bus10018"
}
XinGeSdk:RegisterWithAccount
[core/table.lua:94]:<--Net Send: other.C2GSSendXGToken = {
|  xg_token = "null"
}
[logic/ui/CViewCtrl.lua:94]:CMainMenuView ShowView
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 500
|  active = 0
|  arenamedal = 0
|  attack = 273
|  bcmd = {
|  |  [1] = {
|  |  |  idx = 208
|  |  }
|  |  [10] = {
|  |  |  idx = 105
|  |  }
|  |  [11] = {
|  |  |  idx = 106
|  |  }
|  |  [12] = {
|  |  |  idx = 107
|  |  }
|  |  [13] = {
|  |  |  idx = 108
|  |  }
|  |  [14] = {
|  |  |  idx = 205
|  |  }
|  |  [15] = {
|  |  |  idx = 206
|  |  }
|  |  [16] = {
|  |  |  idx = 207
|  |  }
|  |  [2] = {
|  |  |  cmd = "集火"
|  |  |  idx = 201
|  |  }
|  |  [3] = {
|  |  |  cmd = "封印"
|  |  |  idx = 202
|  |  }
|  |  [4] = {
|  |  |  cmd = "减速"
|  |  |  idx = 203
|  |  }
|  |  [5] = {
|  |  |  cmd = "禁疗"
|  |  |  idx = 204
|  |  }
|  |  [6] = {
|  |  |  cmd = "治疗"
|  |  |  idx = 101
|  |  }
|  |  [7] = {
|  |  |  cmd = "复活"
|  |  |  idx = 102
|  |  }
|  |  [8] = {
|  |  |  cmd = "加速"
|  |  |  idx = 103
|  |  }
|  |  [9] = {
|  |  |  cmd = "解封"
|  |  |  idx = 104
|  |  }
|  }
|  camp = 0
|  chatself = false
|  chubeiexp = 0
|  coin = 12030
|  coin_over = 0
|  color_coin = 0
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 43
|  energy = 107
|  exp = 960
|  followers = {}
|  goldcoin = 0
|  grade = 2
|  hp = 2135
|  kp_sdk_info = {
|  |  create_time = 1679550688
|  |  upgrade_time = 1679550749
|  }
|  max_hp = 2212
|  medal = 0
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 110
|  |  weapon = 2500
|  }
|  name = "掏出杨桃"
|  open_day = 20
|  org_fuben_cnt = 2
|  org_offer = 0
|  power = 696
|  res_abnormal_ratio = 500
|  res_critical_ratio = 500
|  school = 1
|  school_branch = 1
|  sex = 1
|  show_id = 10018
|  skill_point = 1
|  skin = 0
|  speed = 753
|  systemsetting = {}
|  title_info = {}
|  trapmine_point = 0
|  travel_score = 0
|  upvote_amount = 0
}
[logic/misc/CTimeCtrl.lua:64]:HeartBeat-->开启心跳包检测
[core/table.lua:94]:<--Net Send: title.C2GSTitleInfoList = {}
[core/table.lua:94]:-->Net Receive: player.GS2CGamePushSetting = {}
[core/table.lua:94]:-->Net Receive: player.GS2CShapeList = {
|  shapes = {
|  |  [1] = 110
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CGuidanceInfo = {
|  guidanceinfo = {
|  |  key = {
|  |  |  [1] = 7000041
|  |  |  [2] = 7000051
|  |  |  [3] = 6010002
|  |  |  [4] = 1027099
|  |  |  [5] = 1028099
|  |  }
|  |  version = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>>>>>>>>>>>>>>>>>>>>> GS2CGuidanceInfo </color>
[core/table.lua:94]:-->Net Receive: travel.GS2CLoginTravelPartner = {
|  travel_partner = {
|  |  server_time = **********
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CServerGradeInfo = {
|  days = 4
|  server_grade = 75
}
[core/table.lua:94]:-->Net Receive: scene.GS2CShowScene = {
|  map_id = 101000
|  scene_id = 12
|  scene_name = "帝都"
}
[core/table.lua:94]:-->Net Receive: task.GS2CLoginTask = {
|  shimen_status = 1
|  taskdata = {
|  |  [1] = {
|  |  |  acceptnpc = 10007
|  |  |  clientnpc = {
|  |  |  |  [1] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 302
|  |  |  |  |  }
|  |  |  |  |  name = "重华"
|  |  |  |  |  npcid = 8793
|  |  |  |  |  npctype = 10007
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 12800
|  |  |  |  |  |  y = 9700
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  detaildesc = "摆脱少女重华的纠缠！"
|  |  |  name = "少女重华"
|  |  |  patrolinfo = {}
|  |  |  placeinfo = {}
|  |  |  shapeinfo = {}
|  |  |  statusinfo = {
|  |  |  |  note = "CreateTask"
|  |  |  |  status = 2
|  |  |  }
|  |  |  submitnpc = 10007
|  |  |  target = 10007
|  |  |  targetdesc = "遭到质问"
|  |  |  taskid = 10003
|  |  |  taskitem = {}
|  |  |  tasktype = 1
|  |  |  traceinfo = {}
|  |  |  type = 2
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>login task</color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x65e98540"
|  |  AssociatedPick = "function: 0x65e985a0"
|  |  AssociatedSubmit = "function: 0x65e98570"
|  |  CreateDefalutData = "function: 0x65e96548"
|  |  GetChaptetFubenData = "function: 0x65e98388"
|  |  GetProgressThing = "function: 0x65e98600"
|  |  GetRemainTime = "function: 0x65e98200"
|  |  GetStatus = "function: 0x65e98660"
|  |  GetTaskClientExtStrDic = "function: 0x65e985d0"
|  |  GetTaskTypeSpriteteName = "function: 0x65e98290"
|  |  GetTraceInfo = "function: 0x65e983e8"
|  |  GetTraceNpcType = "function: 0x65e98260"
|  |  GetValue = "function: 0x65e982c8"
|  |  IsAbandon = "function: 0x65e98328"
|  |  IsAddEscortDynamicNpc = "function: 0x65e98998"
|  |  IsMissMengTask = "function: 0x65e98230"
|  |  IsPassChaterFuben = "function: 0x65e983b8"
|  |  IsTaskSpecityAction = "function: 0x65e98358"
|  |  IsTaskSpecityCategory = "function: 0x65e98510"
|  |  New = "function: 0x65e95a28"
|  |  NewByData = "function: 0x65e9d4a0"
|  |  RaiseProgressIdx = "function: 0x65e98630"
|  |  RefreshTask = "function: 0x65e982f8"
|  |  ResetEndTime = "function: 0x65e96510"
|  |  SetStatus = "function: 0x65e964e0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x65e964b0"
|  }
|  m_CData = {
|  |  ChapterFb = "1,1"
|  |  autoDoNextTask = 0
|  |  clientExtStr = ""
|  |  name = "少女重华"
|  |  submitNpcId = 10007
|  |  submitRewardStr = {
|  |  |  [1] = "R1003"
|  |  }
|  |  taskWalkingTips = "想不到帝都也有恶霸。,那个人是刚才的！;none;none"
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10007
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 302
|  |  |  |  }
|  |  |  |  name = "重华"
|  |  |  |  npcid = 8793
|  |  |  |  npctype = 10007
|  |  |  |  pos_info = {
|  |  |  |  |  x = 12800
|  |  |  |  |  y = 9700
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "摆脱少女重华的纠缠！"
|  |  isdone = 0
|  |  name = "少女重华"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10007
|  |  target = 10007
|  |  targetdesc = "遭到质问"
|  |  taskid = 10003
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CTeachProgress = {}
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshPartnerTask = {}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {}
[core/table.lua:94]:-->Net Receive: state.GS2CLoginState = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginHuodongInfo = {
|  convoyinfo = {
|  |  convoy_partner = 1503
|  |  free_time = 1
|  |  pool_info = {
|  |  |  [1] = {
|  |  |  |  level = 1
|  |  |  |  partnerid = 1503
|  |  |  |  pos = 1
|  |  |  |  rewardid = 1001
|  |  |  }
|  |  |  [2] = {
|  |  |  |  level = 2
|  |  |  |  partnerid = 1200
|  |  |  |  pos = 2
|  |  |  |  rewardid = 1002
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [3] = {
|  |  |  |  level = 3
|  |  |  |  partnerid = 1009
|  |  |  |  pos = 3
|  |  |  |  rewardid = 1003
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [4] = {
|  |  |  |  level = 4
|  |  |  |  partnerid = 1011
|  |  |  |  pos = 4
|  |  |  |  rewardid = 1004
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [5] = {
|  |  |  |  level = 5
|  |  |  |  partnerid = 1752
|  |  |  |  pos = 5
|  |  |  |  rewardid = 1005
|  |  |  |  weight = 25
|  |  |  }
|  |  }
|  |  selected_pos = 1
|  |  target_npc = 5037
|  }
|  dailytrain = {}
|  huntinfo = {}
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginChapterInfo = {
|  finalchapter = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  level = 1
|  |  |  open = 1
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: team.GS2CInviteInfo = {}
[core/table.lua:94]:-->Net Receive: mail.GS2CLoginMail = {}
[core/table.lua:94]:-->Net Receive: friend.GS2CLoginFriend = {}
[core/global.lua:59]:<color=#ffeb04>好友版本一致，无需更新</color>
[core/table.lua:94]:-->Net Receive: friend.GS2CApplyList = {}
[core/table.lua:94]:-->Net Receive: friend.GS2CFriendSetting = {
|  setting = {
|  |  notify = 1
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 34042
|  |  |  type = "zsk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 34042
|  |  |  type = "yk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
|  mask = "c"
}
[core/table.lua:94]:CNetCtrl解析mask: GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 34042
|  |  |  type = "zsk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 34042
|  |  |  type = "yk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: store.GS2CPayForColorCoinInfo = {
|  colorcoin_list = {
|  |  [1] = {
|  |  |  key = "goldcoinstore_1005"
|  |  }
|  |  [10] = {
|  |  |  key = "goldcoinstore_2004"
|  |  }
|  |  [11] = {
|  |  |  key = "goldcoinstore_2005"
|  |  }
|  |  [12] = {
|  |  |  key = "goldcoinstore_2006"
|  |  }
|  |  [13] = {
|  |  |  key = "goldcoinstore_2007"
|  |  }
|  |  [14] = {
|  |  |  key = "goldcoinstore_1003"
|  |  }
|  |  [15] = {
|  |  |  key = "goldcoinstore_1004"
|  |  }
|  |  [2] = {
|  |  |  key = "goldcoinstore_1006"
|  |  }
|  |  [3] = {
|  |  |  key = "goldcoinstore_1007"
|  |  }
|  |  [4] = {
|  |  |  key = "goldcoinstore_1008"
|  |  }
|  |  [5] = {
|  |  |  key = "goldcoinstore_1001"
|  |  }
|  |  [6] = {
|  |  |  key = "goldcoinstore_1002"
|  |  }
|  |  [7] = {
|  |  |  key = "goldcoinstore_2001"
|  |  }
|  |  [8] = {
|  |  |  key = "goldcoinstore_2002"
|  |  }
|  |  [9] = {
|  |  |  key = "goldcoinstore_2003"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeRewrad = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraWarState = {
|  time = 1679760000
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraQueueStatus = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliReddot = {
|  cday = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeScore = {
|  cur_id = 1
|  score_info = {}
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginTrapmine = {
|  rare_monster = {
|  |  [1] = {
|  |  |  map_id = 202000
|  |  |  partypes = {
|  |  |  |  [1] = 316
|  |  |  |  [2] = 313
|  |  |  |  [3] = 312
|  |  |  }
|  |  }
|  |  [2] = {
|  |  |  map_id = 204000
|  |  |  partypes = {
|  |  |  |  [1] = 403
|  |  |  |  [2] = 402
|  |  |  |  [3] = 407
|  |  |  }
|  |  }
|  |  [3] = {
|  |  |  map_id = 205000
|  |  |  partypes = {
|  |  |  |  [1] = 508
|  |  |  |  [2] = 513
|  |  |  |  [3] = 509
|  |  |  }
|  |  }
|  |  [4] = {
|  |  |  map_id = 206000
|  |  |  partypes = {
|  |  |  |  [1] = 413
|  |  |  |  [2] = 414
|  |  |  |  [3] = 410
|  |  |  }
|  |  }
|  |  [5] = {
|  |  |  map_id = 210400
|  |  |  partypes = {
|  |  |  |  [1] = 504
|  |  |  |  [2] = 505
|  |  |  |  [3] = 507
|  |  |  }
|  |  }
|  |  [6] = {
|  |  |  map_id = 200000
|  |  |  partypes = {
|  |  |  |  [1] = 417
|  |  |  |  [2] = 502
|  |  |  |  [3] = 501
|  |  |  }
|  |  }
|  |  [7] = {
|  |  |  map_id = 201000
|  |  |  partypes = {
|  |  |  |  [1] = 308
|  |  |  |  [2] = 301
|  |  |  |  [3] = 302
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcFightInfoList = {
|  info_list = {
|  |  [1] = {
|  |  |  npc_type = 10024
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [10] = {
|  |  |  npc_type = 10007
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [11] = {
|  |  |  npc_type = 10008
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [12] = {
|  |  |  npc_type = 10009
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [13] = {
|  |  |  npc_type = 10011
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [14] = {
|  |  |  npc_type = 10012
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [15] = {
|  |  |  npc_type = 10013
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [16] = {
|  |  |  npc_type = 10000
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [17] = {
|  |  |  npc_type = 10023
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [18] = {
|  |  |  npc_type = 10014
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [19] = {
|  |  |  npc_type = 10015
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [2] = {
|  |  |  npc_type = 10002
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [20] = {
|  |  |  npc_type = 10016
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [21] = {
|  |  |  npc_type = 10017
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [22] = {
|  |  |  npc_type = 10018
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [23] = {
|  |  |  npc_type = 10019
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [24] = {
|  |  |  npc_type = 10020
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [25] = {
|  |  |  npc_type = 10021
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [26] = {
|  |  |  npc_type = 10022
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [3] = {
|  |  |  npc_type = 10010
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [4] = {
|  |  |  npc_type = 10001
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [5] = {
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [6] = {
|  |  |  npc_type = 10003
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [7] = {
|  |  |  npc_type = 10004
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [8] = {
|  |  |  npc_type = 10005
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [9] = {
|  |  |  npc_type = 10006
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CDrawCardUI = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CNewFieldBoss = {
|  bossid = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHeroBoxRecord = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CDailySignInfo = {
|  sign_info = {
|  |  [1] = {
|  |  |  key = "week"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: item.GS2CLoginItem = {
|  itemdata = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1679550689
|  |  |  equip_info = {
|  |  |  |  pos = 1
|  |  |  |  stone_sid = 3100000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 1
|  |  |  itemlevel = 1
|  |  |  name = "练习红魔钺"
|  |  |  power = 57
|  |  |  sid = 2100000
|  |  }
|  |  [2] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 121
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 24
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1679550689
|  |  |  equip_info = {
|  |  |  |  pos = 2
|  |  |  |  stone_sid = 4000000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 2
|  |  |  itemlevel = 1
|  |  |  name = "练习飘逸巾"
|  |  |  power = 24
|  |  |  sid = 2200000
|  |  }
|  |  [3] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1679550689
|  |  |  equip_info = {
|  |  |  |  pos = 3
|  |  |  |  stone_sid = 4100000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 3
|  |  |  itemlevel = 1
|  |  |  name = "练习剑麻甲"
|  |  |  power = 32
|  |  |  sid = 2310000
|  |  }
|  |  [4] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1679550689
|  |  |  equip_info = {
|  |  |  |  pos = 4
|  |  |  |  stone_sid = 4200000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 4
|  |  |  itemlevel = 1
|  |  |  name = "练习草织戒"
|  |  |  power = 19
|  |  |  sid = 2400000
|  |  }
|  |  [5] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 364
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 72
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1679550689
|  |  |  equip_info = {
|  |  |  |  pos = 5
|  |  |  |  stone_sid = 4300000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 5
|  |  |  itemlevel = 1
|  |  |  name = "练习折桂腰"
|  |  |  power = 72
|  |  |  sid = 2510000
|  |  }
|  |  [6] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "speed"
|  |  |  |  |  value = 33
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 11
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 77
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1679550689
|  |  |  equip_info = {
|  |  |  |  pos = 6
|  |  |  |  stone_sid = 4400000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 6
|  |  |  itemlevel = 1
|  |  |  name = "练习虬草鞋"
|  |  |  power = 77
|  |  |  sid = 2610000
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>loginitem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenPlanName = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CRefreshRewardBack = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2COnlineGift = {
|  onlinetime = 1977
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartner = {}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPicturePosList = {}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayBuy = {}
[core/table.lua:94]:-->Net Receive: skill.GS2CLoginSkill = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3001
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3002
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  needcost = 15
|  |  |  sk = 3004
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  needcost = 25
|  |  |  sk = 3005
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3006
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3007
|  |  |  type = 1
|  |  }
|  }
}
[logic/skill/CSkillCtrl.lua:112]:技能数据登陆协议返回:
[core/table.lua:94]:Table = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3001
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3002
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  needcost = 15
|  |  |  sk = 3004
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  needcost = 25
|  |  |  sk = 3005
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3006
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3007
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CHistoryCharge = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFirstChargeUI = {
|  bOpen = true
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliPoint = {}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginParSoulPlan = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawCnt = {
|  cnt = 1
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliTime = {}
[core/table.lua:94]:-->Net Receive: house.GS2CEnterHouse = {
|  buff_info = {}
|  furniture_info = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  lock_status = 1
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  type = 2
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  type = 3
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  type = 4
|  |  }
|  |  [5] = {
|  |  |  level = 1
|  |  |  type = 5
|  |  }
|  }
|  handle_type = 1
|  max_train = 1
|  max_warm_degree = 1000
|  owner_pid = 10018
|  partner_info = {
|  |  [1] = {
|  |  |  coin = 2707
|  |  |  type = 1001
|  |  }
|  }
|  warm_degree = 10
}
[core/table.lua:94]:-->Net Receive: house.GS2CPartnerExchangeUI = {
|  handle_type = 1
|  love_cnt = 10
|  max_gift_cnt = 10
|  max_love_cnt = 10
|  partner_gift_cnt = 10
}
[core/table.lua:94]:-->Net Receive: house.GS2COpenWorkDesk = {
|  desk_info = {
|  |  [1] = {
|  |  |  lock_status = 1
|  |  |  pos = 1
|  |  |  status = 1
|  |  }
|  |  [2] = {
|  |  |  pos = 2
|  |  |  status = 1
|  |  }
|  |  [3] = {
|  |  |  pos = 3
|  |  |  status = 1
|  |  }
|  |  [4] = {
|  |  |  lock_status = 1
|  |  |  pos = 4
|  |  |  status = 1
|  |  }
|  }
|  handle_type = 1
|  owner_pid = 10018
}
[core/table.lua:94]:-->Net Receive: player.GS2CTodayInfo = {
|  info = {
|  |  mask = "0"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: todyInfo = {}
[core/table.lua:94]:-->Net Receive: player.GS2CGameShare = {
|  game_share = {
|  |  [1] = {
|  |  |  type = "draw_card_share"
|  |  }
|  |  [2] = {
|  |  |  type = "skin_share"
|  |  }
|  |  [3] = {
|  |  |  type = "picture_share"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginRewardInfo = {
|  login_day = 1
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  abnormal_attr_ratio = 500
|  |  attack = 273
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 43
|  |  hp = 2135
|  |  mask = "87ff00"
|  |  max_hp = 2212
|  |  power = 696
|  |  res_abnormal_ratio = 500
|  |  res_critical_ratio = 500
|  |  speed = 753
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 500
|  attack = 273
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 43
|  hp = 2135
|  max_hp = 2212
|  power = 696
|  res_abnormal_ratio = 500
|  res_critical_ratio = 500
|  speed = 753
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterScene = {
|  eid = 34
|  pos_info = {
|  |  face_y = 72424
|  |  x = 22831
|  |  y = 17781
|  }
|  scene_id = 12
}
[core/global.lua:59]:<color=#ffeb04>result  nil</color>
[core/global.lua:59]:<color=#ffeb04>=00000=  userdata: 0x7da77ca8 nil</color>
[logic/ui/CSprite.lua:159]:DynamicSprite dSprInfo    Item    10012
[logic/ui/CViewBase.lua:125]:CMainMenuView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/global.lua:59]:<color=#ffeb04>读取地图： 1020</color>
[core/global.lua:59]:<color=#ffeb04>Unity2017.4.30f1 localScale</color>
[logic/ui/CViewCtrl.lua:104]:CLoginView     CloseView
[logic/base/CResCtrl.lua:596]:res gc step start!
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[core/global.lua:59]:<color=#ffeb04>地图加载完成: 1020 ,当前地图: nil</color>
[logic/map/CMapCtrl.lua:1134]:<color=#00FF00> >>> .AddTaskChapterNpcNpc | 实例一个跟踪Npc | Table:AddTraceNpc </color>
[logic/map/CMapCtrl.lua:1134]:<color=#00FF00> >>> .AddTaskChapterNpcNpc | 实例一个跟踪Npc | Table:AddTraceNpc </color>
[logic/map/CMapCtrl.lua:1134]:<color=#00FF00> >>> .AddTaskChapterNpcNpc | 实例一个跟踪Npc | Table:AddTraceNpc </color>
[logic/map/CMapCtrl.lua:1134]:<color=#00FF00> >>> .AddTaskChapterNpcNpc | 实例一个跟踪Npc | Table:AddTraceNpc </color>
[logic/map/CMapCtrl.lua:1134]:<color=#00FF00> >>> .AddTaskChapterNpcNpc | 实例一个跟踪Npc | Table:AddTraceNpc </color>
[logic/map/CMapCtrl.lua:1134]:<color=#00FF00> >>> .AddTaskChapterNpcNpc | 实例一个跟踪Npc | Table:AddTraceNpc </color>
[core/table.lua:94]:Table = {}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayRedDot = {}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayMain = {}
[core/table.lua:94]:-->Net Receive: task.GS2CLoginAchieveTask = {}
[core/global.lua:59]:<color=#ffeb04>LoginAchieveTask</color>
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 696
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  mask = "1997c2e0000000"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  give_org_equip = {}
|  give_org_wish = {}
|  is_equip_wish = 0
|  is_org_wish = 0
|  org_build_status = 0
|  org_build_time = 0
|  org_id = 0
|  org_leader = ""
|  org_level = 0
|  org_pos = 0
|  org_red_packet = 0
|  org_sign_reward = 0
|  org_status = 0
|  orgname = ""
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1102
|  |  |  }
|  |  |  name = "峰馆长"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 2
|  |  npctype = 5040
|  }
|  eid = 1
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 1
|  pos_info = {
|  |  x = 31800
|  |  y = 17600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1102
|  }
|  name = "峰馆长"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1508
|  |  |  }
|  |  |  name = "小叶"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 5
|  |  npctype = 5041
|  }
|  eid = 2
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 2
|  pos_info = {
|  |  x = 40000
|  |  y = 14600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1508
|  }
|  name = "小叶"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1009
|  |  |  }
|  |  |  name = "神父"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 8
|  |  npctype = 5042
|  }
|  eid = 3
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 3
|  pos_info = {
|  |  x = 12200
|  |  y = 25000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1009
|  }
|  name = "神父"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1503
|  |  |  }
|  |  |  name = "喵小布"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 21
|  |  npctype = 5047
|  }
|  eid = 6
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 6
|  pos_info = {
|  |  x = 33000
|  |  y = 9500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1503
|  }
|  name = "喵小布"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1012
|  |  |  }
|  |  |  name = "克瑞斯汀"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 24
|  |  npctype = 5048
|  }
|  eid = 7
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 7
|  pos_info = {
|  |  x = 20200
|  |  y = 21100
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1012
|  }
|  name = "克瑞斯汀"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1510
|  |  |  }
|  |  |  name = "小佳"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 57
|  |  npctype = 5064
|  }
|  eid = 8
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 8
|  pos_info = {
|  |  x = 13800
|  |  y = 20000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1510
|  }
|  name = "小佳"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [3] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 306
|  |  |  }
|  |  |  name = "袁雀"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 60
|  |  npctype = 5001
|  }
|  eid = 9
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 9
|  pos_info = {
|  |  x = 6600
|  |  y = 26200
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 306
|  }
|  name = "袁雀"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1501
|  |  |  }
|  |  |  name = "扳尾"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 63
|  |  npctype = 5002
|  }
|  eid = 10
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 10
|  pos_info = {
|  |  x = 6000
|  |  y = 21200
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1501
|  }
|  name = "扳尾"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1014
|  |  |  }
|  |  |  name = "乔焱"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 66
|  |  npctype = 5003
|  }
|  eid = 11
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 11
|  pos_info = {
|  |  x = 13200
|  |  y = 24600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1014
|  }
|  name = "乔焱"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1010
|  |  |  }
|  |  |  name = "丽丝"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 69
|  |  npctype = 5004
|  }
|  eid = 12
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 12
|  pos_info = {
|  |  x = 45600
|  |  y = 26900
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1010
|  }
|  name = "丽丝"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1011
|  |  |  }
|  |  |  name = "遥"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 72
|  |  npctype = 5005
|  }
|  eid = 13
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 13
|  pos_info = {
|  |  x = 45500
|  |  y = 20000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1011
|  }
|  name = "遥"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 414
|  |  |  }
|  |  |  name = "蓝堂昼"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 75
|  |  npctype = 5006
|  }
|  eid = 14
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 14
|  pos_info = {
|  |  x = 28000
|  |  y = 14500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 414
|  }
|  name = "蓝堂昼"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 311
|  |  |  }
|  |  |  name = "花常酒"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 78
|  |  npctype = 5007
|  }
|  eid = 15
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 15
|  pos_info = {
|  |  x = 35600
|  |  y = 13400
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 311
|  }
|  name = "花常酒"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 508
|  |  |  }
|  |  |  name = "魂夕"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 81
|  |  npctype = 5008
|  }
|  eid = 16
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 16
|  pos_info = {
|  |  x = 24000
|  |  y = 12900
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 508
|  }
|  name = "魂夕"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1016
|  |  |  }
|  |  |  name = "水生"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 196
|  |  npctype = 5009
|  }
|  eid = 17
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 17
|  pos_info = {
|  |  x = 24000
|  |  y = 22000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1016
|  }
|  name = "水生"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1151
|  |  |  }
|  |  |  name = "邓酒爷"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 202
|  |  npctype = 5011
|  }
|  eid = 19
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 19
|  pos_info = {
|  |  x = 22300
|  |  y = 5100
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1151
|  }
|  name = "邓酒爷"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1502
|  |  |  }
|  |  |  name = "荆鸣"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 205
|  |  npctype = 5012
|  }
|  eid = 20
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 20
|  pos_info = {
|  |  x = 7000
|  |  y = 11300
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1502
|  }
|  name = "荆鸣"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 417
|  |  |  }
|  |  |  name = "松姑子"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 220
|  |  npctype = 5019
|  }
|  eid = 21
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 21
|  pos_info = {
|  |  x = 42300
|  |  y = 24600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 417
|  }
|  name = "松姑子"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_player = {
|  |  block = {
|  |  |  mask = "63e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 130
|  |  |  |  weapon = 2000
|  |  |  }
|  |  |  name = "20米之大人"
|  |  |  show_id = 10017
|  |  }
|  |  pid = 10017
|  }
|  eid = 33
|  scene_id = 12
|  type = 1
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 33
|  pos_info = {
|  |  face_y = 301467
|  |  x = 17284
|  |  y = 17586
|  }
|  scene_id = 12
|  type = 1
}
[core/table.lua:94]:CNetCtrl解析mask: PlayerAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 130
|  |  weapon = 2000
|  }
|  name = "20米之大人"
|  show_id = 10017
}
[core/table.lua:94]:-->Net Receive: title.GS2CTitleInfoList = {
|  infos = {
|  |  [1] = {
|  |  |  name = "8段"
|  |  |  tid = 1008
|  |  }
|  |  [10] = {
|  |  |  name = "应用宝贵族"
|  |  |  tid = 1017
|  |  }
|  |  [11] = {
|  |  |  name = "王者公会"
|  |  |  tid = 1018
|  |  }
|  |  [12] = {
|  |  |  name = "王者公会会长"
|  |  |  tid = 1019
|  |  }
|  |  [13] = {
|  |  |  name = "黄金公会"
|  |  |  tid = 1020
|  |  }
|  |  [14] = {
|  |  |  name = "黄金公会会长"
|  |  |  tid = 1021
|  |  }
|  |  [15] = {
|  |  |  name = "白银公会"
|  |  |  tid = 1022
|  |  }
|  |  [16] = {
|  |  |  name = "白银公会会长"
|  |  |  tid = 1023
|  |  }
|  |  [17] = {
|  |  |  name = "明日之星"
|  |  |  tid = 1024
|  |  }
|  |  [18] = {
|  |  |  name = "独孤求败"
|  |  |  tid = 1025
|  |  }
|  |  [19] = {
|  |  |  name = "谁与争锋"
|  |  |  tid = 1026
|  |  }
|  |  [2] = {
|  |  |  name = "测-贵族宝"
|  |  |  tid = 1009
|  |  }
|  |  [20] = {
|  |  |  name = "所向披靡"
|  |  |  tid = 1027
|  |  }
|  |  [21] = {
|  |  |  name = "地牢终结者"
|  |  |  tid = 1028
|  |  }
|  |  [22] = {
|  |  |  name = "地牢征服者"
|  |  |  tid = 1029
|  |  }
|  |  [23] = {
|  |  |  name = "地牢破坏者"
|  |  |  tid = 1030
|  |  }
|  |  [24] = {
|  |  |  name = "眯眯眼是变态"
|  |  |  tid = 1031
|  |  }
|  |  [25] = {
|  |  |  name = "邻家妹妹"
|  |  |  tid = 1032
|  |  }
|  |  [26] = {
|  |  |  name = "兄控"
|  |  |  tid = 1033
|  |  }
|  |  [27] = {
|  |  |  name = "农夫三拳"
|  |  |  tid = 1034
|  |  }
|  |  [28] = {
|  |  |  name = "赌神"
|  |  |  tid = 1035
|  |  }
|  |  [29] = {
|  |  |  name = "神之右手"
|  |  |  tid = 1036
|  |  }
|  |  [3] = {
|  |  |  name = "测-我就是托"
|  |  |  tid = 1010
|  |  }
|  |  [30] = {
|  |  |  name = "茶会噩梦"
|  |  |  tid = 1037
|  |  }
|  |  [31] = {
|  |  |  name = "我是傲娇"
|  |  |  tid = 1038
|  |  }
|  |  [32] = {
|  |  |  name = "闷骚美男子"
|  |  |  tid = 1039
|  |  }
|  |  [33] = {
|  |  |  name = "脸炮"
|  |  |  tid = 1040
|  |  }
|  |  [34] = {
|  |  |  name = "妹控"
|  |  |  tid = 1041
|  |  }
|  |  [35] = {
|  |  |  name = "守夜人"
|  |  |  tid = 1042
|  |  }
|  |  [36] = {
|  |  |  name = "泡泡娘"
|  |  |  tid = 1043
|  |  }
|  |  [37] = {
|  |  |  name = "姐控"
|  |  |  tid = 1044
|  |  }
|  |  [38] = {
|  |  |  name = "迷妹"
|  |  |  tid = 1045
|  |  }
|  |  [39] = {
|  |  |  name = "小天使"
|  |  |  tid = 1046
|  |  }
|  |  [4] = {
|  |  |  name = "测-粗壮大腿"
|  |  |  tid = 1011
|  |  }
|  |  [40] = {
|  |  |  name = "欺诈兔子"
|  |  |  tid = 1047
|  |  }
|  |  [41] = {
|  |  |  name = "天然呆"
|  |  |  tid = 1048
|  |  }
|  |  [42] = {
|  |  |  name = "开局一条狗"
|  |  |  tid = 1049
|  |  }
|  |  [43] = {
|  |  |  name = "种竹专业户"
|  |  |  tid = 1050
|  |  }
|  |  [44] = {
|  |  |  name = "顾家好男人"
|  |  |  tid = 1051
|  |  }
|  |  [45] = {
|  |  |  name = "无敌小钢炮"
|  |  |  tid = 1052
|  |  }
|  |  [46] = {
|  |  |  name = "小豆丁"
|  |  |  tid = 1053
|  |  }
|  |  [47] = {
|  |  |  name = "浪里白条"
|  |  |  tid = 1054
|  |  }
|  |  [48] = {
|  |  |  name = "话唠"
|  |  |  tid = 1055
|  |  }
|  |  [49] = {
|  |  |  name = "角落种蘑菇"
|  |  |  tid = 1056
|  |  }
|  |  [5] = {
|  |  |  name = "攻防战第一公会"
|  |  |  tid = 1012
|  |  }
|  |  [50] = {
|  |  |  name = "天然疯"
|  |  |  tid = 1057
|  |  }
|  |  [51] = {
|  |  |  name = "吃货"
|  |  |  tid = 1058
|  |  }
|  |  [52] = {
|  |  |  name = "控制狂"
|  |  |  tid = 1059
|  |  }
|  |  [53] = {
|  |  |  name = "女王大人"
|  |  |  tid = 1060
|  |  }
|  |  [54] = {
|  |  |  name = "八卦小王子"
|  |  |  tid = 1061
|  |  }
|  |  [55] = {
|  |  |  name = "三无"
|  |  |  tid = 1062
|  |  }
|  |  [56] = {
|  |  |  name = "养得累"
|  |  |  tid = 1063
|  |  }
|  |  [57] = {
|  |  |  name = "扑街之王"
|  |  |  tid = 1064
|  |  }
|  |  [58] = {
|  |  |  name = "抖S"
|  |  |  tid = 1065
|  |  }
|  |  [59] = {
|  |  |  name = "工作狂人"
|  |  |  tid = 1066
|  |  }
|  |  [6] = {
|  |  |  name = "攻防战第一司令"
|  |  |  tid = 1013
|  |  }
|  |  [60] = {
|  |  |  name = "精分中毒患者"
|  |  |  tid = 1067
|  |  }
|  |  [61] = {
|  |  |  name = "神神叨叨"
|  |  |  tid = 1068
|  |  }
|  |  [62] = {
|  |  |  name = "浮夸之神"
|  |  |  tid = 1069
|  |  }
|  |  [63] = {
|  |  |  name = "抖M"
|  |  |  tid = 1070
|  |  }
|  |  [64] = {
|  |  |  name = "自恋狂"
|  |  |  tid = 1071
|  |  }
|  |  [65] = {
|  |  |  name = "天然黑"
|  |  |  tid = 1072
|  |  }
|  |  [66] = {
|  |  |  name = "为所欲为"
|  |  |  tid = 1073
|  |  }
|  |  [67] = {
|  |  |  name = "氪金大佬"
|  |  |  tid = 1074
|  |  }
|  |  [68] = {
|  |  |  name = "应用宝帝王"
|  |  |  tid = 1075
|  |  }
|  |  [69] = {
|  |  |  name = "应用宝至尊"
|  |  |  tid = 1076
|  |  }
|  |  [7] = {
|  |  |  name = "梦魇之主"
|  |  |  tid = 1014
|  |  }
|  |  [70] = {
|  |  |  name = "公会新人"
|  |  |  tid = 1077
|  |  }
|  |  [71] = {
|  |  |  name = "公会成员"
|  |  |  tid = 1078
|  |  }
|  |  [72] = {
|  |  |  name = "公会精英"
|  |  |  tid = 1079
|  |  }
|  |  [73] = {
|  |  |  name = "公会副会"
|  |  |  tid = 1080
|  |  }
|  |  [74] = {
|  |  |  name = "公会会长"
|  |  |  tid = 1081
|  |  }
|  |  [75] = {
|  |  |  name = "情侣专属称谓"
|  |  |  tid = 1082
|  |  }
|  |  [76] = {
|  |  |  name = "命运之子"
|  |  |  tid = 1083
|  |  }
|  |  [77] = {
|  |  |  name = "不做大多数"
|  |  |  tid = 1084
|  |  }
|  |  [78] = {
|  |  |  name = "1段"
|  |  |  tid = 1001
|  |  }
|  |  [79] = {
|  |  |  name = "2段"
|  |  |  tid = 1002
|  |  }
|  |  [8] = {
|  |  |  name = "梦魇骑士"
|  |  |  tid = 1015
|  |  }
|  |  [80] = {
|  |  |  name = "3段"
|  |  |  tid = 1003
|  |  }
|  |  [81] = {
|  |  |  name = "4段"
|  |  |  tid = 1004
|  |  }
|  |  [82] = {
|  |  |  name = "5段"
|  |  |  tid = 1005
|  |  }
|  |  [83] = {
|  |  |  name = "6段"
|  |  |  tid = 1006
|  |  }
|  |  [84] = {
|  |  |  name = "7段"
|  |  |  tid = 1007
|  |  }
|  |  [9] = {
|  |  |  name = "梦魇猎人"
|  |  |  tid = 1016
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = **********
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/03/23 14:32:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/platform/CSdkCtrl.lua:380]:sdk upload: not init
[core/table.lua:94]:Table = {
|  [1] = "16"
|  [2] = "20"
}
[core/global.lua:59]:<color=#ffeb04>2 16 20</color>
[core/table.lua:94]:Table = {
|  [1] = "21"
|  [2] = "24"
}
[core/global.lua:59]:<color=#ffeb04>2 21 24</color>
[core/table.lua:94]:Table = {
|  [1] = "25"
|  [2] = "27"
}
[core/global.lua:59]:<color=#ffeb04>2 25 27</color>
[logic/base/CResCtrl.lua:623]:res gc finish!
[logic/task/CTaskBox.lua:156]:<color=#00FF00> >>> .OnTaskBox | 表数据查看 | 任务导航TaskBox数据输出 </color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x65e98540"
|  |  AssociatedPick = "function: 0x65e985a0"
|  |  AssociatedSubmit = "function: 0x65e98570"
|  |  CreateDefalutData = "function: 0x65e96548"
|  |  GetChaptetFubenData = "function: 0x65e98388"
|  |  GetProgressThing = "function: 0x65e98600"
|  |  GetRemainTime = "function: 0x65e98200"
|  |  GetStatus = "function: 0x65e98660"
|  |  GetTaskClientExtStrDic = "function: 0x65e985d0"
|  |  GetTaskTypeSpriteteName = "function: 0x65e98290"
|  |  GetTraceInfo = "function: 0x65e983e8"
|  |  GetTraceNpcType = "function: 0x65e98260"
|  |  GetValue = "function: 0x65e982c8"
|  |  IsAbandon = "function: 0x65e98328"
|  |  IsAddEscortDynamicNpc = "function: 0x65e98998"
|  |  IsMissMengTask = "function: 0x65e98230"
|  |  IsPassChaterFuben = "function: 0x65e983b8"
|  |  IsTaskSpecityAction = "function: 0x65e98358"
|  |  IsTaskSpecityCategory = "function: 0x65e98510"
|  |  New = "function: 0x65e95a28"
|  |  NewByData = "function: 0x65e9d4a0"
|  |  RaiseProgressIdx = "function: 0x65e98630"
|  |  RefreshTask = "function: 0x65e982f8"
|  |  ResetEndTime = "function: 0x65e96510"
|  |  SetStatus = "function: 0x65e964e0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x65e964b0"
|  }
|  m_CData = {
|  |  ChapterFb = "1,1"
|  |  autoDoNextTask = 0
|  |  clientExtStr = ""
|  |  name = "少女重华"
|  |  submitNpcId = 10007
|  |  submitRewardStr = {
|  |  |  [1] = "R1003"
|  |  }
|  |  taskWalkingTips = "想不到帝都也有恶霸。,那个人是刚才的！;none;none"
|  |  tips = 0
|  |  type = 2
|  }
|  m_ChapterFb = "1,1"
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10007
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 302
|  |  |  |  }
|  |  |  |  name = "重华"
|  |  |  |  npcid = 8793
|  |  |  |  npctype = 10007
|  |  |  |  pos_info = {
|  |  |  |  |  x = 12800
|  |  |  |  |  y = 9700
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "摆脱少女重华的纠缠！"
|  |  isdone = 0
|  |  name = "少女重华"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10007
|  |  target = 10007
|  |  targetdesc = "遭到质问"
|  |  taskid = 10003
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x65e98540"
|  |  AssociatedPick = "function: 0x65e985a0"
|  |  AssociatedSubmit = "function: 0x65e98570"
|  |  CreateDefalutData = "function: 0x65e96548"
|  |  GetChaptetFubenData = "function: 0x65e98388"
|  |  GetProgressThing = "function: 0x65e98600"
|  |  GetRemainTime = "function: 0x65e98200"
|  |  GetStatus = "function: 0x65e98660"
|  |  GetTaskClientExtStrDic = "function: 0x65e985d0"
|  |  GetTaskTypeSpriteteName = "function: 0x65e98290"
|  |  GetTraceInfo = "function: 0x65e983e8"
|  |  GetTraceNpcType = "function: 0x65e98260"
|  |  GetValue = "function: 0x65e982c8"
|  |  IsAbandon = "function: 0x65e98328"
|  |  IsAddEscortDynamicNpc = "function: 0x65e98998"
|  |  IsMissMengTask = "function: 0x65e98230"
|  |  IsPassChaterFuben = "function: 0x65e983b8"
|  |  IsTaskSpecityAction = "function: 0x65e98358"
|  |  IsTaskSpecityCategory = "function: 0x65e98510"
|  |  New = "function: 0x65e95a28"
|  |  NewByData = "function: 0x65e9d4a0"
|  |  RaiseProgressIdx = "function: 0x65e98630"
|  |  RefreshTask = "function: 0x65e982f8"
|  |  ResetEndTime = "function: 0x65e96510"
|  |  SetStatus = "function: 0x65e964e0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x65e964b0"
|  }
|  m_CData = {
|  |  ChapterFb = "1,1"
|  |  autoDoNextTask = 0
|  |  clientExtStr = ""
|  |  name = "少女重华"
|  |  submitNpcId = 10007
|  |  submitRewardStr = {
|  |  |  [1] = "R1003"
|  |  }
|  |  taskWalkingTips = "想不到帝都也有恶霸。,那个人是刚才的！;none;none"
|  |  tips = 0
|  |  type = 2
|  }
|  m_ChapterFb = "1,1"
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10007
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 302
|  |  |  |  }
|  |  |  |  name = "重华"
|  |  |  |  npcid = 8793
|  |  |  |  npctype = 10007
|  |  |  |  pos_info = {
|  |  |  |  |  x = 12800
|  |  |  |  |  y = 9700
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "摆脱少女重华的纠缠！"
|  |  isdone = 0
|  |  name = "少女重华"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10007
|  |  target = 10007
|  |  targetdesc = "遭到质问"
|  |  taskid = 10003
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 23067
}
[core/table.lua:94]:<--Net Send: huodong.C2GSFightChapterFb = {
|  chapter = 1
|  level = 1
|  type = 1
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 23067
}
[core/table.lua:94]:-->Net Receive: war.GS2CShowWar = {
|  lineup = "3"
|  war_id = 77
|  war_type = 10001
}
[core/global.lua:59]:<color=#ffeb04>war_id: 77</color>
[core/global.lua:59]:<color=#ffeb04>CViewCtrl.CloseAll--> false</color>
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/ui/CViewCtrl"]:280: in function 'CloseAll'
	[string "logic/war/CWarCtrl"]:670: in function 'SwitchEnv'
	[string "logic/war/CWarCtrl"]:587: in function 'Start'
	[string "net/netwar"]:51: in function <[string "net/netwar"]:5>
	[C]: in function 'xxpcall'
	[string "net/CNetCtrl"]:318: in function 'ProtoCall'
	[string "net/CNetCtrl"]:300: in function 'ProcessProto'
	[string "net/CNetCtrl"]:251: in function 'ProtoHandlerCheck'
	[string "net/CNetCtrl"]:262: in function 'Receive'
	[string "net/CNetCtrl"]:175: in function 'OnSocketEvent'
	[string "net/CNetCtrl"]:111: in function 'm_Callback'
	[string "net/CTcpClient"]:43: in function <[string "net/CTcpClient"]:37>
[core/global.lua:59]:<color=#ffeb04>CloseAll-->CloseView:  CMainMenuView</color>
[logic/ui/CViewCtrl.lua:104]:CMainMenuView     CloseView
[logic/ui/CViewCtrl.lua:94]:CWarFloatView ShowView
[logic/ui/CViewCtrl.lua:94]:CWarMainView ShowView
[core/global.lua:59]:<color=#ffeb04>=00000=  userdata: 0x7da77ca8 nil</color>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/war/CWarCtrl"]:595: in function 'Start'
	[string "net/netwar"]:51: in function <[string "net/netwar"]:5>
	[C]: in function 'xxpcall'
	[string "net/CNetCtrl"]:318: in function 'ProtoCall'
	[string "net/CNetCtrl"]:300: in function 'ProcessProto'
	[string "net/CNetCtrl"]:251: in function 'ProtoHandlerCheck'
	[string "net/CNetCtrl"]:262: in function 'Receive'
	[string "net/CNetCtrl"]:175: in function 'OnSocketEvent'
	[string "net/CNetCtrl"]:111: in function 'm_Callback'
	[string "net/CTcpClient"]:43: in function <[string "net/CTcpClient"]:37>
[core/table.lua:94]:<--Net Send: war.C2GSWarSetPlaySpeed = {
|  play_speed = 77
|  war_id = 77
}
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:98: in function <[string "logic/ui/CViewBase"]:97>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/war/CWarRT"]:33: in function 'CheckShow'
	[string "logic/war/CWarRT"]:12: in function 'ctor'
	[string "core/class"]:20: in function 'New'
	[string "logic/base/CGameObjContainer"]:33: in function 'NewUI'
	[string "logic/war/CWarMainView"]:17: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:258: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:245: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:224: in function <[string "logic/base/CResCtrl"]:223>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:265: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:297: in function <[string "logic/base/CResCtrl"]:294>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[logic/ui/CViewBase.lua:125]:CWarMainView LoadDone!
[core/global.lua:59]:<color=#ffeb04>读取地图： 5020</color>
[core/global.lua:59]:<color=#ffeb04>Unity2017.4.30f1 localScale</color>
[core/global.lua:59]:<color=#ffeb04>找到新的引导类型 War1</color>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/war/CWarRT"]:33: in function 'CheckShow'
	[string "logic/war/CWarRT"]:19: in function <[string "logic/war/CWarRT"]:17>
	[C]: in function 'xxpcall'
	[string "logic/base/CCtrlBase"]:40: in function 'Notify'
	[string "logic/base/CCtrlBase"]:58: in function 'OnEvent'
	[string "logic/base/CCtrlBase"]:77: in function <[string "logic/base/CCtrlBase"]:72>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/war/CWarRT"]:33: in function <[string "logic/war/CWarRT"]:28>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/mainmenu/CMainMenuExpandBox"]:280: in function <[string "logic/mainmenu/CMainMenuExpandBox"]:251>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[logic/ui/CViewBase.lua:125]:CWarFloatView LoadDone!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[core/global.lua:59]:<color=#ffeb04>地图加载完成: 5020 ,当前地图: 1020</color>
[core/global.lua:59]:<color=#ffeb04>删除地图: 1020</color>
[logic/base/CResCtrl.lua:596]:res gc step start!
[core/table.lua:94]:-->Net Receive: war.GS2CEnterWar = {}
[core/table.lua:94]:-->Net Receive: war.GS2CWarWave = {
|  cur_wave = 1
|  sum_wave = 1
}
[core/global.lua:59]:<color=#ffeb04>--->GS2CWarWave: 1 1</color>
[core/table.lua:94]:-->Net Receive: war.GS2CWarAddWarrior = {
|  camp_id = 1
|  type = 1
|  war_id = 77
|  warrior = {
|  |  pflist = {
|  |  |  [1] = {
|  |  |  |  id = 3002
|  |  |  |  level = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  id = 3001
|  |  |  |  level = 1
|  |  |  }
|  |  }
|  |  pid = 10018
|  |  pos = 1
|  |  status = {
|  |  |  hp = 2212
|  |  |  mask = "7e"
|  |  |  max_hp = 2212
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 110
|  |  |  |  weapon = 2500
|  |  |  }
|  |  |  name = "掏出杨桃"
|  |  |  status = 1
|  |  }
|  |  wid = 1
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  auto_skill = 0
|  hp = 2212
|  max_hp = 2212
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 110
|  |  weapon = 2500
|  }
|  name = "掏出杨桃"
|  status = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarAddWarrior = {
|  camp_id = 2
|  npcwarrior = {
|  |  pos = 1
|  |  show_lv = 1
|  |  special_skill = {
|  |  |  cur_grid = 2
|  |  |  skill_id = 115002
|  |  |  sum_grid = 2
|  |  }
|  |  status = {
|  |  |  hp = 651
|  |  |  mask = "7e"
|  |  |  max_hp = 651
|  |  |  model_info = {
|  |  |  |  shape = 1150
|  |  |  |  weapon = 2100
|  |  |  }
|  |  |  name = "飞龙哥"
|  |  |  status = 1
|  |  }
|  |  wid = 2
|  }
|  type = 2
|  war_id = 77
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  auto_skill = 0
|  hp = 651
|  max_hp = 651
|  model_info = {
|  |  shape = 1150
|  |  weapon = 2100
|  }
|  name = "飞龙哥"
|  status = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSpeed = {
|  speed_list = {
|  |  [1] = {
|  |  |  speed = 84
|  |  |  wid = 2
|  |  }
|  |  [2] = {
|  |  |  speed = 753
|  |  |  wid = 1
|  |  }
|  }
|  war_id = 77
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 80
|  camp_id = 1
|  sp = 80
|  war_id = 77
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarBoutStart = {
|  bout_id = 1
|  war_id = 77
}
[core/global.lua:59]:<color=#ffeb04>--->GS2CWarBoutStart: 1</color>
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 3001
|  |  |  wid = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSpeed = {
|  speed_list = {
|  |  [1] = {
|  |  |  camp = 1
|  |  |  speed = 753
|  |  |  wid = 1
|  |  }
|  |  [2] = {
|  |  |  camp = 2
|  |  |  speed = 84
|  |  |  wid = 2
|  |  }
|  }
|  war_id = 77
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSP = {
|  addsp = 10
|  camp_id = 1
|  sp = 90
|  war_id = 77
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionStart = {
|  action_id = 1
|  left_time = 15
|  war_id = 77
|  wid = 1
}
[core/global.lua:59]:<color=#ffeb04>--->Proto SectionStart: 1</color>
[core/table.lua:94]:-->Net Receive: war.GS2CWarChapterInfo = {
|  start_time = 1679553162
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarSetPlaySpeed = {
|  play_speed = 77
|  war_id = 77
}
[logic/base/CResCtrl.lua:623]:res gc finish!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>UpdateNewWaveTag:true</color>
[logic/war/CWarCtrl.lua:1404]:自定义站位    1    3.************    -3.3234009742737
[logic/war/CWarCtrl.lua:1404]:自定义站位    1    -2.3599998950958    2.3900001049042
[logic/model/CModel.lua:181]:造型:    1150    ,没有武器    2100
[logic/ui/CViewCtrl.lua:104]:CWarBossView     CloseView
[core/global.lua:59]:<color=#ffeb04>UpdateNewWaveTag:false</color>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/war/CWarRT"]:33: in function 'CheckShow'
	[string "logic/war/CWarRT"]:24: in function 'Bout'
	[string "logic/war/CWarMainView"]:59: in function 'Bout'
	[string "logic/war/CWarMainView"]:33: in function <[string "logic/war/CWarMainView"]:29>
	[C]: in function 'xxpcall'
	[string "logic/base/CCtrlBase"]:40: in function 'Notify'
	[string "logic/base/CCtrlBase"]:58: in function 'OnEvent'
	[string "logic/base/CCtrlBase"]:77: in function <[string "logic/base/CCtrlBase"]:72>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>刷新全部站位,  10001</color>
[logic/war/CWarCtrl.lua:1404]:自定义站位    1    3.************    -3.3234009742737
[logic/war/CWarCtrl.lua:1404]:自定义站位    1    -2.3599998950958    2.3900001049042
[core/table.lua:94]:ally_player:1 = {}
[core/table.lua:94]:enemy_player:0 = {}
[core/global.lua:59]:<color=#ffeb04>SectionStart  1 1 1 flag: 0</color>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/war/CWarRT"]:33: in function 'CheckShow'
	[string "logic/war/CWarRT"]:19: in function <[string "logic/war/CWarRT"]:17>
	[C]: in function 'xxpcall'
	[string "logic/base/CCtrlBase"]:40: in function 'Notify'
	[string "logic/base/CCtrlBase"]:58: in function 'OnEvent'
	[string "logic/base/CCtrlBase"]:77: in function <[string "logic/base/CCtrlBase"]:72>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/war/CWarRT"]:33: in function <[string "logic/war/CWarRT"]:28>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[logic/ui/CViewCtrl.lua:94]:CGuideView ShowView
[core/table.lua:94]:<--Net Send: war.C2GSSelectCmd = {
|  skill = 3001
|  war_id = 77
|  wid = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 3001
|  |  |  wid = 1
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/task/CTaskCtrl"]:2434: in function 'StartAutoDoingShiMen'
	[string "logic/guide/CGuideCtrl"]:2383: in function 'StopActionWhenGuide'
	[string "logic/guide/CGuideView"]:31: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:258: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:245: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:224: in function <[string "logic/base/CResCtrl"]:223>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:265: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:308: in function <[string "logic/base/CResCtrl"]:302>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[logic/ui/CViewBase.lua:125]:CGuideView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 War1_1 1</color>
[core/table.lua:94]:<--Net Send: war.C2GSWarAutoFight = {
|  type = 0
|  war_id = 77
}
[core/table.lua:94]:<--Net Send: war.C2GSWarStop = {
|  war_id = 77
}
[core/table.lua:94]:<--Net Send: war.C2GSWarStop = {
|  war_id = 77
}
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/table.lua:94]:-->Net Receive: war.GS2CWarWarriorStatus = {
|  status = {
|  |  mask = "40"
|  }
|  type = 1
|  war_id = 77
|  wid = 1
}
[core/table.lua:94]:CNetCtrl解析mask: WarriorStatus = {
|  auto_skill = 0
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarTarget = {
|  war_id = 77
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarStatus = {
|  war_id = 77
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarStatus = {
|  war_id = 77
}
[core/table.lua:94]:<--Net Send: war.C2GSSelectCmd = {
|  skill = 3001
|  war_id = 77
|  wid = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 3001
|  |  |  wid = 1
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>引导结束 War1 War1_1 1</color>
[core/table.lua:94]:<--Net Send: war.C2GSWarStart = {
|  war_id = 77
}
[core/table.lua:94]:<--Net Send: war.C2GSWarStart = {
|  war_id = 77
}
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 War1_2 2</color>
[core/table.lua:94]:<--Net Send: war.C2GSWarStop = {
|  war_id = 77
}
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/table.lua:94]:-->Net Receive: war.GS2CWarStatus = {
|  left_time = 13
|  status = 1
|  war_id = 77
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionStart = {
|  action_id = 1
|  left_time = 13
|  war_id = 77
}
[core/global.lua:59]:<color=#ffeb04>--->Proto SectionStart: 1</color>
[logic/ui/CViewBase.lua:131]:CGuideMaskView LoadDone, not in loadingview!
[core/table.lua:94]:<--Net Send: war.C2GSSelectCmd = {
|  skill = 3001
|  war_id = 77
|  wid = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarStatus = {
|  left_time = 13
|  status = 1
|  war_id = 77
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionStart = {
|  action_id = 1
|  left_time = 13
|  war_id = 77
}
[core/global.lua:59]:<color=#ffeb04>--->Proto SectionStart: 1</color>
[core/table.lua:94]:-->Net Receive: war.GS2CWarStatus = {
|  war_id = 77
}
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 3001
|  |  |  wid = 1
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: war.C2GSSelectCmd = {
|  skill = 3001
|  war_id = 77
|  wid = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 3001
|  |  |  wid = 1
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1679553168
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/03/23 14:32:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: war.C2GSSelectCmd = {
|  skill = 3001
|  war_id = 77
|  wid = 1
}
[core/global.lua:59]:<color=#ffeb04>引导结束 War1 War1_2 2</color>
[core/table.lua:94]:<--Net Send: war.C2GSWarStart = {
|  war_id = 77
}
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 War1_3 3</color>
[core/table.lua:94]:<--Net Send: war.C2GSWarStop = {
|  war_id = 77
}
[logic/ui/CViewBase.lua:131]:CGuideMaskView LoadDone, not in loadingview!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  war = {
|  |  [1] = "War1"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 3001
|  |  |  wid = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: war.GS2CWarStatus = {
|  left_time = 13
|  status = 1
|  war_id = 77
}
[core/table.lua:94]:-->Net Receive: war.GS2CActionStart = {
|  action_id = 1
|  left_time = 13
|  war_id = 77
}
[core/global.lua:59]:<color=#ffeb04>--->Proto SectionStart: 1</color>
[core/table.lua:94]:-->Net Receive: war.GS2CWarStatus = {
|  war_id = 77
}
[core/table.lua:94]:<--Net Send: war.C2GSSelectCmd = {
|  skill = 3001
|  war_id = 77
|  wid = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 3001
|  |  |  wid = 1
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: war.C2GSSelectCmd = {
|  skill = 3001
|  war_id = 77
|  wid = 1
}
[core/table.lua:94]:-->Net Receive: war.GS2CSelectCmd = {
|  cmd = {
|  |  [1] = {
|  |  |  skill = 3001
|  |  |  wid = 1
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1679553178
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/03/23 14:32:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1679553188
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/03/23 14:33:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1679553198
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/03/23 14:33:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1679553208
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/03/23 14:33:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1679553218
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/03/23 14:33:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1679553228
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/03/23 14:33:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1679553238
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/03/23 14:33:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1679553248
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/03/23 14:34:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
