with:824  hight:465
[logic/misc/CShareCtrl.lua:28]:jit    true    SSE2    SSE3    SSE4.1    BMI2    fold    cse    dce    fwd    dse    narrow    loop    abc    sink    fuse
[logic/login/CLoginCtrl.lua:5]:公告初始化...
[logic/base/CHttpCtrl.lua:32]:http get ->    https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials&client_id=rlU84dprH6I7sMjBkqM0gizl&client_secret=c15395c2433c4a904d22aa27eab8321b&
[logic/ui/CViewCtrl.lua:94]:CNotifyView ShowView
[logic/ui/CViewCtrl.lua:94]:CBottomView ShowView
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x5b01c258"
|  json_result = true
|  timer = 16
}
[logic/audio/CSpeechCtrl.lua:72]:OnTokenGet err
[logic/ui/CViewBase.lua:125]:CNotifyView LoadDone!
[logic/ui/CViewBase.lua:125]:CBottomView LoadDone!
[logic/base/CResCtrl.lua:199]:-->resource init done!!! 12
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {}
[logic/ui/CViewCtrl.lua:94]:CLoginView ShowView
[logic/base/CHttpCtrl.lua:32]:http get ->    http://**************:88/Note/note.json
[logic/ui/CViewBase.lua:125]:CLoginView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x7b6dbba8"
|  json_result = true
|  timer = 58
}
[core/table.lua:94]:cb tResult-> = {
|  info = {
|  |  role_list = {}
|  |  server_info = {
|  |  |  groups = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 1
|  |  |  |  |  name = "1-10区"
|  |  |  |  }
|  |  |  }
|  |  |  infoList = {
|  |  |  |  [1] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]新服限时-大狂欢活动[-]"
|  |  |  |  |  |  |  |  title = "活动名称"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "开服咯"
|  |  |  |  }
|  |  |  }
|  |  |  ports = {}
|  |  |  role_list = ""
|  |  |  servers = {
|  |  |  |  [1] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 1
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "测试1服"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1680537600
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7011
|  |  |  |  |  |  [2] = 7012
|  |  |  |  |  |  [3] = 7013
|  |  |  |  |  |  [4] = 7014
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10001"
|  |  |  |  |  start_time = 1680537600
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  }
|  |  }
|  |  token = ""
|  }
}
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {
|  [1] = {
|  |  group_id = 1
|  |  id = 1
|  |  name = "1-10区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  id = 1
|  |  |  |  ip = "**************"
|  |  |  |  name = "测试1服"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = 1680537600
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  |  [2] = 7012
|  |  |  |  |  [3] = 7013
|  |  |  |  |  [4] = 7014
|  |  |  |  }
|  |  |  |  server_id = 1
|  |  |  |  start_time = 1680537600
|  |  |  |  state = 2
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:设置公告: = {
|  [1] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]新服限时-大狂欢活动[-]"
|  |  |  |  |  title = "活动名称"
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "开服咯"
|  }
}
[core/table.lua:94]:http post -> = {
|  headers = {
|  |  Content-Type = "application/x-www-form-urlencoded"
|  }
|  url = "http://**************:8081/common/accountList"
}
[core/global.lua:59]:<color=#ffeb04>CLoginServerPage.SetServer</color>
[logic/login/CLoginCtrl.lua:154]:ConnectServer =     table:0x7B6DEE88    table:0x7B898978
[net/CNetCtrl.lua:114]:Test连接    **************    7013
[core/table.lua:94]:-->Net Receive: login.GS2CHello = {
|  time = **********
}
[core/table.lua:94]:<--Net Send: login.C2GSQueryLogin = {
|  res_file_version = {}
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:04:24
[net/netlogin.lua:208]:netlogin.GS2CQueryLogin-->
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 1001
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginAccount = {
|  account = "test"
|  client_svn_version = 96
|  client_version = "1.15.0.0"
|  device = "B760M GAMING (JGINYUE)"
|  imei = ""
|  is_qrcode = 0
|  mac = "00-E0-1A-9A-48-AB"
|  os = "Windows 10  (10.0.0) 64bit"
|  platform = 4
|  udid = "6370fc50ee13b3bf1fd91cc507270f729c390721"
}
[core/global.lua:59]:<color=#ffeb04>更新代码: 1049</color>
[logic/base/Utils.lua:400]:local framever, dllver, resver = C_api.Utils.GetResVersion()
if resver >= 96 or Utils.IsEditor() then
	function CWelfareCtrl.IsOpenCostSave(self)
		local b = false
		local t = g_TimeCtrl:GetTimeS()	
		if self.m_CostSaveStartTime ~= 0 and self.m_CostSaveEndTime ~= 0 and t > self.m_CostSaveStartTime and t < self.m_CostSaveEndTime then
			b = true
		end
		return b
	end
	function CPartnerHireView.IsCanYFRH(self, iParID)
		local d = data.partnerhiredata.DATA
		if table.index(d[iParID]["recommand_list"], "一发入魂") then
			return self:IsCanSSRDraw()
		end
	end
	function CPartnerEquipPage.ChangePart(self)
		local oItem = g_ItemCtrl:GetItem(self.m_CurItemID)
		if oItem and oItem:GetValue("level") == define.Partner.ParEquip.MaxLevel and oItem:GetValue("star") < define.Partner.ParEquip.MaxStar then
			if self.m_CurPart ~= self.m_UpStarPart and self.m_CurPart ~= self.m_UpStonePart then
				self.m_UpStarBtn:SetSelected(true)
				self:SwitchPart()
			end
		end
	end
	data.netdata.BAN["proto"]["herobox"]["item"]["GS2CItemQuickUse"] = true
end

[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 1001
}
[core/table.lua:94]:-->Net Receive: login.GS2CCheckInviteCodeResult = {
|  msg = "邀请码验证已关闭"
|  result = 1
}
[core/table.lua:94]:-->Net Receive: login.GS2CLoginAccount = {
|  account = "test"
|  role_list = {
|  |  [1] = {
|  |  |  grade = 36
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 160
|  |  |  |  weapon = 2300
|  |  |  }
|  |  |  pid = 10003
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginRole = {
|  account = "test"
|  pid = 10003
}
[core/global.lua:59]:<color=#ffeb04>读取系统设置本地数据，帐号为  test</color>
[core/global.lua:59]:<color=#ffeb04>test 的系统设置本地数据为 nil，使用默认数据</color>
[core/table.lua:94]:-->Net Receive: login.GS2CLoginRole = {
|  account = "test"
|  pid = 10003
|  role = {
|  |  active = 58
|  |  bcmd = {
|  |  |  [1] = {
|  |  |  |  idx = 208
|  |  |  }
|  |  |  [10] = {
|  |  |  |  idx = 105
|  |  |  }
|  |  |  [11] = {
|  |  |  |  cmd = "封印"
|  |  |  |  idx = 202
|  |  |  }
|  |  |  [12] = {
|  |  |  |  cmd = "减速"
|  |  |  |  idx = 203
|  |  |  }
|  |  |  [13] = {
|  |  |  |  idx = 108
|  |  |  }
|  |  |  [14] = {
|  |  |  |  idx = 205
|  |  |  }
|  |  |  [15] = {
|  |  |  |  idx = 206
|  |  |  }
|  |  |  [16] = {
|  |  |  |  idx = 207
|  |  |  }
|  |  |  [2] = {
|  |  |  |  cmd = "集火"
|  |  |  |  idx = 201
|  |  |  }
|  |  |  [3] = {
|  |  |  |  cmd = "禁疗"
|  |  |  |  idx = 204
|  |  |  }
|  |  |  [4] = {
|  |  |  |  idx = 106
|  |  |  }
|  |  |  [5] = {
|  |  |  |  idx = 107
|  |  |  }
|  |  |  [6] = {
|  |  |  |  cmd = "治疗"
|  |  |  |  idx = 101
|  |  |  }
|  |  |  [7] = {
|  |  |  |  cmd = "复活"
|  |  |  |  idx = 102
|  |  |  }
|  |  |  [8] = {
|  |  |  |  cmd = "加速"
|  |  |  |  idx = 103
|  |  |  }
|  |  |  [9] = {
|  |  |  |  cmd = "解封"
|  |  |  |  idx = 104
|  |  |  }
|  |  }
|  |  coin = 667054
|  |  energy = 183
|  |  exp = 118806
|  |  goldcoin = 870
|  |  grade = 36
|  |  kp_sdk_info = {
|  |  |  create_time = **********
|  |  |  upgrade_time = **********
|  |  }
|  |  mask = "7e6683d1ffffffe"
|  |  model_info = {
|  |  |  color = {
|  |  |  |  [1] = 0
|  |  |  }
|  |  |  shape = 160
|  |  |  weapon = 2300
|  |  }
|  |  name = "人类d青蒿娘"
|  |  open_day = 6
|  |  org_fuben_cnt = 2
|  |  power = **********
|  |  school = 3
|  |  school_branch = 1
|  |  sex = 2
|  |  show_id = 10003
|  |  skill_point = 50
|  |  systemsetting = {}
|  }
|  role_token = "**********0008"
|  xg_account = "bus10003"
}
[logic/ui/CViewCtrl.lua:94]:CMainMenuView ShowView
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 0
|  active = 58
|  arenamedal = 0
|  attack = 0
|  bcmd = {
|  |  [1] = {
|  |  |  idx = 208
|  |  }
|  |  [10] = {
|  |  |  idx = 105
|  |  }
|  |  [11] = {
|  |  |  cmd = "封印"
|  |  |  idx = 202
|  |  }
|  |  [12] = {
|  |  |  cmd = "减速"
|  |  |  idx = 203
|  |  }
|  |  [13] = {
|  |  |  idx = 108
|  |  }
|  |  [14] = {
|  |  |  idx = 205
|  |  }
|  |  [15] = {
|  |  |  idx = 206
|  |  }
|  |  [16] = {
|  |  |  idx = 207
|  |  }
|  |  [2] = {
|  |  |  cmd = "集火"
|  |  |  idx = 201
|  |  }
|  |  [3] = {
|  |  |  cmd = "禁疗"
|  |  |  idx = 204
|  |  }
|  |  [4] = {
|  |  |  idx = 106
|  |  }
|  |  [5] = {
|  |  |  idx = 107
|  |  }
|  |  [6] = {
|  |  |  cmd = "治疗"
|  |  |  idx = 101
|  |  }
|  |  [7] = {
|  |  |  cmd = "复活"
|  |  |  idx = 102
|  |  }
|  |  [8] = {
|  |  |  cmd = "加速"
|  |  |  idx = 103
|  |  }
|  |  [9] = {
|  |  |  cmd = "解封"
|  |  |  idx = 104
|  |  }
|  }
|  camp = 0
|  chatself = false
|  chubeiexp = 0
|  coin = 667054
|  coin_over = 0
|  color_coin = 0
|  critical_damage = 0
|  critical_ratio = 0
|  cure_critical_ratio = 0
|  defense = 0
|  energy = 183
|  exp = 118806
|  followers = {}
|  goldcoin = 870
|  grade = 36
|  hp = 0
|  kp_sdk_info = {
|  |  create_time = **********
|  |  upgrade_time = **********
|  }
|  max_hp = 0
|  medal = 0
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 160
|  |  weapon = 2300
|  }
|  name = "人类d青蒿娘"
|  open_day = 6
|  org_fuben_cnt = 2
|  org_offer = 0
|  power = **********
|  res_abnormal_ratio = 0
|  res_critical_ratio = 0
|  school = 3
|  school_branch = 1
|  sex = 2
|  show_id = 10003
|  skill_point = 50
|  skin = 0
|  speed = 0
|  systemsetting = {}
|  title_info = {}
|  trapmine_point = 0
|  travel_score = 0
|  upvote_amount = 0
}
[logic/misc/CTimeCtrl.lua:64]:HeartBeat-->开启心跳包检测
[core/table.lua:94]:<--Net Send: title.C2GSTitleInfoList = {}
[core/table.lua:94]:-->Net Receive: player.GS2CGamePushSetting = {}
[core/table.lua:94]:-->Net Receive: player.GS2CShapeList = {
|  shapes = {
|  |  [1] = 160
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CGuidanceInfo = {
|  guidanceinfo = {
|  |  key = {
|  |  |  [1] = 7000041
|  |  |  [10] = 2002099
|  |  |  [11] = 7000081
|  |  |  [12] = 2006099
|  |  |  [13] = 1003099
|  |  |  [14] = 3006099
|  |  |  [15] = 3007099
|  |  |  [16] = 3008099
|  |  |  [17] = 3003099
|  |  |  [18] = 7000091
|  |  |  [19] = 3004099
|  |  |  [2] = 7000051
|  |  |  [20] = 3005099
|  |  |  [21] = 1004099
|  |  |  [22] = 3009099
|  |  |  [23] = 3010099
|  |  |  [24] = 3011099
|  |  |  [25] = 1006099
|  |  |  [26] = 3016099
|  |  |  [27] = 1010099
|  |  |  [28] = 1005099
|  |  |  [29] = 3012099
|  |  |  [3] = 6010002
|  |  |  [30] = 7000071
|  |  |  [31] = 3014099
|  |  |  [32] = 3015099
|  |  |  [33] = 3013099
|  |  |  [34] = 1007099
|  |  |  [35] = 3019099
|  |  |  [36] = 5000700
|  |  |  [37] = 1012099
|  |  |  [38] = 1008099
|  |  |  [39] = 3020099
|  |  |  [4] = 1027099
|  |  |  [40] = 5003099
|  |  |  [41] = 5000600
|  |  |  [42] = 1009099
|  |  |  [43] = 1011099
|  |  |  [44] = 5011099
|  |  |  [45] = 1013099
|  |  |  [46] = 1014099
|  |  |  [47] = 5000900
|  |  |  [48] = 1015099
|  |  |  [49] = 1016099
|  |  |  [5] = 1028099
|  |  |  [6] = 2001099
|  |  |  [7] = 7000061
|  |  |  [8] = 1001099
|  |  |  [9] = 3002099
|  |  }
|  |  version = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>>>>>>>>>>>>>>>>>>>>> GS2CGuidanceInfo </color>
[core/table.lua:94]:-->Net Receive: travel.GS2CLoginTravelPartner = {
|  travel_partner = {
|  |  server_time = **********
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CServerGradeInfo = {
|  days = 2
|  server_grade = 60
}
[core/table.lua:94]:-->Net Receive: scene.GS2CShowScene = {
|  map_id = 101000
|  scene_id = 12
|  scene_name = "帝都"
}
[core/global.lua:59]:<color=#ffeb04>CResCtrl.MoveToSecondary</color>
[core/table.lua:94]:-->Net Receive: task.GS2CLoginTask = {
|  shimen_status = 1
|  taskdata = {
|  |  [1] = {
|  |  |  acceptgrade = 20
|  |  |  acceptnpc = 10610
|  |  |  clientnpc = {
|  |  |  |  [1] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 302
|  |  |  |  |  }
|  |  |  |  |  name = "重华"
|  |  |  |  |  npcid = 8135
|  |  |  |  |  npctype = 10610
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 45260
|  |  |  |  |  |  y = 13640
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 313
|  |  |  |  |  }
|  |  |  |  |  name = "檀"
|  |  |  |  |  npcid = 8136
|  |  |  |  |  npctype = 10611
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 45670
|  |  |  |  |  |  y = 12860
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  detaildesc = "离开教堂之后竟然再次遇到重华。"
|  |  |  name = "偶遇"
|  |  |  patrolinfo = {}
|  |  |  pickiteminfo = {}
|  |  |  placeinfo = {}
|  |  |  shapeinfo = {}
|  |  |  statusinfo = {
|  |  |  |  note = "CreateTask"
|  |  |  |  status = 2
|  |  |  }
|  |  |  submitnpc = 10610
|  |  |  target = 10610
|  |  |  targetdesc = "选手们"
|  |  |  taskid = 10030
|  |  |  taskitem = {}
|  |  |  tasktype = 1
|  |  |  traceinfo = {}
|  |  |  type = 2
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>login task</color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x5a94bb00"
|  |  AssociatedPick = "function: 0x5a94bb60"
|  |  AssociatedSubmit = "function: 0x5a94bb30"
|  |  CreateDefalutData = "function: 0x5a949b08"
|  |  GetChaptetFubenData = "function: 0x5a94b948"
|  |  GetProgressThing = "function: 0x5a94bbc0"
|  |  GetRemainTime = "function: 0x5a94b7c0"
|  |  GetStatus = "function: 0x5a94bc20"
|  |  GetTaskClientExtStrDic = "function: 0x5a94bb90"
|  |  GetTaskTypeSpriteteName = "function: 0x5a94b850"
|  |  GetTraceInfo = "function: 0x5a94b9a8"
|  |  GetTraceNpcType = "function: 0x5a94b820"
|  |  GetValue = "function: 0x5a94b888"
|  |  IsAbandon = "function: 0x5a94b8e8"
|  |  IsAddEscortDynamicNpc = "function: 0x5a94bf58"
|  |  IsMissMengTask = "function: 0x5a94b7f0"
|  |  IsPassChaterFuben = "function: 0x5a94b978"
|  |  IsTaskSpecityAction = "function: 0x5a94b918"
|  |  IsTaskSpecityCategory = "function: 0x5a94bad0"
|  |  New = "function: 0x5a948fe8"
|  |  NewByData = "function: 0x5a950a60"
|  |  RaiseProgressIdx = "function: 0x5a94bbf0"
|  |  RefreshTask = "function: 0x5a94b8b8"
|  |  ResetEndTime = "function: 0x5a949ad0"
|  |  SetStatus = "function: 0x5a949aa0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x5a949a70"
|  }
|  m_CData = {
|  |  ChapterFb = "1,8"
|  |  autoDoNextTask = 10031
|  |  clientExtStr = ""
|  |  name = "偶遇"
|  |  submitNpcId = 10610
|  |  submitRewardStr = {
|  |  |  [1] = "R1030"
|  |  }
|  |  taskWalkingTips = "要怎么样才能潜入教会呢？;none;none"
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 20
|  |  acceptnpc = 10610
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 302
|  |  |  |  }
|  |  |  |  name = "重华"
|  |  |  |  npcid = 8135
|  |  |  |  npctype = 10610
|  |  |  |  pos_info = {
|  |  |  |  |  x = 45260
|  |  |  |  |  y = 13640
|  |  |  |  }
|  |  |  }
|  |  |  [2] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 313
|  |  |  |  }
|  |  |  |  name = "檀"
|  |  |  |  npcid = 8136
|  |  |  |  npctype = 10611
|  |  |  |  pos_info = {
|  |  |  |  |  x = 45670
|  |  |  |  |  y = 12860
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "离开教堂之后竟然再次遇到重华。"
|  |  isdone = 0
|  |  name = "偶遇"
|  |  patrolinfo = {}
|  |  pickiteminfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10610
|  |  target = 10610
|  |  targetdesc = "选手们"
|  |  taskid = 10030
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CTeachProgress = {}
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshPartnerTask = {
|  partnertask_progress = {
|  |  [1] = {
|  |  |  parid = 313
|  |  |  status = 1
|  |  |  taskid = 62195
|  |  }
|  |  [2] = {
|  |  |  parid = 403
|  |  |  status = 1
|  |  |  taskid = 62169
|  |  }
|  |  [3] = {
|  |  |  parid = 501
|  |  |  status = 1
|  |  |  taskid = 62085
|  |  }
|  |  [4] = {
|  |  |  parid = 502
|  |  |  status = 1
|  |  |  taskid = 62090
|  |  }
|  |  [5] = {
|  |  |  parid = 302
|  |  |  status = 1
|  |  |  taskid = 62080
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  day_task = {
|  |  [1] = 1024
|  |  [10] = 1011
|  |  [11] = 2001
|  |  [12] = 2002
|  |  [13] = 2003
|  |  [2] = 1003
|  |  [3] = 1016
|  |  [4] = 3005
|  |  [5] = 1017
|  |  [6] = 1001
|  |  [7] = 3008
|  |  [8] = 3009
|  |  [9] = 1006
|  }
}
[core/table.lua:94]:-->Net Receive: state.GS2CLoginState = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginHuodongInfo = {
|  convoyinfo = {
|  |  convoy_partner = 1750
|  |  free_time = 1
|  |  pool_info = {
|  |  |  [1] = {
|  |  |  |  level = 1
|  |  |  |  partnerid = 1503
|  |  |  |  pos = 1
|  |  |  |  rewardid = 1001
|  |  |  }
|  |  |  [2] = {
|  |  |  |  level = 2
|  |  |  |  partnerid = 1016
|  |  |  |  pos = 2
|  |  |  |  rewardid = 1002
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [3] = {
|  |  |  |  level = 3
|  |  |  |  partnerid = 1750
|  |  |  |  pos = 3
|  |  |  |  rewardid = 1003
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [4] = {
|  |  |  |  level = 4
|  |  |  |  partnerid = 1011
|  |  |  |  pos = 4
|  |  |  |  rewardid = 1004
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [5] = {
|  |  |  |  level = 5
|  |  |  |  partnerid = 1014
|  |  |  |  pos = 5
|  |  |  |  rewardid = 1005
|  |  |  |  weight = 25
|  |  |  }
|  |  }
|  |  selected_pos = 3
|  |  target_npc = 5030
|  }
|  dailytrain = {
|  |  reward_times = 200
|  }
|  hireinfo = {
|  |  [1] = {
|  |  |  parid = 501
|  |  |  times = 1
|  |  }
|  |  [2] = {
|  |  |  parid = 502
|  |  |  times = 1
|  |  }
|  |  [3] = {
|  |  |  parid = 403
|  |  |  times = 1
|  |  }
|  }
|  huntinfo = {
|  |  npcinfo = {
|  |  |  [1] = {
|  |  |  |  level = 1
|  |  |  |  status = 1
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginChapterInfo = {
|  extrareward_info = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  level = 6
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  chapter = 1
|  |  |  level = 4
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  chapter = 1
|  |  |  level = 8
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  }
|  finalchapter = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  level = 8
|  |  |  open = 1
|  |  |  pass = 1
|  |  |  type = 1
|  |  }
|  }
|  totalstar_info = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  reward_status = 3
|  |  |  star = 24
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: handbook.GS2CLoginBookList = {
|  book_list = {
|  |  [1] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 313131
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 113131
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100313
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [2] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 314031
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 114031
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100403
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [3] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 315011
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  id = 315012
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115011
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100501
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [4] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 313021
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = {
|  |  |  |  |  |  [1] = 113023
|  |  |  |  |  }
|  |  |  |  |  id = 313022
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 113021
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100302
|  |  |  red_point = 3
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [5] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = {
|  |  |  |  |  |  [1] = 115022
|  |  |  |  |  }
|  |  |  |  |  id = 315021
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115021
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100502
|  |  |  red_point = 3
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  }
|  red_points = {
|  |  [1] = {
|  |  |  book_type = 1
|  |  |  red_point = 1
|  |  }
|  |  [2] = {
|  |  |  book_type = 2
|  |  }
|  |  [3] = {
|  |  |  book_type = 3
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: team.GS2CInviteInfo = {}
[core/table.lua:94]:-->Net Receive: mail.GS2CLoginMail = {}
[core/table.lua:94]:-->Net Receive: friend.GS2CLoginFriend = {
|  friend_onlinestatus_list = {
|  |  [1] = {
|  |  |  pid = 10004
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>好友版本一致，无需更新</color>
[core/table.lua:94]:<--Net Send: friend.C2GSSimpleFriendList = {
|  pidlist = {
|  |  [1] = 10004
|  }
}
[core/table.lua:94]:-->Net Receive: friend.GS2CApplyList = {}
[core/table.lua:94]:-->Net Receive: friend.GS2CFriendSetting = {
|  setting = {
|  |  notify = 1
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 28536
|  |  |  type = "zsk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 28536
|  |  |  type = "yk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
|  mask = "c"
}
[core/table.lua:94]:CNetCtrl解析mask: GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 28536
|  |  |  type = "zsk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 28536
|  |  |  type = "yk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: store.GS2CPayForColorCoinInfo = {
|  colorcoin_list = {
|  |  [1] = {
|  |  |  key = "goldcoinstore_1005"
|  |  }
|  |  [10] = {
|  |  |  key = "goldcoinstore_2004"
|  |  }
|  |  [11] = {
|  |  |  key = "goldcoinstore_2005"
|  |  }
|  |  [12] = {
|  |  |  key = "goldcoinstore_2006"
|  |  }
|  |  [13] = {
|  |  |  key = "goldcoinstore_2007"
|  |  }
|  |  [14] = {
|  |  |  key = "goldcoinstore_1003"
|  |  }
|  |  [15] = {
|  |  |  key = "goldcoinstore_1004"
|  |  }
|  |  [2] = {
|  |  |  key = "goldcoinstore_1006"
|  |  }
|  |  [3] = {
|  |  |  key = "goldcoinstore_1007"
|  |  }
|  |  [4] = {
|  |  |  key = "goldcoinstore_1008"
|  |  }
|  |  [5] = {
|  |  |  key = "goldcoinstore_1001"
|  |  }
|  |  [6] = {
|  |  |  key = "goldcoinstore_1002"
|  |  }
|  |  [7] = {
|  |  |  key = "goldcoinstore_2001"
|  |  }
|  |  [8] = {
|  |  |  key = "goldcoinstore_2002"
|  |  }
|  |  [9] = {
|  |  |  key = "goldcoinstore_2003"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: item.GS2CLoginItem = {
|  itemdata = {
|  |  [1] = {
|  |  |  amount = 4
|  |  |  create_time = 1693474041
|  |  |  id = 1
|  |  |  itemlevel = 2
|  |  |  name = "一星云母"
|  |  |  sid = 14031
|  |  }
|  |  [10] = {
|  |  |  amount = 1
|  |  |  create_time = 1693555469
|  |  |  id = 13
|  |  |  itemlevel = 2
|  |  |  name = "1级黄金宝石"
|  |  |  sid = 18300
|  |  }
|  |  [11] = {
|  |  |  amount = 1
|  |  |  create_time = 1693365676
|  |  |  id = 12
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 2
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101003
|  |  }
|  |  [12] = {
|  |  |  amount = 1
|  |  |  create_time = 1693365500
|  |  |  id = 9
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 1
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101006
|  |  }
|  |  [13] = {
|  |  |  amount = 1
|  |  |  create_time = 1693554677
|  |  |  id = 10
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 5
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101001
|  |  }
|  |  [14] = {
|  |  |  amount = 1
|  |  |  create_time = 1693473978
|  |  |  id = 11
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 3
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101007
|  |  }
|  |  [15] = {
|  |  |  amount = 8
|  |  |  create_time = 1693554614
|  |  |  id = 15
|  |  |  itemlevel = 3
|  |  |  name = "重华碎片"
|  |  |  sid = 20302
|  |  }
|  |  [16] = {
|  |  |  amount = 1
|  |  |  create_time = 1693555466
|  |  |  id = 16
|  |  |  itemlevel = 3
|  |  |  name = "人灵决·低"
|  |  |  sid = 27405
|  |  }
|  |  [17] = {
|  |  |  amount = 1
|  |  |  create_time = 1693473966
|  |  |  id = 17
|  |  |  itemlevel = 3
|  |  |  name = "梦觉书·低"
|  |  |  sid = 27402
|  |  }
|  |  [18] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  }
|  |  |  create_time = **********
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 3
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 1
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  pos = 1
|  |  |  |  stone_sid = 3300000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 18
|  |  |  itemlevel = 1
|  |  |  name = "练习魍臂铠"
|  |  |  power = 57
|  |  |  sid = 2120000
|  |  }
|  |  [19] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 121
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 24
|  |  |  |  }
|  |  |  }
|  |  |  create_time = **********
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 1
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 17
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  pos = 2
|  |  |  |  stone_sid = 4000000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 22
|  |  |  itemlevel = 1
|  |  |  name = "练习飘逸巾"
|  |  |  power = 24
|  |  |  sid = 2200000
|  |  }
|  |  [2] = {
|  |  |  amount = 2
|  |  |  create_time = 1693382338
|  |  |  id = 2
|  |  |  itemlevel = 2
|  |  |  name = "鲜肉包"
|  |  |  sid = 14001
|  |  }
|  |  [20] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  }
|  |  |  create_time = **********
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 1
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 17
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  pos = 3
|  |  |  |  stone_sid = 4110000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 20
|  |  |  itemlevel = 1
|  |  |  name = "练习絮语衣"
|  |  |  power = 32
|  |  |  sid = 2320000
|  |  }
|  |  [21] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  }
|  |  |  create_time = **********
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "speed"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 7
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 3
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  pos = 4
|  |  |  |  stone_sid = 4200000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 21
|  |  |  itemlevel = 1
|  |  |  name = "练习草织戒"
|  |  |  power = 19
|  |  |  sid = 2400000
|  |  }
|  |  [22] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 364
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 72
|  |  |  |  }
|  |  |  }
|  |  |  create_time = **********
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 3
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 17
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  pos = 5
|  |  |  |  stone_sid = 4310000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 19
|  |  |  itemlevel = 1
|  |  |  name = "练习离叶腰"
|  |  |  power = 72
|  |  |  sid = 2520000
|  |  }
|  |  [23] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "speed"
|  |  |  |  |  value = 33
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 11
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 77
|  |  |  |  }
|  |  |  }
|  |  |  create_time = **********
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "speed"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 7
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 3
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  pos = 6
|  |  |  |  stone_sid = 4410000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 23
|  |  |  itemlevel = 1
|  |  |  name = "练习青藤鞋"
|  |  |  power = 77
|  |  |  sid = 2620000
|  |  }
|  |  [3] = {
|  |  |  amount = 58
|  |  |  create_time = 1693554614
|  |  |  id = 3
|  |  |  itemlevel = 3
|  |  |  name = "焦糖包"
|  |  |  sid = 14011
|  |  }
|  |  [4] = {
|  |  |  amount = 2
|  |  |  create_time = 1693382549
|  |  |  id = 4
|  |  |  itemlevel = 4
|  |  |  name = "万能碎片"
|  |  |  sid = 14002
|  |  }
|  |  [5] = {
|  |  |  amount = 10
|  |  |  create_time = 1693555469
|  |  |  id = 5
|  |  |  itemlevel = 5
|  |  |  name = "扫荡券"
|  |  |  sid = 10030
|  |  }
|  |  [6] = {
|  |  |  amount = 1
|  |  |  create_time = 1693474198
|  |  |  id = 6
|  |  |  itemlevel = 4
|  |  |  name = "灵魂钥匙"
|  |  |  sid = 10040
|  |  }
|  |  [7] = {
|  |  |  amount = 1
|  |  |  create_time = 1693554583
|  |  |  id = 7
|  |  |  itemlevel = 4
|  |  |  name = "50级橙武礼包"
|  |  |  sid = 12083
|  |  }
|  |  [8] = {
|  |  |  amount = 2
|  |  |  create_time = 1693555113
|  |  |  id = 8
|  |  |  itemlevel = 4
|  |  |  name = "星象图"
|  |  |  sid = 10024
|  |  |  treasure_info = {
|  |  |  |  treasure_mapid = 205000
|  |  |  |  treasure_posx = 25
|  |  |  |  treasure_posy = 5
|  |  |  }
|  |  }
|  |  [9] = {
|  |  |  amount = 1
|  |  |  create_time = 1693474296
|  |  |  id = 14
|  |  |  itemlevel = 2
|  |  |  name = "3级绯红宝石"
|  |  |  sid = 18002
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>loginitem</color>
[logic/ui/CSprite.lua:159]:DynamicSprite dSprInfo    Item    10012
[logic/ui/CViewBase.lua:125]:CMainMenuView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeRewrad = {
|  end_time = 1693839539
|  reward_info = {
|  |  [1] = {
|  |  |  left_amount = 2
|  |  |  rmb = 328
|  |  }
|  |  [2] = {
|  |  |  left_amount = 2
|  |  |  rmb = 648
|  |  }
|  }
|  schedule = 1
|  start_time = 1693753200
}
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenPlanName = {
|  fuwen_name = {
|  |  [1] = {
|  |  |  name = "默认方案"
|  |  |  plan = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CRemoveTitles = {
|  tidlist = {
|  |  [1] = 1001
|  |  [2] = 1002
|  |  [3] = 1003
|  |  [4] = 1004
|  |  [5] = 1005
|  |  [6] = 1006
|  |  [7] = 1007
|  |  [8] = 1008
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "8段"
|  |  tid = 1008
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "2段"
|  |  tid = 1002
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "3段"
|  |  tid = 1003
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "4段"
|  |  tid = 1004
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "5段"
|  |  tid = 1005
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "6段"
|  |  tid = 1006
|  }
}
[core/table.lua:94]:-->Net Receive: title.GS2CUpdateTitleInfo = {
|  info = {
|  |  name = "7段"
|  |  tid = 1007
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTimeResumeInfo = {
|  end_time = 1693839539
|  plan_id = 1
|  start_time = 1693753200
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CRefreshTimeResume = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeScore = {
|  cur_id = 1
|  end_time = 1694617139
|  score_info = {}
|  start_time = 1693321200
|  status = 1
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterScene = {
|  eid = 32
|  pos_info = {
|  |  face_y = 232356
|  |  x = 22117
|  |  y = 21135
|  }
|  scene_id = 12
}
[core/global.lua:59]:<color=#ffeb04>result  nil</color>
[core/global.lua:59]:<color=#ffeb04>=00000=  userdata: 0x5b3548d8 nil</color>
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814665
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:04:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>读取地图： 1020</color>
[core/global.lua:59]:<color=#ffeb04>Unity2017.4.30f1 localScale</color>
[logic/ui/CViewCtrl.lua:104]:CLoginView     CloseView
[logic/base/CResCtrl.lua:599]:res gc step start!
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[core/global.lua:59]:<color=#ffeb04>地图加载完成: 1020 ,当前地图: nil</color>
[core/table.lua:94]:Table = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CGradeGiftInfo = {
|  buy_gift = {
|  |  items = {
|  |  |  [1] = {
|  |  |  |  amount = 5
|  |  |  |  sid = 10040
|  |  |  |  virtual = 10040
|  |  |  }
|  |  |  [2] = {
|  |  |  |  amount = 150
|  |  |  |  sid = 11001
|  |  |  |  virtual = 11001
|  |  |  }
|  |  |  [3] = {
|  |  |  |  amount = 66666
|  |  |  |  sid = 1002
|  |  |  |  virtual = 1002
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  discount = 12
|  endtime = 1693857864
|  free_gift = {
|  |  items = {
|  |  |  [1] = {
|  |  |  |  amount = 10
|  |  |  |  sid = 11001
|  |  |  |  virtual = 11001
|  |  |  }
|  |  |  [2] = {
|  |  |  |  amount = 6666
|  |  |  |  sid = 1002
|  |  |  |  virtual = 1002
|  |  |  }
|  |  }
|  }
|  grade = 40
|  ios_payid = "com.kaopu.ylq.appstore.lb.12"
|  now_price = 12
|  old_price = 100
|  payid = "com.kaopu.ylq.lb.12"
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliReddot = {
|  cday = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHDDayChargeInfo = {
|  code = 10
|  endtime = 1694617139
|  list = {
|  |  [1] = {
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  id = 2
|  |  }
|  |  [3] = {
|  |  |  id = 3
|  |  }
|  |  [4] = {
|  |  |  id = 4
|  |  }
|  |  [5] = {
|  |  |  id = 5
|  |  }
|  }
|  starttime = 1693321200
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CNotifyQuestion = {
|  desc = "[44efe9]突击测验[-]正在进行中"
|  end_time = 1693810860
|  server_time = **********
|  status = 2
|  type = 1
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:04:24
[core/table.lua:94]:-->Net Receive: huodong.GS2CQuestionInfo = {
|  answer_list = {
|  |  [1] = "满江红"
|  |  [2] = "西江月"
|  |  [3] = "花间集"
|  |  [4] = "浪淘沙"
|  }
|  base_reward = 2000
|  desc = "[现世知识]名句“流水落花春去也，天上人间”的词牌名是？"
|  end_time = 1693814804
|  id = 1
|  server_time = **********
|  type = 1
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:04:24
[core/table.lua:94]:-->Net Receive: huodong.GS2CRushRankInfo = {
|  rush = {
|  |  [1] = {
|  |  |  endtime = 1693929600
|  |  |  idx = 117
|  |  |  show_endtime = 1694016000
|  |  }
|  |  [2] = {
|  |  |  endtime = 1693929600
|  |  |  idx = 115
|  |  |  show_endtime = 1694016000
|  |  }
|  |  [3] = {
|  |  |  endtime = 1693929600
|  |  |  idx = 118
|  |  |  show_endtime = 1694016000
|  |  }
|  |  [4] = {
|  |  |  endtime = 1693929600
|  |  |  idx = 105
|  |  |  show_endtime = 1694016000
|  |  }
|  |  [5] = {
|  |  |  endtime = 1693929600
|  |  |  idx = 106
|  |  |  show_endtime = 1694016000
|  |  }
|  |  [6] = {
|  |  |  endtime = 1693929600
|  |  |  idx = 113
|  |  |  show_endtime = 1694016000
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraWarState = {
|  time = 1694016000
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraQueueStatus = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CDailySignInfo = {
|  sign_info = {
|  |  [1] = {
|  |  |  key = "week"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CRefreshRewardBack = {
|  info = {
|  |  [1] = {
|  |  |  free = 1
|  |  |  left = 2
|  |  |  sid = 2006
|  |  |  vip = 100
|  |  }
|  |  [2] = {
|  |  |  free = 1
|  |  |  left = 1
|  |  |  sid = 2001
|  |  |  vip = 30
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:94]:CWelfareView ShowView
[core/table.lua:94]:-->Net Receive: huodong.GS2CResumeRestore = {
|  end_time = 1694617139
|  plan_id = 1
|  start_time = 1693321200
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CRefreshResumeRestore = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHeroBoxRecord = {}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartnerList = {
|  partner_list = {
|  |  [1] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 736
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 178
|  |  |  equip_list = {
|  |  |  |  [1] = 9
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 101870
|  |  |  grade = 35
|  |  |  hp = 6645
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 6645
|  |  |  model_info = {
|  |  |  |  shape = 302
|  |  |  |  skin = 203020
|  |  |  }
|  |  |  name = "重华"
|  |  |  parid = 1
|  |  |  partner_type = 302
|  |  |  patahp = 6645
|  |  |  power = 2169
|  |  |  power_rank = 1
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 600
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30201
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30202
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30203
|  |  |  |  }
|  |  |  }
|  |  |  speed = 420
|  |  |  star = 1
|  |  }
|  |  [2] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 718
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 168
|  |  |  equip_list = {
|  |  |  |  [1] = 12
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 97050
|  |  |  grade = 34
|  |  |  hp = 6297
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 6297
|  |  |  model_info = {
|  |  |  |  shape = 502
|  |  |  |  skin = 205020
|  |  |  }
|  |  |  name = "马面面"
|  |  |  parid = 2
|  |  |  partner_type = 502
|  |  |  patahp = 6297
|  |  |  power = 2077
|  |  |  power_rank = 1
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 500
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50201
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50202
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50203
|  |  |  |  }
|  |  |  }
|  |  |  speed = 315
|  |  |  star = 1
|  |  }
|  |  [3] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 752
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 600
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 169
|  |  |  equip_list = {
|  |  |  |  [1] = 11
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 94470
|  |  |  grade = 34
|  |  |  hp = 6983
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 6983
|  |  |  model_info = {
|  |  |  |  shape = 403
|  |  |  |  skin = 204030
|  |  |  }
|  |  |  name = "蛇姬"
|  |  |  parid = 3
|  |  |  partner_type = 403
|  |  |  patahp = 6983
|  |  |  power = 2241
|  |  |  power_rank = 1
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 600
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40301
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40302
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40303
|  |  |  |  }
|  |  |  }
|  |  |  speed = 735
|  |  |  star = 1
|  |  }
|  |  [4] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 419
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 1000
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 81
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 14370
|  |  |  grade = 12
|  |  |  hp = 3223
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 3223
|  |  |  model_info = {
|  |  |  |  shape = 501
|  |  |  |  skin = 205010
|  |  |  }
|  |  |  name = "阿坊"
|  |  |  parid = 4
|  |  |  partner_type = 501
|  |  |  patahp = 3223
|  |  |  power = 1101
|  |  |  power_rank = 1
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 400
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50101
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50102
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50103
|  |  |  |  }
|  |  |  }
|  |  |  speed = 70
|  |  |  star = 1
|  |  }
|  |  [5] = {
|  |  |  abnormal_attr_ratio = 600
|  |  |  amount = 1
|  |  |  attack = 681
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 191
|  |  |  equip_list = {
|  |  |  |  [1] = 10
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 81380
|  |  |  grade = 32
|  |  |  hp = 6583
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 6583
|  |  |  model_info = {
|  |  |  |  shape = 313
|  |  |  |  skin = 203130
|  |  |  }
|  |  |  name = "檀"
|  |  |  parid = 5
|  |  |  partner_type = 313
|  |  |  patahp = 6583
|  |  |  power = 2355
|  |  |  power_rank = 1
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 700
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31301
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31302
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31303
|  |  |  |  }
|  |  |  }
|  |  |  speed = 665
|  |  |  star = 2
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartner = {
|  fight_info = {
|  |  [1] = {
|  |  |  parid = 1
|  |  |  pos = 1
|  |  }
|  |  [2] = {
|  |  |  parid = 2
|  |  |  pos = 2
|  |  }
|  |  [3] = {
|  |  |  parid = 3
|  |  |  pos = 3
|  |  }
|  |  [4] = {
|  |  |  parid = 5
|  |  |  pos = 4
|  |  }
|  }
|  owned_equip_list = {
|  |  [1] = 6101001
|  }
|  owned_partner_list = {
|  |  [1] = 313
|  |  [2] = 403
|  |  [3] = 501
|  |  [4] = 302
|  |  [5] = 502
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPicturePosList = {}
[core/table.lua:94]:-->Net Receive: skill.GS2CLoginSkill = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3401
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3402
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  needcost = 15
|  |  |  sk = 3403
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  needcost = 25
|  |  |  sk = 3405
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3406
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3407
|  |  |  type = 1
|  |  }
|  }
}
[logic/skill/CSkillCtrl.lua:112]:技能数据登陆协议返回:
[core/table.lua:94]:Table = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3401
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3402
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  needcost = 15
|  |  |  sk = 3403
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  needcost = 25
|  |  |  sk = 3405
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3406
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3407
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginParSoulPlan = {}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcFightInfoList = {
|  info_list = {
|  |  [1] = {
|  |  |  npc_type = 10024
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [10] = {
|  |  |  npc_type = 10007
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [11] = {
|  |  |  npc_type = 10008
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [12] = {
|  |  |  npc_type = 10009
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [13] = {
|  |  |  npc_type = 10011
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [14] = {
|  |  |  npc_type = 10012
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [15] = {
|  |  |  npc_type = 10013
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [16] = {
|  |  |  npc_type = 10000
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [17] = {
|  |  |  npc_type = 10023
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [18] = {
|  |  |  npc_type = 10014
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [19] = {
|  |  |  npc_type = 10015
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [2] = {
|  |  |  npc_type = 10002
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [20] = {
|  |  |  npc_type = 10016
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [21] = {
|  |  |  npc_type = 10017
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [22] = {
|  |  |  npc_type = 10018
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [23] = {
|  |  |  npc_type = 10019
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [24] = {
|  |  |  npc_type = 10020
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [25] = {
|  |  |  npc_type = 10021
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [26] = {
|  |  |  npc_type = 10022
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [3] = {
|  |  |  npc_type = 10010
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [4] = {
|  |  |  npc_type = 10001
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [5] = {
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [6] = {
|  |  |  npc_type = 10003
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [7] = {
|  |  |  npc_type = 10004
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [8] = {
|  |  |  npc_type = 10005
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [9] = {
|  |  |  npc_type = 10006
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CDrawCardUI = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2COneRMBGift = {
|  endtime = 1693839539
|  gift = {
|  |  [1] = {
|  |  |  key = 1
|  |  }
|  |  [2] = {
|  |  |  key = 2
|  |  }
|  |  [3] = {
|  |  |  key = 3
|  |  }
|  }
|  starttime = 1693753200
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginTrapmine = {
|  rare_monster = {
|  |  [1] = {
|  |  |  map_id = 202000
|  |  |  partypes = {
|  |  |  |  [1] = 312
|  |  |  |  [2] = 401
|  |  |  |  [3] = 313
|  |  |  }
|  |  }
|  |  [2] = {
|  |  |  map_id = 204000
|  |  |  partypes = {
|  |  |  |  [1] = 404
|  |  |  |  [2] = 402
|  |  |  |  [3] = 403
|  |  |  }
|  |  }
|  |  [3] = {
|  |  |  map_id = 205000
|  |  |  partypes = {
|  |  |  |  [1] = 508
|  |  |  |  [2] = 513
|  |  |  |  [3] = 509
|  |  |  }
|  |  }
|  |  [4] = {
|  |  |  map_id = 206000
|  |  |  partypes = {
|  |  |  |  [1] = 413
|  |  |  |  [2] = 410
|  |  |  |  [3] = 414
|  |  |  }
|  |  }
|  |  [5] = {
|  |  |  map_id = 210400
|  |  |  partypes = {
|  |  |  |  [1] = 504
|  |  |  |  [2] = 505
|  |  |  |  [3] = 507
|  |  |  }
|  |  }
|  |  [6] = {
|  |  |  map_id = 200000
|  |  |  partypes = {
|  |  |  |  [1] = 416
|  |  |  |  [2] = 417
|  |  |  |  [3] = 501
|  |  |  }
|  |  }
|  |  [7] = {
|  |  |  map_id = 201000
|  |  |  partypes = {
|  |  |  |  [1] = 303
|  |  |  |  [2] = 302
|  |  |  |  [3] = 311
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CNewFieldBoss = {
|  bossid = {
|  |  [1] = 1
|  |  [2] = 2
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2COnlineGift = {
|  onlinetime = 564
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHDAddChargeInfo = {
|  endtime = 1693839539
|  list = {
|  |  [1] = {
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  id = 2
|  |  }
|  |  [3] = {
|  |  |  id = 3
|  |  }
|  |  [4] = {
|  |  |  id = 4
|  |  }
|  |  [5] = {
|  |  |  id = 5
|  |  }
|  }
|  starttime = 1693753200
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CRankBack = {
|  endtime = 1694617139
|  starttime = 1693321200
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayBuy = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CHistoryCharge = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFirstChargeUI = {
|  bOpen = true
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliPoint = {
|  point = 52
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawCnt = {
|  cnt = 4
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliTime = {
|  endtime = 1694617139
|  starttime = 1693321200
}
[core/table.lua:94]:-->Net Receive: player.GS2CTodayInfo = {
|  info = {
|  |  mask = "0"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: todyInfo = {}
[core/table.lua:94]:-->Net Receive: player.GS2CGameShare = {
|  game_share = {
|  |  [1] = {
|  |  |  type = "picture_share"
|  |  }
|  |  [2] = {
|  |  |  type = "skin_share"
|  |  }
|  |  [3] = {
|  |  |  type = "draw_card_share"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  mask = "1997c2e0000000"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  give_org_equip = {}
|  give_org_wish = {}
|  is_equip_wish = 0
|  is_org_wish = 0
|  org_build_status = 0
|  org_build_time = 0
|  org_id = 0
|  org_leader = ""
|  org_level = 0
|  org_pos = 0
|  org_red_packet = 0
|  org_sign_reward = 0
|  org_status = 0
|  orgname = ""
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginRewardInfo = {
|  breed_val = 58
|  login_day = 4
|  rewarded_day = 15
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  abnormal_attr_ratio = 660
|  |  attack = 425
|  |  critical_damage = 15000
|  |  critical_ratio = 1350
|  |  cure_critical_ratio = 500
|  |  defense = 46
|  |  mask = "87ff00"
|  |  max_hp = 3768
|  |  power = 1643
|  |  res_abnormal_ratio = 660
|  |  res_critical_ratio = 500
|  |  speed = 767
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 660
|  attack = 425
|  critical_damage = 15000
|  critical_ratio = 1350
|  cure_critical_ratio = 500
|  defense = 46
|  hp = 0
|  max_hp = 3768
|  power = 1643
|  res_abnormal_ratio = 660
|  res_critical_ratio = 500
|  speed = 767
}
[core/table.lua:94]:-->Net Receive: house.GS2CEnterHouse = {
|  buff_info = {}
|  furniture_info = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  lock_status = 1
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  type = 2
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  type = 3
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  type = 4
|  |  }
|  |  [5] = {
|  |  |  level = 1
|  |  |  type = 5
|  |  }
|  }
|  handle_type = 1
|  max_train = 1
|  max_warm_degree = 1000
|  owner_pid = 10003
|  partner_info = {
|  |  [1] = {
|  |  |  type = 1001
|  |  }
|  |  [2] = {
|  |  |  coin = 2640
|  |  |  type = 1003
|  |  }
|  }
|  warm_degree = 30
}
[core/table.lua:94]:-->Net Receive: house.GS2CPartnerExchangeUI = {
|  handle_type = 1
|  love_cnt = 10
|  max_gift_cnt = 10
|  max_love_cnt = 10
|  partner_gift_cnt = 10
}
[core/table.lua:94]:-->Net Receive: house.GS2COpenWorkDesk = {
|  desk_info = {
|  |  [1] = {
|  |  |  lock_status = 1
|  |  |  pos = 1
|  |  |  status = 1
|  |  }
|  |  [2] = {
|  |  |  pos = 2
|  |  |  status = 1
|  |  }
|  |  [3] = {
|  |  |  pos = 3
|  |  |  status = 1
|  |  }
|  |  [4] = {
|  |  |  lock_status = 1
|  |  |  pos = 4
|  |  |  status = 1
|  |  }
|  }
|  handle_type = 1
|  owner_pid = 10003
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {
|  infolist = {
|  |  [1] = {
|  |  |  blist = {
|  |  |  |  [1] = 1
|  |  |  |  [2] = 2
|  |  |  |  [3] = 3
|  |  |  }
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  blist = {
|  |  |  |  [1] = 8
|  |  |  }
|  |  |  id = 4
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayRedDot = {
|  days = {
|  |  [1] = 1
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayMain = {
|  server_day = 5
}
[core/table.lua:94]:-->Net Receive: task.GS2CLoginAchieveTask = {
|  info = {
|  |  [1] = {
|  |  |  achievetype = 2
|  |  |  describe = "领取1次成就奖励"
|  |  |  name = "成就奖励"
|  |  |  target = 1
|  |  |  taskid = 31510
|  |  }
|  |  [2] = {
|  |  |  achievetype = 1
|  |  |  describe = "主角任意技能升至2级"
|  |  |  name = "提升技能"
|  |  |  target = 1
|  |  |  taskid = 31003
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>LoginAchieveTask</color>
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1102
|  |  |  }
|  |  |  name = "峰馆长"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 2
|  |  npctype = 5040
|  }
|  eid = 1
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 1
|  pos_info = {
|  |  x = 31800
|  |  y = 17600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1102
|  }
|  name = "峰馆长"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1508
|  |  |  }
|  |  |  name = "小叶"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 5
|  |  npctype = 5041
|  }
|  eid = 2
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 2
|  pos_info = {
|  |  x = 40000
|  |  y = 14600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1508
|  }
|  name = "小叶"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1009
|  |  |  }
|  |  |  name = "神父"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 8
|  |  npctype = 5042
|  }
|  eid = 3
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 3
|  pos_info = {
|  |  x = 12200
|  |  y = 25000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1009
|  }
|  name = "神父"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1012
|  |  |  }
|  |  |  name = "克瑞斯汀"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 24
|  |  npctype = 5048
|  }
|  eid = 7
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 7
|  pos_info = {
|  |  x = 20200
|  |  y = 21100
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1012
|  }
|  name = "克瑞斯汀"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1510
|  |  |  }
|  |  |  name = "小佳"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 57
|  |  npctype = 5064
|  }
|  eid = 8
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 8
|  pos_info = {
|  |  x = 13800
|  |  y = 20000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1510
|  }
|  name = "小佳"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
|  [3] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 306
|  |  |  }
|  |  |  name = "袁雀"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 60
|  |  npctype = 5001
|  }
|  eid = 9
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 9
|  pos_info = {
|  |  x = 6600
|  |  y = 26200
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 306
|  }
|  name = "袁雀"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1501
|  |  |  }
|  |  |  name = "扳尾"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 63
|  |  npctype = 5002
|  }
|  eid = 10
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 10
|  pos_info = {
|  |  x = 6000
|  |  y = 21200
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1501
|  }
|  name = "扳尾"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1014
|  |  |  }
|  |  |  name = "乔焱"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 66
|  |  npctype = 5003
|  }
|  eid = 11
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 11
|  pos_info = {
|  |  x = 13200
|  |  y = 24600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1014
|  }
|  name = "乔焱"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1010
|  |  |  }
|  |  |  name = "丽丝"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 69
|  |  npctype = 5004
|  }
|  eid = 12
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 12
|  pos_info = {
|  |  x = 45600
|  |  y = 26900
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1010
|  }
|  name = "丽丝"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1011
|  |  |  }
|  |  |  name = "遥"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 72
|  |  npctype = 5005
|  }
|  eid = 13
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 13
|  pos_info = {
|  |  x = 45500
|  |  y = 20000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1011
|  }
|  name = "遥"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 414
|  |  |  }
|  |  |  name = "蓝堂昼"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 75
|  |  npctype = 5006
|  }
|  eid = 14
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 14
|  pos_info = {
|  |  x = 28000
|  |  y = 14500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 414
|  }
|  name = "蓝堂昼"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 311
|  |  |  }
|  |  |  name = "花常酒"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 78
|  |  npctype = 5007
|  }
|  eid = 15
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 15
|  pos_info = {
|  |  x = 35600
|  |  y = 13400
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 311
|  }
|  name = "花常酒"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 508
|  |  |  }
|  |  |  name = "魂夕"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 81
|  |  npctype = 5008
|  }
|  eid = 16
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 16
|  pos_info = {
|  |  x = 24000
|  |  y = 12900
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 508
|  }
|  name = "魂夕"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1502
|  |  |  }
|  |  |  name = "荆鸣"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 205
|  |  npctype = 5012
|  }
|  eid = 20
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 20
|  pos_info = {
|  |  x = 7000
|  |  y = 11300
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1502
|  }
|  name = "荆鸣"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1016
|  |  |  }
|  |  |  name = "水生"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 196
|  |  npctype = 5009
|  }
|  eid = 17
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 17
|  pos_info = {
|  |  x = 24000
|  |  y = 22000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1016
|  }
|  name = "水生"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 417
|  |  |  }
|  |  |  name = "松姑子"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 220
|  |  npctype = 5019
|  }
|  eid = 21
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 21
|  pos_info = {
|  |  x = 42300
|  |  y = 24600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 417
|  }
|  name = "松姑子"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: title.GS2CTitleInfoList = {
|  infos = {
|  |  [1] = {
|  |  |  name = "8段"
|  |  |  tid = 1008
|  |  }
|  |  [10] = {
|  |  |  name = "应用宝贵族"
|  |  |  tid = 1017
|  |  }
|  |  [11] = {
|  |  |  name = "王者公会"
|  |  |  tid = 1018
|  |  }
|  |  [12] = {
|  |  |  name = "王者公会会长"
|  |  |  tid = 1019
|  |  }
|  |  [13] = {
|  |  |  name = "黄金公会"
|  |  |  tid = 1020
|  |  }
|  |  [14] = {
|  |  |  name = "黄金公会会长"
|  |  |  tid = 1021
|  |  }
|  |  [15] = {
|  |  |  name = "白银公会"
|  |  |  tid = 1022
|  |  }
|  |  [16] = {
|  |  |  name = "白银公会会长"
|  |  |  tid = 1023
|  |  }
|  |  [17] = {
|  |  |  name = "明日之星"
|  |  |  tid = 1024
|  |  }
|  |  [18] = {
|  |  |  name = "独孤求败"
|  |  |  tid = 1025
|  |  }
|  |  [19] = {
|  |  |  name = "谁与争锋"
|  |  |  tid = 1026
|  |  }
|  |  [2] = {
|  |  |  name = "测-贵族宝"
|  |  |  tid = 1009
|  |  }
|  |  [20] = {
|  |  |  name = "所向披靡"
|  |  |  tid = 1027
|  |  }
|  |  [21] = {
|  |  |  name = "地牢终结者"
|  |  |  tid = 1028
|  |  }
|  |  [22] = {
|  |  |  name = "地牢征服者"
|  |  |  tid = 1029
|  |  }
|  |  [23] = {
|  |  |  name = "地牢破坏者"
|  |  |  tid = 1030
|  |  }
|  |  [24] = {
|  |  |  name = "眯眯眼是变态"
|  |  |  tid = 1031
|  |  }
|  |  [25] = {
|  |  |  name = "邻家妹妹"
|  |  |  tid = 1032
|  |  }
|  |  [26] = {
|  |  |  name = "兄控"
|  |  |  tid = 1033
|  |  }
|  |  [27] = {
|  |  |  name = "农夫三拳"
|  |  |  tid = 1034
|  |  }
|  |  [28] = {
|  |  |  name = "赌神"
|  |  |  tid = 1035
|  |  }
|  |  [29] = {
|  |  |  name = "神之右手"
|  |  |  tid = 1036
|  |  }
|  |  [3] = {
|  |  |  name = "测-我就是托"
|  |  |  tid = 1010
|  |  }
|  |  [30] = {
|  |  |  name = "茶会噩梦"
|  |  |  tid = 1037
|  |  }
|  |  [31] = {
|  |  |  name = "我是傲娇"
|  |  |  tid = 1038
|  |  }
|  |  [32] = {
|  |  |  name = "闷骚美男子"
|  |  |  tid = 1039
|  |  }
|  |  [33] = {
|  |  |  name = "脸炮"
|  |  |  tid = 1040
|  |  }
|  |  [34] = {
|  |  |  name = "妹控"
|  |  |  tid = 1041
|  |  }
|  |  [35] = {
|  |  |  name = "守夜人"
|  |  |  tid = 1042
|  |  }
|  |  [36] = {
|  |  |  name = "泡泡娘"
|  |  |  tid = 1043
|  |  }
|  |  [37] = {
|  |  |  name = "姐控"
|  |  |  tid = 1044
|  |  }
|  |  [38] = {
|  |  |  name = "迷妹"
|  |  |  tid = 1045
|  |  }
|  |  [39] = {
|  |  |  name = "小天使"
|  |  |  tid = 1046
|  |  }
|  |  [4] = {
|  |  |  name = "测-粗壮大腿"
|  |  |  tid = 1011
|  |  }
|  |  [40] = {
|  |  |  name = "欺诈兔子"
|  |  |  tid = 1047
|  |  }
|  |  [41] = {
|  |  |  name = "天然呆"
|  |  |  tid = 1048
|  |  }
|  |  [42] = {
|  |  |  name = "开局一条狗"
|  |  |  tid = 1049
|  |  }
|  |  [43] = {
|  |  |  name = "种竹专业户"
|  |  |  tid = 1050
|  |  }
|  |  [44] = {
|  |  |  name = "顾家好男人"
|  |  |  tid = 1051
|  |  }
|  |  [45] = {
|  |  |  name = "无敌小钢炮"
|  |  |  tid = 1052
|  |  }
|  |  [46] = {
|  |  |  name = "小豆丁"
|  |  |  tid = 1053
|  |  }
|  |  [47] = {
|  |  |  name = "浪里白条"
|  |  |  tid = 1054
|  |  }
|  |  [48] = {
|  |  |  name = "话唠"
|  |  |  tid = 1055
|  |  }
|  |  [49] = {
|  |  |  name = "角落种蘑菇"
|  |  |  tid = 1056
|  |  }
|  |  [5] = {
|  |  |  name = "攻防战第一公会"
|  |  |  tid = 1012
|  |  }
|  |  [50] = {
|  |  |  name = "天然疯"
|  |  |  tid = 1057
|  |  }
|  |  [51] = {
|  |  |  name = "吃货"
|  |  |  tid = 1058
|  |  }
|  |  [52] = {
|  |  |  name = "控制狂"
|  |  |  tid = 1059
|  |  }
|  |  [53] = {
|  |  |  name = "女王大人"
|  |  |  tid = 1060
|  |  }
|  |  [54] = {
|  |  |  name = "八卦小王子"
|  |  |  tid = 1061
|  |  }
|  |  [55] = {
|  |  |  name = "三无"
|  |  |  tid = 1062
|  |  }
|  |  [56] = {
|  |  |  name = "养得累"
|  |  |  tid = 1063
|  |  }
|  |  [57] = {
|  |  |  name = "扑街之王"
|  |  |  tid = 1064
|  |  }
|  |  [58] = {
|  |  |  name = "抖S"
|  |  |  tid = 1065
|  |  }
|  |  [59] = {
|  |  |  name = "工作狂人"
|  |  |  tid = 1066
|  |  }
|  |  [6] = {
|  |  |  name = "攻防战第一司令"
|  |  |  tid = 1013
|  |  }
|  |  [60] = {
|  |  |  name = "精分中毒患者"
|  |  |  tid = 1067
|  |  }
|  |  [61] = {
|  |  |  name = "神神叨叨"
|  |  |  tid = 1068
|  |  }
|  |  [62] = {
|  |  |  name = "浮夸之神"
|  |  |  tid = 1069
|  |  }
|  |  [63] = {
|  |  |  name = "抖M"
|  |  |  tid = 1070
|  |  }
|  |  [64] = {
|  |  |  name = "自恋狂"
|  |  |  tid = 1071
|  |  }
|  |  [65] = {
|  |  |  name = "天然黑"
|  |  |  tid = 1072
|  |  }
|  |  [66] = {
|  |  |  name = "为所欲为"
|  |  |  tid = 1073
|  |  }
|  |  [67] = {
|  |  |  name = "氪金大佬"
|  |  |  tid = 1074
|  |  }
|  |  [68] = {
|  |  |  name = "应用宝帝王"
|  |  |  tid = 1075
|  |  }
|  |  [69] = {
|  |  |  name = "应用宝至尊"
|  |  |  tid = 1076
|  |  }
|  |  [7] = {
|  |  |  name = "梦魇之主"
|  |  |  tid = 1014
|  |  }
|  |  [70] = {
|  |  |  name = "公会新人"
|  |  |  tid = 1077
|  |  }
|  |  [71] = {
|  |  |  name = "公会成员"
|  |  |  tid = 1078
|  |  }
|  |  [72] = {
|  |  |  name = "公会精英"
|  |  |  tid = 1079
|  |  }
|  |  [73] = {
|  |  |  name = "公会副会"
|  |  |  tid = 1080
|  |  }
|  |  [74] = {
|  |  |  name = "公会会长"
|  |  |  tid = 1081
|  |  }
|  |  [75] = {
|  |  |  name = "情侣专属称谓"
|  |  |  tid = 1082
|  |  }
|  |  [76] = {
|  |  |  name = "命运之子"
|  |  |  tid = 1083
|  |  }
|  |  [77] = {
|  |  |  name = "不做大多数"
|  |  |  tid = 1084
|  |  }
|  |  [78] = {
|  |  |  name = "1段"
|  |  |  tid = 1001
|  |  }
|  |  [79] = {
|  |  |  name = "2段"
|  |  |  tid = 1002
|  |  }
|  |  [8] = {
|  |  |  name = "梦魇骑士"
|  |  |  tid = 1015
|  |  }
|  |  [80] = {
|  |  |  name = "3段"
|  |  |  tid = 1003
|  |  }
|  |  [81] = {
|  |  |  name = "4段"
|  |  |  tid = 1004
|  |  }
|  |  [82] = {
|  |  |  name = "5段"
|  |  |  tid = 1005
|  |  }
|  |  [83] = {
|  |  |  name = "6段"
|  |  |  tid = 1006
|  |  }
|  |  [84] = {
|  |  |  name = "7段"
|  |  |  tid = 1007
|  |  }
|  |  [9] = {
|  |  |  name = "梦魇猎人"
|  |  |  tid = 1016
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: friend.GS2CSendSimpleInfo = {
|  frdlist = {
|  |  [1] = {
|  |  |  grade = 14
|  |  |  name = "耗电与修女"
|  |  |  pid = 10004
|  |  }
|  }
}
[logic/ui/CViewBase.lua:125]:CWelfareView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x7b87da30"
|  json_result = true
|  timer = 64
}
[logic/platform/CSdkCtrl.lua:380]:sdk upload: not init
[core/table.lua:94]:Table = {
|  [1] = "16"
|  [2] = "20"
}
[core/global.lua:59]:<color=#ffeb04>36 16 20</color>
[core/table.lua:94]:Table = {
|  [1] = "21"
|  [2] = "24"
}
[core/global.lua:59]:<color=#ffeb04>36 21 24</color>
[core/table.lua:94]:Table = {
|  [1] = "25"
|  [2] = "27"
}
[core/global.lua:59]:<color=#ffeb04>36 25 27</color>
[logic/base/CResCtrl.lua:626]:res gc finish!
[logic/ui/CViewCtrl.lua:104]:CWelfareView     CloseView
[logic/ui/CViewCtrl.lua:94]:CAttrMainView ShowView
[logic/ui/CViewBase.lua:125]:CAttrMainView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CAttrMainView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814675
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:04:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814685
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:04:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814695
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:04:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814705
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:05:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814715
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:05:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814725
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:05:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: mail.GS2CAddMail = {
|  simpleinfo = {
|  |  createtime = 1693814726
|  |  hasattach = 1
|  |  keeptime = 1296000
|  |  mailid = 1
|  |  title = "礼包"
|  }
}
[logic/ui/CViewCtrl.lua:94]:CFriendMainView ShowView
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 17001
}
[core/table.lua:94]:<--Net Send: mail.C2GSOpenMail = {
|  mailid = 1
}
[logic/ui/CViewBase.lua:125]:CFriendMainView LoadDone!
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 17001
}
[core/table.lua:94]:-->Net Receive: mail.GS2CMailInfo = {
|  attachs = {
|  |  [1] = {
|  |  |  sid = 13103
|  |  |  type = 1
|  |  |  val = 1
|  |  }
|  }
|  context = "礼包"
|  hasattach = 1
|  keeptime = 1296000
|  mailid = 1
|  name = "系统"
|  opened = 1
|  title = "礼包"
|  validtime = 1695110726
}
[core/global.lua:59]:<color=#ffeb04>CMailCtrl.UpdateMailInfo, mailid = 1</color>
[core/table.lua:94]:-->Net Receive: mail.GS2CMailOpened = {
|  mailids = {
|  |  [1] = 1
|  }
}
[logic/ui/CViewCtrl.lua:94]:ItemTipsSimpleInfoView ShowView
[logic/ui/CViewBase.lua:125]:ItemTipsSimpleInfoView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814735
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:05:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814745
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:05:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>领取邮件附件, mailid = 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 17002
}
[core/table.lua:94]:<--Net Send: mail.C2GSAcceptAttach = {
|  mailid = 1
}
[logic/ui/CViewCtrl.lua:104]:ItemTipsSimpleInfoView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 17002
}
[core/table.lua:94]:-->Net Receive: mail.GS2CDelAttach = {
|  mailid = 1
}
[core/table.lua:94]:-->Net Receive: mail.GS2CMailOpened = {
|  mailids = {
|  |  [1] = 1
|  }
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得 #P[3级豪礼礼包] x 1#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1693814726
|  |  id = 24
|  |  itemlevel = 3
|  |  name = "3级豪礼礼包"
|  |  sid = 13103
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CItemQuickUse = {
|  id = 24
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  sid = 13103
|  |  |  virtual = 13103
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[logic/ui/CViewCtrl.lua:94]:CItemQuickUseView ShowView
[logic/ui/CViewBase.lua:125]:CItemQuickUseView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 5001
}
[core/table.lua:94]:<--Net Send: item.C2GSItemUse = {
|  amount = 1
|  itemid = 24
|  target = 10003
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 5001
}
[core/table.lua:94]:-->Net Receive: item.GS2CItemAmount = {
|  create_time = 1693814726
|  id = 24
}
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 5
|  |  create_time = 1693814750
|  |  id = 25
|  |  itemlevel = 2
|  |  name = "神格·暴"
|  |  sid = 11205
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CItemAmount = {
|  amount = 6
|  create_time = 1693814750
|  id = 6
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#B[神格·暴] x 5#n，#O[灵魂钥匙] x 5#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 5
|  |  |  sid = 10040
|  |  |  virtual = 10040
|  |  }
|  |  [2] = {
|  |  |  amount = 5
|  |  |  sid = 11205
|  |  |  virtual = 11205
|  |  }
|  |  [3] = {
|  |  |  amount = 188888
|  |  |  sid = 1002
|  |  |  virtual = 1002
|  |  }
|  |  [4] = {
|  |  |  amount = 488
|  |  |  sid = 1003
|  |  |  virtual = 1003
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  coin = 855942
|  |  goldcoin = 1358
|  |  mask = "30"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  coin = 855942
|  goldcoin = 1358
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#w1#G188888#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#w2#G488#n"
|  type = 6
}
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemQuickUseView     CloseView
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814755
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:05:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CFriendMainView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814765
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:06:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814775
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:06:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814785
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:06:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814795
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:06:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: mail.GS2CAddMail = {
|  simpleinfo = {
|  |  createtime = 1693814795
|  |  hasattach = 1
|  |  keeptime = 1296000
|  |  mailid = 2
|  |  title = "礼包"
|  }
}
[logic/ui/CViewCtrl.lua:94]:CFriendMainView ShowView
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 17001
}
[core/table.lua:94]:<--Net Send: mail.C2GSOpenMail = {
|  mailid = 2
}
[logic/ui/CViewBase.lua:125]:CFriendMainView LoadDone!
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 17001
}
[core/table.lua:94]:-->Net Receive: mail.GS2CMailInfo = {
|  attachs = {
|  |  [1] = {
|  |  |  sid = 13319
|  |  |  type = 1
|  |  |  val = 1
|  |  }
|  }
|  context = "礼包"
|  hasattach = 1
|  keeptime = 1296000
|  mailid = 2
|  name = "系统"
|  opened = 1
|  title = "礼包"
|  validtime = 1695110795
}
[core/global.lua:59]:<color=#ffeb04>CMailCtrl.UpdateMailInfo, mailid = 2</color>
[core/table.lua:94]:-->Net Receive: mail.GS2CMailOpened = {
|  mailids = {
|  |  [1] = 2
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 17001
}
[core/table.lua:94]:<--Net Send: mail.C2GSOpenMail = {
|  mailid = 2
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 17001
}
[core/table.lua:94]:-->Net Receive: mail.GS2CMailInfo = {
|  attachs = {
|  |  [1] = {
|  |  |  sid = 13319
|  |  |  type = 1
|  |  |  val = 1
|  |  }
|  }
|  context = "礼包"
|  hasattach = 1
|  keeptime = 1296000
|  mailid = 2
|  name = "系统"
|  opened = 1
|  title = "礼包"
|  validtime = 1695110795
}
[core/global.lua:59]:<color=#ffeb04>CMailCtrl.UpdateMailInfo, mailid = 2</color>
[core/table.lua:94]:-->Net Receive: mail.GS2CMailOpened = {
|  mailids = {
|  |  [1] = 2
|  }
}
[logic/ui/CViewCtrl.lua:94]:ItemTipsSimpleInfoView ShowView
[logic/ui/CViewBase.lua:125]:ItemTipsSimpleInfoView LoadDone!
[core/table.lua:94]:-->Net Receive: huodong.GS2CNotifyQuestion = {
|  desc = "[44efe9]突击测验[-]正在进行中"
|  end_time = 1693814809
|  server_time = 1693814804
|  status = 2
|  type = 1
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:06:44
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814805
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:06:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>领取邮件附件, mailid = 2</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 17002
}
[core/table.lua:94]:<--Net Send: mail.C2GSAcceptAttach = {
|  mailid = 2
}
[logic/ui/CViewCtrl.lua:104]:ItemTipsSimpleInfoView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 17002
}
[core/table.lua:94]:-->Net Receive: mail.GS2CDelAttach = {
|  mailid = 2
}
[core/table.lua:94]:-->Net Receive: mail.GS2CMailOpened = {
|  mailids = {
|  |  [1] = 2
|  }
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得 #B[328元礼包] x 1#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1693814795
|  |  id = 26
|  |  itemlevel = 2
|  |  name = "328元礼包"
|  |  sid = 13319
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CItemQuickUse = {
|  id = 26
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  sid = 13319
|  |  |  virtual = 13319
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[logic/ui/CViewCtrl.lua:94]:CItemQuickUseView ShowView
[logic/ui/CViewBase.lua:125]:CItemQuickUseView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 5001
}
[core/table.lua:94]:<--Net Send: item.C2GSItemUse = {
|  amount = 1
|  itemid = 26
|  target = 10003
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 5001
}
[core/table.lua:94]:-->Net Receive: item.GS2CItemAmount = {
|  create_time = 1693814795
|  id = 26
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 1640000
|  |  |  sid = 1002
|  |  |  virtual = 1002
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  coin = 2495942
|  |  mask = "20"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  coin = 2495942
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#w1#********#n"
|  type = 6
}
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemQuickUseView     CloseView
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:104]:CFriendMainView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814815
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:06:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814825
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:07:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814835
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:07:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: mail.GS2CAddMail = {
|  simpleinfo = {
|  |  createtime = 1693814844
|  |  hasattach = 1
|  |  keeptime = 1296000
|  |  mailid = 3
|  |  title = "礼包"
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814845
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:07:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CFriendMainView ShowView
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 17001
}
[core/table.lua:94]:<--Net Send: mail.C2GSOpenMail = {
|  mailid = 3
}
[logic/ui/CViewBase.lua:125]:CFriendMainView LoadDone!
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 17001
}
[core/table.lua:94]:-->Net Receive: mail.GS2CMailInfo = {
|  attachs = {
|  |  [1] = {
|  |  |  sid = 13111
|  |  |  type = 1
|  |  |  val = 1
|  |  }
|  }
|  context = "礼包"
|  hasattach = 1
|  keeptime = 1296000
|  mailid = 3
|  name = "系统"
|  opened = 1
|  title = "礼包"
|  validtime = 1695110844
}
[core/global.lua:59]:<color=#ffeb04>CMailCtrl.UpdateMailInfo, mailid = 3</color>
[core/table.lua:94]:-->Net Receive: mail.GS2CMailOpened = {
|  mailids = {
|  |  [1] = 3
|  }
}
[logic/ui/CViewCtrl.lua:94]:ItemTipsSimpleInfoView ShowView
[logic/ui/CViewBase.lua:125]:ItemTipsSimpleInfoView LoadDone!
[logic/ui/CViewCtrl.lua:104]:ItemTipsSimpleInfoView     CloseView
[core/global.lua:59]:<color=#ffeb04>领取邮件附件, mailid = 3</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 17002
}
[core/table.lua:94]:<--Net Send: mail.C2GSAcceptAttach = {
|  mailid = 3
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 17002
}
[core/table.lua:94]:-->Net Receive: mail.GS2CDelAttach = {
|  mailid = 3
}
[core/table.lua:94]:-->Net Receive: mail.GS2CMailOpened = {
|  mailids = {
|  |  [1] = 3
|  }
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得 #O[4级定制礼包] x 1#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1693814844
|  |  id = 27
|  |  itemlevel = 4
|  |  name = "4级定制礼包"
|  |  sid = 13111
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CItemQuickUse = {
|  id = 27
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  sid = 13111
|  |  |  virtual = 13111
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[logic/ui/CViewCtrl.lua:94]:CItemQuickUseView ShowView
[logic/ui/CViewBase.lua:125]:CItemQuickUseView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814855
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:07:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 5001
}
[core/table.lua:94]:<--Net Send: item.C2GSItemUse = {
|  amount = 1
|  itemid = 27
|  target = 10003
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 5001
}
[core/table.lua:94]:-->Net Receive: item.GS2CItemAmount = {
|  create_time = 1693814844
|  id = 27
}
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1693814855
|  |  id = 28
|  |  itemlevel = 3
|  |  name = "5级宝石礼包"
|  |  sid = 13002
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CItemQuickUse = {
|  id = 28
}
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1693814855
|  |  id = 29
|  |  itemlevel = 4
|  |  name = "红色可选御灵"
|  |  sid = 13271
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CItemQuickUse = {
|  id = 29
}
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1693814855
|  |  id = 30
|  |  itemlevel = 2
|  |  name = "大红包"
|  |  sid = 11502
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#P[5级宝石礼包] x 1#n，#O[红色可选御灵] x 1#n，#B[大红包] x 1#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  sid = 11502
|  |  |  virtual = 11502
|  |  }
|  |  [2] = {
|  |  |  amount = 1
|  |  |  sid = 13002
|  |  |  virtual = 13002
|  |  }
|  |  [3] = {
|  |  |  amount = 1
|  |  |  sid = 13271
|  |  |  virtual = 13271
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 5001
}
[core/table.lua:94]:<--Net Send: item.C2GSItemUse = {
|  amount = 1
|  itemid = 28
|  target = 10003
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 5001
}
[core/table.lua:94]:-->Net Receive: item.GS2CItemAmount = {
|  create_time = 1693814855
|  id = 28
}
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1693814859
|  |  id = 31
|  |  itemlevel = 2
|  |  name = "5级翠星宝石"
|  |  sid = 18404
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1693814859
|  |  id = 32
|  |  itemlevel = 2
|  |  name = "5级黄金宝石"
|  |  sid = 18304
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1693814859
|  |  id = 33
|  |  itemlevel = 2
|  |  name = "5级疾风宝石"
|  |  sid = 18504
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1693814859
|  |  id = 34
|  |  itemlevel = 2
|  |  name = "5级双生宝石"
|  |  sid = 18204
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1693814859
|  |  id = 35
|  |  itemlevel = 2
|  |  name = "5级八云宝石"
|  |  sid = 18104
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CAddItem = {
|  itemdata = {
|  |  amount = 1
|  |  create_time = 1693814859
|  |  id = 36
|  |  itemlevel = 2
|  |  name = "5级绯红宝石"
|  |  sid = 18004
|  }
}
[core/global.lua:59]:<color=#ffeb04>AddItem</color>
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#B[5级翠星宝石] x 1#n，#B[5级黄金宝石] x 1#n，#B[5级疾风宝石] x 1#n，#B[5级双生宝石] x 1#n，#B[5级八云宝石] x 1#n，#B[5级绯红宝石] x 1#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: openui.GS2CShowItem = {
|  item_list = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  sid = 18504
|  |  |  virtual = 18504
|  |  }
|  |  [2] = {
|  |  |  amount = 1
|  |  |  sid = 18004
|  |  |  virtual = 18004
|  |  }
|  |  [3] = {
|  |  |  amount = 1
|  |  |  sid = 18404
|  |  |  virtual = 18404
|  |  }
|  |  [4] = {
|  |  |  amount = 1
|  |  |  sid = 18104
|  |  |  virtual = 18104
|  |  }
|  |  [5] = {
|  |  |  amount = 1
|  |  |  sid = 18304
|  |  |  virtual = 18304
|  |  }
|  |  [6] = {
|  |  |  amount = 1
|  |  |  sid = 18204
|  |  |  virtual = 18204
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemRewardListView ShowView
[logic/ui/CViewBase.lua:125]:CItemRewardListView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemQuickUseView     CloseView
[logic/ui/CViewCtrl.lua:104]:CItemRewardListView     CloseView
[logic/ui/CViewCtrl.lua:104]:CFriendMainView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814865
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:07:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CItemBagMainView ShowView
[logic/ui/CPopupBox.lua:148]:index ..1
[logic/ui/CViewBase.lua:125]:CItemBagMainView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CItemBagMainView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814875
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:07:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814885
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:08:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814895
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:08:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814905
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:08:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814915
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:08:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814925
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:08:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814935
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:08:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814945
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:09:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814955
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:09:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814965
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:09:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814975
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:09:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814985
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:09:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693814995
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:09:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815005
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:10:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815015
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:10:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815025
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:10:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815035
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:10:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815045
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:10:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815055
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:10:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815065
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:11:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815075
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:11:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815085
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:11:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815095
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:11:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815105
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:11:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815115
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:11:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815125
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:12:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815135
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:12:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815145
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:12:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815155
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:12:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815165
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:12:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815175
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:12:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815185
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:13:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815195
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:13:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815205
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:13:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815215
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:13:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815225
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:13:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815235
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:13:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815245
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:14:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815255
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:14:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815265
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:14:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815275
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:14:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815285
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:14:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815295
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:14:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815305
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:15:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815315
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:15:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815325
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:15:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815335
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:15:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815345
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:15:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815355
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:15:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815365
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:16:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815375
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:16:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815385
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:16:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815395
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:16:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815405
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:16:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815415
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:16:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815425
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:17:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815435
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:17:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815445
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:17:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815455
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:17:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815465
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:17:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CLockScreenView ShowView
[logic/ui/CViewBase.lua:125]:CLockScreenView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815475
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:17:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815485
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:18:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815495
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:18:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815505
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:18:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815515
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:18:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815525
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:18:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815535
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:18:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815545
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:19:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815555
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:19:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815565
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:19:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815575
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:19:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815585
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:19:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815595
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:19:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815605
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:20:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815615
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:20:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815625
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:20:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815635
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:20:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815645
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:20:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815655
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:20:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815665
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:21:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815675
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:21:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815685
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:21:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815695
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:21:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815705
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:21:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815715
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:21:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815725
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:22:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815735
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:22:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815745
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:22:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815755
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:22:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815765
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:22:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815775
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:22:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815785
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:23:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815795
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:23:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815805
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:23:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815815
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:23:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815825
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:23:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815835
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:23:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815845
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:24:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815855
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:24:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815865
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:24:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815875
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:24:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815885
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:24:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815895
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:24:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815905
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:25:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815915
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:25:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815925
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:25:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815935
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:25:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815945
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:25:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815955
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:25:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815965
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:26:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815975
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:26:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815985
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:26:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693815995
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:26:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816005
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:26:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816015
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:26:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816025
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:27:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816035
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:27:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816045
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:27:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816055
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:27:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816065
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:27:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816075
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:27:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816085
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:28:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816095
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:28:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816105
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:28:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816115
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:28:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816125
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:28:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816135
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:28:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816145
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:29:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816155
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:29:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816165
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:29:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816175
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:29:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816185
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:29:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816195
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:29:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816205
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:30:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816215
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:30:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816225
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:30:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816235
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:30:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816245
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:30:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816255
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:30:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816265
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:31:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816275
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:31:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816285
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:31:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816295
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:31:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816305
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:31:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816315
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:31:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816325
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:32:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816335
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:32:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816345
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:32:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816355
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:32:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816365
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:32:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816375
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:32:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816385
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:33:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816395
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:33:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816405
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:33:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816415
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:33:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816425
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:33:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816435
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:33:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816445
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:34:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816455
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:34:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816465
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:34:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816475
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:34:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816485
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:34:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816495
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:34:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816505
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:35:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816515
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:35:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816525
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:35:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816535
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:35:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816545
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:35:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816555
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:35:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816565
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:36:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816575
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:36:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816585
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:36:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816595
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:36:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816605
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:36:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816615
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:36:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816625
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:37:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816635
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:37:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816645
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:37:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816655
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:37:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816665
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:37:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816675
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:37:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816685
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:38:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816695
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:38:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816705
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:38:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816715
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:38:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816725
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:38:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816735
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:38:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816745
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:39:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816755
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:39:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816765
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:39:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816775
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:39:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816785
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:39:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CLockScreenView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816795
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:39:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816805
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:40:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816815
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:40:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816825
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:40:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816835
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:40:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816845
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:40:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816855
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:40:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816865
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:41:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816875
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:41:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816885
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:41:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816895
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:41:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816905
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:41:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816915
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:41:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816925
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:42:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816935
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:42:15
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816945
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:42:25
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816955
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:42:35
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816965
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:42:45
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816975
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:42:55
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816985
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:43:05
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693816996
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:43:16
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817006
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:43:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817016
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:43:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817026
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:43:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817036
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:43:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817046
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:44:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817056
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:44:16
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817066
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:44:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817076
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:44:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817086
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:44:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817096
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:44:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817106
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:45:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817116
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:45:16
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817126
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:45:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817136
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:45:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817146
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:45:46
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817156
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:45:56
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817166
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:46:06
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817176
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:46:16
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817186
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:46:26
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1693817196
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/09/04 16:46:36
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[net/CNetCtrl.lua:216]:CNetCtrl.AutoReconnect开始重连
[core/global.lua:59]:<color=#ffeb04>重置游戏</color>
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "main"]:206: in function 'ResetGame'
	[string "logic/login/CLoginCtrl"]:441: in function 'Reconnect'
	[string "net/CNetCtrl"]:219: in function 'AutoReconnect'
	[string "net/CNetCtrl"]:180: in function 'OnSocketEvent'
	[string "net/CNetCtrl"]:111: in function 'm_Callback'
	[string "net/CTcpClient"]:43: in function <[string "net/CTcpClient"]:37>
[core/table.lua:94]:Table = {}
[core/global.lua:59]:<color=#ffeb04>CViewCtrl.CloseAll--> nil</color>
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/ui/CViewCtrl"]:280: in function 'CloseAll'
	[string "main"]:231: in function 'ResetGame'
	[string "logic/login/CLoginCtrl"]:441: in function 'Reconnect'
	[string "net/CNetCtrl"]:219: in function 'AutoReconnect'
	[string "net/CNetCtrl"]:180: in function 'OnSocketEvent'
	[string "net/CNetCtrl"]:111: in function 'm_Callback'
	[string "net/CTcpClient"]:43: in function <[string "net/CTcpClient"]:37>
[core/global.lua:59]:<color=#ffeb04>CloseAll-->CloseView:  CMainMenuView</color>
[logic/ui/CViewCtrl.lua:104]:CMainMenuView     CloseView
[logic/base/CResCtrl.lua:599]:res gc step start!
[logic/login/CLoginCtrl.lua:154]:ConnectServer =     table:0x7B6DEE88    table:0x7E7A5668
[net/CNetCtrl.lua:114]:Test连接    **************    7012
[core/table.lua:94]:CLoginCtrl.Reconnect: = {
|  account = "test"
|  pid = 10003
|  role_token = "**********0008"
}
你的主机中的软件中止了一个已建立的连接。

  at System.Net.Sockets.Socket+SocketAsyncResult.CheckIfThrowDelayedException () [0x00000] in <filename unknown>:0 
  at System.Net.Sockets.Socket.EndReceive (IAsyncResult asyncResult, System.Net.Sockets.SocketError& errorCode) [0x00000] in <filename unknown>:0 
  at System.Net.Sockets.Socket.EndReceive (IAsyncResult result) [0x00000] in <filename unknown>:0 
  at TcpClient.OnReceive (IAsyncResult ar) [0x00031] in D:\work\YLZ\client20170430_chuangyou_0.1\Assets\Scripts\Net\Tcp\TcpClient.cs:210 
你的主机中的软件中止了一个已建立的连接。

  at System.Net.Sockets.Socket+SocketAsyncResult.CheckIfThrowDelayedException () [0x00000] in <filename unknown>:0 
  at System.Net.Sockets.Socket.EndReceive (IAsyncResult asyncResult, System.Net.Sockets.SocketError& errorCode) [0x00000] in <filename unknown>:0 
  at System.Net.Sockets.Socket.EndReceive (IAsyncResult result) [0x00000] in <filename unknown>:0 
  at TcpClient.OnReceive (IAsyncResult ar) [0x00031] in D:\work\YLZ\client20170430_chuangyou_0.1\Assets\Scripts\Net\Tcp\TcpClient.cs:210 
[logic/base/CResCtrl.lua:626]:res gc finish!
