%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: ui_eff_denglu_yun
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: 437, y: 235, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      - time: 10
        value: {x: 599, y: 273, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      - time: 19.966667
        value: {x: 437, y: 235, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 1
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: -409, y: 229, z: 0}
        inSlope: {x: -15.6, y: 1.6, z: 0}
        outSlope: {x: -15.6, y: 1.6, z: 0}
        tangentMode: 0
      - time: 10
        value: {x: -565, y: 245, z: 0}
        inSlope: {x: 0.02608633, y: -0.0026755333, z: 0}
        outSlope: {x: 0.02608633, y: -0.0026755333, z: 0}
        tangentMode: 0
      - time: 19.966667
        value: {x: -409, y: 229, z: 0}
        inSlope: {x: 15.652173, y: -1.6053511, z: 0}
        outSlope: {x: 15.652173, y: -1.6053511, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 2
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 30
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - path: 2212294583
      attribute: 1
      script: {fileID: 0}
      classID: 4
      customType: 0
      isPPtrCurve: 0
    - path: 450215437
      attribute: 1
      script: {fileID: 0}
      classID: 4
      customType: 0
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 19.966667
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 437
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: 10
        value: 599
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: 19.966667
        value: 437
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.x
    path: 1
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 235
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: 10
        value: 273
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: 19.966667
        value: 235
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.y
    path: 1
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: 10
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: 19.966667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.z
    path: 1
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -409
        inSlope: -15.6
        outSlope: -15.6
        tangentMode: 10
      - time: 10
        value: -565
        inSlope: 0.02608633
        outSlope: 0.02608633
        tangentMode: 10
      - time: 19.966667
        value: -409
        inSlope: 15.652173
        outSlope: 15.652173
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.x
    path: 2
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 229
        inSlope: 1.6
        outSlope: 1.6
        tangentMode: 10
      - time: 10
        value: 245
        inSlope: -0.0026755333
        outSlope: -0.0026755333
        tangentMode: 10
      - time: 19.966667
        value: 229
        inSlope: -1.6053511
        outSlope: -1.6053511
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.y
    path: 2
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 10
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 19.966667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.z
    path: 2
    classID: 4
    script: {fileID: 0}
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_GenerateMotionCurves: 0
  m_Events: []
