%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: magic_eff_3202_ying04
  m_Shader: {fileID: 203, guid: 0000000000000000f000000000000000, type: 0}
  m_ShaderKeywords: _EMISSION
  m_LightmapFlags: 1
  m_CustomRenderQueue: -1
  stringTagMap: {}
  m_SavedProperties:
    serializedVersion: 2
    m_TexEnvs:
    - first:
        name: _BumpMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailAlbedoMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailMask
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailNormalMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _Dissolve_Tex
      second:
        m_Texture: {fileID: 2800000, guid: 6475569e57de23843b4c52854998a6af, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _Distort_Tex
      second:
        m_Texture: {fileID: 2800000, guid: f32d8cbdfa3df5e48a885c48e037a62b, type: 3}
        m_Scale: {x: 1, y: 0.5}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _EmissionMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MainTex
      second:
        m_Texture: {fileID: 2800000, guid: b50d3ce312b131247825ce4d66a35af7, type: 3}
        m_Scale: {x: 0.65, y: 1}
        m_Offset: {x: 0.28, y: 0}
    - first:
        name: _MetallicGlossMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _OcclusionMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _ParallaxMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _raodong
      second:
        m_Texture: {fileID: 2800000, guid: 6475569e57de23843b4c52854998a6af, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _tex
      second:
        m_Texture: {fileID: 2800000, guid: be024874fa8f50047a80447002b73b53, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - first:
        name: _BumpScale
      second: 1
    - first:
        name: _Cutoff
      second: 0.5
    - first:
        name: _DetailNormalMapScale
      second: 1
    - first:
        name: _Dist_SppedX
      second: -25
    - first:
        name: _Dist_SppedY
      second: 18
    - first:
        name: _Dist_amountX
      second: 1.85
    - first:
        name: _Dist_amountY
      second: 5
    - first:
        name: _DstBlend
      second: 0
    - first:
        name: _GlossMapScale
      second: 1
    - first:
        name: _Glossiness
      second: 0.5
    - first:
        name: _GlossyReflections
      second: 1
    - first:
        name: _InvFade
      second: 1
    - first:
        name: _MT_Rotate
      second: 0
    - first:
        name: _MainTex_Power
      second: 4.5
    - first:
        name: _Metallic
      second: 0
    - first:
        name: _Mode
      second: 0
    - first:
        name: _OcclusionStrength
      second: 1
    - first:
        name: _Parallax
      second: 0.02
    - first:
        name: _SmoothnessTextureChannel
      second: 0
    - first:
        name: _SpecularHighlights
      second: 1
    - first:
        name: _SrcBlend
      second: 1
    - first:
        name: _UVSec
      second: 0
    - first:
        name: _ZT
      second: 2
    - first:
        name: _ZWrite
      second: 1
    - first:
        name: _diss_amount
      second: -0.01
    - first:
        name: _node_8634
      second: 1
    - first:
        name: _particle_controlA
      second: 0
    - first:
        name: _qiangdu
      second: 0.1
    - first:
        name: _sudu
      second: 0.2
    m_Colors:
    - first:
        name: _Color
      second: {r: 1, g: 1, b: 1, a: 1}
    - first:
        name: _EmissionColor
      second: {r: 0, g: 0, b: 0, a: 1}
    - first:
        name: _MainColor
      second: {r: 1, g: 0.31034482, b: 0, a: 1}
    - first:
        name: _TintColor
      second: {r: 1, g: 0.35172415, b: 0, a: 0.5}
    - first:
        name: _color
      second: {r: 0.5, g: 0.5, b: 0.5, a: 1}
