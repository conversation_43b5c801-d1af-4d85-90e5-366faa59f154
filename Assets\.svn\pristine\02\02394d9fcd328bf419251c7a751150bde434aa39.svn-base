with:857  hight:437
[logic/misc/CShareCtrl.lua:18]:ShareCallback ctor
[logic/login/CLoginCtrl.lua:5]:公告初始化...
[logic/base/CHttpCtrl.lua:32]:http get ->    https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials&client_id=rlU84dprH6I7sMjBkqM0gizl&client_secret=c15395c2433c4a904d22aa27eab8321b&
[logic/ui/CViewCtrl.lua:94]:CNotifyView ShowView
[logic/ui/CViewCtrl.lua:94]:CBottomView ShowView
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x3b12efb0"
|  json_result = true
|  timer = 16
}
[logic/audio/CSpeechCtrl.lua:72]:OnTokenGet err
[logic/ui/CViewBase.lua:125]:CNotifyView LoadDone!
[logic/base/CResCtrl.lua:198]:-->resource init done!!! 12
[logic/ui/CViewBase.lua:125]:CBottomView LoadDone!
[core/table.lua:94]:CServerCtrl.SetServerData: = {
|  [1] = {
|  |  group_id = 1
|  |  name = "1-10区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  ip = "**************"
|  |  |  |  name = "双线1区"
|  |  |  |  new = 0
|  |  |  |  server_id = 1001
|  |  |  |  state = 2
|  |  |  }
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:94]:CLoginView ShowView
[logic/ui/CViewBase.lua:125]:CLoginView LoadDone!
[logic/login/CLoginServerPage.lua:54]:000000000000000000000000    nil
[logic/ui/CViewCtrl.lua:94]:CLoginNoticeView ShowView
[core/table.lua:94]:设置公告: = {
|  [1] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "text1"
|  |  |  |  |  title = "新服7天限时-大狂欢活动"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "text2"
|  |  |  |  |  title = "title2"
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = "ceshi"
|  |  }
|  |  hot = 2
|  |  title = "系统公告"
|  }
|  [2] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "text1"
|  |  |  |  |  title = "title1"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "text2"
|  |  |  |  |  title = "title2"
|  |  |  |  }
|  |  |  }
|  |  |  pic = "Schedule/bg_schedule_1001.png"
|  |  |  text = "ceshi2"
|  |  }
|  |  hot = 1
|  |  title = "系统公告2"
|  }
}
[logic/ui/CViewBase.lua:125]:CLoginNoticeView LoadDone!
[logic/ui/CViewCtrl.lua:94]:CLockScreenView ShowView
[logic/ui/CViewBase.lua:125]:CLockScreenView LoadDone!
