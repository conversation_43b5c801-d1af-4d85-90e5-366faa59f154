fileFormatVersion: 2
guid: 9deb1356b66521b469479fd804df9ab3
timeCreated: **********
licenseType: Pro
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: Bip001
    100002: Bip001 Footsteps
    100004: Bip001 Head
    100006: Bip001 HeadNub
    100008: Bip001 L Calf
    100010: Bip001 L Clavicle
    100012: Bip001 L Finger0
    100014: Bip001 L Finger01
    100016: Bip001 L Finger0Nub
    100018: Bip001 L Finger1
    100020: Bip001 L Finger11
    100022: Bip001 L Finger1Nub
    100024: Bip001 L Foot
    100026: Bip001 L Forearm
    100028: Bip001 L Hand
    100030: Bip001 L Thigh
    100032: Bip001 L Toe0
    100034: Bip001 L Toe0Nub
    100036: Bip001 L UpperArm
    100038: Bip001 Neck
    100040: Bip001 Pelvis
    100042: Bip001 Prop2
    100044: Bip001 R Calf
    100046: Bip001 R Clavicle
    100048: Bip001 R Finger0
    100050: Bip001 R Finger01
    100052: Bip001 R Finger0Nub
    100054: Bip001 R Finger1
    100056: Bip001 R Finger11
    100058: Bip001 R Finger1Nub
    100060: Bip001 R Foot
    100062: Bip001 R Forearm
    100064: Bip001 R Hand
    100066: Bip001 R Thigh
    100068: Bip001 R Toe0
    100070: Bip001 R Toe0Nub
    100072: Bip001 R UpperArm
    100074: Bip001 Spine
    100076: Bip001 Spine1
    100078: Bone001
    100080: Bone002
    100082: Bone003
    100084: Bone004
    100086: Bone007
    100088: Bone007(mirrored)
    100090: Bone008
    100092: Bone008(mirrored)
    100094: Bone009
    100096: Bone009(mirrored)
    100098: Bone010
    100100: Bone010(mirrored)
    100102: Bone011
    100104: Bone011(mirrored)
    100106: Bone012
    100108: Bone012(mirrored)
    100110: Bone013
    100112: Bone013(mirrored)
    100114: Bone014
    100116: Bone014(mirrored)
    100118: Bone015
    100120: Bone015(mirrored)
    100122: Bone016
    100124: Bone017
    100126: Bone018
    100128: Bone019
    100130: Bone020
    100132: Bone026
    100134: Bone027
    100136: Bone028
    100138: Bone029
    100140: Bone030
    100142: Bone030(mirrored)
    100144: Bone031
    100146: Bone031(mirrored)
    100148: Bone032
    100150: Bone032(mirrored)
    100152: Bone033
    100154: Bone033(mirrored)
    100156: Bone034
    100158: Bone034(mirrored)
    100160: Bone035
    100162: Bone036
    100164: Bone037
    100166: Bone038
    100168: Bone039
    100170: Bone040
    100172: Bone041
    100174: Bone042
    100176: Bone043
    100178: Bone043(mirrored)
    100180: Bone044
    100182: Bone044(mirrored)
    100184: Bone049
    100186: model708
    100188: //RootNode
    100190: weapon402
    100192: weapon708
    400000: Bip001
    400002: Bip001 Footsteps
    400004: Bip001 Head
    400006: Bip001 HeadNub
    400008: Bip001 L Calf
    400010: Bip001 L Clavicle
    400012: Bip001 L Finger0
    400014: Bip001 L Finger01
    400016: Bip001 L Finger0Nub
    400018: Bip001 L Finger1
    400020: Bip001 L Finger11
    400022: Bip001 L Finger1Nub
    400024: Bip001 L Foot
    400026: Bip001 L Forearm
    400028: Bip001 L Hand
    400030: Bip001 L Thigh
    400032: Bip001 L Toe0
    400034: Bip001 L Toe0Nub
    400036: Bip001 L UpperArm
    400038: Bip001 Neck
    400040: Bip001 Pelvis
    400042: Bip001 Prop2
    400044: Bip001 R Calf
    400046: Bip001 R Clavicle
    400048: Bip001 R Finger0
    400050: Bip001 R Finger01
    400052: Bip001 R Finger0Nub
    400054: Bip001 R Finger1
    400056: Bip001 R Finger11
    400058: Bip001 R Finger1Nub
    400060: Bip001 R Foot
    400062: Bip001 R Forearm
    400064: Bip001 R Hand
    400066: Bip001 R Thigh
    400068: Bip001 R Toe0
    400070: Bip001 R Toe0Nub
    400072: Bip001 R UpperArm
    400074: Bip001 Spine
    400076: Bip001 Spine1
    400078: Bone001
    400080: Bone002
    400082: Bone003
    400084: Bone004
    400086: Bone007
    400088: Bone007(mirrored)
    400090: Bone008
    400092: Bone008(mirrored)
    400094: Bone009
    400096: Bone009(mirrored)
    400098: Bone010
    400100: Bone010(mirrored)
    400102: Bone011
    400104: Bone011(mirrored)
    400106: Bone012
    400108: Bone012(mirrored)
    400110: Bone013
    400112: Bone013(mirrored)
    400114: Bone014
    400116: Bone014(mirrored)
    400118: Bone015
    400120: Bone015(mirrored)
    400122: Bone016
    400124: Bone017
    400126: Bone018
    400128: Bone019
    400130: Bone020
    400132: Bone026
    400134: Bone027
    400136: Bone028
    400138: Bone029
    400140: Bone030
    400142: Bone030(mirrored)
    400144: Bone031
    400146: Bone031(mirrored)
    400148: Bone032
    400150: Bone032(mirrored)
    400152: Bone033
    400154: Bone033(mirrored)
    400156: Bone034
    400158: Bone034(mirrored)
    400160: Bone035
    400162: Bone036
    400164: Bone037
    400166: Bone038
    400168: Bone039
    400170: Bone040
    400172: Bone041
    400174: Bone042
    400176: Bone043
    400178: Bone043(mirrored)
    400180: Bone044
    400182: Bone044(mirrored)
    400184: Bone049
    400186: model708
    400188: //RootNode
    400190: weapon402
    400192: weapon708
    4300000: model708
    4300002: weapon708
    9500000: //RootNode
    13700000: model708
    13700002: weapon708
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
