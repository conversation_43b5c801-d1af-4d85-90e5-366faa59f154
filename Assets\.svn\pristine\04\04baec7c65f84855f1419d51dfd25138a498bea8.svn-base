//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class CLASSTAG_live2d_MotionQueueManagerWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(live2d.MotionQueueManager), typeof(System.Object));
		<PERSON><PERSON>RegFunction("startMotion",FUNCTAG_startMotion);
		<PERSON><PERSON>unction("updateParam",FUNCTAG_updateParam);
		L.RegFunction("isFinished",FUNCTAG_isFinished);
		L.RegFunction("stopAllMotions",FUNCTAG_stopAllMotions);
		L.RegFunction("setMotionDebugMode",FUNCTAG_setMotionDebugMode);
		<PERSON><PERSON>unction("New",FUNCTAG__Createlive2d_MotionQueueManager);
		L.RegFunction("__tostring",ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG__Createlive2d_MotionQueueManager(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				live2d.MotionQueueManager obj = new live2d.MotionQueueManager();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: live2d.MotionQueueManager.New");
			}
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_startMotion(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			live2d.MotionQueueManager obj = (live2d.MotionQueueManager)ToLua.CheckObject(L, 1, typeof(live2d.MotionQueueManager));
			live2d.AMotion arg0 = (live2d.AMotion)ToLua.CheckObject(L, 2, typeof(live2d.AMotion));
			bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
			int o = obj.startMotion(arg0, arg1);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_updateParam(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			live2d.MotionQueueManager obj = (live2d.MotionQueueManager)ToLua.CheckObject(L, 1, typeof(live2d.MotionQueueManager));
			live2d.ALive2DModel arg0 = (live2d.ALive2DModel)ToLua.CheckObject(L, 2, typeof(live2d.ALive2DModel));
			bool o = obj.updateParam(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_isFinished(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1 && TypeChecker.CheckTypes(L, 1, typeof(live2d.MotionQueueManager)))
			{
				live2d.MotionQueueManager obj = (live2d.MotionQueueManager)ToLua.ToObject(L, 1);
				bool o = obj.isFinished();
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes(L, 1, typeof(live2d.MotionQueueManager), typeof(int)))
			{
				live2d.MotionQueueManager obj = (live2d.MotionQueueManager)ToLua.ToObject(L, 1);
				int arg0 = (int)LuaDLL.lua_tonumber(L, 2);
				bool o = obj.isFinished(arg0);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: live2d.MotionQueueManager.isFinished");
			}
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_stopAllMotions(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			live2d.MotionQueueManager obj = (live2d.MotionQueueManager)ToLua.CheckObject(L, 1, typeof(live2d.MotionQueueManager));
			obj.stopAllMotions();
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_setMotionDebugMode(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			live2d.MotionQueueManager obj = (live2d.MotionQueueManager)ToLua.CheckObject(L, 1, typeof(live2d.MotionQueueManager));
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.setMotionDebugMode(arg0);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

