//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class CLASSTAG_UnityEngine_SceneManagement_SceneWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.SceneManagement.Scene), null);
		<PERSON><PERSON>unction("IsValid",FUNCTAG_IsValid);
		<PERSON><PERSON>("GetRootGameObjects",FUNCTAG_GetRootGameObjects);
		<PERSON><PERSON>unction("GetHashCode",FUNCTAG_GetHashCode);
		<PERSON><PERSON>RegFunction("Equals",FUNCTAG_Equals);
		<PERSON><PERSON>RegFunction("ToString",FUNCTAG_ToString);
		<PERSON><PERSON>ction("GetType",FUNCTAG_GetType);
		<PERSON><PERSON>unction("New",FUNCTAG__CreateUnityEngine_SceneManagement_Scene);
		<PERSON>.RegFunction("__eq",FUNCTAG_op_Equality);
		<PERSON><PERSON>("path",FUNCTAG_get_path, null);
		<PERSON><PERSON>("name",FUNCTAG_get_name, null);
		<PERSON><PERSON>("isLoaded",FUNCTAG_get_isLoaded, null);
		<PERSON><PERSON>("buildIndex",FUNCT<PERSON>_get_buildIndex, null);
		L.RegVar("isDirty",FUNCTAG_get_isDirty, null);
		L.RegVar("rootCount",FUNCTAG_get_rootCount, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG__CreateUnityEngine_SceneManagement_Scene(IntPtr L)
	{
		UnityEngine.SceneManagement.Scene obj = new UnityEngine.SceneManagement.Scene();
		ToLua.PushValue(L, obj);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_IsValid(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)ToLua.CheckObject(L, 1, typeof(UnityEngine.SceneManagement.Scene));
			bool o = obj.IsValid();
			LuaDLL.lua_pushboolean(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_GetRootGameObjects(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1 && TypeChecker.CheckTypes(L, 1, typeof(UnityEngine.SceneManagement.Scene)))
			{
				UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)ToLua.ToObject(L, 1);
				UnityEngine.GameObject[] o = obj.GetRootGameObjects();
				ToLua.Push(L, o);
				ToLua.SetBack(L, 1, obj);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes(L, 1, typeof(UnityEngine.SceneManagement.Scene), typeof(System.Collections.Generic.List<UnityEngine.GameObject>)))
			{
				UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)ToLua.ToObject(L, 1);
				System.Collections.Generic.List<UnityEngine.GameObject> arg0 = (System.Collections.Generic.List<UnityEngine.GameObject>)ToLua.ToObject(L, 2);
				obj.GetRootGameObjects(arg0);
				ToLua.SetBack(L, 1, obj);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.SceneManagement.Scene.GetRootGameObjects");
			}
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_GetHashCode(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)ToLua.CheckObject(L, 1, typeof(UnityEngine.SceneManagement.Scene));
			int o = obj.GetHashCode();
			LuaDLL.lua_pushinteger(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_Equals(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)ToLua.ToObject(L, 1);
			object arg0 = ToLua.ToVarObject(L, 2);
			bool o = obj.Equals(arg0);
			LuaDLL.lua_pushboolean(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.SceneManagement.Scene arg0 = (UnityEngine.SceneManagement.Scene)ToLua.ToObject(L, 1);
			UnityEngine.SceneManagement.Scene arg1 = (UnityEngine.SceneManagement.Scene)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_ToString(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)ToLua.CheckObject(L, 1, typeof(UnityEngine.SceneManagement.Scene));
			string o = obj.ToString();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_GetType(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)ToLua.CheckObject(L, 1, typeof(UnityEngine.SceneManagement.Scene));
			System.Type o = obj.GetType();
			ToLua.Push(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_path(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)o;
			string ret = obj.path;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index path on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_name(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)o;
			string ret = obj.name;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index name on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_isLoaded(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)o;
			bool ret = obj.isLoaded;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index isLoaded on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_buildIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)o;
			int ret = obj.buildIndex;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index buildIndex on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_isDirty(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)o;
			bool ret = obj.isDirty;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index isDirty on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_rootCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)o;
			int ret = obj.rootCount;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index rootCount on a nil value" : e.Message);
		}
	}
}

