//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class CLASSTAG_UIProgressBarWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UIProgressBar), typeof(UIWidgetContainer));
		<PERSON><PERSON>("Set",FUNCTAG_Set);
		<PERSON><PERSON>("Start",FUNCTAG_Start);
		<PERSON><PERSON>("ForceUpdate",FUNCTAG_ForceUpdate);
		<PERSON><PERSON>("OnPan",FUNCTAG_OnPan);
		<PERSON><PERSON>("current",FUNCTAG_get_current, FUNCTAG_set_current);
		<PERSON><PERSON>("onDragFinished",FUNCTAG_get_onDragFinished, FUNCTAG_set_onDragFinished);
		<PERSON><PERSON>("thumb",FUNCTAG_get_thumb, FUNCTAG_set_thumb);
		<PERSON><PERSON>("minValue",FUNCTAG_get_minValue, FUNCTAG_set_minValue);
		<PERSON><PERSON>("maxValue",FUNCTAG_get_maxValue, FUNCTAG_set_maxValue);
		<PERSON><PERSON>("numberOfSteps",FUNCTAG_get_numberOfSteps, FUNCTAG_set_numberOfSteps);
		L.RegVar("onChange",FUNCTAG_get_onChange, FUNCTAG_set_onChange);
		L.RegVar("cachedTransform",FUNCTAG_get_cachedTransform, null);
		L.RegVar("cachedCamera",FUNCTAG_get_cachedCamera, null);
		L.RegVar("foregroundWidget",FUNCTAG_get_foregroundWidget, FUNCTAG_set_foregroundWidget);
		L.RegVar("backgroundWidget",FUNCTAG_get_backgroundWidget, FUNCTAG_set_backgroundWidget);
		L.RegVar("fillDirection",FUNCTAG_get_fillDirection, FUNCTAG_set_fillDirection);
		L.RegVar("value",FUNCTAG_get_value, FUNCTAG_set_value);
		L.RegVar("alpha",FUNCTAG_get_alpha, FUNCTAG_set_alpha);
		L.RegFunction("OnDragFinished",FUNCTAG_UIProgressBar_OnDragFinished);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_Set(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UIProgressBar obj = (UIProgressBar)ToLua.CheckObject(L, 1, typeof(UIProgressBar));
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
			obj.Set(arg0, arg1);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_Start(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UIProgressBar obj = (UIProgressBar)ToLua.CheckObject(L, 1, typeof(UIProgressBar));
			obj.Start();
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_ForceUpdate(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UIProgressBar obj = (UIProgressBar)ToLua.CheckObject(L, 1, typeof(UIProgressBar));
			obj.ForceUpdate();
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_OnPan(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UIProgressBar obj = (UIProgressBar)ToLua.CheckObject(L, 1, typeof(UIProgressBar));
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.OnPan(arg0);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_current(IntPtr L)
	{
		try
		{
			ToLua.Push(L, UIProgressBar.current);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_onDragFinished(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			UIProgressBar.OnDragFinished ret = obj.onDragFinished;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index onDragFinished on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_thumb(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			UnityEngine.Transform ret = obj.thumb;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index thumb on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_minValue(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			float ret = obj.minValue;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index minValue on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_maxValue(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			float ret = obj.maxValue;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index maxValue on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_numberOfSteps(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			int ret = obj.numberOfSteps;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index numberOfSteps on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_onChange(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			System.Collections.Generic.List<EventDelegate> ret = obj.onChange;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index onChange on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_cachedTransform(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			UnityEngine.Transform ret = obj.cachedTransform;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index cachedTransform on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_cachedCamera(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			UnityEngine.Camera ret = obj.cachedCamera;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index cachedCamera on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_foregroundWidget(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			UIWidget ret = obj.foregroundWidget;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index foregroundWidget on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_backgroundWidget(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			UIWidget ret = obj.backgroundWidget;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index backgroundWidget on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_fillDirection(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			UIProgressBar.FillDirection ret = obj.fillDirection;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index fillDirection on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_value(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			float ret = obj.value;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index value on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_get_alpha(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			float ret = obj.alpha;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index alpha on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_set_current(IntPtr L)
	{
		try
		{
			UIProgressBar arg0 = (UIProgressBar)ToLua.CheckUnityObject(L, 2, typeof(UIProgressBar));
			UIProgressBar.current = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_set_onDragFinished(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			UIProgressBar.OnDragFinished arg0 = null;
			LuaTypes funcType2 = LuaDLL.lua_type(L, 2);

			if (funcType2 != LuaTypes.LUA_TFUNCTION)
			{
				 arg0 = (UIProgressBar.OnDragFinished)ToLua.CheckObject(L, 2, typeof(UIProgressBar.OnDragFinished));
			}
			else
			{
				LuaFunction func = ToLua.ToLuaFunction(L, 2);
				arg0 = DelegateFactory.CreateDelegate(typeof(UIProgressBar.OnDragFinished), func) as UIProgressBar.OnDragFinished;
			}

			obj.onDragFinished = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index onDragFinished on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_set_thumb(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckUnityObject(L, 2, typeof(UnityEngine.Transform));
			obj.thumb = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index thumb on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_set_minValue(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.minValue = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index minValue on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_set_maxValue(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.maxValue = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index maxValue on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_set_numberOfSteps(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.numberOfSteps = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index numberOfSteps on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_set_onChange(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			System.Collections.Generic.List<EventDelegate> arg0 = (System.Collections.Generic.List<EventDelegate>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<EventDelegate>));
			obj.onChange = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index onChange on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_set_foregroundWidget(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			UIWidget arg0 = (UIWidget)ToLua.CheckUnityObject(L, 2, typeof(UIWidget));
			obj.foregroundWidget = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index foregroundWidget on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_set_backgroundWidget(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			UIWidget arg0 = (UIWidget)ToLua.CheckUnityObject(L, 2, typeof(UIWidget));
			obj.backgroundWidget = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index backgroundWidget on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_set_fillDirection(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			UIProgressBar.FillDirection arg0 = (UIProgressBar.FillDirection)(int)LuaDLL.lua_tonumber(L, 2);
			obj.fillDirection = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index fillDirection on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_set_value(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.value = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index value on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_set_alpha(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIProgressBar obj = (UIProgressBar)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.alpha = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o == null ? "attempt to index alpha on a nil value" : e.Message);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FUNCTAG_UIProgressBar_OnDragFinished(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateFactory.CreateDelegate(typeof(UIProgressBar.OnDragFinished), func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateFactory.CreateDelegate(typeof(UIProgressBar.OnDragFinished), func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

