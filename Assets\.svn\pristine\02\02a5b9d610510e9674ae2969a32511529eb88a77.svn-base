%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1001 &100100000
Prefab:
  m_ObjectHideFlags: 1
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications: []
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 0}
  m_RootGameObject: {fileID: 1000012118066058}
  m_IsPrefabParent: 1
--- !u!1 &1000010280338976
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 4000012862641712}
  - 114: {fileID: 114000011561332046}
  - 114: {fileID: 114000012895224770}
  - 65: {fileID: 65000012926552760}
  m_Layer: 5
  m_Name: CloseMask
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1000010523102144
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 4000014267500848}
  - 114: {fileID: 114000011041815212}
  m_Layer: 5
  m_Name: Sprite
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1000011184258812
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 4000010270699130}
  - 65: {fileID: 65000011758958488}
  - 114: {fileID: 114000011126696266}
  - 114: {fileID: 114000012970915222}
  m_Layer: 5
  m_Name: Container
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1000011993040672
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 4000012308409704}
  - 114: {fileID: 114000012672759096}
  - 114: {fileID: 114000012146244556}
  m_Layer: 5
  m_Name: Grid
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1000012064820302
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 4000010024922646}
  - 114: {fileID: 114000012456531162}
  m_Layer: 5
  m_Name: ItemGrid
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1000012118066058
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 4000011631675906}
  - 114: {fileID: 114000011136511388}
  - 114: {fileID: 114000011810598162}
  - 54: {fileID: 54000012008076306}
  m_Layer: 5
  m_Name: EqualRewardView
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1000012183054702
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 4000010573459790}
  - 114: {fileID: 114000010081693284}
  m_Layer: 5
  m_Name: Sprite
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1000012588466880
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 4000012717872254}
  - 114: {fileID: 114000014214108774}
  - 114: {fileID: 114000013670252954}
  m_Layer: 5
  m_Name: Scroll View
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1000012669650360
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 4000012180243508}
  m_Layer: 5
  m_Name: PagePart
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1000012687323816
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 4000013588602504}
  - 114: {fileID: 114000011308706108}
  m_Layer: 5
  m_Name: Sprite (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1000013358909172
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 4000013942290806}
  - 114: {fileID: 114000014135451708}
  - 114: {fileID: 114000010108494482}
  m_Layer: 5
  m_Name: AwardBox
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1000013470082204
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 4000010159744282}
  - 114: {fileID: 114000012184008684}
  m_Layer: 5
  m_Name: Sprite
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1000013708289530
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 4000011834346880}
  - 114: {fileID: 114000010775345548}
  m_Layer: 5
  m_Name: TitleLabel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1000014077858134
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 4000012298541532}
  - 114: {fileID: 114000013277463792}
  - 65: {fileID: 65000010107021562}
  - 114: {fileID: 114000011298671174}
  - 114: {fileID: 114000013169179610}
  m_Layer: 5
  m_Name: Container
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1000014151798748
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 4000010535907172}
  - 114: {fileID: 114000011048117642}
  m_Layer: 5
  m_Name: Label
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4000010024922646
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000012064820302}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 65.7, y: -82.8, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 4000013942290806}
  m_RootOrder: 0
--- !u!4 &4000010159744282
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000013470082204}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -123, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 4000010573459790}
  m_RootOrder: 0
--- !u!4 &4000010270699130
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000011184258812}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.9983999, y: 0.9983999, z: 0.9983999}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children:
  - {fileID: 4000012180243508}
  - {fileID: 4000011834346880}
  m_Father: {fileID: 4000011631675906}
  m_RootOrder: 1
--- !u!4 &4000010535907172
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000014151798748}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 14.2, y: -22.3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 4000013942290806}
  m_RootOrder: 1
--- !u!4 &4000010573459790
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000012183054702}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -4, y: 1.2, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children:
  - {fileID: 4000010159744282}
  - {fileID: 4000013588602504}
  m_Father: {fileID: 4000011834346880}
  m_RootOrder: 0
--- !u!4 &4000011631675906
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000012118066058}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children:
  - {fileID: 4000012862641712}
  - {fileID: 4000010270699130}
  m_Father: {fileID: 0}
  m_RootOrder: 0
--- !u!4 &4000011834346880
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000013708289530}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 239.3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children:
  - {fileID: 4000010573459790}
  m_Father: {fileID: 4000010270699130}
  m_RootOrder: 1
--- !u!4 &4000012180243508
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000012669650360}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 240, y: -13, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children:
  - {fileID: 4000012298541532}
  - {fileID: 4000012717872254}
  m_Father: {fileID: 4000010270699130}
  m_RootOrder: 0
--- !u!4 &4000012298541532
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000014077858134}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -248, y: -1, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 4000012180243508}
  m_RootOrder: 0
--- !u!4 &4000012308409704
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000011993040672}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -236, y: 221, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children:
  - {fileID: 4000013942290806}
  m_Father: {fileID: 4000012717872254}
  m_RootOrder: 0
--- !u!4 &4000012717872254
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000012588466880}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -242, y: -4, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children:
  - {fileID: 4000012308409704}
  m_Father: {fileID: 4000012180243508}
  m_RootOrder: 1
--- !u!4 &4000012862641712
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000010280338976}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.9983999, y: 0.9983999, z: 0.9983999}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 4000011631675906}
  m_RootOrder: 0
--- !u!4 &4000013588602504
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000012687323816}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 124, y: 0, z: 0}
  m_LocalScale: {x: 1.0012499, y: 1.0012499, z: 1.0012499}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 4000010573459790}
  m_RootOrder: 1
--- !u!4 &4000013942290806
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000013358909172}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children:
  - {fileID: 4000010024922646}
  - {fileID: 4000010535907172}
  - {fileID: 4000014267500848}
  m_Father: {fileID: 4000012308409704}
  m_RootOrder: 0
--- !u!4 &4000014267500848
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000010523102144}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 235, y: -68, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 4000013942290806}
  m_RootOrder: 2
--- !u!54 &54000012008076306
Rigidbody:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000012118066058}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &65000010107021562
BoxCollider:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000014077858134}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 490, y: 454, z: 0}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!65 &65000011758958488
BoxCollider:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000011184258812}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 495, y: 553, z: 0}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!65 &65000012926552760
BoxCollider:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000010280338976}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1334, y: 750, z: 0}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &114000010081693284
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000012183054702}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1b3dc54f924693f41b5cbecb267e647a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  leftAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  rightAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  bottomAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  topAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  updateAnchors: 1
  mColor: {r: 1, g: 1, b: 1, a: 1}
  mPivot: 4
  mWidth: 200
  mHeight: 50
  mDepth: 3
  autoResizeBoxCollider: 0
  hideIfOffScreen: 0
  keepAspectRatio: 0
  aspectRatio: 4
  ignoreCalcBounds: 0
  mType: 1
  mFillDirection: 4
  mFillAmount: 1
  mInvert: 0
  mFlip: 0
  mApplyGradient: 0
  mGradientTop: {r: 1, g: 1, b: 1, a: 1}
  mGradientBottom: {r: 0.7, g: 0.7, b: 0.7, a: 1}
  centerType: 1
  leftType: 1
  rightType: 1
  bottomType: 1
  topType: 1
  mAtlas: {fileID: 114000011015527676, guid: ac3c19386e6617e40ada7c8e793e9234, type: 2}
  mSpriteName: pic_tip_fenge
  mFillCenter: 1
--- !u!114 &114000010108494482
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000013358909172}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6c4e592b15e7ce5489c098d105388b79, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  objectArray:
  - id: 1
    gameObject: {fileID: 1000014151798748}
  - id: 2
    gameObject: {fileID: 1000012064820302}
--- !u!114 &114000010775345548
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000013708289530}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e9d0b5f3bbe925a408bd595c79d0bf63, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  leftAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  rightAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  bottomAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  topAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  updateAnchors: 1
  mColor: {r: 0.39607844, g: 0.2901961, b: 0.2, a: 1}
  mPivot: 4
  mWidth: 204
  mHeight: 34
  mDepth: 18
  autoResizeBoxCollider: 0
  hideIfOffScreen: 0
  keepAspectRatio: 0
  aspectRatio: 5
  ignoreCalcBounds: 0
  keepCrispWhenShrunk: 1
  mTrueTypeFont: {fileID: 0}
  mFont: {fileID: 11400000, guid: e7db66872f5bae042bd04a229e348576, type: 2}
  mText: "\u6BCF\u5468\u6392\u540D\u5956\u52B1"
  mFontSize: 34
  mFontStyle: 0
  mAlignment: 0
  mEncoding: 1
  mMaxLineCount: 0
  mEffectStyle: 0
  mEffectColor: {r: 0, g: 0, b: 0, a: 1}
  mSymbols: 1
  mEffectDistance: {x: 1, y: 1}
  mOverflow: 2
  mMaterial: {fileID: 0}
  mApplyGradient: 0
  mGradientTop: {r: 1, g: 1, b: 1, a: 1}
  mGradientBottom: {r: 0.7, g: 0.7, b: 0.7, a: 1}
  mSpacingX: 0
  mSpacingY: 0
  mUseFloatSpacing: 0
  mFloatSpacingX: 0
  mFloatSpacingY: 0
  mOverflowEllipsis: 0
  mOverflowWidth: 0
  mModifier: 0
  mTextBold: 0
  mTextStyle: 
  mShrinkToFit: 0
  mMaxLineWidth: 0
  mMaxLineHeight: 0
  mLineWidth: 0
  mMultiline: 1
--- !u!114 &114000011041815212
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000010523102144}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1b3dc54f924693f41b5cbecb267e647a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  leftAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  rightAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  bottomAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  topAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  updateAnchors: 1
  mColor: {r: 1, g: 1, b: 1, a: 1}
  mPivot: 4
  mWidth: 467
  mHeight: 129
  mDepth: 1
  autoResizeBoxCollider: 0
  hideIfOffScreen: 0
  keepAspectRatio: 0
  aspectRatio: 3.620155
  ignoreCalcBounds: 0
  mType: 1
  mFillDirection: 4
  mFillAmount: 1
  mInvert: 0
  mFlip: 0
  mApplyGradient: 0
  mGradientTop: {r: 1, g: 1, b: 1, a: 1}
  mGradientBottom: {r: 0.7, g: 0.7, b: 0.7, a: 1}
  centerType: 1
  leftType: 1
  rightType: 1
  bottomType: 1
  topType: 1
  mAtlas: {fileID: 114000010734364990, guid: e646804bff4ca3c4f95691956717f2b2, type: 2}
  mSpriteName: pic_yj_di
  mFillCenter: 1
--- !u!114 &114000011048117642
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000014151798748}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e9d0b5f3bbe925a408bd595c79d0bf63, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  leftAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  rightAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  bottomAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  topAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  updateAnchors: 1
  mColor: {r: 1, g: 0.9529412, b: 0.8509804, a: 1}
  mPivot: 3
  mWidth: 38
  mHeight: 22
  mDepth: 10
  autoResizeBoxCollider: 0
  hideIfOffScreen: 0
  keepAspectRatio: 0
  aspectRatio: 1.7272727
  ignoreCalcBounds: 0
  keepCrispWhenShrunk: 1
  mTrueTypeFont: {fileID: 0}
  mFont: {fileID: 11400000, guid: e7db66872f5bae042bd04a229e348576, type: 2}
  mText: dfdf
  mFontSize: 22
  mFontStyle: 0
  mAlignment: 0
  mEncoding: 1
  mMaxLineCount: 0
  mEffectStyle: 2
  mEffectColor: {r: 0.39607844, g: 0.29803923, b: 0.23137255, a: 1}
  mSymbols: 1
  mEffectDistance: {x: 2, y: 2}
  mOverflow: 2
  mMaterial: {fileID: 0}
  mApplyGradient: 0
  mGradientTop: {r: 1, g: 1, b: 1, a: 1}
  mGradientBottom: {r: 0.7, g: 0.7, b: 0.7, a: 1}
  mSpacingX: 0
  mSpacingY: 0
  mUseFloatSpacing: 0
  mFloatSpacingX: 0
  mFloatSpacingY: 0
  mOverflowEllipsis: 0
  mOverflowWidth: 0
  mModifier: 0
  mTextBold: 0
  mTextStyle: 
  mShrinkToFit: 0
  mMaxLineWidth: 0
  mMaxLineHeight: 0
  mLineWidth: 0
  mMultiline: 1
--- !u!114 &114000011126696266
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000011184258812}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1b3dc54f924693f41b5cbecb267e647a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  leftAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  rightAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  bottomAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  topAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  updateAnchors: 1
  mColor: {r: 1, g: 1, b: 1, a: 1}
  mPivot: 4
  mWidth: 495
  mHeight: 553
  mDepth: 0
  autoResizeBoxCollider: 1
  hideIfOffScreen: 0
  keepAspectRatio: 0
  aspectRatio: 0.8951175
  ignoreCalcBounds: 0
  mType: 1
  mFillDirection: 4
  mFillAmount: 1
  mInvert: 0
  mFlip: 0
  mApplyGradient: 0
  mGradientTop: {r: 1, g: 1, b: 1, a: 1}
  mGradientBottom: {r: 0.7, g: 0.7, b: 0.7, a: 1}
  centerType: 1
  leftType: 1
  rightType: 1
  bottomType: 1
  topType: 1
  mAtlas: {fileID: 114000011015527676, guid: ac3c19386e6617e40ada7c8e793e9234, type: 2}
  mSpriteName: bg_erji_di3
  mFillCenter: 1
--- !u!114 &114000011136511388
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000012118066058}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ae942c9068183dc40a9d01f648273726, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  leftAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  rightAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  bottomAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  topAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  updateAnchors: 1
  showInPanelTool: 1
  generateNormals: 0
  widgetsAreStatic: 0
  cullWhileDragging: 1
  alwaysOnScreen: 0
  anchorOffset: 0
  softBorderPadding: 1
  renderQueue: 0
  startingRenderQueue: 3000
  uiEffectDrawCallCount: 0
  mClipTexture: {fileID: 0}
  mAlpha: 1
  mClipping: 0
  mClipRange: {x: 0, y: 0, z: 300, w: 200}
  mClipSoftness: {x: 4, y: 4}
  mDepth: 1
  mSortingOrder: 0
  mSortingLayerName: 
  mClipOffset: {x: 0, y: 0}
--- !u!114 &114000011298671174
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000014077858134}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 559f26b2d8525c04db625d240c6473e2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  eventID: 0
--- !u!114 &114000011308706108
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000012687323816}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1b3dc54f924693f41b5cbecb267e647a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  leftAnchor:
    target: {fileID: 4000011834346880}
    relative: 1
    absolute: 3
  rightAnchor:
    target: {fileID: 4000011834346880}
    relative: 1
    absolute: 33
  bottomAnchor:
    target: {fileID: 4000011834346880}
    relative: 0
    absolute: -1
  topAnchor:
    target: {fileID: 4000011834346880}
    relative: 1
    absolute: 3
  updateAnchors: 1
  mColor: {r: 1, g: 1, b: 1, a: 1}
  mPivot: 4
  mWidth: 30
  mHeight: 38
  mDepth: 5
  autoResizeBoxCollider: 0
  hideIfOffScreen: 0
  keepAspectRatio: 0
  aspectRatio: 0.7894737
  ignoreCalcBounds: 0
  mType: 0
  mFillDirection: 4
  mFillAmount: 1
  mInvert: 0
  mFlip: 1
  mApplyGradient: 0
  mGradientTop: {r: 1, g: 1, b: 1, a: 1}
  mGradientBottom: {r: 0.7, g: 0.7, b: 0.7, a: 1}
  centerType: 1
  leftType: 1
  rightType: 1
  bottomType: 1
  topType: 1
  mAtlas: {fileID: 114000011015527676, guid: ac3c19386e6617e40ada7c8e793e9234, type: 2}
  mSpriteName: pic_tips_biaoti_diwen_1
  mFillCenter: 1
--- !u!114 &114000011561332046
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000010280338976}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 858a20c1b21a3f94bb5b2d3b901c9aaf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  leftAnchor:
    target: {fileID: 4000011631675906}
    relative: 0
    absolute: 0
  rightAnchor:
    target: {fileID: 4000011631675906}
    relative: 1
    absolute: 0
  bottomAnchor:
    target: {fileID: 4000011631675906}
    relative: 0
    absolute: 0
  topAnchor:
    target: {fileID: 4000011631675906}
    relative: 1
    absolute: 0
  updateAnchors: 0
  mColor: {r: 1, g: 1, b: 1, a: 1}
  mPivot: 4
  mWidth: 1334
  mHeight: 750
  mDepth: 0
  autoResizeBoxCollider: 1
  hideIfOffScreen: 0
  keepAspectRatio: 0
  aspectRatio: 1.7786666
  ignoreCalcBounds: 0
--- !u!114 &114000011810598162
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000012118066058}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6c4e592b15e7ce5489c098d105388b79, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  objectArray:
  - id: 1
    gameObject: {fileID: 1000013708289530}
  - id: 2
    gameObject: {fileID: 1000011993040672}
  - id: 3
    gameObject: {fileID: 1000011339485748, guid: 7517b2aee09b942448521e0eef32a9cd,
      type: 2}
  - id: 4
    gameObject: {fileID: 1000013358909172}
  - id: 5
    gameObject: {fileID: 1000010280338976}
--- !u!114 &114000012146244556
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000011993040672}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 858a20c1b21a3f94bb5b2d3b901c9aaf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  leftAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  rightAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  bottomAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  topAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  updateAnchors: 1
  mColor: {r: 1, g: 1, b: 1, a: 1}
  mPivot: 0
  mWidth: 480
  mHeight: 450
  mDepth: 0
  autoResizeBoxCollider: 0
  hideIfOffScreen: 0
  keepAspectRatio: 0
  aspectRatio: 1.0666667
  ignoreCalcBounds: 0
--- !u!114 &114000012184008684
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000013470082204}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1b3dc54f924693f41b5cbecb267e647a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  leftAnchor:
    target: {fileID: 4000011834346880}
    relative: 0
    absolute: -40
  rightAnchor:
    target: {fileID: 4000011834346880}
    relative: 0
    absolute: -10
  bottomAnchor:
    target: {fileID: 4000011834346880}
    relative: 0
    absolute: -1
  topAnchor:
    target: {fileID: 4000011834346880}
    relative: 1
    absolute: 3
  updateAnchors: 1
  mColor: {r: 1, g: 1, b: 1, a: 1}
  mPivot: 4
  mWidth: 30
  mHeight: 38
  mDepth: 5
  autoResizeBoxCollider: 0
  hideIfOffScreen: 0
  keepAspectRatio: 0
  aspectRatio: 0.7894737
  ignoreCalcBounds: 0
  mType: 0
  mFillDirection: 4
  mFillAmount: 1
  mInvert: 0
  mFlip: 0
  mApplyGradient: 0
  mGradientTop: {r: 1, g: 1, b: 1, a: 1}
  mGradientBottom: {r: 0.7, g: 0.7, b: 0.7, a: 1}
  centerType: 1
  leftType: 1
  rightType: 1
  bottomType: 1
  topType: 1
  mAtlas: {fileID: 114000011015527676, guid: ac3c19386e6617e40ada7c8e793e9234, type: 2}
  mSpriteName: pic_tips_biaoti_diwen_1
  mFillCenter: 1
--- !u!114 &114000012456531162
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000012064820302}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 07c4de3b4b6fe9045b059ee627c100df, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  arrangement: 0
  sorting: 0
  pivot: 0
  maxPerLine: 0
  cellWidth: 110
  cellHeight: 200
  animateSmoothly: 0
  hideInactive: 0
  keepWithinPanel: 0
  reposition: 1
  sorted: 0
--- !u!114 &114000012672759096
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000011993040672}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 07c4de3b4b6fe9045b059ee627c100df, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  arrangement: 0
  sorting: 0
  pivot: 0
  maxPerLine: 1
  cellWidth: 200
  cellHeight: 135
  animateSmoothly: 0
  hideInactive: 1
  keepWithinPanel: 0
  reposition: 1
  sorted: 0
--- !u!114 &114000012895224770
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000010280338976}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 559f26b2d8525c04db625d240c6473e2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  eventID: 0
--- !u!114 &114000012970915222
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000011184258812}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 559f26b2d8525c04db625d240c6473e2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  eventID: 0
--- !u!114 &114000013169179610
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000014077858134}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f02842fa4878db54f9587ff4de7d9f2d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  scrollView: {fileID: 114000013670252954}
  draggablePanel: {fileID: 0}
--- !u!114 &114000013277463792
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000014077858134}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 858a20c1b21a3f94bb5b2d3b901c9aaf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  leftAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  rightAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  bottomAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  topAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  updateAnchors: 1
  mColor: {r: 1, g: 1, b: 1, a: 1}
  mPivot: 4
  mWidth: 490
  mHeight: 454
  mDepth: 2
  autoResizeBoxCollider: 1
  hideIfOffScreen: 0
  keepAspectRatio: 0
  aspectRatio: 1.0792952
  ignoreCalcBounds: 0
--- !u!114 &114000013670252954
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000012588466880}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d81807633ea807d4c8e3fff7e10c6000, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  movement: 1
  dragEffect: 2
  restrictWithinPanel: 1
  constrainOnDrag: 0
  disableDragIfFits: 0
  smoothDragStart: 1
  iOSDragEmulation: 1
  scrollWheelFactor: 0.25
  momentumAmount: 35
  dampenStrength: 9
  horizontalScrollBar: {fileID: 0}
  verticalScrollBar: {fileID: 0}
  showScrollBars: 1
  customMovement: {x: 1, y: 0}
  contentPivot: 0
  scale: {x: 0, y: 0, z: 0}
  relativePositionOnReset: {x: 0, y: 0}
  centerOnChild: {fileID: 0}
--- !u!114 &114000014135451708
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000013358909172}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 858a20c1b21a3f94bb5b2d3b901c9aaf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  leftAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  rightAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  bottomAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  topAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  updateAnchors: 1
  mColor: {r: 1, g: 1, b: 1, a: 1}
  mPivot: 0
  mWidth: 470
  mHeight: 130
  mDepth: 0
  autoResizeBoxCollider: 0
  hideIfOffScreen: 0
  keepAspectRatio: 0
  aspectRatio: 3.6153846
  ignoreCalcBounds: 0
--- !u!114 &114000014214108774
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1000012588466880}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ae942c9068183dc40a9d01f648273726, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  leftAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  rightAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  bottomAnchor:
    target: {fileID: 0}
    relative: 0
    absolute: 0
  topAnchor:
    target: {fileID: 0}
    relative: 1
    absolute: 0
  updateAnchors: 1
  showInPanelTool: 1
  generateNormals: 0
  widgetsAreStatic: 0
  cullWhileDragging: 1
  alwaysOnScreen: 0
  anchorOffset: 0
  softBorderPadding: 1
  renderQueue: 0
  startingRenderQueue: 3002
  uiEffectDrawCallCount: 0
  mClipTexture: {fileID: 0}
  mAlpha: 1
  mClipping: 3
  mClipRange: {x: 0, y: 0, z: 480, w: 450}
  mClipSoftness: {x: 4, y: 4}
  mDepth: 2
  mSortingOrder: 0
  mSortingLayerName: 
  mClipOffset: {x: 0, y: 0}
