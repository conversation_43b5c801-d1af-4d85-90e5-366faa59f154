-- ./excel/item/equip.xlsx
return {

    [18000] = {
        abnormal_attr_ratio = 0.0,
        attack = 7,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在武器上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 100,
        gain_way_id = {456, 432, 45, 41},
        gift_price = 0,
        giftable = 0,
        icon = 18000,
        id = 18000,
        introduction = "攻击+11",
        level = 1,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "1级绯红宝石",
        nimbus = 0,
        pos = 1,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 800,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 300,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18001] = {
        abnormal_attr_ratio = 0.0,
        attack = 10,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在武器上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 300,
        gain_way_id = {456, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18001,
        id = 18001,
        introduction = "攻击+18",
        level = 2,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "2级绯红宝石",
        nimbus = 0,
        pos = 1,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 1600,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 900,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18002] = {
        abnormal_attr_ratio = 0.0,
        attack = 14,
        bag_show_type = 2,
        buy_cost = {{["limit"] = 1000, ["coin"] = 1, ["cost"] = 36, ["discount"] = 0}},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在武器上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 900,
        gain_way_id = {456, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18002,
        id = 18002,
        introduction = "攻击+27",
        level = 3,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "3级绯红宝石",
        nimbus = 0,
        pos = 1,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 3200,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 2700,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18003] = {
        abnormal_attr_ratio = 0.0,
        attack = 20,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在武器上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 2700,
        gain_way_id = {456, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18003,
        id = 18003,
        introduction = "攻击+42",
        level = 4,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "4级绯红宝石",
        nimbus = 0,
        pos = 1,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 6400,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 8100,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18004] = {
        abnormal_attr_ratio = 0.0,
        attack = 30,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在武器上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 8100,
        gain_way_id = {456, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18004,
        id = 18004,
        introduction = "攻击+65",
        level = 5,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "5级绯红宝石",
        nimbus = 0,
        pos = 1,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 12800,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 24300,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18005] = {
        abnormal_attr_ratio = 0.0,
        attack = 44,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在武器上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 24300,
        gain_way_id = {456, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18005,
        id = 18005,
        introduction = "攻击+91",
        level = 6,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "6级绯红宝石",
        nimbus = 0,
        pos = 1,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 25600,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 72900,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18006] = {
        abnormal_attr_ratio = 0.0,
        attack = 66,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在武器上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 72900,
        gain_way_id = {456, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18006,
        id = 18006,
        introduction = "攻击+110",
        level = 7,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "7级绯红宝石",
        nimbus = 0,
        pos = 1,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 51200,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 218700,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18007] = {
        abnormal_attr_ratio = 0.0,
        attack = 98,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在武器上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 218700,
        gain_way_id = {456, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18007,
        id = 18007,
        introduction = "攻击+133",
        level = 8,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "8级绯红宝石",
        nimbus = 0,
        pos = 1,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 102400,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 656100,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18008] = {
        abnormal_attr_ratio = 0.0,
        attack = 146,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在武器上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 656100,
        gain_way_id = {456, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18008,
        id = 18008,
        introduction = "攻击+161",
        level = 9,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "9级绯红宝石",
        nimbus = 0,
        pos = 1,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 204800,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 1968300,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18009] = {
        abnormal_attr_ratio = 0.0,
        attack = 218,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在武器上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 1968300,
        gain_way_id = {456, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18009,
        id = 18009,
        introduction = "攻击+218",
        level = 10,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "10级绯红宝石",
        nimbus = 0,
        pos = 1,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 409600,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 5904900,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18100] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在项链上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 100,
        gain_way_id = {459, 435, 45, 41},
        gift_price = 0,
        giftable = 0,
        icon = 18300,
        id = 18100,
        introduction = "抗暴率+0.2%",
        level = 1,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "1级八云宝石",
        nimbus = 0,
        pos = 2,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 20.0,
        sale_price = 400,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 300,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18101] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在项链上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 300,
        gain_way_id = {459, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18301,
        id = 18101,
        introduction = "抗暴率+0.3%",
        level = 2,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "2级八云宝石",
        nimbus = 0,
        pos = 2,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 30.0,
        sale_price = 800,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 900,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18102] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {{["limit"] = 1000, ["coin"] = 1, ["cost"] = 18, ["discount"] = 0}},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在项链上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 900,
        gain_way_id = {459, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18302,
        id = 18102,
        introduction = "抗暴率+0.4%",
        level = 3,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "3级八云宝石",
        nimbus = 0,
        pos = 2,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 40.0,
        sale_price = 1600,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 2700,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18103] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在项链上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 2700,
        gain_way_id = {459, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18303,
        id = 18103,
        introduction = "抗暴率+0.5%",
        level = 4,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "4级八云宝石",
        nimbus = 0,
        pos = 2,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 50.0,
        sale_price = 3200,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 8100,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18104] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在项链上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 8100,
        gain_way_id = {459, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18304,
        id = 18104,
        introduction = "抗暴率+0.6%",
        level = 5,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "5级八云宝石",
        nimbus = 0,
        pos = 2,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 60.0,
        sale_price = 6400,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 24300,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18105] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在项链上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 24300,
        gain_way_id = {459, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18305,
        id = 18105,
        introduction = "抗暴率+0.7%",
        level = 6,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "6级八云宝石",
        nimbus = 0,
        pos = 2,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 70.0,
        sale_price = 12800,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 72900,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18106] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在项链上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 72900,
        gain_way_id = {459, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18306,
        id = 18106,
        introduction = "抗暴率+0.8%",
        level = 7,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "7级八云宝石",
        nimbus = 0,
        pos = 2,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 80.0,
        sale_price = 25600,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 218700,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18107] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在项链上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 218700,
        gain_way_id = {459, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18307,
        id = 18107,
        introduction = "抗暴率+0.9%",
        level = 8,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "8级八云宝石",
        nimbus = 0,
        pos = 2,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 90.0,
        sale_price = 51200,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 656100,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18108] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在项链上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 656100,
        gain_way_id = {459, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18308,
        id = 18108,
        introduction = "抗暴率+1.1%",
        level = 9,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "9级八云宝石",
        nimbus = 0,
        pos = 2,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 110.0,
        sale_price = 102400,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 1968300,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18109] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在项链上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 1968300,
        gain_way_id = {459, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18309,
        id = 18109,
        introduction = "抗暴率+1.4%",
        level = 10,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "10级八云宝石",
        nimbus = 0,
        pos = 2,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 140.0,
        sale_price = 204800,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 5904900,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18200] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 2,
        description = "\n镶嵌：可镶嵌在衣服上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 100,
        gain_way_id = {460, 436, 45, 41},
        gift_price = 0,
        giftable = 0,
        icon = 18100,
        id = 18200,
        introduction = "防御+2",
        level = 1,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "1级双生宝石",
        nimbus = 0,
        pos = 3,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 400,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 300,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18201] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 4,
        description = "\n镶嵌：可镶嵌在衣服上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 300,
        gain_way_id = {460, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18101,
        id = 18201,
        introduction = "防御+4",
        level = 2,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "2级双生宝石",
        nimbus = 0,
        pos = 3,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 800,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 900,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18202] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {{["limit"] = 1000, ["coin"] = 1, ["cost"] = 18, ["discount"] = 0}},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 6,
        description = "\n镶嵌：可镶嵌在衣服上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 900,
        gain_way_id = {460, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18102,
        id = 18202,
        introduction = "防御+6",
        level = 3,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "3级双生宝石",
        nimbus = 0,
        pos = 3,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 1600,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 2700,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18203] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 8,
        description = "\n镶嵌：可镶嵌在衣服上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 2700,
        gain_way_id = {460, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18103,
        id = 18203,
        introduction = "防御+8",
        level = 4,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "4级双生宝石",
        nimbus = 0,
        pos = 3,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 3200,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 8100,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18204] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 10,
        description = "\n镶嵌：可镶嵌在衣服上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 8100,
        gain_way_id = {460, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18104,
        id = 18204,
        introduction = "防御+10",
        level = 5,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "5级双生宝石",
        nimbus = 0,
        pos = 3,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 6400,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 24300,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18205] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 12,
        description = "\n镶嵌：可镶嵌在衣服上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 24300,
        gain_way_id = {460, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18105,
        id = 18205,
        introduction = "防御+12",
        level = 6,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "6级双生宝石",
        nimbus = 0,
        pos = 3,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 12800,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 72900,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18206] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 14,
        description = "\n镶嵌：可镶嵌在衣服上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 72900,
        gain_way_id = {460, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18106,
        id = 18206,
        introduction = "防御+14",
        level = 7,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "7级双生宝石",
        nimbus = 0,
        pos = 3,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 25600,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 218700,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18207] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 16,
        description = "\n镶嵌：可镶嵌在衣服上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 218700,
        gain_way_id = {460, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18107,
        id = 18207,
        introduction = "防御+16",
        level = 8,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "8级双生宝石",
        nimbus = 0,
        pos = 3,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 51200,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 656100,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18208] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 18,
        description = "\n镶嵌：可镶嵌在衣服上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 656100,
        gain_way_id = {460, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18108,
        id = 18208,
        introduction = "防御+18",
        level = 9,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "9级双生宝石",
        nimbus = 0,
        pos = 3,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 102400,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 1968300,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18209] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 24,
        description = "\n镶嵌：可镶嵌在衣服上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 1968300,
        gain_way_id = {460, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18109,
        id = 18209,
        introduction = "防御+24",
        level = 10,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "10级双生宝石",
        nimbus = 0,
        pos = 3,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 204800,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 5904900,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18300] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 30.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在戒指上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 100,
        gain_way_id = {457, 433, 45, 41},
        gift_price = 0,
        giftable = 0,
        icon = 18200,
        id = 18300,
        introduction = "暴击率+0.3%",
        level = 1,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "1级黄金宝石",
        nimbus = 0,
        pos = 4,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 800,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 300,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18301] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 50.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在戒指上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 300,
        gain_way_id = {457, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18201,
        id = 18301,
        introduction = "暴击率+0.5%",
        level = 2,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "2级黄金宝石",
        nimbus = 0,
        pos = 4,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 1600,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 900,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18302] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {{["limit"] = 1000, ["coin"] = 1, ["cost"] = 36, ["discount"] = 0}},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 60.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在戒指上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 900,
        gain_way_id = {457, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18202,
        id = 18302,
        introduction = "暴击率+0.6%",
        level = 3,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "3级黄金宝石",
        nimbus = 0,
        pos = 4,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 3200,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 2700,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18303] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 80.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在戒指上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 2700,
        gain_way_id = {457, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18203,
        id = 18303,
        introduction = "暴击率+0.8%",
        level = 4,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "4级黄金宝石",
        nimbus = 0,
        pos = 4,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 6400,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 8100,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18304] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 100.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在戒指上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 8100,
        gain_way_id = {457, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18204,
        id = 18304,
        introduction = "暴击率+1%",
        level = 5,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "5级黄金宝石",
        nimbus = 0,
        pos = 4,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 12800,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 24300,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18305] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 130.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在戒指上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 24300,
        gain_way_id = {457, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18205,
        id = 18305,
        introduction = "暴击率+1.3%",
        level = 6,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "6级黄金宝石",
        nimbus = 0,
        pos = 4,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 25600,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 72900,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18306] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 150.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在戒指上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 72900,
        gain_way_id = {457, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18206,
        id = 18306,
        introduction = "暴击率+1.5%",
        level = 7,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "7级黄金宝石",
        nimbus = 0,
        pos = 4,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 51200,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 218700,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18307] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 170.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在戒指上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 218700,
        gain_way_id = {457, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18207,
        id = 18307,
        introduction = "暴击率+1.7%",
        level = 8,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "8级黄金宝石",
        nimbus = 0,
        pos = 4,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 102400,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 656100,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18308] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 200.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在戒指上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 656100,
        gain_way_id = {457, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18208,
        id = 18308,
        introduction = "暴击率+2%",
        level = 9,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "9级黄金宝石",
        nimbus = 0,
        pos = 4,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 204800,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 1968300,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18309] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 260.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在戒指上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 1968300,
        gain_way_id = {457, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18209,
        id = 18309,
        introduction = "暴击率+2.6%",
        level = 10,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "10级黄金宝石",
        nimbus = 0,
        pos = 4,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 409600,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 5904900,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18400] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在腰带上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 100,
        gain_way_id = {461, 437, 45, 41},
        gift_price = 0,
        giftable = 0,
        icon = 18400,
        id = 18400,
        introduction = "气血+65",
        level = 1,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 53,
        min_grade = 30,
        name = "1级翠星宝石",
        nimbus = 0,
        pos = 5,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 400,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 300,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18401] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在腰带上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 300,
        gain_way_id = {461, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18401,
        id = 18401,
        introduction = "气血+109",
        level = 2,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 79,
        min_grade = 30,
        name = "2级翠星宝石",
        nimbus = 0,
        pos = 5,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 800,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 900,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18402] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {{["limit"] = 1000, ["coin"] = 1, ["cost"] = 18, ["discount"] = 0}},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在腰带上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 900,
        gain_way_id = {461, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18402,
        id = 18402,
        introduction = "气血+166",
        level = 3,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 118,
        min_grade = 30,
        name = "3级翠星宝石",
        nimbus = 0,
        pos = 5,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 1600,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 2700,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18403] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在腰带上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 2700,
        gain_way_id = {461, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18403,
        id = 18403,
        introduction = "气血+272",
        level = 4,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 176,
        min_grade = 30,
        name = "4级翠星宝石",
        nimbus = 0,
        pos = 5,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 3200,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 8100,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18404] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在腰带上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 8100,
        gain_way_id = {461, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18404,
        id = 18404,
        introduction = "气血+434",
        level = 5,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 264,
        min_grade = 30,
        name = "5级翠星宝石",
        nimbus = 0,
        pos = 5,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 6400,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 24300,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18405] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在腰带上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 24300,
        gain_way_id = {461, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18405,
        id = 18405,
        introduction = "气血+640",
        level = 6,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 395,
        min_grade = 30,
        name = "6级翠星宝石",
        nimbus = 0,
        pos = 5,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 12800,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 72900,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18406] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在腰带上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 72900,
        gain_way_id = {461, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18406,
        id = 18406,
        introduction = "气血+807",
        level = 7,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 592,
        min_grade = 30,
        name = "7级翠星宝石",
        nimbus = 0,
        pos = 5,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 25600,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 218700,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18407] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在腰带上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 218700,
        gain_way_id = {461, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18407,
        id = 18407,
        introduction = "气血+1018",
        level = 8,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 888,
        min_grade = 30,
        name = "8级翠星宝石",
        nimbus = 0,
        pos = 5,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 51200,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 656100,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18408] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在腰带上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 656100,
        gain_way_id = {461, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18408,
        id = 18408,
        introduction = "气血+1311",
        level = 9,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 1332,
        min_grade = 30,
        name = "9级翠星宝石",
        nimbus = 0,
        pos = 5,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 102400,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 1968300,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18409] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在腰带上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 1968300,
        gain_way_id = {461, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18409,
        id = 18409,
        introduction = "气血+1998",
        level = 10,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 1998,
        min_grade = 30,
        name = "10级翠星宝石",
        nimbus = 0,
        pos = 5,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 204800,
        sort = 4,
        speed = 0,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 5904900,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18500] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在鞋子上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 100,
        gain_way_id = {458, 434, 45, 41},
        gift_price = 0,
        giftable = 0,
        icon = 18500,
        id = 18500,
        introduction = "速度+6",
        level = 1,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "1级疾风宝石",
        nimbus = 0,
        pos = 6,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 800,
        sort = 4,
        speed = 6,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 300,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18501] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在鞋子上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 300,
        gain_way_id = {458, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18501,
        id = 18501,
        introduction = "速度+8",
        level = 2,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "2级疾风宝石",
        nimbus = 0,
        pos = 6,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 1600,
        sort = 4,
        speed = 8,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 900,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18502] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {{["limit"] = 1000, ["coin"] = 1, ["cost"] = 36, ["discount"] = 0}},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在鞋子上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 900,
        gain_way_id = {458, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18502,
        id = 18502,
        introduction = "速度+10",
        level = 3,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "3级疾风宝石",
        nimbus = 0,
        pos = 6,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 3200,
        sort = 4,
        speed = 10,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 2700,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18503] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在鞋子上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 2700,
        gain_way_id = {458, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18503,
        id = 18503,
        introduction = "速度+14",
        level = 4,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "4级疾风宝石",
        nimbus = 0,
        pos = 6,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 6400,
        sort = 4,
        speed = 14,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 8100,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18504] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在鞋子上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 8100,
        gain_way_id = {458, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18504,
        id = 18504,
        introduction = "速度+19",
        level = 5,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "5级疾风宝石",
        nimbus = 0,
        pos = 6,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 12800,
        sort = 4,
        speed = 19,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 24300,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18505] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在鞋子上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 24300,
        gain_way_id = {458, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18505,
        id = 18505,
        introduction = "速度+23",
        level = 6,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "6级疾风宝石",
        nimbus = 0,
        pos = 6,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 25600,
        sort = 4,
        speed = 23,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 72900,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18506] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在鞋子上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 72900,
        gain_way_id = {458, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18506,
        id = 18506,
        introduction = "速度+27",
        level = 7,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "7级疾风宝石",
        nimbus = 0,
        pos = 6,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 51200,
        sort = 4,
        speed = 27,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 218700,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18507] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在鞋子上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 218700,
        gain_way_id = {458, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18507,
        id = 18507,
        introduction = "速度+32",
        level = 8,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "8级疾风宝石",
        nimbus = 0,
        pos = 6,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 102400,
        sort = 4,
        speed = 32,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 656100,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18508] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在鞋子上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 656100,
        gain_way_id = {458, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18508,
        id = 18508,
        introduction = "速度+38",
        level = 9,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "9级疾风宝石",
        nimbus = 0,
        pos = 6,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 204800,
        sort = 4,
        speed = 38,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 1968300,
        use_cost = 1,
        use_type = "forge_gem",
    },

    [18509] = {
        abnormal_attr_ratio = 0.0,
        attack = 0,
        bag_show_type = 2,
        buy_cost = {},
        buy_price = 0,
        can_store = 1,
        circulation_type = 0,
        critical_damage = 0.0,
        critical_ratio = 0.0,
        cure_critical_ratio = 0.0,
        defense = 0,
        description = "\n镶嵌：可镶嵌在鞋子上  \n幽溟镇遗址中发现的上古宝石。",
        exp = 1968300,
        gain_way_id = {458, 432, 45, 41, 30004},
        gift_price = 0,
        giftable = 0,
        icon = 18509,
        id = 18509,
        introduction = "速度+49",
        level = 10,
        max_grade = 200,
        max_overlay = 999,
        maxhp = 0,
        min_grade = 30,
        name = "10级疾风宝石",
        nimbus = 0,
        pos = 6,
        quality = 2,
        quickable = 0,
        res_abnormal_ratio = 0.0,
        res_critical_ratio = 0.0,
        sale_price = 409600,
        sort = 4,
        speed = 49,
        stallable = 0,
        taskid = 0,
        type = 3,
        up_level_exp = 5904900,
        use_cost = 1,
        use_type = "forge_gem",
    },

}
