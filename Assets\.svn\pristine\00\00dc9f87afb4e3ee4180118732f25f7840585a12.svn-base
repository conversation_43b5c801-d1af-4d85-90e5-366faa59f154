module(...)
--magic editor build
DATA={
	atk_stophit=false,
	cmds={
		[1]={
			args={
				add_type=[[merge]],
				condition_name=[[screen1024]],
				false_group=[[1]],
				group_type=[[condition]],
				true_group=[[1024]],
			},
			func_name=[[GroupCmd]],
			start_time=0,
		},
		[2]={args={time=2.78,},func_name=[[HideUI]],start_time=0,},
		[3]={args={player_swipe=false,},func_name=[[CameraLock]],start_time=0,},
		[4]={
			args={path=[[UI/Magic/WarStartView.prefab]],time=1,},
			func_name=[[LoadUI]],
			start_time=0.5,
		},
		[5]={args={path_percent=0,},func_name=[[CameraPathPercent]],start_time=3.21,},
		[6]={args={player_swipe=false,},func_name=[[CameraLock]],start_time=3.211,},
		[7]={args={player_swipe=true,},func_name=[[CameraLock]],start_time=3.212,},
		[8]={args={},func_name=[[End]],start_time=3.3,},
	},
	group_cmds={
		["1"]={
			[1]={
				args={
					begin_prepare=[[97_01]],
					begin_type=[[begin_prepare]],
					calc_face=false,
					ease_type=[[OutSine]],
					end_prepare=[[97_02]],
					end_type=[[end_prepare]],
					excutor=[[camobj]],
					look_at_pos=false,
					move_time=1,
					move_type=[[line]],
				},
				func_name=[[Move]],
				start_time=2.2,
			},
			[2]={
				args={excutor=[[camobj]],face_to=[[prepare]],prepare_pos=[[97_02]],time=1,},
				func_name=[[FaceTo]],
				start_time=2.201,
			},
		},
		["1024"]={
			[1]={
				args={
					begin_prepare=[[97_03]],
					begin_type=[[begin_prepare]],
					calc_face=false,
					ease_type=[[OutSine]],
					end_prepare=[[97_04]],
					end_type=[[end_prepare]],
					excutor=[[camobj]],
					look_at_pos=false,
					move_time=1,
					move_type=[[line]],
				},
				func_name=[[Move]],
				start_time=2.2,
			},
			[2]={
				args={excutor=[[camobj]],face_to=[[prepare]],prepare_pos=[[97_04]],time=1,},
				func_name=[[FaceTo]],
				start_time=2.201,
			},
		},
	},
	pre_load_res={[1]=[[UI/Magic/WarStartView.prefab]],},
	run_env=[[war]],
	type=1,
	wait_goback=false,
}
