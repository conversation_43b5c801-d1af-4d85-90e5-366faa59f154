fileFormatVersion: 2
guid: 107e3027d967d5d499dfc6a29e3c0486
timeCreated: **********
licenseType: Pro
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: adorn_2
    100002: Bip001
    100004: Bip001 Footsteps
    100006: Bip001 Head
    100008: Bip001 HeadNub
    100010: Bip001 L Calf
    100012: Bip001 L Clavicle
    100014: Bip001 L Finger0
    100016: Bip001 L Finger01
    100018: Bip001 L Finger0Nub
    100020: Bip001 L Finger1
    100022: Bip001 L Finger11
    100024: Bip001 L Finger1Nub
    100026: Bip001 L Foot
    100028: Bip001 L Forearm
    100030: Bip001 L Hand
    100032: Bip001 L Thigh
    100034: Bip001 L Toe0
    100036: Bip001 L Toe0Nub
    100038: Bip001 L UpperArm
    100040: Bip001 Neck
    100042: Bip001 Pelvis
    100044: Bip001 Prop1
    100046: Bip001 Prop2
    100048: Bip001 R Calf
    100050: Bip001 R Clavicle
    100052: Bip001 R Finger0
    100054: Bip001 R Finger01
    100056: Bip001 R Finger0Nub
    100058: Bip001 R Finger1
    100060: Bip001 R Finger11
    100062: Bip001 R Finger1Nub
    100064: Bip001 R Foot
    100066: Bip001 R Forearm
    100068: Bip001 R Hand
    100070: Bip001 R Thigh
    100072: Bip001 R Toe0
    100074: Bip001 R Toe0Nub
    100076: Bip001 R UpperArm
    100078: Bip001 Spine
    100080: Bip001 Spine1
    100082: Bone001
    100084: Bone002
    100086: Bone003
    100088: Bone004
    100090: Bone005
    100092: Bone006
    100094: Bone007
    100096: Bone008
    100098: Bone009
    100100: Bone010
    100102: Bone011
    100104: Bone012
    100106: Bone013
    100108: Bone014
    100110: Bone015
    100112: Bone016
    100114: Bone017
    100116: Bone018
    100118: Bone066
    100120: Bone067
    100122: Bone068
    100124: Bone069
    100126: Bone070
    100128: Bone071
    100130: Bone072
    100132: Bone073
    100134: model150
    100136: //RootNode
    100138: mount_1
    400000: adorn_2
    400002: Bip001
    400004: Bip001 Footsteps
    400006: Bip001 Head
    400008: Bip001 HeadNub
    400010: Bip001 L Calf
    400012: Bip001 L Clavicle
    400014: Bip001 L Finger0
    400016: Bip001 L Finger01
    400018: Bip001 L Finger0Nub
    400020: Bip001 L Finger1
    400022: Bip001 L Finger11
    400024: Bip001 L Finger1Nub
    400026: Bip001 L Foot
    400028: Bip001 L Forearm
    400030: Bip001 L Hand
    400032: Bip001 L Thigh
    400034: Bip001 L Toe0
    400036: Bip001 L Toe0Nub
    400038: Bip001 L UpperArm
    400040: Bip001 Neck
    400042: Bip001 Pelvis
    400044: Bip001 Prop1
    400046: Bip001 Prop2
    400048: Bip001 R Calf
    400050: Bip001 R Clavicle
    400052: Bip001 R Finger0
    400054: Bip001 R Finger01
    400056: Bip001 R Finger0Nub
    400058: Bip001 R Finger1
    400060: Bip001 R Finger11
    400062: Bip001 R Finger1Nub
    400064: Bip001 R Foot
    400066: Bip001 R Forearm
    400068: Bip001 R Hand
    400070: Bip001 R Thigh
    400072: Bip001 R Toe0
    400074: Bip001 R Toe0Nub
    400076: Bip001 R UpperArm
    400078: Bip001 Spine
    400080: Bip001 Spine1
    400082: Bone001
    400084: Bone002
    400086: Bone003
    400088: Bone004
    400090: Bone005
    400092: Bone006
    400094: Bone007
    400096: Bone008
    400098: Bone009
    400100: Bone010
    400102: Bone011
    400104: Bone012
    400106: Bone013
    400108: Bone014
    400110: Bone015
    400112: Bone016
    400114: Bone017
    400116: Bone018
    400118: Bone066
    400120: Bone067
    400122: Bone068
    400124: Bone069
    400126: Bone070
    400128: Bone071
    400130: Bone072
    400132: Bone073
    400134: model150
    400136: //RootNode
    400138: mount_1
    4300000: model150
    4300002: mount_1
    4300004: adorn_2
    9500000: //RootNode
    13700000: adorn_2
    13700002: model150
    13700004: mount_1
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
