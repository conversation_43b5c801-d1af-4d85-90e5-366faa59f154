%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: smoke_06_blediss
  m_Shader: {fileID: 4800000, guid: e6869e87aa509af438fdc814930559f8, type: 3}
  m_ShaderKeywords: 
  m_LightmapFlags: 5
  m_CustomRenderQueue: -1
  stringTagMap: {}
  m_SavedProperties:
    serializedVersion: 2
    m_TexEnvs:
    - first:
        name: _Dssoled
      second:
        m_Texture: {fileID: 2800000, guid: 7cc0325bf07da464b8fbd4ea1a9a83a6, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MainTex
      second:
        m_Texture: {fileID: 2800000, guid: f58dc0dafad2c3c44937d4144b0f30b2, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _TEX
      second:
        m_Texture: {fileID: 2800000, guid: f58dc0dafad2c3c44937d4144b0f30b2, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - first:
        name: _ALPHA
      second: 1
    - first:
        name: _Cutoff
      second: 0.5
    - first:
        name: _InvFade
      second: 1
    - first:
        name: _Light
      second: 1
    - first:
        name: _Power
      second: 3
    - first:
        name: _VertexColor_power
      second: 4
    m_Colors:
    - first:
        name: _Color
      second: {r: 1, g: 1, b: 1, a: 1}
    - first:
        name: _TintColor
      second: {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
