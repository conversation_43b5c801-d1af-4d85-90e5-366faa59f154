with:870  hight:417
[logic/misc/CShareCtrl.lua:18]:ShareCallback ctor
[logic/login/CLoginCtrl.lua:5]:公告初始化...
[logic/base/CHttpCtrl.lua:32]:http get ->    https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials&client_id=rlU84dprH6I7sMjBkqM0gizl&client_secret=c15395c2433c4a904d22aa27eab8321b&
[logic/ui/CViewCtrl.lua:94]:CNotifyView ShowView
[logic/ui/CViewCtrl.lua:94]:CBottomView ShowView
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x6f9331a8"
|  json_result = true
|  timer = 16
}
[logic/audio/CSpeechCtrl.lua:72]:OnTokenGet err
[logic/ui/CViewBase.lua:125]:CNotifyView LoadDone!
[logic/ui/CViewBase.lua:125]:CBottomView LoadDone!
[logic/base/CResCtrl.lua:198]:-->resource init done!!! 12
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {}
[logic/ui/CViewCtrl.lua:94]:CLoginView ShowView
[logic/base/CHttpCtrl.lua:32]:http get ->    http://************:88/Note/note.json
[logic/login/CLoginAccountPage.lua:79]:SendToCenterServer:    http://************:88/Note/note.json    
[logic/ui/CViewBase.lua:125]:CLoginView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x8e1e5008"
|  json_result = true
|  timer = 60
}
[core/table.lua:94]:cb tResult-> = {
|  info = {
|  |  role_list = {}
|  |  server_info = {
|  |  |  groups = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 1
|  |  |  |  |  name = "1-10区"
|  |  |  |  }
|  |  |  }
|  |  |  infoList = {
|  |  |  |  [1] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "新服7天限时-大狂欢活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [10] = {
|  |  |  |  |  |  |  |  text = "传说伙伴碎片*100、梦觉书·零*20、梦觉书·低*20、梦觉书·高*20"
|  |  |  |  |  |  |  |  title = "新区累计充值10000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [11] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "单日累计充值活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [12] = {
|  |  |  |  |  |  |  |  text = "永久"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [13] = {
|  |  |  |  |  |  |  |  text = "1.活动为单日累计充值，高档位可领取低档位所有物品。
2.活动为单日累计充值，仅计算单日内玩家累计充值总额，次日重新计算。"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [14] = {
|  |  |  |  |  |  |  |  text = "向客服申请"
|  |  |  |  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [15] = {
|  |  |  |  |  |  |  |  text = "装备材料*10、金币*5w、熊猫蛋糕*5"
|  |  |  |  |  |  |  |  title = "单日累计充值100元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [16] = {
|  |  |  |  |  |  |  |  text = "一发入魂碎片*10、金币铜币*10w、体力药水*1、鲜肉包*30"
|  |  |  |  |  |  |  |  title = "单日累计充值500元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [17] = {
|  |  |  |  |  |  |  |  text = "一发入魂碎片*20、金币*20w、体力药水*2、焦糖包*60"
|  |  |  |  |  |  |  |  title = "单日累计充值1000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [18] = {
|  |  |  |  |  |  |  |  text = "装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1"
|  |  |  |  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [19] = {
|  |  |  |  |  |  |  |  text = "装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3"
|  |  |  |  |  |  |  |  title = "单日累计充值5000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "新服前7天"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [20] = {
|  |  |  |  |  |  |  |  text = "一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*80"
|  |  |  |  |  |  |  |  title = "单日累计充值10000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [21] = {
|  |  |  |  |  |  |  |  text = "一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1"
|  |  |  |  |  |  |  |  title = "单日累计充值20000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [22] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "永久累计充值活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [23] = {
|  |  |  |  |  |  |  |  text = "永久"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [24] = {
|  |  |  |  |  |  |  |  text = "活动为永久累计充值，高档位可领取低档位所有物品。、r
每个档位奖励仅可领取一次"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [25] = {
|  |  |  |  |  |  |  |  text = "向客服申请"
|  |  |  |  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [26] = {
|  |  |  |  |  |  |  |  text = "装备材料*50、伙伴觉醒材料*50、金币*1w"
|  |  |  |  |  |  |  |  title = "永久累计充值500元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [27] = {
|  |  |  |  |  |  |  |  text = "喇叭100、2星精英伙伴*10、装备原石*5、附魔材料*5、金币*5w"
|  |  |  |  |  |  |  |  title = "永久累计充值1000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [28] = {
|  |  |  |  |  |  |  |  text = "喇叭300、2星精英伙伴*20、装备原石*10、附魔材料*10、金币*10w"
|  |  |  |  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [29] = {
|  |  |  |  |  |  |  |  text = "喇叭500、3星传说伙伴*1、装备原石*30、附魔材料*30、金币*50w"
|  |  |  |  |  |  |  |  title = "永久累计充值5000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "单日累计充值达到对应档位，不重复高领低档，每个档位仅可领取一次奖励、请及时参加并申请领取对应档位。"
|  |  |  |  |  |  |  |  title = "活动规则"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [30] = {
|  |  |  |  |  |  |  |  text = "一发入魂碎片*100、万能碎片*100、金币*100w"
|  |  |  |  |  |  |  |  title = "永久累充10000元礼包"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [31] = {
|  |  |  |  |  |  |  |  text = "一发入魂碎片*200、万能碎片*200、金币*200w"
|  |  |  |  |  |  |  |  title = "永久累充20000元礼包"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [32] = {
|  |  |  |  |  |  |  |  text = "万能碎片*500、金币*500w"
|  |  |  |  |  |  |  |  title = "永久累充40000元礼包"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [33] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "单日累计充值货币返利1:10"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [34] = {
|  |  |  |  |  |  |  |  text = "永久"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [35] = {
|  |  |  |  |  |  |  |  text = "1.本活动为阶梯式活动，当日累计充值的金额满足返利阶梯后，按照当日充值金额满足的最高档次进行发放，次日自动返利，无需申请
2.本活动为长期活动，与其他返利活动叠加。
充值100-1999元，返利50%钻石
充值2000-4999元，返利100%钻石
充值5000-9999元，返利150%钻石
充值10000-19999元，返利200%钻石
充值20000+元，返利250%钻石
以上返利会在第二天自动已邮件形式发送至玩家"
|  |  |  |  |  |  |  |  title = "活动说明:"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [36] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "服务器冠名活动"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [37] = {
|  |  |  |  |  |  |  |  text = "永久"
|  |  |  |  |  |  |  |  title = "活动时间："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [38] = {
|  |  |  |  |  |  |  |  text = ""
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [39] = {
|  |  |  |  |  |  |  |  text = "全体玩家"
|  |  |  |  |  |  |  |  title = "【活动对象】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "向客服申请"
|  |  |  |  |  |  |  |  title = "发放时间方式："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [40] = {
|  |  |  |  |  |  |  |  text = "我的服务器，我为自己代言。"
|  |  |  |  |  |  |  |  title = "【活动宣言】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [41] = {
|  |  |  |  |  |  |  |  text = "区服冠名权*1"
|  |  |  |  |  |  |  |  title = "【冠名奖励】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [42] = {
|  |  |  |  |  |  |  |  text = "  亲爱的玩家：
  也许你在游戏里已经称霸全区，但是出了服务器，出了游戏，还有谁认识你现在就给你一个机会，获得至高无上的荣誉，让更多的人知道你的强大。特此举行“我的服务器，我为自己代言”，服务器玩家冠名活动。
即日起，只要满足以下条件，即可获得服务器冠名权：
  1）开服前3天累计充值满10000元
  2）开服前7日累计充值满20000元，且区服处于未冠名状态，获得服务器冠名权
  备注：本活动遵循先到先得原则。如出现多个满足条件的，冠名资格由第一个满足条件的获得。"
|  |  |  |  |  |  |  |  title = "【活动内容】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [43] = {
|  |  |  |  |  |  |  |  text = "1、活动时间内有玩家满足条件后活动结束，活动期间没有玩家满足条件则保持原服务器名称。
2、服务器冠名长度为4个字。（必须遵守相关法律法规，不得使用负面或影响社会风气，不得出现敏感词的名称）
3、由于冠名操作需要时间，工作人员会在7个工作日内，完成冠名操作。
4，每个服，冠名只有一次。被冠名的服务器不予再次冠名"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [5] = {
|  |  |  |  |  |  |  |  text = "精英伙伴碎片*50、攻击宝石礼包*1、防御宝石礼包*1、2星精英伙伴*1"
|  |  |  |  |  |  |  |  title = "新区累计充值500元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [6] = {
|  |  |  |  |  |  |  |  text = "精英伙伴碎片*100、攻击宝石礼包*3、防御宝石礼包*3、2星精英伙伴*2"
|  |  |  |  |  |  |  |  title = "新区累计充值1000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [7] = {
|  |  |  |  |  |  |  |  text = "传说伙伴碎片*20、攻击宝石礼包*5、防御宝石礼包*5、2星精英伙伴*3"
|  |  |  |  |  |  |  |  title = "新区累计充值2000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [8] = {
|  |  |  |  |  |  |  |  text = "传说伙伴碎片*30、攻击宝石礼包*10、防御宝石礼包*10"
|  |  |  |  |  |  |  |  title = "新区累计充值3000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [9] = {
|  |  |  |  |  |  |  |  text = "传说伙伴碎片*50、梦觉书·零*10、梦觉书·低*10、梦觉书·高*10"
|  |  |  |  |  |  |  |  title = "新区累计充值5000元"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = "ceshi"
|  |  |  |  |  }
|  |  |  |  |  hot = 1
|  |  |  |  |  title = "限时大狂欢"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "1、转出条件：上款游戏充值超过100元的玩家
2、转入条件：新游戏充值超过100元"
|  |  |  |  |  |  |  |  title = "一、参与转游条件"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "★新游单日充值100-999元
老游戏充值金额20%转入新游（转入金额上限为5000元）
★新游单日充值1000-2999元
老游戏充值金额30%转入新游（转入金额上限为10000元）
★新游单日充值3000-4999元
老游戏充值金额40%转入新游（转入金额上限为10000元）
★新游单日充值5000-9999元
老游戏充值金额60%转入新游（转入金额上限为20000元）
★新游单日充值10000-19999元
老游戏充值金额80%转入新游（转入金额上限为30000元）
★新游单日充值20000以上
老游戏充值金额100%转入新游（转入金额上限为40000元）"
|  |  |  |  |  |  |  |  title = "二、阶梯转游返利"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "A，玩家新游戏充值200元，老游戏充值2200元，则转入货币为：2200*30%*新游货币比例
B，玩家新游戏充值200元，老游戏充值10000元，则转入货币为：5000*30%*新游货币比例"
|  |  |  |  |  |  |  |  title = "【举例】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "1，每个账号仅支持一次转游！
2，转游玩家在老游戏A和B都有过充值，但只能选择其中一个游戏进行转出到新游戏，不接受多个游戏同时转入一个新游戏
3，转游福利仅限玩家新角色创建的7天内申请，创建时间超过游戏7天则无法无法享受。
4，以上返利仅为游戏货币奖励，不计入vip经验、累充活动和线下返利
5，封停账号：玩家申请转游成功之后封停上款游戏角色"
|  |  |  |  |  |  |  |  title = "【温馨提示】："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = "ceshi"
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "转游/转区"
|  |  |  |  }
|  |  |  }
|  |  |  ports = {
|  |  |  |  [1] = 7011
|  |  |  |  [2] = 7012
|  |  |  |  [3] = 27011
|  |  |  |  [4] = 27012
|  |  |  |  [5] = 27013
|  |  |  }
|  |  |  role_list = ""
|  |  |  servers = {
|  |  |  |  [1] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 1001
|  |  |  |  |  ip = "************"
|  |  |  |  |  name = "双线1区"
|  |  |  |  |  new = 0
|  |  |  |  |  open_time = 1506844350
|  |  |  |  |  server_id = 1001
|  |  |  |  |  start_time = 1506844350
|  |  |  |  |  state = 2
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 1002
|  |  |  |  |  ip = "************"
|  |  |  |  |  name = "双线2区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1506844350
|  |  |  |  |  server_id = 1002
|  |  |  |  |  start_time = 1506844350
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  }
|  |  }
|  |  token = ""
|  }
}
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {
|  [1] = {
|  |  group_id = 1
|  |  id = 1
|  |  name = "1-10区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  id = 1001
|  |  |  |  ip = "************"
|  |  |  |  name = "双线1区"
|  |  |  |  new = 0
|  |  |  |  open_time = 1506844350
|  |  |  |  server_id = 1001
|  |  |  |  start_time = 1506844350
|  |  |  |  state = 2
|  |  |  }
|  |  |  [2] = {
|  |  |  |  group = 1
|  |  |  |  id = 1002
|  |  |  |  ip = "************"
|  |  |  |  name = "双线2区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1506844350
|  |  |  |  server_id = 1002
|  |  |  |  start_time = 1506844350
|  |  |  |  state = 1
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:设置公告: = {
|  [1] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "新服7天限时-大狂欢活动"
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  text = "传说伙伴碎片*100、梦觉书·零*20、梦觉书·低*20、梦觉书·高*20"
|  |  |  |  |  title = "新区累计充值10000元"
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "单日累计充值活动"
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  text = "永久"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [13] = {
|  |  |  |  |  text = "1.活动为单日累计充值，高档位可领取低档位所有物品。
2.活动为单日累计充值，仅计算单日内玩家累计充值总额，次日重新计算。"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [14] = {
|  |  |  |  |  text = "向客服申请"
|  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  }
|  |  |  |  [15] = {
|  |  |  |  |  text = "装备材料*10、金币*5w、熊猫蛋糕*5"
|  |  |  |  |  title = "单日累计充值100元"
|  |  |  |  }
|  |  |  |  [16] = {
|  |  |  |  |  text = "一发入魂碎片*10、金币铜币*10w、体力药水*1、鲜肉包*30"
|  |  |  |  |  title = "单日累计充值500元"
|  |  |  |  }
|  |  |  |  [17] = {
|  |  |  |  |  text = "一发入魂碎片*20、金币*20w、体力药水*2、焦糖包*60"
|  |  |  |  |  title = "单日累计充值1000元"
|  |  |  |  }
|  |  |  |  [18] = {
|  |  |  |  |  text = "装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1"
|  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  }
|  |  |  |  [19] = {
|  |  |  |  |  text = "装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3"
|  |  |  |  |  title = "单日累计充值5000元"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "新服前7天"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [20] = {
|  |  |  |  |  text = "一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*80"
|  |  |  |  |  title = "单日累计充值10000元"
|  |  |  |  }
|  |  |  |  [21] = {
|  |  |  |  |  text = "一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1"
|  |  |  |  |  title = "单日累计充值20000元"
|  |  |  |  }
|  |  |  |  [22] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "永久累计充值活动"
|  |  |  |  }
|  |  |  |  [23] = {
|  |  |  |  |  text = "永久"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [24] = {
|  |  |  |  |  text = "活动为永久累计充值，高档位可领取低档位所有物品。、r
每个档位奖励仅可领取一次"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [25] = {
|  |  |  |  |  text = "向客服申请"
|  |  |  |  |  title = "此活动申请方式为："
|  |  |  |  }
|  |  |  |  [26] = {
|  |  |  |  |  text = "装备材料*50、伙伴觉醒材料*50、金币*1w"
|  |  |  |  |  title = "永久累计充值500元"
|  |  |  |  }
|  |  |  |  [27] = {
|  |  |  |  |  text = "喇叭100、2星精英伙伴*10、装备原石*5、附魔材料*5、金币*5w"
|  |  |  |  |  title = "永久累计充值1000元"
|  |  |  |  }
|  |  |  |  [28] = {
|  |  |  |  |  text = "喇叭300、2星精英伙伴*20、装备原石*10、附魔材料*10、金币*10w"
|  |  |  |  |  title = "单日累计充值2000元"
|  |  |  |  }
|  |  |  |  [29] = {
|  |  |  |  |  text = "喇叭500、3星传说伙伴*1、装备原石*30、附魔材料*30、金币*50w"
|  |  |  |  |  title = "永久累计充值5000元"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "单日累计充值达到对应档位，不重复高领低档，每个档位仅可领取一次奖励、请及时参加并申请领取对应档位。"
|  |  |  |  |  title = "活动规则"
|  |  |  |  }
|  |  |  |  [30] = {
|  |  |  |  |  text = "一发入魂碎片*100、万能碎片*100、金币*100w"
|  |  |  |  |  title = "永久累充10000元礼包"
|  |  |  |  }
|  |  |  |  [31] = {
|  |  |  |  |  text = "一发入魂碎片*200、万能碎片*200、金币*200w"
|  |  |  |  |  title = "永久累充20000元礼包"
|  |  |  |  }
|  |  |  |  [32] = {
|  |  |  |  |  text = "万能碎片*500、金币*500w"
|  |  |  |  |  title = "永久累充40000元礼包"
|  |  |  |  }
|  |  |  |  [33] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "单日累计充值货币返利1:10"
|  |  |  |  }
|  |  |  |  [34] = {
|  |  |  |  |  text = "永久"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [35] = {
|  |  |  |  |  text = "1.本活动为阶梯式活动，当日累计充值的金额满足返利阶梯后，按照当日充值金额满足的最高档次进行发放，次日自动返利，无需申请
2.本活动为长期活动，与其他返利活动叠加。
充值100-1999元，返利50%钻石
充值2000-4999元，返利100%钻石
充值5000-9999元，返利150%钻石
充值10000-19999元，返利200%钻石
充值20000+元，返利250%钻石
以上返利会在第二天自动已邮件形式发送至玩家"
|  |  |  |  |  title = "活动说明:"
|  |  |  |  }
|  |  |  |  [36] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "服务器冠名活动"
|  |  |  |  }
|  |  |  |  [37] = {
|  |  |  |  |  text = "永久"
|  |  |  |  |  title = "活动时间："
|  |  |  |  }
|  |  |  |  [38] = {
|  |  |  |  |  text = ""
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [39] = {
|  |  |  |  |  text = "全体玩家"
|  |  |  |  |  title = "【活动对象】："
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "向客服申请"
|  |  |  |  |  title = "发放时间方式："
|  |  |  |  }
|  |  |  |  [40] = {
|  |  |  |  |  text = "我的服务器，我为自己代言。"
|  |  |  |  |  title = "【活动宣言】："
|  |  |  |  }
|  |  |  |  [41] = {
|  |  |  |  |  text = "区服冠名权*1"
|  |  |  |  |  title = "【冠名奖励】："
|  |  |  |  }
|  |  |  |  [42] = {
|  |  |  |  |  text = "  亲爱的玩家：
  也许你在游戏里已经称霸全区，但是出了服务器，出了游戏，还有谁认识你现在就给你一个机会，获得至高无上的荣誉，让更多的人知道你的强大。特此举行“我的服务器，我为自己代言”，服务器玩家冠名活动。
即日起，只要满足以下条件，即可获得服务器冠名权：
  1）开服前3天累计充值满10000元
  2）开服前7日累计充值满20000元，且区服处于未冠名状态，获得服务器冠名权
  备注：本活动遵循先到先得原则。如出现多个满足条件的，冠名资格由第一个满足条件的获得。"
|  |  |  |  |  title = "【活动内容】："
|  |  |  |  }
|  |  |  |  [43] = {
|  |  |  |  |  text = "1、活动时间内有玩家满足条件后活动结束，活动期间没有玩家满足条件则保持原服务器名称。
2、服务器冠名长度为4个字。（必须遵守相关法律法规，不得使用负面或影响社会风气，不得出现敏感词的名称）
3、由于冠名操作需要时间，工作人员会在7个工作日内，完成冠名操作。
4，每个服，冠名只有一次。被冠名的服务器不予再次冠名"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  text = "精英伙伴碎片*50、攻击宝石礼包*1、防御宝石礼包*1、2星精英伙伴*1"
|  |  |  |  |  title = "新区累计充值500元"
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  text = "精英伙伴碎片*100、攻击宝石礼包*3、防御宝石礼包*3、2星精英伙伴*2"
|  |  |  |  |  title = "新区累计充值1000元"
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  text = "传说伙伴碎片*20、攻击宝石礼包*5、防御宝石礼包*5、2星精英伙伴*3"
|  |  |  |  |  title = "新区累计充值2000元"
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  text = "传说伙伴碎片*30、攻击宝石礼包*10、防御宝石礼包*10"
|  |  |  |  |  title = "新区累计充值3000元"
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  text = "传说伙伴碎片*50、梦觉书·零*10、梦觉书·低*10、梦觉书·高*10"
|  |  |  |  |  title = "新区累计充值5000元"
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = "ceshi"
|  |  }
|  |  hot = 1
|  |  title = "限时大狂欢"
|  }
|  [2] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "1、转出条件：上款游戏充值超过100元的玩家
2、转入条件：新游戏充值超过100元"
|  |  |  |  |  title = "一、参与转游条件"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "★新游单日充值100-999元
老游戏充值金额20%转入新游（转入金额上限为5000元）
★新游单日充值1000-2999元
老游戏充值金额30%转入新游（转入金额上限为10000元）
★新游单日充值3000-4999元
老游戏充值金额40%转入新游（转入金额上限为10000元）
★新游单日充值5000-9999元
老游戏充值金额60%转入新游（转入金额上限为20000元）
★新游单日充值10000-19999元
老游戏充值金额80%转入新游（转入金额上限为30000元）
★新游单日充值20000以上
老游戏充值金额100%转入新游（转入金额上限为40000元）"
|  |  |  |  |  title = "二、阶梯转游返利"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "A，玩家新游戏充值200元，老游戏充值2200元，则转入货币为：2200*30%*新游货币比例
B，玩家新游戏充值200元，老游戏充值10000元，则转入货币为：5000*30%*新游货币比例"
|  |  |  |  |  title = "【举例】："
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "1，每个账号仅支持一次转游！
2，转游玩家在老游戏A和B都有过充值，但只能选择其中一个游戏进行转出到新游戏，不接受多个游戏同时转入一个新游戏
3，转游福利仅限玩家新角色创建的7天内申请，创建时间超过游戏7天则无法无法享受。
4，以上返利仅为游戏货币奖励，不计入vip经验、累充活动和线下返利
5，封停账号：玩家申请转游成功之后封停上款游戏角色"
|  |  |  |  |  title = "【温馨提示】："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = "ceshi"
|  |  }
|  |  hot = 2
|  |  title = "转游/转区"
|  }
}
[core/global.lua:59]:<color=#ffeb04>CLoginServerPage.SetServer</color>
[logic/ui/CViewCtrl.lua:94]:CSelectServerView ShowView
[core/global.lua:59]:<color=#ffeb04>服务器按钮 我在这里...</color>
[core/table.lua:94]:GroupServers------------------------> = {
|  [1] = {
|  |  group_id = 1
|  |  id = 1
|  |  name = "1-10区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  id = 1001
|  |  |  |  ip = "************"
|  |  |  |  name = "双线1区"
|  |  |  |  new = 0
|  |  |  |  open_time = 1506844350
|  |  |  |  server_id = 1001
|  |  |  |  start_time = 1506844350
|  |  |  |  state = 2
|  |  |  }
|  |  |  [2] = {
|  |  |  |  group = 1
|  |  |  |  id = 1002
|  |  |  |  ip = "************"
|  |  |  |  name = "双线2区"
|  |  |  |  new = 1
|  |  |  |  open_time = 1506844350
|  |  |  |  server_id = 1002
|  |  |  |  start_time = 1506844350
|  |  |  |  state = 1
|  |  |  }
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>ting</color>
[core/global.lua:59]:<color=#ffeb04>CServerBox.SetServer</color>
[core/global.lua:59]:<color=#ffeb04>CServerBox.SetServer</color>
[logic/ui/CViewBase.lua:125]:CSelectServerView LoadDone!
[core/global.lua:59]:<color=#ffeb04>CSelectServerView.SetServer</color>
[core/global.lua:59]:<color=#ffeb04>CLoginServerPage.SetServer</color>
[logic/login/CLoginCtrl.lua:151]:ConnectServer =     table:0x49DDF068    table:0x49DDF358
[net/CNetCtrl.lua:114]:Test连接    ************    27013
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[core/table.lua:94]:-->Net Receive: login.GS2CHello = {
|  time = **********
}
[core/table.lua:94]:<--Net Send: login.C2GSQueryLogin = {
|  res_file_version = {}
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 21:31:39
[net/netlogin.lua:208]:netlogin.GS2CQueryLogin-->
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 1001
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginAccount = {
|  account = "demo02"
|  client_svn_version = 96
|  client_version = "1.15.0.0"
|  device = "B760M-VDH (JGINYUE)"
|  imei = ""
|  is_qrcode = 0
|  mac = "00-E0-1B-74-72-10"
|  os = "Windows 10  (10.0.0) 64bit"
|  platform = 4
|  udid = "552ae815ac1f51979f8a3ae596276f67ae3d10cf"
}
[core/global.lua:59]:<color=#ffeb04>更新代码: 1049</color>
[logic/base/Utils.lua:398]:local framever, dllver, resver = C_api.Utils.GetResVersion()
if resver >= 96 or Utils.IsEditor() then
	function CWelfareCtrl.IsOpenCostSave(self)
		local b = false
		local t = g_TimeCtrl:GetTimeS()	
		if self.m_CostSaveStartTime ~= 0 and self.m_CostSaveEndTime ~= 0 and t > self.m_CostSaveStartTime and t < self.m_CostSaveEndTime then
			b = true
		end
		return b
	end
	function CPartnerHireView.IsCanYFRH(self, iParID)
		local d = data.partnerhiredata.DATA
		if table.index(d[iParID]["recommand_list"], "一发入魂") then
			return self:IsCanSSRDraw()
		end
	end
	function CPartnerEquipPage.ChangePart(self)
		local oItem = g_ItemCtrl:GetItem(self.m_CurItemID)
		if oItem and oItem:GetValue("level") == define.Partner.ParEquip.MaxLevel and oItem:GetValue("star") < define.Partner.ParEquip.MaxStar then
			if self.m_CurPart ~= self.m_UpStarPart and self.m_CurPart ~= self.m_UpStonePart then
				self.m_UpStarBtn:SetSelected(true)
				self:SwitchPart()
			end
		end
	end
	data.netdata.BAN["proto"]["herobox"]["item"]["GS2CItemQuickUse"] = true
end

[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 1001
}
[core/table.lua:94]:-->Net Receive: login.GS2CCheckInviteCodeResult = {
|  msg = "邀请码验证已关闭"
|  result = 1
}
[core/table.lua:94]:-->Net Receive: login.GS2CLoginAccount = {
|  account = "demo02"
|  role_list = {
|  |  [1] = {
|  |  |  grade = 1
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 120
|  |  |  }
|  |  |  name = "DEBUG10124"
|  |  |  pid = 10124
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginRole = {
|  account = "demo02"
|  pid = 10124
}
[core/global.lua:59]:<color=#ffeb04>读取系统设置本地数据，帐号为  demo02</color>
[core/global.lua:59]:<color=#ffeb04>demo02 的系统设置本地数据为 nil，使用默认数据</color>
[core/table.lua:94]:-->Net Receive: login.GS2CLoginRole = {
|  account = "demo02"
|  pid = 10124
|  role = {
|  |  abnormal_attr_ratio = 820
|  |  attack = 533
|  |  bcmd = {
|  |  |  [1] = {
|  |  |  |  idx = 208
|  |  |  }
|  |  |  [10] = {
|  |  |  |  idx = 105
|  |  |  }
|  |  |  [11] = {
|  |  |  |  idx = 106
|  |  |  }
|  |  |  [12] = {
|  |  |  |  idx = 107
|  |  |  }
|  |  |  [13] = {
|  |  |  |  idx = 108
|  |  |  }
|  |  |  [14] = {
|  |  |  |  idx = 205
|  |  |  }
|  |  |  [15] = {
|  |  |  |  idx = 206
|  |  |  }
|  |  |  [16] = {
|  |  |  |  idx = 207
|  |  |  }
|  |  |  [2] = {
|  |  |  |  cmd = "集火"
|  |  |  |  idx = 201
|  |  |  }
|  |  |  [3] = {
|  |  |  |  cmd = "封印"
|  |  |  |  idx = 202
|  |  |  }
|  |  |  [4] = {
|  |  |  |  cmd = "减速"
|  |  |  |  idx = 203
|  |  |  }
|  |  |  [5] = {
|  |  |  |  cmd = "禁疗"
|  |  |  |  idx = 204
|  |  |  }
|  |  |  [6] = {
|  |  |  |  cmd = "治疗"
|  |  |  |  idx = 101
|  |  |  }
|  |  |  [7] = {
|  |  |  |  cmd = "复活"
|  |  |  |  idx = 102
|  |  |  }
|  |  |  [8] = {
|  |  |  |  cmd = "加速"
|  |  |  |  idx = 103
|  |  |  }
|  |  |  [9] = {
|  |  |  |  cmd = "解封"
|  |  |  |  idx = 104
|  |  |  }
|  |  }
|  |  coin = 10000
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 43
|  |  energy = 285
|  |  exp = 1000480
|  |  grade = 56
|  |  hp = 8494
|  |  kp_sdk_info = {
|  |  |  create_time = 1680355823
|  |  |  upgrade_time = 1680355854
|  |  }
|  |  mask = "7e6683d1ffffffe"
|  |  max_hp = 8702
|  |  model_info = {
|  |  |  color = {
|  |  |  |  [1] = 0
|  |  |  }
|  |  |  shape = 120
|  |  |  weapon = 2500
|  |  }
|  |  name = "灌篮d神ID"
|  |  open_day = 29
|  |  org_fuben_cnt = 2
|  |  power = 2510
|  |  res_abnormal_ratio = 820
|  |  res_critical_ratio = 500
|  |  school = 1
|  |  school_branch = 1
|  |  sex = 2
|  |  show_id = 10124
|  |  skill_point = 105
|  |  speed = 753
|  |  systemsetting = {}
|  |  trapmine_point = 50
|  }
|  role_token = "**************"
|  xg_account = "bus10124"
}
[logic/ui/CViewCtrl.lua:94]:CMainMenuView ShowView
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 820
|  active = 0
|  arenamedal = 0
|  attack = 533
|  bcmd = {
|  |  [1] = {
|  |  |  idx = 208
|  |  }
|  |  [10] = {
|  |  |  idx = 105
|  |  }
|  |  [11] = {
|  |  |  idx = 106
|  |  }
|  |  [12] = {
|  |  |  idx = 107
|  |  }
|  |  [13] = {
|  |  |  idx = 108
|  |  }
|  |  [14] = {
|  |  |  idx = 205
|  |  }
|  |  [15] = {
|  |  |  idx = 206
|  |  }
|  |  [16] = {
|  |  |  idx = 207
|  |  }
|  |  [2] = {
|  |  |  cmd = "集火"
|  |  |  idx = 201
|  |  }
|  |  [3] = {
|  |  |  cmd = "封印"
|  |  |  idx = 202
|  |  }
|  |  [4] = {
|  |  |  cmd = "减速"
|  |  |  idx = 203
|  |  }
|  |  [5] = {
|  |  |  cmd = "禁疗"
|  |  |  idx = 204
|  |  }
|  |  [6] = {
|  |  |  cmd = "治疗"
|  |  |  idx = 101
|  |  }
|  |  [7] = {
|  |  |  cmd = "复活"
|  |  |  idx = 102
|  |  }
|  |  [8] = {
|  |  |  cmd = "加速"
|  |  |  idx = 103
|  |  }
|  |  [9] = {
|  |  |  cmd = "解封"
|  |  |  idx = 104
|  |  }
|  }
|  camp = 0
|  chatself = false
|  chubeiexp = 0
|  coin = 10000
|  coin_over = 0
|  color_coin = 0
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 43
|  energy = 285
|  exp = 1000480
|  followers = {}
|  goldcoin = 0
|  grade = 56
|  hp = 8494
|  kp_sdk_info = {
|  |  create_time = 1680355823
|  |  upgrade_time = 1680355854
|  }
|  max_hp = 8702
|  medal = 0
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 120
|  |  weapon = 2500
|  }
|  name = "灌篮d神ID"
|  open_day = 29
|  org_fuben_cnt = 2
|  org_offer = 0
|  power = 2510
|  res_abnormal_ratio = 820
|  res_critical_ratio = 500
|  school = 1
|  school_branch = 1
|  sex = 2
|  show_id = 10124
|  skill_point = 105
|  skin = 0
|  speed = 753
|  systemsetting = {}
|  title_info = {}
|  trapmine_point = 50
|  travel_score = 0
|  upvote_amount = 0
}
[net/netlogin.lua:152]:create_time:    1680356135    1680355823    nil
[net/netlogin.lua:154]:g_ShareCtrl.m_ShareManager.Quick_CreateRole createRole
[logic/misc/CTimeCtrl.lua:64]:HeartBeat-->开启心跳包检测
[core/table.lua:94]:<--Net Send: title.C2GSTitleInfoList = {}
[core/table.lua:94]:-->Net Receive: player.GS2CGamePushSetting = {}
[core/table.lua:94]:-->Net Receive: player.GS2CShapeList = {
|  shapes = {
|  |  [1] = 120
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CGuidanceInfo = {
|  guidanceinfo = {
|  |  key = {
|  |  |  [1] = 7000041
|  |  |  [2] = 7000051
|  |  |  [3] = 5000300
|  |  |  [4] = 5000800
|  |  |  [5] = 1001099
|  |  }
|  |  version = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>>>>>>>>>>>>>>>>>>>>> GS2CGuidanceInfo </color>
[core/table.lua:94]:-->Net Receive: travel.GS2CLoginTravelPartner = {
|  travel_partner = {
|  |  server_time = **********
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CServerGradeInfo = {
|  days = 4
|  server_grade = 80
}
[core/table.lua:94]:-->Net Receive: scene.GS2CShowScene = {
|  map_id = 101000
|  scene_id = 12
|  scene_name = "帝都"
}
[core/table.lua:94]:-->Net Receive: task.GS2CLoginTask = {
|  shimen_status = 1
|  taskdata = {
|  |  [1] = {
|  |  |  acceptnpc = 10001
|  |  |  clientnpc = {
|  |  |  |  [1] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 1150
|  |  |  |  |  }
|  |  |  |  |  name = "飞龙哥"
|  |  |  |  |  npcid = 9267
|  |  |  |  |  npctype = 10001
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 20100
|  |  |  |  |  |  y = 15200
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 1151
|  |  |  |  |  }
|  |  |  |  |  name = "邓酒爷"
|  |  |  |  |  npcid = 9268
|  |  |  |  |  npctype = 10002
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 19500
|  |  |  |  |  |  y = 16100
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 302
|  |  |  |  |  }
|  |  |  |  |  name = "重华"
|  |  |  |  |  npcid = 9269
|  |  |  |  |  npctype = 10003
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 18000
|  |  |  |  |  |  y = 15300
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 1007
|  |  |  |  |  }
|  |  |  |  |  name = "锅铲小弟"
|  |  |  |  |  npcid = 9270
|  |  |  |  |  npctype = 10004
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 17400
|  |  |  |  |  |  y = 15800
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 1005
|  |  |  |  |  }
|  |  |  |  |  name = "咸鱼小弟"
|  |  |  |  |  npcid = 9271
|  |  |  |  |  npctype = 10005
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 19400
|  |  |  |  |  |  y = 14600
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 1005
|  |  |  |  |  }
|  |  |  |  |  name = "飞鱼小弟"
|  |  |  |  |  npcid = 9272
|  |  |  |  |  npctype = 10006
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 18300
|  |  |  |  |  |  y = 14600
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  detaildesc = "没有想到帝都也有这种人，恶意勒索的人最可恶了！"
|  |  |  name = "大展身手"
|  |  |  patrolinfo = {}
|  |  |  placeinfo = {}
|  |  |  shapeinfo = {}
|  |  |  statusinfo = {
|  |  |  |  note = "CreateTask"
|  |  |  |  status = 2
|  |  |  }
|  |  |  submitnpc = 10001
|  |  |  target = 10001
|  |  |  targetdesc = "恶人先告状"
|  |  |  taskid = 10002
|  |  |  taskitem = {}
|  |  |  tasktype = 1
|  |  |  traceinfo = {}
|  |  |  type = 2
|  |  }
|  |  [2] = {
|  |  |  acceptnpc = 5034
|  |  |  clientnpc = {
|  |  |  |  [1] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 302
|  |  |  |  |  }
|  |  |  |  |  name = "重华"
|  |  |  |  |  npcid = 9273
|  |  |  |  |  npctype = 85018
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 5000
|  |  |  |  |  |  y = 24000
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  detaildesc = "去[159a80]八门村猫饭店找喵小萌[654a33]买下包子，送给[159a80]帝都统帅部的重华[654a33]等各位伙伴！
[c54420]◇请根据上述提示找到关键建筑旁的人物完成委托！"
|  |  |  name = "小萌的委托"
|  |  |  needitem = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 1
|  |  |  |  |  itemid = 11601
|  |  |  |  }
|  |  |  }
|  |  |  patrolinfo = {}
|  |  |  placeinfo = {}
|  |  |  shapeinfo = {}
|  |  |  statusinfo = {
|  |  |  |  note = "CreateTask"
|  |  |  |  status = 2
|  |  |  }
|  |  |  submitnpc = 85018
|  |  |  target = 5034
|  |  |  targetdesc = "八门村特产"
|  |  |  taskid = 2006
|  |  |  taskitem = {}
|  |  |  tasktype = 1
|  |  |  traceinfo = {}
|  |  |  type = 9
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>login task</color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x56ff09d8"
|  |  AssociatedPick = "function: 0x56ff0a38"
|  |  AssociatedSubmit = "function: 0x56ff0a08"
|  |  CreateDefalutData = "function: 0x56fee9b8"
|  |  GetChaptetFubenData = "function: 0x56ff06f8"
|  |  GetProgressThing = "function: 0x56ff0a98"
|  |  GetRemainTime = "function: 0x56fee950"
|  |  GetStatus = "function: 0x56ff0af8"
|  |  GetTaskClientExtStrDic = "function: 0x56ff0a68"
|  |  GetTaskTypeSpriteteName = "function: 0x56ff06c8"
|  |  GetTraceInfo = "function: 0x56ff0820"
|  |  GetTraceNpcType = "function: 0x56ff0698"
|  |  GetValue = "function: 0x56ff0760"
|  |  IsAbandon = "function: 0x56ff07c0"
|  |  IsAddEscortDynamicNpc = "function: 0x56ff0e30"
|  |  IsMissMengTask = "function: 0x56fee980"
|  |  IsPassChaterFuben = "function: 0x56ff0728"
|  |  IsTaskSpecityAction = "function: 0x56ff07f0"
|  |  IsTaskSpecityCategory = "function: 0x56ff09a8"
|  |  New = "function: 0x56ff5328"
|  |  NewByData = "function: 0x56fedef8"
|  |  RaiseProgressIdx = "function: 0x56ff0ac8"
|  |  RefreshTask = "function: 0x56ff0790"
|  |  ResetEndTime = "function: 0x56feea20"
|  |  SetStatus = "function: 0x56ff0e60"
|  |  classname = "CTask"
|  |  ctor = "function: 0x56fee920"
|  }
|  m_CData = {
|  |  ChapterFb = ""
|  |  autoDoNextTask = 0
|  |  clientExtStr = ""
|  |  name = "大展身手"
|  |  submitNpcId = 10001
|  |  submitRewardStr = {
|  |  |  [1] = "R1002"
|  |  }
|  |  taskWalkingTips = "终于到帝都了;none;none"
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10001
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 1150
|  |  |  |  }
|  |  |  |  name = "飞龙哥"
|  |  |  |  npcid = 9267
|  |  |  |  npctype = 10001
|  |  |  |  pos_info = {
|  |  |  |  |  x = 20100
|  |  |  |  |  y = 15200
|  |  |  |  }
|  |  |  }
|  |  |  [2] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 1151
|  |  |  |  }
|  |  |  |  name = "邓酒爷"
|  |  |  |  npcid = 9268
|  |  |  |  npctype = 10002
|  |  |  |  pos_info = {
|  |  |  |  |  x = 19500
|  |  |  |  |  y = 16100
|  |  |  |  }
|  |  |  }
|  |  |  [3] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 302
|  |  |  |  }
|  |  |  |  name = "重华"
|  |  |  |  npcid = 9269
|  |  |  |  npctype = 10003
|  |  |  |  pos_info = {
|  |  |  |  |  x = 18000
|  |  |  |  |  y = 15300
|  |  |  |  }
|  |  |  }
|  |  |  [4] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 1007
|  |  |  |  }
|  |  |  |  name = "锅铲小弟"
|  |  |  |  npcid = 9270
|  |  |  |  npctype = 10004
|  |  |  |  pos_info = {
|  |  |  |  |  x = 17400
|  |  |  |  |  y = 15800
|  |  |  |  }
|  |  |  }
|  |  |  [5] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 1005
|  |  |  |  }
|  |  |  |  name = "咸鱼小弟"
|  |  |  |  npcid = 9271
|  |  |  |  npctype = 10005
|  |  |  |  pos_info = {
|  |  |  |  |  x = 19400
|  |  |  |  |  y = 14600
|  |  |  |  }
|  |  |  }
|  |  |  [6] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 1005
|  |  |  |  }
|  |  |  |  name = "飞鱼小弟"
|  |  |  |  npcid = 9272
|  |  |  |  npctype = 10006
|  |  |  |  pos_info = {
|  |  |  |  |  x = 18300
|  |  |  |  |  y = 14600
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "没有想到帝都也有这种人，恶意勒索的人最可恶了！"
|  |  isdone = 0
|  |  name = "大展身手"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10001
|  |  target = 10001
|  |  targetdesc = "恶人先告状"
|  |  taskid = 10002
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/global.lua:59]:<color=#ffeb04>login task</color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x56ff09d8"
|  |  AssociatedPick = "function: 0x56ff0a38"
|  |  AssociatedSubmit = "function: 0x56ff0a08"
|  |  CreateDefalutData = "function: 0x56fee9b8"
|  |  GetChaptetFubenData = "function: 0x56ff06f8"
|  |  GetProgressThing = "function: 0x56ff0a98"
|  |  GetRemainTime = "function: 0x56fee950"
|  |  GetStatus = "function: 0x56ff0af8"
|  |  GetTaskClientExtStrDic = "function: 0x56ff0a68"
|  |  GetTaskTypeSpriteteName = "function: 0x56ff06c8"
|  |  GetTraceInfo = "function: 0x56ff0820"
|  |  GetTraceNpcType = "function: 0x56ff0698"
|  |  GetValue = "function: 0x56ff0760"
|  |  IsAbandon = "function: 0x56ff07c0"
|  |  IsAddEscortDynamicNpc = "function: 0x56ff0e30"
|  |  IsMissMengTask = "function: 0x56fee980"
|  |  IsPassChaterFuben = "function: 0x56ff0728"
|  |  IsTaskSpecityAction = "function: 0x56ff07f0"
|  |  IsTaskSpecityCategory = "function: 0x56ff09a8"
|  |  New = "function: 0x56ff5328"
|  |  NewByData = "function: 0x56fedef8"
|  |  RaiseProgressIdx = "function: 0x56ff0ac8"
|  |  RefreshTask = "function: 0x56ff0790"
|  |  ResetEndTime = "function: 0x56feea20"
|  |  SetStatus = "function: 0x56ff0e60"
|  |  classname = "CTask"
|  |  ctor = "function: 0x56fee920"
|  }
|  m_CData = {
|  |  ChapterFb = ""
|  |  clientExtStr = ""
|  |  name = "小萌的委托"
|  |  submitNpcId = 85018
|  |  submitRewardStr = {
|  |  |  [1] = "R1001"
|  |  }
|  |  tips = 0
|  |  type = 9
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5034
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 302
|  |  |  |  }
|  |  |  |  name = "重华"
|  |  |  |  npcid = 9273
|  |  |  |  npctype = 85018
|  |  |  |  pos_info = {
|  |  |  |  |  x = 5000
|  |  |  |  |  y = 24000
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "去[159a80]八门村猫饭店找喵小萌[654a33]买下包子，送给[159a80]帝都统帅部的重华[654a33]等各位伙伴！
[c54420]◇请根据上述提示找到关键建筑旁的人物完成委托！"
|  |  isdone = 0
|  |  name = "小萌的委托"
|  |  needitem = {
|  |  |  [1] = {
|  |  |  |  amount = 1
|  |  |  |  itemid = 11601
|  |  |  }
|  |  }
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 85018
|  |  target = 5034
|  |  targetdesc = "八门村特产"
|  |  taskid = 2006
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 9
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 1
|  |  icon = "pic_sjweituo"
|  |  id = 9
|  |  menu_index = 5
|  |  menu_show_index = 5
|  |  menu_show_index_sort = 1
|  |  name = "日常"
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CTeachProgress = {}
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshPartnerTask = {}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  day_task = {
|  |  [1] = 1016
|  |  [10] = 1003
|  |  [11] = 1020
|  |  [12] = 1006
|  |  [13] = 1019
|  |  [14] = 1007
|  |  [15] = 1022
|  |  [16] = 1005
|  |  [17] = 1018
|  |  [18] = 1004
|  |  [19] = 1002
|  |  [2] = 3005
|  |  [20] = 1012
|  |  [21] = 1015
|  |  [3] = 1017
|  |  [4] = 3008
|  |  [5] = 1011
|  |  [6] = 3009
|  |  [7] = 1001
|  |  [8] = 1021
|  |  [9] = 1024
|  }
}
[core/table.lua:94]:-->Net Receive: state.GS2CLoginState = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginHuodongInfo = {
|  convoyinfo = {
|  |  convoy_partner = 1505
|  |  free_time = 1
|  |  pool_info = {
|  |  |  [1] = {
|  |  |  |  level = 1
|  |  |  |  partnerid = 1505
|  |  |  |  pos = 1
|  |  |  |  rewardid = 1001
|  |  |  }
|  |  |  [2] = {
|  |  |  |  level = 2
|  |  |  |  partnerid = 1016
|  |  |  |  pos = 2
|  |  |  |  rewardid = 1002
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [3] = {
|  |  |  |  level = 3
|  |  |  |  partnerid = 1750
|  |  |  |  pos = 3
|  |  |  |  rewardid = 1003
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [4] = {
|  |  |  |  level = 4
|  |  |  |  partnerid = 1002
|  |  |  |  pos = 4
|  |  |  |  rewardid = 1004
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [5] = {
|  |  |  |  level = 5
|  |  |  |  partnerid = 1014
|  |  |  |  pos = 5
|  |  |  |  rewardid = 1005
|  |  |  |  weight = 25
|  |  |  }
|  |  }
|  |  selected_pos = 1
|  |  target_npc = 5032
|  }
|  dailytrain = {
|  |  reward_times = 60
|  }
|  huntinfo = {
|  |  npcinfo = {
|  |  |  [1] = {
|  |  |  |  level = 1
|  |  |  |  status = 1
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginChapterInfo = {}
[core/table.lua:94]:-->Net Receive: team.GS2CInviteInfo = {}
[core/table.lua:94]:-->Net Receive: mail.GS2CLoginMail = {
|  simpleinfo = {
|  |  [1] = {
|  |  |  createtime = 1680355854
|  |  |  keeptime = 7776000
|  |  |  mailid = 1
|  |  |  subject = "喵小萌的来信"
|  |  |  title = "删测福利"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: friend.GS2CLoginFriend = {}
[core/global.lua:59]:<color=#ffeb04>好友版本一致，无需更新</color>
[core/table.lua:94]:-->Net Receive: friend.GS2CApplyList = {}
[core/table.lua:94]:-->Net Receive: friend.GS2CFriendSetting = {
|  setting = {
|  |  notify = 1
|  }
}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcFightInfoList = {
|  info_list = {
|  |  [1] = {
|  |  |  npc_type = 10024
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [10] = {
|  |  |  npc_type = 10007
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [11] = {
|  |  |  npc_type = 10008
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [12] = {
|  |  |  npc_type = 10009
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [13] = {
|  |  |  npc_type = 10011
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [14] = {
|  |  |  npc_type = 10012
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [15] = {
|  |  |  npc_type = 10013
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [16] = {
|  |  |  npc_type = 10000
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [17] = {
|  |  |  npc_type = 10023
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [18] = {
|  |  |  npc_type = 10014
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [19] = {
|  |  |  npc_type = 10015
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [2] = {
|  |  |  npc_type = 10002
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [20] = {
|  |  |  npc_type = 10016
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [21] = {
|  |  |  npc_type = 10017
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [22] = {
|  |  |  npc_type = 10018
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [23] = {
|  |  |  npc_type = 10019
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [24] = {
|  |  |  npc_type = 10020
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [25] = {
|  |  |  npc_type = 10021
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [26] = {
|  |  |  npc_type = 10022
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [3] = {
|  |  |  npc_type = 10010
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [4] = {
|  |  |  npc_type = 10001
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [5] = {
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [6] = {
|  |  |  npc_type = 10003
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [7] = {
|  |  |  npc_type = 10004
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [8] = {
|  |  |  npc_type = 10005
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [9] = {
|  |  |  npc_type = 10006
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2COnlineGift = {
|  onlinetime = 58
}
[logic/ui/CSprite.lua:159]:DynamicSprite dSprInfo    Item    10012
[logic/ui/CViewBase.lua:125]:CMainMenuView LoadDone!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/ui/CViewBase"]:139: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:258: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:216: in function <[string "logic/base/CResCtrl"]:211>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: item.GS2CLoginItem = {
|  itemdata = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1680355823
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 1
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 3
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  pos = 1
|  |  |  |  stone_sid = 3100000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 1
|  |  |  itemlevel = 1
|  |  |  name = "练习红魔钺"
|  |  |  power = 57
|  |  |  sid = 2100000
|  |  }
|  |  [2] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 121
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 24
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1680355823
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 17
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 1
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  pos = 2
|  |  |  |  stone_sid = 4000000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 2
|  |  |  itemlevel = 1
|  |  |  name = "练习飘逸巾"
|  |  |  power = 24
|  |  |  sid = 2200000
|  |  }
|  |  [3] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1680355823
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 17
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 1
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  pos = 3
|  |  |  |  stone_sid = 4110000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 3
|  |  |  itemlevel = 1
|  |  |  name = "练习絮语衣"
|  |  |  power = 32
|  |  |  sid = 2320000
|  |  }
|  |  [4] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1680355823
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 3
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "speed"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 7
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  pos = 4
|  |  |  |  stone_sid = 4200000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 4
|  |  |  itemlevel = 1
|  |  |  name = "练习草织戒"
|  |  |  power = 19
|  |  |  sid = 2400000
|  |  }
|  |  [5] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 364
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 72
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1680355823
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 17
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 3
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  pos = 5
|  |  |  |  stone_sid = 4310000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 5
|  |  |  itemlevel = 1
|  |  |  name = "练习离叶腰"
|  |  |  power = 72
|  |  |  sid = 2520000
|  |  }
|  |  [6] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 11
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "speed"
|  |  |  |  |  value = 33
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 77
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1680355823
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 3
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "speed"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 7
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  pos = 6
|  |  |  |  stone_sid = 4410000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 6
|  |  |  itemlevel = 1
|  |  |  name = "练习青藤鞋"
|  |  |  power = 77
|  |  |  sid = 2620000
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>loginitem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenPlanName = {
|  fuwen_name = {
|  |  [1] = {
|  |  |  name = "默认方案"
|  |  |  plan = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeScore = {
|  cur_id = 1
|  end_time = 1682388000
|  score_info = {}
|  start_time = 1679709600
|  status = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CGradeGiftInfo = {
|  buy_gift = {
|  |  items = {
|  |  |  [1] = {
|  |  |  |  amount = 3
|  |  |  |  sid = 10040
|  |  |  |  virtual = 10040
|  |  |  }
|  |  |  [2] = {
|  |  |  |  amount = 50
|  |  |  |  sid = 14011
|  |  |  |  virtual = 14011
|  |  |  }
|  |  |  [3] = {
|  |  |  |  amount = 66666
|  |  |  |  sid = 1002
|  |  |  |  virtual = 1002
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  discount = 2
|  endtime = 1680366654
|  free_gift = {
|  |  items = {
|  |  |  [1] = {
|  |  |  |  amount = 3
|  |  |  |  sid = 14011
|  |  |  |  virtual = 14011
|  |  |  }
|  |  |  [2] = {
|  |  |  |  amount = 6666
|  |  |  |  sid = 1002
|  |  |  |  virtual = 1002
|  |  |  }
|  |  }
|  }
|  grade = 30
|  ios_payid = "com.kaopu.ylq.appstore.lb.12"
|  now_price = 12
|  old_price = 600
|  payid = "com.kaopu.ylq.lb.12"
|  status = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CNewFieldBoss = {
|  bossid = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginTrapmine = {
|  rare_monster = {
|  |  [1] = {
|  |  |  map_id = 202000
|  |  |  partypes = {
|  |  |  |  [1] = 312
|  |  |  |  [2] = 401
|  |  |  |  [3] = 313
|  |  |  }
|  |  }
|  |  [2] = {
|  |  |  map_id = 204000
|  |  |  partypes = {
|  |  |  |  [1] = 404
|  |  |  |  [2] = 409
|  |  |  |  [3] = 403
|  |  |  }
|  |  }
|  |  [3] = {
|  |  |  map_id = 205000
|  |  |  partypes = {
|  |  |  |  [1] = 508
|  |  |  |  [2] = 509
|  |  |  |  [3] = 513
|  |  |  }
|  |  }
|  |  [4] = {
|  |  |  map_id = 206000
|  |  |  partypes = {
|  |  |  |  [1] = 412
|  |  |  |  [2] = 414
|  |  |  |  [3] = 415
|  |  |  }
|  |  }
|  |  [5] = {
|  |  |  map_id = 210400
|  |  |  partypes = {
|  |  |  |  [1] = 505
|  |  |  |  [2] = 506
|  |  |  |  [3] = 507
|  |  |  }
|  |  }
|  |  [6] = {
|  |  |  map_id = 200000
|  |  |  partypes = {
|  |  |  |  [1] = 416
|  |  |  |  [2] = 417
|  |  |  |  [3] = 501
|  |  |  }
|  |  }
|  |  [7] = {
|  |  |  map_id = 201000
|  |  |  partypes = {
|  |  |  |  [1] = 308
|  |  |  |  [2] = 302
|  |  |  |  [3] = 311
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartner = {}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPicturePosList = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 8901
|  |  |  type = "zsk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 8901
|  |  |  type = "yk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
|  mask = "c"
}
[core/table.lua:94]:CNetCtrl解析mask: GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 8901
|  |  |  type = "zsk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 8901
|  |  |  type = "yk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: store.GS2CPayForColorCoinInfo = {
|  colorcoin_list = {
|  |  [1] = {
|  |  |  key = "goldcoinstore_1005"
|  |  }
|  |  [10] = {
|  |  |  key = "goldcoinstore_2004"
|  |  }
|  |  [11] = {
|  |  |  key = "goldcoinstore_2005"
|  |  }
|  |  [12] = {
|  |  |  key = "goldcoinstore_2006"
|  |  }
|  |  [13] = {
|  |  |  key = "goldcoinstore_2007"
|  |  }
|  |  [14] = {
|  |  |  key = "goldcoinstore_1003"
|  |  }
|  |  [15] = {
|  |  |  key = "goldcoinstore_1004"
|  |  }
|  |  [2] = {
|  |  |  key = "goldcoinstore_1006"
|  |  }
|  |  [3] = {
|  |  |  key = "goldcoinstore_1007"
|  |  }
|  |  [4] = {
|  |  |  key = "goldcoinstore_1008"
|  |  }
|  |  [5] = {
|  |  |  key = "goldcoinstore_1001"
|  |  }
|  |  [6] = {
|  |  |  key = "goldcoinstore_1002"
|  |  }
|  |  [7] = {
|  |  |  key = "goldcoinstore_2001"
|  |  }
|  |  [8] = {
|  |  |  key = "goldcoinstore_2002"
|  |  }
|  |  [9] = {
|  |  |  key = "goldcoinstore_2003"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: skill.GS2CLoginSkill = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3001
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3002
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  needcost = 15
|  |  |  sk = 3004
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  needcost = 25
|  |  |  sk = 3005
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3006
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3007
|  |  |  type = 1
|  |  }
|  }
}
[logic/skill/CSkillCtrl.lua:112]:技能数据登陆协议返回:
[core/table.lua:94]:Table = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3001
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3002
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  needcost = 15
|  |  |  sk = 3004
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  needcost = 25
|  |  |  sk = 3005
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3006
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3007
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeRewrad = {
|  end_time = 1682388000
|  reward_info = {
|  |  [1] = {
|  |  |  left_amount = 2
|  |  |  rmb = 328
|  |  }
|  |  [2] = {
|  |  |  left_amount = 2
|  |  |  rmb = 648
|  |  }
|  }
|  schedule = 1
|  start_time = 1679709600
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraWarState = {
|  state = 2
|  time = 1680364800
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginParSoulPlan = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraQueueStatus = {}
[core/table.lua:94]:-->Net Receive: arena.GS2CRefreshTeamArenaLeftTime = {
|  end_time = 1680357600
|  start_time = 1680354000
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliReddot = {
|  cday = 1
|  flag = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTimeResumeInfo = {
|  end_time = 1682388000
|  plan_id = 1
|  start_time = 1679709600
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CRefreshTimeResume = {}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterScene = {
|  eid = 35
|  pos_info = {
|  |  face_y = 4861
|  |  x = 21590
|  |  y = 20288
|  }
|  scene_id = 12
}
[core/global.lua:59]:<color=#ffeb04>result  Partner_FWCD_Onen</color>
[core/table.lua:94]:<--Net Send: teach.C2GSClearGuidance = {
|  key = {
|  |  [1] = 1001099
|  }
}
[core/global.lua:59]:<color=#ffeb04>=00000=  userdata: 0x8ec6eae0 nil</color>
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = **********
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 21:31:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Partner_FWCD_One_MainMenu"
|  |  [10] = "Open_Lilian"
|  |  [11] = "Open_Org"
|  |  [12] = "Open_Forge"
|  |  [13] = "Open_Equipfuben"
|  |  [14] = "Open_Arena"
|  |  [15] = "Open_MingLei"
|  |  [16] = "Open_Trapmine"
|  |  [17] = "Open_Pefuben"
|  |  [18] = "Open_Convoy"
|  |  [19] = "Open_Travel"
|  |  [2] = "Open_ZhaoMu"
|  |  [20] = "Open_Pata"
|  |  [21] = "Open_MapBook"
|  |  [22] = "Open_Forge_composite"
|  |  [23] = "Open_YJFuben"
|  |  [24] = "Open_FieldBoss"
|  |  [25] = "Open_EqualArena"
|  |  [3] = "Open_ZhaoMu_Two"
|  |  [4] = "Open_ZhaoMu_Three"
|  |  [5] = "Open_Skill_Three"
|  |  [6] = "Open_Skill_Four"
|  |  [7] = "Open_Shimen"
|  |  [8] = "Open_House"
|  |  [9] = "Open_Achieve"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>找到新的引导类型 Partner_FWCD_One_MainMenu</color>
[core/global.lua:59]:<color=#ffeb04>读取地图： 1020</color>
[core/global.lua:59]:<color=#ffeb04>Unity2017.4.30f1 localScale</color>
[logic/ui/CViewCtrl.lua:104]:CLoginView     CloseView
[logic/base/CResCtrl.lua:596]:res gc step start!
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[core/global.lua:59]:<color=#ffeb04>地图加载完成: 1020 ,当前地图: nil</color>
[core/table.lua:94]:Table = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHDDayChargeInfo = {
|  code = 2
|  endtime = 1682388000
|  starttime = 1679709600
}
[core/table.lua:94]:-->Net Receive: huodong.GS2COneRMBGift = {
|  endtime = 1682388000
|  gift = {
|  |  [1] = {
|  |  |  key = 1
|  |  }
|  |  [2] = {
|  |  |  key = 2
|  |  }
|  |  [3] = {
|  |  |  key = 3
|  |  }
|  }
|  starttime = 1679709600
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHDAddChargeInfo = {
|  endtime = 1682388000
|  list = {
|  |  [1] = {
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  id = 2
|  |  }
|  |  [3] = {
|  |  |  id = 3
|  |  }
|  |  [4] = {
|  |  |  id = 4
|  |  }
|  |  [5] = {
|  |  |  id = 5
|  |  }
|  }
|  starttime = 1679709600
}
[core/table.lua:94]:-->Net Receive: partner.GS2CDrawCardUI = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHeroBoxRecord = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CRankBack = {
|  endtime = 1682388000
|  starttime = 1679709600
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CDailySignInfo = {
|  sign_info = {
|  |  [1] = {
|  |  |  key = "week"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CRefreshRewardBack = {}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayBuy = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CHistoryCharge = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFirstChargeUI = {
|  bOpen = true
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliPoint = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawCnt = {
|  cnt = 1
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliTime = {
|  endtime = 1682388000
|  starttime = 1679709600
}
[core/table.lua:94]:-->Net Receive: house.GS2CEnterHouse = {
|  buff_info = {}
|  furniture_info = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  lock_status = 1
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  type = 2
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  type = 3
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  type = 4
|  |  }
|  |  [5] = {
|  |  |  level = 1
|  |  |  type = 5
|  |  }
|  }
|  handle_type = 1
|  max_train = 1
|  max_warm_degree = 1000
|  owner_pid = 10124
|  partner_info = {
|  |  [1] = {
|  |  |  coin = 2190
|  |  |  type = 1001
|  |  }
|  }
|  warm_degree = 2
}
[core/table.lua:94]:-->Net Receive: house.GS2CPartnerExchangeUI = {
|  handle_type = 1
|  love_cnt = 10
|  max_gift_cnt = 10
|  max_love_cnt = 10
|  partner_gift_cnt = 10
}
[core/table.lua:94]:-->Net Receive: house.GS2COpenWorkDesk = {
|  desk_info = {
|  |  [1] = {
|  |  |  lock_status = 1
|  |  |  pos = 1
|  |  |  status = 1
|  |  }
|  |  [2] = {
|  |  |  pos = 2
|  |  |  status = 1
|  |  }
|  |  [3] = {
|  |  |  pos = 3
|  |  |  status = 1
|  |  }
|  |  [4] = {
|  |  |  lock_status = 1
|  |  |  pos = 4
|  |  |  status = 1
|  |  }
|  }
|  handle_type = 1
|  owner_pid = 10124
}
[core/table.lua:94]:-->Net Receive: player.GS2CTodayInfo = {
|  info = {
|  |  mask = "0"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: todyInfo = {}
[core/table.lua:94]:-->Net Receive: player.GS2CGameShare = {
|  game_share = {
|  |  [1] = {
|  |  |  type = "draw_card_share"
|  |  }
|  |  [2] = {
|  |  |  type = "skin_share"
|  |  }
|  |  [3] = {
|  |  |  type = "picture_share"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginRewardInfo = {
|  login_day = 1
}
[logic/ui/CViewCtrl.lua:94]:CLoginRewardView ShowView
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {
|  infolist = {
|  |  [1] = {
|  |  |  blist = {
|  |  |  |  [1] = 1
|  |  |  }
|  |  |  id = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  abnormal_attr_ratio = 820
|  |  attack = 545
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 46
|  |  hp = 8494
|  |  mask = "87ff00"
|  |  max_hp = 8753
|  |  power = 2563
|  |  res_abnormal_ratio = 820
|  |  res_critical_ratio = 500
|  |  speed = 767
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 820
|  attack = 545
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 46
|  hp = 8494
|  max_hp = 8753
|  power = 2563
|  res_abnormal_ratio = 820
|  res_critical_ratio = 500
|  speed = 767
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayRedDot = {
|  days = {
|  |  [1] = 4
|  |  [2] = 1
|  |  [3] = 2
|  |  [4] = 3
|  |  [5] = 5
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayMain = {}
[core/table.lua:94]:-->Net Receive: task.GS2CLoginAchieveTask = {
|  info = {
|  |  [1] = {
|  |  |  achievetype = 1
|  |  |  describe = "领取1次在线奖励"
|  |  |  name = "在线奖励"
|  |  |  target = 1
|  |  |  taskid = 31001
|  |  }
|  |  [2] = {
|  |  |  achievetype = 2
|  |  |  describe = "穿戴一件符文"
|  |  |  name = "穿戴符文（1）"
|  |  |  target = 1
|  |  |  taskid = 31524
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>LoginAchieveTask</color>
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2510
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  mask = "1997c2e0000000"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  give_org_equip = {}
|  give_org_wish = {}
|  is_equip_wish = 0
|  is_org_wish = 0
|  org_build_status = 0
|  org_build_time = 0
|  org_id = 0
|  org_leader = ""
|  org_level = 0
|  org_pos = 0
|  org_red_packet = 0
|  org_sign_reward = 0
|  org_status = 0
|  orgname = ""
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2563
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayDegree = {
|  info = {
|  |  cur = 2563
|  |  id = 10602
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1102
|  |  |  }
|  |  |  name = "峰馆长"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 2
|  |  npctype = 5040
|  }
|  eid = 1
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 1
|  pos_info = {
|  |  x = 31800
|  |  y = 17600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1102
|  }
|  name = "峰馆长"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1508
|  |  |  }
|  |  |  name = "小叶"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 5
|  |  npctype = 5041
|  }
|  eid = 2
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 2
|  pos_info = {
|  |  x = 40000
|  |  y = 14600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1508
|  }
|  name = "小叶"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1009
|  |  |  }
|  |  |  name = "神父"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 8
|  |  npctype = 5042
|  }
|  eid = 3
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 3
|  pos_info = {
|  |  x = 12200
|  |  y = 25000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1009
|  }
|  name = "神父"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1012
|  |  |  }
|  |  |  name = "克瑞斯汀"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 24
|  |  npctype = 5048
|  }
|  eid = 7
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 7
|  pos_info = {
|  |  x = 20200
|  |  y = 21100
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1012
|  }
|  name = "克瑞斯汀"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1510
|  |  |  }
|  |  |  name = "小佳"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 57
|  |  npctype = 5064
|  }
|  eid = 8
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 8
|  pos_info = {
|  |  x = 13800
|  |  y = 20000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1510
|  }
|  name = "小佳"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
|  [3] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 306
|  |  |  }
|  |  |  name = "袁雀"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 60
|  |  npctype = 5001
|  }
|  eid = 9
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 9
|  pos_info = {
|  |  x = 6600
|  |  y = 26200
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 306
|  }
|  name = "袁雀"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1501
|  |  |  }
|  |  |  name = "扳尾"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 63
|  |  npctype = 5002
|  }
|  eid = 10
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 10
|  pos_info = {
|  |  x = 6000
|  |  y = 21200
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1501
|  }
|  name = "扳尾"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1014
|  |  |  }
|  |  |  name = "乔焱"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 66
|  |  npctype = 5003
|  }
|  eid = 11
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 11
|  pos_info = {
|  |  x = 13200
|  |  y = 24600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1014
|  }
|  name = "乔焱"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1010
|  |  |  }
|  |  |  name = "丽丝"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 69
|  |  npctype = 5004
|  }
|  eid = 12
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 12
|  pos_info = {
|  |  x = 45600
|  |  y = 26900
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1010
|  }
|  name = "丽丝"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1011
|  |  |  }
|  |  |  name = "遥"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 72
|  |  npctype = 5005
|  }
|  eid = 13
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 13
|  pos_info = {
|  |  x = 45500
|  |  y = 20000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1011
|  }
|  name = "遥"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 414
|  |  |  }
|  |  |  name = "蓝堂昼"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 75
|  |  npctype = 5006
|  }
|  eid = 14
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 14
|  pos_info = {
|  |  x = 28000
|  |  y = 14500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 414
|  }
|  name = "蓝堂昼"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 311
|  |  |  }
|  |  |  name = "花常酒"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 78
|  |  npctype = 5007
|  }
|  eid = 15
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 15
|  pos_info = {
|  |  x = 35600
|  |  y = 13400
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 311
|  }
|  name = "花常酒"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 508
|  |  |  }
|  |  |  name = "魂夕"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 81
|  |  npctype = 5008
|  }
|  eid = 16
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 16
|  pos_info = {
|  |  x = 24000
|  |  y = 12900
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 508
|  }
|  name = "魂夕"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  shape = 513
|  |  |  }
|  |  |  name = "夜叉"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 8904
|  |  npctype = 1002
|  |  titlename = "协同比武接引"
|  }
|  eid = 32
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 32
|  pos_info = {
|  |  x = 16000
|  |  y = 12000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  shape = 513
|  }
|  name = "夜叉"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1016
|  |  |  }
|  |  |  name = "水生"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 196
|  |  npctype = 5009
|  }
|  eid = 17
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 17
|  pos_info = {
|  |  x = 24000
|  |  y = 22000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1016
|  }
|  name = "水生"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 417
|  |  |  }
|  |  |  name = "松姑子"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 220
|  |  npctype = 5019
|  }
|  eid = 21
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 21
|  pos_info = {
|  |  x = 42300
|  |  y = 24600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 417
|  }
|  name = "松姑子"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1502
|  |  |  }
|  |  |  name = "荆鸣"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 205
|  |  npctype = 5012
|  }
|  eid = 20
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 20
|  pos_info = {
|  |  x = 7000
|  |  y = 11300
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1502
|  }
|  name = "荆鸣"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: title.GS2CTitleInfoList = {
|  infos = {
|  |  [1] = {
|  |  |  name = "8段"
|  |  |  tid = 1008
|  |  }
|  |  [10] = {
|  |  |  name = "应用宝贵族"
|  |  |  tid = 1017
|  |  }
|  |  [11] = {
|  |  |  name = "王者公会"
|  |  |  tid = 1018
|  |  }
|  |  [12] = {
|  |  |  name = "王者公会会长"
|  |  |  tid = 1019
|  |  }
|  |  [13] = {
|  |  |  name = "黄金公会"
|  |  |  tid = 1020
|  |  }
|  |  [14] = {
|  |  |  name = "黄金公会会长"
|  |  |  tid = 1021
|  |  }
|  |  [15] = {
|  |  |  name = "白银公会"
|  |  |  tid = 1022
|  |  }
|  |  [16] = {
|  |  |  name = "白银公会会长"
|  |  |  tid = 1023
|  |  }
|  |  [17] = {
|  |  |  name = "明日之星"
|  |  |  tid = 1024
|  |  }
|  |  [18] = {
|  |  |  name = "独孤求败"
|  |  |  tid = 1025
|  |  }
|  |  [19] = {
|  |  |  name = "谁与争锋"
|  |  |  tid = 1026
|  |  }
|  |  [2] = {
|  |  |  name = "测-贵族宝"
|  |  |  tid = 1009
|  |  }
|  |  [20] = {
|  |  |  name = "所向披靡"
|  |  |  tid = 1027
|  |  }
|  |  [21] = {
|  |  |  name = "地牢终结者"
|  |  |  tid = 1028
|  |  }
|  |  [22] = {
|  |  |  name = "地牢征服者"
|  |  |  tid = 1029
|  |  }
|  |  [23] = {
|  |  |  name = "地牢破坏者"
|  |  |  tid = 1030
|  |  }
|  |  [24] = {
|  |  |  name = "眯眯眼是变态"
|  |  |  tid = 1031
|  |  }
|  |  [25] = {
|  |  |  name = "邻家妹妹"
|  |  |  tid = 1032
|  |  }
|  |  [26] = {
|  |  |  name = "兄控"
|  |  |  tid = 1033
|  |  }
|  |  [27] = {
|  |  |  name = "农夫三拳"
|  |  |  tid = 1034
|  |  }
|  |  [28] = {
|  |  |  name = "赌神"
|  |  |  tid = 1035
|  |  }
|  |  [29] = {
|  |  |  name = "神之右手"
|  |  |  tid = 1036
|  |  }
|  |  [3] = {
|  |  |  name = "测-我就是托"
|  |  |  tid = 1010
|  |  }
|  |  [30] = {
|  |  |  name = "茶会噩梦"
|  |  |  tid = 1037
|  |  }
|  |  [31] = {
|  |  |  name = "我是傲娇"
|  |  |  tid = 1038
|  |  }
|  |  [32] = {
|  |  |  name = "闷骚美男子"
|  |  |  tid = 1039
|  |  }
|  |  [33] = {
|  |  |  name = "脸炮"
|  |  |  tid = 1040
|  |  }
|  |  [34] = {
|  |  |  name = "妹控"
|  |  |  tid = 1041
|  |  }
|  |  [35] = {
|  |  |  name = "守夜人"
|  |  |  tid = 1042
|  |  }
|  |  [36] = {
|  |  |  name = "泡泡娘"
|  |  |  tid = 1043
|  |  }
|  |  [37] = {
|  |  |  name = "姐控"
|  |  |  tid = 1044
|  |  }
|  |  [38] = {
|  |  |  name = "迷妹"
|  |  |  tid = 1045
|  |  }
|  |  [39] = {
|  |  |  name = "小天使"
|  |  |  tid = 1046
|  |  }
|  |  [4] = {
|  |  |  name = "测-粗壮大腿"
|  |  |  tid = 1011
|  |  }
|  |  [40] = {
|  |  |  name = "欺诈兔子"
|  |  |  tid = 1047
|  |  }
|  |  [41] = {
|  |  |  name = "天然呆"
|  |  |  tid = 1048
|  |  }
|  |  [42] = {
|  |  |  name = "开局一条狗"
|  |  |  tid = 1049
|  |  }
|  |  [43] = {
|  |  |  name = "种竹专业户"
|  |  |  tid = 1050
|  |  }
|  |  [44] = {
|  |  |  name = "顾家好男人"
|  |  |  tid = 1051
|  |  }
|  |  [45] = {
|  |  |  name = "无敌小钢炮"
|  |  |  tid = 1052
|  |  }
|  |  [46] = {
|  |  |  name = "小豆丁"
|  |  |  tid = 1053
|  |  }
|  |  [47] = {
|  |  |  name = "浪里白条"
|  |  |  tid = 1054
|  |  }
|  |  [48] = {
|  |  |  name = "话唠"
|  |  |  tid = 1055
|  |  }
|  |  [49] = {
|  |  |  name = "角落种蘑菇"
|  |  |  tid = 1056
|  |  }
|  |  [5] = {
|  |  |  name = "攻防战第一公会"
|  |  |  tid = 1012
|  |  }
|  |  [50] = {
|  |  |  name = "天然疯"
|  |  |  tid = 1057
|  |  }
|  |  [51] = {
|  |  |  name = "吃货"
|  |  |  tid = 1058
|  |  }
|  |  [52] = {
|  |  |  name = "控制狂"
|  |  |  tid = 1059
|  |  }
|  |  [53] = {
|  |  |  name = "女王大人"
|  |  |  tid = 1060
|  |  }
|  |  [54] = {
|  |  |  name = "八卦小王子"
|  |  |  tid = 1061
|  |  }
|  |  [55] = {
|  |  |  name = "三无"
|  |  |  tid = 1062
|  |  }
|  |  [56] = {
|  |  |  name = "养得累"
|  |  |  tid = 1063
|  |  }
|  |  [57] = {
|  |  |  name = "扑街之王"
|  |  |  tid = 1064
|  |  }
|  |  [58] = {
|  |  |  name = "抖S"
|  |  |  tid = 1065
|  |  }
|  |  [59] = {
|  |  |  name = "工作狂人"
|  |  |  tid = 1066
|  |  }
|  |  [6] = {
|  |  |  name = "攻防战第一司令"
|  |  |  tid = 1013
|  |  }
|  |  [60] = {
|  |  |  name = "精分中毒患者"
|  |  |  tid = 1067
|  |  }
|  |  [61] = {
|  |  |  name = "神神叨叨"
|  |  |  tid = 1068
|  |  }
|  |  [62] = {
|  |  |  name = "浮夸之神"
|  |  |  tid = 1069
|  |  }
|  |  [63] = {
|  |  |  name = "抖M"
|  |  |  tid = 1070
|  |  }
|  |  [64] = {
|  |  |  name = "自恋狂"
|  |  |  tid = 1071
|  |  }
|  |  [65] = {
|  |  |  name = "天然黑"
|  |  |  tid = 1072
|  |  }
|  |  [66] = {
|  |  |  name = "为所欲为"
|  |  |  tid = 1073
|  |  }
|  |  [67] = {
|  |  |  name = "氪金大佬"
|  |  |  tid = 1074
|  |  }
|  |  [68] = {
|  |  |  name = "应用宝帝王"
|  |  |  tid = 1075
|  |  }
|  |  [69] = {
|  |  |  name = "应用宝至尊"
|  |  |  tid = 1076
|  |  }
|  |  [7] = {
|  |  |  name = "梦魇之主"
|  |  |  tid = 1014
|  |  }
|  |  [70] = {
|  |  |  name = "公会新人"
|  |  |  tid = 1077
|  |  }
|  |  [71] = {
|  |  |  name = "公会成员"
|  |  |  tid = 1078
|  |  }
|  |  [72] = {
|  |  |  name = "公会精英"
|  |  |  tid = 1079
|  |  }
|  |  [73] = {
|  |  |  name = "公会副会"
|  |  |  tid = 1080
|  |  }
|  |  [74] = {
|  |  |  name = "公会会长"
|  |  |  tid = 1081
|  |  }
|  |  [75] = {
|  |  |  name = "情侣专属称谓"
|  |  |  tid = 1082
|  |  }
|  |  [76] = {
|  |  |  name = "命运之子"
|  |  |  tid = 1083
|  |  }
|  |  [77] = {
|  |  |  name = "不做大多数"
|  |  |  tid = 1084
|  |  }
|  |  [78] = {
|  |  |  name = "1段"
|  |  |  tid = 1001
|  |  }
|  |  [79] = {
|  |  |  name = "2段"
|  |  |  tid = 1002
|  |  }
|  |  [8] = {
|  |  |  name = "梦魇骑士"
|  |  |  tid = 1015
|  |  }
|  |  [80] = {
|  |  |  name = "3段"
|  |  |  tid = 1003
|  |  }
|  |  [81] = {
|  |  |  name = "4段"
|  |  |  tid = 1004
|  |  }
|  |  [82] = {
|  |  |  name = "5段"
|  |  |  tid = 1005
|  |  }
|  |  [83] = {
|  |  |  name = "6段"
|  |  |  tid = 1006
|  |  }
|  |  [84] = {
|  |  |  name = "7段"
|  |  |  tid = 1007
|  |  }
|  |  [9] = {
|  |  |  name = "梦魇猎人"
|  |  |  tid = 1016
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:94]:CGuideView ShowView
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Partner_FWCD_One_MainMenu"
|  |  [10] = "Open_Lilian"
|  |  [11] = "Open_Org"
|  |  [12] = "Open_Forge"
|  |  [13] = "Open_Equipfuben"
|  |  [14] = "Open_Arena"
|  |  [15] = "Open_MingLei"
|  |  [16] = "Open_Trapmine"
|  |  [17] = "Open_Pefuben"
|  |  [18] = "Open_Convoy"
|  |  [19] = "Open_Travel"
|  |  [2] = "Open_ZhaoMu"
|  |  [20] = "Open_Pata"
|  |  [21] = "Open_MapBook"
|  |  [22] = "Open_Forge_composite"
|  |  [23] = "Open_YJFuben"
|  |  [24] = "Open_FieldBoss"
|  |  [25] = "Open_EqualArena"
|  |  [3] = "Open_ZhaoMu_Two"
|  |  [4] = "Open_ZhaoMu_Three"
|  |  [5] = "Open_Skill_Three"
|  |  [6] = "Open_Skill_Four"
|  |  [7] = "Open_Shimen"
|  |  [8] = "Open_House"
|  |  [9] = "Open_Achieve"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/mainmenu/CMainMenuExpandBox"]:280: in function <[string "logic/mainmenu/CMainMenuExpandBox"]:251>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Partner_FWCD_One_MainMenu"
|  |  [10] = "Open_Lilian"
|  |  [11] = "Open_Org"
|  |  [12] = "Open_Forge"
|  |  [13] = "Open_Equipfuben"
|  |  [14] = "Open_Arena"
|  |  [15] = "Open_MingLei"
|  |  [16] = "Open_Trapmine"
|  |  [17] = "Open_Pefuben"
|  |  [18] = "Open_Convoy"
|  |  [19] = "Open_Travel"
|  |  [2] = "Open_ZhaoMu"
|  |  [20] = "Open_Pata"
|  |  [21] = "Open_MapBook"
|  |  [22] = "Open_Forge_composite"
|  |  [23] = "Open_YJFuben"
|  |  [24] = "Open_FieldBoss"
|  |  [25] = "Open_EqualArena"
|  |  [3] = "Open_ZhaoMu_Two"
|  |  [4] = "Open_ZhaoMu_Three"
|  |  [5] = "Open_Skill_Three"
|  |  [6] = "Open_Skill_Four"
|  |  [7] = "Open_Shimen"
|  |  [8] = "Open_House"
|  |  [9] = "Open_Achieve"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Partner_FWCD_One_MainMenu"
|  |  [10] = "Open_Lilian"
|  |  [11] = "Open_Org"
|  |  [12] = "Open_Forge"
|  |  [13] = "Open_Equipfuben"
|  |  [14] = "Open_Arena"
|  |  [15] = "Open_MingLei"
|  |  [16] = "Open_Trapmine"
|  |  [17] = "Open_Pefuben"
|  |  [18] = "Open_Convoy"
|  |  [19] = "Open_Travel"
|  |  [2] = "Open_ZhaoMu"
|  |  [20] = "Open_Pata"
|  |  [21] = "Open_MapBook"
|  |  [22] = "Open_Forge_composite"
|  |  [23] = "Open_YJFuben"
|  |  [24] = "Open_FieldBoss"
|  |  [25] = "Open_EqualArena"
|  |  [3] = "Open_ZhaoMu_Two"
|  |  [4] = "Open_ZhaoMu_Three"
|  |  [5] = "Open_Skill_Three"
|  |  [6] = "Open_Skill_Four"
|  |  [7] = "Open_Shimen"
|  |  [8] = "Open_House"
|  |  [9] = "Open_Achieve"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Partner_FWCD_One_MainMenu"
|  |  [10] = "Open_Lilian"
|  |  [11] = "Open_Org"
|  |  [12] = "Open_Forge"
|  |  [13] = "Open_Equipfuben"
|  |  [14] = "Open_Arena"
|  |  [15] = "Open_MingLei"
|  |  [16] = "Open_Trapmine"
|  |  [17] = "Open_Pefuben"
|  |  [18] = "Open_Convoy"
|  |  [19] = "Open_Travel"
|  |  [2] = "Open_ZhaoMu"
|  |  [20] = "Open_Pata"
|  |  [21] = "Open_MapBook"
|  |  [22] = "Open_Forge_composite"
|  |  [23] = "Open_YJFuben"
|  |  [24] = "Open_FieldBoss"
|  |  [25] = "Open_EqualArena"
|  |  [3] = "Open_ZhaoMu_Two"
|  |  [4] = "Open_ZhaoMu_Three"
|  |  [5] = "Open_Skill_Three"
|  |  [6] = "Open_Skill_Four"
|  |  [7] = "Open_Shimen"
|  |  [8] = "Open_House"
|  |  [9] = "Open_Achieve"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Partner_FWCD_One_MainMenu"
|  |  [10] = "Open_Lilian"
|  |  [11] = "Open_Org"
|  |  [12] = "Open_Forge"
|  |  [13] = "Open_Equipfuben"
|  |  [14] = "Open_Arena"
|  |  [15] = "Open_MingLei"
|  |  [16] = "Open_Trapmine"
|  |  [17] = "Open_Pefuben"
|  |  [18] = "Open_Convoy"
|  |  [19] = "Open_Travel"
|  |  [2] = "Open_ZhaoMu"
|  |  [20] = "Open_Pata"
|  |  [21] = "Open_MapBook"
|  |  [22] = "Open_Forge_composite"
|  |  [23] = "Open_YJFuben"
|  |  [24] = "Open_FieldBoss"
|  |  [25] = "Open_EqualArena"
|  |  [3] = "Open_ZhaoMu_Two"
|  |  [4] = "Open_ZhaoMu_Three"
|  |  [5] = "Open_Skill_Three"
|  |  [6] = "Open_Skill_Four"
|  |  [7] = "Open_Shimen"
|  |  [8] = "Open_House"
|  |  [9] = "Open_Achieve"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/mainmenu/CMainMenuExpandBox"]:280: in function <[string "logic/mainmenu/CMainMenuExpandBox"]:251>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[logic/platform/CSdkCtrl.lua:380]:sdk upload: not init
[core/table.lua:94]:Table = {
|  [1] = "16"
|  [2] = "20"
}
[core/global.lua:59]:<color=#ffeb04>56 16 20</color>
[core/table.lua:94]:Table = {
|  [1] = "21"
|  [2] = "24"
}
[core/global.lua:59]:<color=#ffeb04>56 21 24</color>
[core/table.lua:94]:Table = {
|  [1] = "25"
|  [2] = "27"
}
[core/global.lua:59]:<color=#ffeb04>56 25 27</color>
[logic/ui/CViewBase.lua:125]:CLoginRewardView LoadDone!
[logic/ui/CViewBase.lua:125]:CGuideView LoadDone!
[logic/base/CResCtrl.lua:623]:res gc finish!
[logic/ui/CViewCtrl.lua:104]:CLoginRewardView     CloseView
[core/global.lua:59]:<color=#ffeb04>找到新的引导类型 Partner_FWCD_One_MainMenu</color>
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 Partner_FWCD_One_MainMenu_1 1</color>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Partner_FWCD_One_MainMenu"
|  |  [10] = "Open_Lilian"
|  |  [11] = "Open_Org"
|  |  [12] = "Open_Forge"
|  |  [13] = "Open_Equipfuben"
|  |  [14] = "Open_Arena"
|  |  [15] = "Open_MingLei"
|  |  [16] = "Open_Trapmine"
|  |  [17] = "Open_Pefuben"
|  |  [18] = "Open_Convoy"
|  |  [19] = "Open_Travel"
|  |  [2] = "Open_ZhaoMu"
|  |  [20] = "Open_Pata"
|  |  [21] = "Open_MapBook"
|  |  [22] = "Open_Forge_composite"
|  |  [23] = "Open_YJFuben"
|  |  [24] = "Open_FieldBoss"
|  |  [25] = "Open_EqualArena"
|  |  [3] = "Open_ZhaoMu_Two"
|  |  [4] = "Open_ZhaoMu_Three"
|  |  [5] = "Open_Skill_Three"
|  |  [6] = "Open_Skill_Four"
|  |  [7] = "Open_Shimen"
|  |  [8] = "Open_House"
|  |  [9] = "Open_Achieve"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Partner_FWCD_One_MainMenu"
|  |  [10] = "Open_Lilian"
|  |  [11] = "Open_Org"
|  |  [12] = "Open_Forge"
|  |  [13] = "Open_Equipfuben"
|  |  [14] = "Open_Arena"
|  |  [15] = "Open_MingLei"
|  |  [16] = "Open_Trapmine"
|  |  [17] = "Open_Pefuben"
|  |  [18] = "Open_Convoy"
|  |  [19] = "Open_Travel"
|  |  [2] = "Open_ZhaoMu"
|  |  [20] = "Open_Pata"
|  |  [21] = "Open_MapBook"
|  |  [22] = "Open_Forge_composite"
|  |  [23] = "Open_YJFuben"
|  |  [24] = "Open_FieldBoss"
|  |  [25] = "Open_EqualArena"
|  |  [3] = "Open_ZhaoMu_Two"
|  |  [4] = "Open_ZhaoMu_Three"
|  |  [5] = "Open_Skill_Three"
|  |  [6] = "Open_Skill_Four"
|  |  [7] = "Open_Shimen"
|  |  [8] = "Open_House"
|  |  [9] = "Open_Achieve"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>引导结束 Partner_FWCD_One_MainMenu Partner_FWCD_One_MainMenu_1 1</color>
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Partner_FWCD_One_MainMenu_1"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1000101
|  }
}
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 Partner_FWCD_One_MainMenu_2 2</color>
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Partner_FWCD_One_MainMenu_2"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1000102
|  }
}
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Partner_FWCD_One_MainMenu"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1001099
|  }
}
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_ZhaoMu"
|  |  [10] = "Open_Org"
|  |  [11] = "Open_Forge"
|  |  [12] = "Open_Equipfuben"
|  |  [13] = "Open_Arena"
|  |  [14] = "Open_MingLei"
|  |  [15] = "Open_Trapmine"
|  |  [16] = "Open_Pefuben"
|  |  [17] = "Open_Convoy"
|  |  [18] = "Open_Travel"
|  |  [19] = "Open_Pata"
|  |  [2] = "Open_ZhaoMu_Two"
|  |  [20] = "Open_MapBook"
|  |  [21] = "Open_Forge_composite"
|  |  [22] = "Open_YJFuben"
|  |  [23] = "Open_FieldBoss"
|  |  [24] = "Open_EqualArena"
|  |  [3] = "Open_ZhaoMu_Three"
|  |  [4] = "Open_Skill_Three"
|  |  [5] = "Open_Skill_Four"
|  |  [6] = "Open_Shimen"
|  |  [7] = "Open_House"
|  |  [8] = "Open_Achieve"
|  |  [9] = "Open_Lilian"
|  }
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[logic/ui/CViewBase.lua:131]:CGuideMaskView LoadDone, not in loadingview!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/ui/CViewBase"]:139: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:258: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:245: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:224: in function <[string "logic/base/CResCtrl"]:223>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:265: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:308: in function <[string "logic/base/CResCtrl"]:302>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680355909
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 21:31:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680355919
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 21:31:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680355929
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 21:32:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680355939
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 21:32:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680355949
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 21:32:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680355959
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 21:32:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680355969
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 21:32:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680355979
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 21:32:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680355989
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 21:33:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680355999
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 21:33:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680356009
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 21:33:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>引导结束 Partner_FWCD_One_MainMenu Partner_FWCD_One_MainMenu_2 2</color>
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Partner_FWCD_One_MainMenu_2"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1000102
|  }
}
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewCtrl.lua:104]:CGuideView     CloseView
[logic/guide/CGuideCtrl.lua:333]:已完成引导Partner_FWCD_One_MainMenu
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Partner_FWCD_One_MainMenu"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1001099
|  }
}
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/guide/CGuideCtrl"]:338: in function 'Continue'
	[string "logic/guide/CGuideView"]:426: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function 'func'
	[string "logic/guide/CGuideView"]:116: in function <[string "logic/guide/CGuideView"]:116>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_ZhaoMu"
|  |  [10] = "Open_Org"
|  |  [11] = "Open_Forge"
|  |  [12] = "Open_Equipfuben"
|  |  [13] = "Open_Arena"
|  |  [14] = "Open_MingLei"
|  |  [15] = "Open_Trapmine"
|  |  [16] = "Open_Pefuben"
|  |  [17] = "Open_Convoy"
|  |  [18] = "Open_Travel"
|  |  [19] = "Open_Pata"
|  |  [2] = "Open_ZhaoMu_Two"
|  |  [20] = "Open_MapBook"
|  |  [21] = "Open_Forge_composite"
|  |  [22] = "Open_YJFuben"
|  |  [23] = "Open_FieldBoss"
|  |  [24] = "Open_EqualArena"
|  |  [3] = "Open_ZhaoMu_Three"
|  |  [4] = "Open_Skill_Three"
|  |  [5] = "Open_Skill_Four"
|  |  [6] = "Open_Shimen"
|  |  [7] = "Open_House"
|  |  [8] = "Open_Achieve"
|  |  [9] = "Open_Lilian"
|  }
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/guide/CGuideCtrl"]:338: in function 'Continue'
	[string "logic/guide/CGuideView"]:426: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function 'func'
	[string "logic/guide/CGuideView"]:116: in function <[string "logic/guide/CGuideView"]:116>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[core/global.lua:59]:<color=#ffeb04>找到新的引导类型 Partner_FWCD_One_PartnerMain</color>
[logic/ui/CViewBase.lua:125]:CGuideMaskView LoadDone!
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/ui/CViewBase"]:139: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:258: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:245: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:224: in function <[string "logic/base/CResCtrl"]:223>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:265: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:297: in function <[string "logic/base/CResCtrl"]:294>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_ZhaoMu"
|  |  [10] = "Open_Org"
|  |  [11] = "Open_Forge"
|  |  [12] = "Open_Equipfuben"
|  |  [13] = "Open_Arena"
|  |  [14] = "Open_MingLei"
|  |  [15] = "Open_Trapmine"
|  |  [16] = "Open_Pefuben"
|  |  [17] = "Open_Convoy"
|  |  [18] = "Open_Travel"
|  |  [19] = "Open_Pata"
|  |  [2] = "Open_ZhaoMu_Two"
|  |  [20] = "Open_MapBook"
|  |  [21] = "Open_Forge_composite"
|  |  [22] = "Open_YJFuben"
|  |  [23] = "Open_FieldBoss"
|  |  [24] = "Open_EqualArena"
|  |  [3] = "Open_ZhaoMu_Three"
|  |  [4] = "Open_Skill_Three"
|  |  [5] = "Open_Skill_Four"
|  |  [6] = "Open_Shimen"
|  |  [7] = "Open_House"
|  |  [8] = "Open_Achieve"
|  |  [9] = "Open_Lilian"
|  }
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_ZhaoMu"
|  |  [10] = "Open_Org"
|  |  [11] = "Open_Forge"
|  |  [12] = "Open_Equipfuben"
|  |  [13] = "Open_Arena"
|  |  [14] = "Open_MingLei"
|  |  [15] = "Open_Trapmine"
|  |  [16] = "Open_Pefuben"
|  |  [17] = "Open_Convoy"
|  |  [18] = "Open_Travel"
|  |  [19] = "Open_Pata"
|  |  [2] = "Open_ZhaoMu_Two"
|  |  [20] = "Open_MapBook"
|  |  [21] = "Open_Forge_composite"
|  |  [22] = "Open_YJFuben"
|  |  [23] = "Open_FieldBoss"
|  |  [24] = "Open_EqualArena"
|  |  [3] = "Open_ZhaoMu_Three"
|  |  [4] = "Open_Skill_Three"
|  |  [5] = "Open_Skill_Four"
|  |  [6] = "Open_Shimen"
|  |  [7] = "Open_House"
|  |  [8] = "Open_Achieve"
|  |  [9] = "Open_Lilian"
|  }
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_ZhaoMu"
|  |  [10] = "Open_Org"
|  |  [11] = "Open_Forge"
|  |  [12] = "Open_Equipfuben"
|  |  [13] = "Open_Arena"
|  |  [14] = "Open_MingLei"
|  |  [15] = "Open_Trapmine"
|  |  [16] = "Open_Pefuben"
|  |  [17] = "Open_Convoy"
|  |  [18] = "Open_Travel"
|  |  [19] = "Open_Pata"
|  |  [2] = "Open_ZhaoMu_Two"
|  |  [20] = "Open_MapBook"
|  |  [21] = "Open_Forge_composite"
|  |  [22] = "Open_YJFuben"
|  |  [23] = "Open_FieldBoss"
|  |  [24] = "Open_EqualArena"
|  |  [3] = "Open_ZhaoMu_Three"
|  |  [4] = "Open_Skill_Three"
|  |  [5] = "Open_Skill_Four"
|  |  [6] = "Open_Shimen"
|  |  [7] = "Open_House"
|  |  [8] = "Open_Achieve"
|  |  [9] = "Open_Lilian"
|  }
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 24007
}
[core/table.lua:94]:<--Net Send: rank.C2GSOpenRankUI = {
|  idx = 103
|  page = 1
|  query = {
|  |  [1] = "my_rank"
|  |  [2] = "upvote_info"
|  |  [3] = "rank_info"
|  }
|  subtype = 0
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 24007
}
[core/table.lua:94]:-->Net Receive: rank.GS2CRankUpvoteInfo = {
|  idx = 103
|  page = 1
|  upvote_info = {
|  |  [1] = {
|  |  |  count = 1
|  |  |  key = 10019
|  |  }
|  |  [10] = {
|  |  |  key = 10119
|  |  }
|  |  [11] = {
|  |  |  key = 10116
|  |  }
|  |  [12] = {
|  |  |  key = 10003
|  |  }
|  |  [13] = {
|  |  |  key = 10015
|  |  }
|  |  [14] = {
|  |  |  key = 10014
|  |  }
|  |  [15] = {
|  |  |  key = 10099
|  |  }
|  |  [16] = {
|  |  |  key = 10005
|  |  }
|  |  [17] = {
|  |  |  key = 10024
|  |  }
|  |  [18] = {
|  |  |  key = 10012
|  |  }
|  |  [19] = {
|  |  |  key = 10061
|  |  }
|  |  [2] = {
|  |  |  count = 1
|  |  |  key = 10022
|  |  }
|  |  [20] = {
|  |  |  key = 10031
|  |  }
|  |  [3] = {
|  |  |  key = 10094
|  |  }
|  |  [4] = {
|  |  |  key = 10109
|  |  }
|  |  [5] = {
|  |  |  key = 10098
|  |  }
|  |  [6] = {
|  |  |  key = 10096
|  |  }
|  |  [7] = {
|  |  |  key = 10001
|  |  }
|  |  [8] = {
|  |  |  key = 10097
|  |  }
|  |  [9] = {
|  |  |  count = 1
|  |  |  key = 10008
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: rank.GS2CMyRank = {
|  end_time = 1680357600
|  idx = 103
|  rank_count = 120
|  warpower_rank = {
|  |  grade = 56
|  |  name = "灌篮d神ID"
|  |  pid = 10124
|  |  warpower = 2563
|  }
}
[core/global.lua:59]:<color=#ffeb04>nextRefreshTime: 2023/04/01 22:00:00</color>
[core/table.lua:94]:-->Net Receive: rank.GS2CGetRankInfo = {
|  idx = 103
|  page = 1
|  warpower_rank = {
|  |  [1] = {
|  |  |  grade = 85
|  |  |  name = "囧o呱太"
|  |  |  pid = 10019
|  |  |  rank = 1
|  |  |  rank_shift = 101
|  |  |  school = 1
|  |  |  shape = 110
|  |  |  warpower = 87763
|  |  }
|  |  [10] = {
|  |  |  grade = 17
|  |  |  name = "皇帝"
|  |  |  pid = 10119
|  |  |  rank = 10
|  |  |  rank_shift = 110
|  |  |  school = 2
|  |  |  shape = 130
|  |  |  warpower = 6473
|  |  }
|  |  [11] = {
|  |  |  grade = 25
|  |  |  name = "消散的三兄弟"
|  |  |  pid = 10116
|  |  |  rank = 11
|  |  |  rank_shift = 111
|  |  |  school = 1
|  |  |  shape = 110
|  |  |  warpower = 5974
|  |  }
|  |  [12] = {
|  |  |  grade = 16
|  |  |  name = "大长腿v海盗"
|  |  |  pid = 10003
|  |  |  rank = 12
|  |  |  rank_shift = 112
|  |  |  school = 1
|  |  |  shape = 110
|  |  |  warpower = 5940
|  |  }
|  |  [13] = {
|  |  |  grade = 15
|  |  |  name = "3cmm乌龟"
|  |  |  pid = 10015
|  |  |  rank = 13
|  |  |  rank_shift = 113
|  |  |  school = 2
|  |  |  shape = 140
|  |  |  warpower = 5810
|  |  }
|  |  [14] = {
|  |  |  grade = 14
|  |  |  name = "拉裤链家具"
|  |  |  pid = 10014
|  |  |  rank = 14
|  |  |  rank_shift = 114
|  |  |  school = 1
|  |  |  shape = 110
|  |  |  warpower = 5380
|  |  }
|  |  [15] = {
|  |  |  grade = 13
|  |  |  name = "s型d食人花"
|  |  |  pid = 10099
|  |  |  rank = 15
|  |  |  rank_shift = 115
|  |  |  school = 1
|  |  |  shape = 110
|  |  |  warpower = 4895
|  |  }
|  |  [16] = {
|  |  |  grade = 12
|  |  |  name = "正解猎人印记"
|  |  |  pid = 10005
|  |  |  rank = 16
|  |  |  rank_shift = 116
|  |  |  school = 1
|  |  |  shape = 120
|  |  |  warpower = 4875
|  |  }
|  |  [17] = {
|  |  |  grade = 11
|  |  |  name = "单马尾o锁骨"
|  |  |  pid = 10024
|  |  |  rank = 17
|  |  |  rank_shift = 117
|  |  |  school = 3
|  |  |  shape = 150
|  |  |  warpower = 4751
|  |  }
|  |  [18] = {
|  |  |  grade = 9
|  |  |  name = "春待月之黑板"
|  |  |  pid = 10012
|  |  |  rank = 18
|  |  |  rank_shift = 118
|  |  |  school = 3
|  |  |  shape = 160
|  |  |  warpower = 4423
|  |  }
|  |  [19] = {
|  |  |  grade = 10
|  |  |  name = "小颗粒"
|  |  |  pid = 10061
|  |  |  rank = 19
|  |  |  rank_shift = 119
|  |  |  school = 2
|  |  |  shape = 140
|  |  |  warpower = 4309
|  |  }
|  |  [2] = {
|  |  |  grade = 33
|  |  |  name = "q型m御兄"
|  |  |  pid = 10022
|  |  |  rank = 2
|  |  |  rank_shift = 102
|  |  |  school = 1
|  |  |  shape = 110
|  |  |  warpower = 19343
|  |  }
|  |  [20] = {
|  |  |  grade = 10
|  |  |  name = "狡诈v砂糖梨"
|  |  |  pid = 10031
|  |  |  rank = 20
|  |  |  rank_shift = 120
|  |  |  school = 1
|  |  |  shape = 110
|  |  |  warpower = 4289
|  |  }
|  |  [3] = {
|  |  |  grade = 40
|  |  |  name = "叽叽"
|  |  |  pid = 10094
|  |  |  rank = 3
|  |  |  rank_shift = 103
|  |  |  school = 2
|  |  |  shape = 140
|  |  |  warpower = 15066
|  |  }
|  |  [4] = {
|  |  |  grade = 40
|  |  |  name = "神经质v耳"
|  |  |  pid = 10109
|  |  |  rank = 4
|  |  |  rank_shift = 104
|  |  |  school = 2
|  |  |  shape = 130
|  |  |  warpower = 11298
|  |  }
|  |  [5] = {
|  |  |  grade = 29
|  |  |  name = "白羊座o忍蜂"
|  |  |  pid = 10098
|  |  |  rank = 5
|  |  |  rank_shift = 105
|  |  |  school = 3
|  |  |  shape = 160
|  |  |  warpower = 10056
|  |  }
|  |  [6] = {
|  |  |  grade = 21
|  |  |  name = "石頭丶"
|  |  |  pid = 10096
|  |  |  rank = 6
|  |  |  rank_shift = 106
|  |  |  school = 2
|  |  |  shape = 130
|  |  |  warpower = 7571
|  |  }
|  |  [7] = {
|  |  |  grade = 22
|  |  |  name = "敬佩d疾风"
|  |  |  pid = 10001
|  |  |  rank = 7
|  |  |  rank_shift = 107
|  |  |  school = 2
|  |  |  shape = 140
|  |  |  warpower = 7480
|  |  }
|  |  [8] = {
|  |  |  grade = 22
|  |  |  name = "双子座o仆人"
|  |  |  pid = 10097
|  |  |  rank = 8
|  |  |  rank_shift = 108
|  |  |  school = 1
|  |  |  shape = 110
|  |  |  warpower = 7168
|  |  }
|  |  [9] = {
|  |  |  grade = 27
|  |  |  name = "花痴臭小子"
|  |  |  pid = 10008
|  |  |  rank = 9
|  |  |  rank_shift = 109
|  |  |  school = 3
|  |  |  shape = 150
|  |  |  warpower = 6747
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:94]:CRankView ShowView
[logic/ui/CViewBase.lua:125]:CRankView LoadDone!
[logic/ui/CPopupBox.lua:174]:index ..2
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/ui/CViewBase"]:139: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:258: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:245: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:224: in function <[string "logic/base/CResCtrl"]:223>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:265: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:308: in function <[string "logic/base/CResCtrl"]:302>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:98: in function <[string "logic/ui/CViewBase"]:97>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680356019
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 21:33:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CRankView     CloseView
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_ZhaoMu"
|  |  [10] = "Open_Org"
|  |  [11] = "Open_Forge"
|  |  [12] = "Open_Equipfuben"
|  |  [13] = "Open_Arena"
|  |  [14] = "Open_MingLei"
|  |  [15] = "Open_Trapmine"
|  |  [16] = "Open_Pefuben"
|  |  [17] = "Open_Convoy"
|  |  [18] = "Open_Travel"
|  |  [19] = "Open_Pata"
|  |  [2] = "Open_ZhaoMu_Two"
|  |  [20] = "Open_MapBook"
|  |  [21] = "Open_Forge_composite"
|  |  [22] = "Open_YJFuben"
|  |  [23] = "Open_FieldBoss"
|  |  [24] = "Open_EqualArena"
|  |  [3] = "Open_ZhaoMu_Three"
|  |  [4] = "Open_Skill_Three"
|  |  [5] = "Open_Skill_Four"
|  |  [6] = "Open_Shimen"
|  |  [7] = "Open_House"
|  |  [8] = "Open_Achieve"
|  |  [9] = "Open_Lilian"
|  }
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_ZhaoMu"
|  |  [10] = "Open_Org"
|  |  [11] = "Open_Forge"
|  |  [12] = "Open_Equipfuben"
|  |  [13] = "Open_Arena"
|  |  [14] = "Open_MingLei"
|  |  [15] = "Open_Trapmine"
|  |  [16] = "Open_Pefuben"
|  |  [17] = "Open_Convoy"
|  |  [18] = "Open_Travel"
|  |  [19] = "Open_Pata"
|  |  [2] = "Open_ZhaoMu_Two"
|  |  [20] = "Open_MapBook"
|  |  [21] = "Open_Forge_composite"
|  |  [22] = "Open_YJFuben"
|  |  [23] = "Open_FieldBoss"
|  |  [24] = "Open_EqualArena"
|  |  [3] = "Open_ZhaoMu_Three"
|  |  [4] = "Open_Skill_Three"
|  |  [5] = "Open_Skill_Four"
|  |  [6] = "Open_Shimen"
|  |  [7] = "Open_House"
|  |  [8] = "Open_Achieve"
|  |  [9] = "Open_Lilian"
|  }
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_ZhaoMu"
|  |  [10] = "Open_Org"
|  |  [11] = "Open_Forge"
|  |  [12] = "Open_Equipfuben"
|  |  [13] = "Open_Arena"
|  |  [14] = "Open_MingLei"
|  |  [15] = "Open_Trapmine"
|  |  [16] = "Open_Pefuben"
|  |  [17] = "Open_Convoy"
|  |  [18] = "Open_Travel"
|  |  [19] = "Open_Pata"
|  |  [2] = "Open_ZhaoMu_Two"
|  |  [20] = "Open_MapBook"
|  |  [21] = "Open_Forge_composite"
|  |  [22] = "Open_YJFuben"
|  |  [23] = "Open_FieldBoss"
|  |  [24] = "Open_EqualArena"
|  |  [3] = "Open_ZhaoMu_Three"
|  |  [4] = "Open_Skill_Three"
|  |  [5] = "Open_Skill_Four"
|  |  [6] = "Open_Shimen"
|  |  [7] = "Open_House"
|  |  [8] = "Open_Achieve"
|  |  [9] = "Open_Lilian"
|  }
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/mainmenu/CMainMenuExpandBox"]:280: in function <[string "logic/mainmenu/CMainMenuExpandBox"]:251>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_ZhaoMu"
|  |  [10] = "Open_Org"
|  |  [11] = "Open_Forge"
|  |  [12] = "Open_Equipfuben"
|  |  [13] = "Open_Arena"
|  |  [14] = "Open_MingLei"
|  |  [15] = "Open_Trapmine"
|  |  [16] = "Open_Pefuben"
|  |  [17] = "Open_Convoy"
|  |  [18] = "Open_Travel"
|  |  [19] = "Open_Pata"
|  |  [2] = "Open_ZhaoMu_Two"
|  |  [20] = "Open_MapBook"
|  |  [21] = "Open_Forge_composite"
|  |  [22] = "Open_YJFuben"
|  |  [23] = "Open_FieldBoss"
|  |  [24] = "Open_EqualArena"
|  |  [3] = "Open_ZhaoMu_Three"
|  |  [4] = "Open_Skill_Three"
|  |  [5] = "Open_Skill_Four"
|  |  [6] = "Open_Shimen"
|  |  [7] = "Open_House"
|  |  [8] = "Open_Achieve"
|  |  [9] = "Open_Lilian"
|  }
|  view = {
|  |  [1] = "Partner_FWCD_One_PartnerMain"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/mainmenu/CMainMenuExpandBox"]:280: in function <[string "logic/mainmenu/CMainMenuExpandBox"]:251>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:26: in function <[string "main"]:23>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1680356029
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2023/04/01 21:33:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
