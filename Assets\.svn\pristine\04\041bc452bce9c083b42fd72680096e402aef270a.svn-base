with:905  hight:522
[logic/misc/CShareCtrl.lua:28]:jit    true    SSE2    SSE3    SSE4.1    BMI2    fold    cse    dce    fwd    dse    narrow    loop    abc    sink    fuse
[logic/login/CLoginCtrl.lua:5]:公告初始化...
[logic/base/CHttpCtrl.lua:32]:http get ->    https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials&client_id=rlU84dprH6I7sMjBkqM0gizl&client_secret=c15395c2433c4a904d22aa27eab8321b&
[logic/ui/CViewCtrl.lua:94]:CNotifyView ShowView
[logic/ui/CViewCtrl.lua:94]:CBottomView ShowView
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x58fed3a8"
|  json_result = true
|  timer = 16
}
[logic/audio/CSpeechCtrl.lua:72]:OnTokenGet err
[logic/ui/CViewBase.lua:125]:CNotifyView LoadDone!
[logic/ui/CViewBase.lua:125]:CBottomView LoadDone!
[logic/base/CResCtrl.lua:199]:-->resource init done!!! 12
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {}
[logic/ui/CViewCtrl.lua:94]:CLoginView ShowView
[logic/base/CHttpCtrl.lua:32]:http get ->    http://gamepl.net/gm/Note/note2401.json
[logic/ui/CViewBase.lua:125]:CLoginView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x8edba9d0"
|  json_result = true
|  timer = 58
}
[logic/login/CLoginAccountPage.lua:79]:OnTokenGet err
[core/table.lua:94]:http post -> = {
|  headers = {
|  |  Content-Type = "application/x-www-form-urlencoded"
|  }
|  url = "http://gamepl.net/gm/common/accountList"
}
[core/global.lua:59]:<color=#ffeb04>CLoginServerPage.SetServer</color>
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x8ede3388"
|  json_result = true
|  timer = 63
}
[logic/ui/CViewCtrl.lua:94]:CSelectServerView ShowView
[core/table.lua:94]:GroupServers------------------------> = {}
[logic/ui/CViewBase.lua:125]:CSelectServerView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
