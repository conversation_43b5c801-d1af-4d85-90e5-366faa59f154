module(...)
--magic editor build
DATA={
	atk_stophit=true,
	cmds={
		[1]={
			args={
				action_name=[[runWar]],
				action_time=0.49,
				end_frame=19,
				excutor=[[atkobj]],
				start_frame=0,
			},
			func_name=[[PlayAction]],
			start_time=0,
		},
		[2]={
			args={
				begin_type=[[current]],
				calc_face=false,
				ease_type=[[Linear]],
				end_relative={base_pos=[[center]],depth=0,relative_angle=0,relative_dis=-1.5,},
				end_type=[[end_relative]],
				excutor=[[atkobj]],
				look_at_pos=false,
				move_time=0.2,
				move_type=[[line]],
			},
			func_name=[[Move]],
			start_time=0,
		},
		[3]={args={alive_time=0.5,},func_name=[[Name]],start_time=0,},
		[4]={
			args={sound_path=[[Magic/sound_magic_100702_1.wav]],sound_rate=1,},
			func_name=[[PlaySound]],
			start_time=0,
		},
		[5]={
			args={action_name=[[attack2]],excutor=[[atkobj]],},
			func_name=[[PlayAction]],
			start_time=0.19,
		},
		[6]={args={},func_name=[[MagcAnimStart]],start_time=0.19,},
		[7]={
			args={
				alive_time=1.5,
				effect={
					is_cached=true,
					magic_layer=[[center]],
					path=[[Effect/Magic/magic_eff_1007/Prefabs/magic_eff_100702_att.prefab]],
					preload=true,
				},
				effect_dir_type=[[forward]],
				effect_pos={base_pos=[[atk]],depth=0,relative_angle=0,relative_dis=0,},
				excutor=[[atkobj]],
			},
			func_name=[[StandEffect]],
			start_time=0.5,
		},
		[8]={
			args={
				consider_hight=false,
				damage_follow=true,
				face_atk=true,
				hurt_delta=0,
				play_anim=true,
			},
			func_name=[[VicHitInfo]],
			start_time=0.55,
		},
		[9]={
			args={shake_dis=0.03,shake_rate=10,shake_time=0.4,},
			func_name=[[ShakeScreen]],
			start_time=0.55,
		},
		[10]={
			args={
				alive_time=0.5,
				bind_type=[[pos]],
				body_pos=[[waist]],
				effect={
					is_cached=true,
					magic_layer=[[center]],
					path=[[Effect/Magic/magic_eff_1007/Prefabs/magic_eff_100701_hit.prefab]],
					preload=true,
				},
				excutor=[[vicobjs]],
				height=0,
			},
			func_name=[[BodyEffect]],
			start_time=0.6,
		},
		[11]={args={},func_name=[[MagcAnimEnd]],start_time=0.7,},
		[12]={args={},func_name=[[End]],start_time=1.3,},
	},
	group_cmds={},
	magic_anim_end_time=0.7,
	magic_anim_start_time=0.19,
	pre_load_res={
		[1]=[[Effect/Magic/magic_eff_1007/Prefabs/magic_eff_100702_att.prefab]],
		[2]=[[Effect/Magic/magic_eff_1007/Prefabs/magic_eff_100701_hit.prefab]],
	},
	run_env=[[war]],
	type=1,
	wait_goback=true,
}
