fileFormatVersion: 2
guid: 03bfd548898fda645b76ab011619eb95
timeCreated: **********
licenseType: Pro
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: Bip001
    100002: Bip001 Footsteps
    100004: Bip001 Head
    100006: Bip001 HeadNub
    100008: Bip001 L Calf
    100010: Bip001 L Clavicle
    100012: Bip001 L Finger0
    100014: Bip001 L Finger01
    100016: Bip001 L Finger0Nub
    100018: Bip001 L Finger1
    100020: Bip001 L Finger11
    100022: Bip001 L Finger1Nub
    100024: Bip001 L Foot
    100026: Bip001 L Forearm
    100028: Bip001 L Hand
    100030: Bip001 L Thigh
    100032: Bip001 L Toe0
    100034: Bip001 L Toe0Nub
    100036: Bip001 L UpperArm
    100038: Bip001 Neck
    100040: Bip001 Pelvis
    100042: Bip001 Prop1
    100044: Bip001 R Calf
    100046: Bip001 R Clavicle
    100048: Bip001 R Finger0
    100050: Bip001 R Finger01
    100052: Bip001 R Finger0Nub
    100054: Bip001 R Finger1
    100056: Bip001 R Finger11
    100058: Bip001 R Finger1Nub
    100060: Bip001 R Foot
    100062: Bip001 R Forearm
    100064: Bip001 R Hand
    100066: Bip001 R Thigh
    100068: Bip001 R Toe0
    100070: Bip001 R Toe0Nub
    100072: Bip001 R UpperArm
    100074: Bip001 Spine
    100076: Bip001 Spine1
    100078: Bone001
    100080: Bone002
    100082: Bone003
    100084: Bone004
    100086: Bone005
    100088: Bone006
    100090: Bone007
    100092: Bone008
    100094: Bone009
    100096: Bone010
    100098: Bone011
    100100: Bone012
    100102: Bone013
    100104: Bone014
    100106: Bone015
    100108: Bone016
    100110: Bone017
    100112: Bone018
    100114: Bone019
    100116: Bone020
    100118: Bone021
    100120: Bone022
    100122: Bone023
    100124: Bone024
    100126: Bone025
    100128: Bone026
    100130: Bone027
    100132: Bone028
    100134: //RootNode
    100136: pet_401
    100138: weapon401_1
    100140: Bip001 L Finger2
    100142: Bip001 L Finger2Nub
    100144: Bip001 R Finger2
    100146: Bip001 R Finger2Nub
    100148: Bone029
    100150: Bone030
    100152: Bone031
    100154: Bone032
    100156: Bone033
    100158: Bone034
    100160: Bone035
    100162: Bone036
    100164: Bone037
    100166: Bone038
    100168: Bone039
    100170: Bone040
    100172: Bone041
    100174: Bone042
    100176: Bone043
    100178: model507
    100180: Bone009(mirrored)
    100182: Bone010(mirrored)
    100184: Bone011(mirrored)
    100186: Bone012(mirrored)
    100188: Bone013(mirrored)
    100190: Bone044
    100192: Bone045
    100194: Bone046
    100196: Bone047
    100198: Bone048
    100200: Bone049
    100202: Bone050
    100204: Bone051
    100206: Bone052
    100208: Bone053
    100210: Bone054
    100212: Bone055
    100214: Bone056
    100216: Bone056(mirrored)
    100218: Bone057
    100220: Bone057(mirrored)
    100222: Bone058
    100224: Bone058(mirrored)
    100226: Bone059
    100228: Bone059(mirrored)
    100230: Bone060
    100232: Bone061
    100234: Bone062
    100236: Bone063
    100238: pet_508
    100240: Point001
    100242: weapon508_1
    100244: wuqi01
    100246: wuqi02
    100248: wuqi03
    100250: wuqi04
    100252: wuqi05
    100254: wuqi06
    100256: wuqi07
    100258: wuqi08
    100260: wuqi_xuni
    100262: model508
    100264: weapon508
    100266: Bip001 L Toe01
    100268: Bip001 L Toe02
    100270: Bone005(mirrored)
    100272: model509
    100274: Particle View 001
    100276: Particle View 001 1
    100278: Particle View 001 2
    100280: Particle View 001 3
    100282: Particle View 001 4
    100284: Particle View 001 5
    100286: Bip001 Xtra01
    100288: Bip001 Xtra01Nub
    100290: Bip001 Xtra01Opp
    100292: Bip001 Xtra01OppNub
    100294: Bone004(mirrored)
    100296: Bone020(mirrored)
    100298: Bone021(mirrored)
    100300: Bone025(mirrored)
    100302: Bone026(mirrored)
    100304: Bone027(mirrored)
    100306: Bone029(mirrored)
    100308: Bone030(mirrored)
    100310: Bone032(mirrored)
    100312: Bone033(mirrored)
    100314: Dummy001
    100316: model510
    100318: weapon510_1
    100320: weapon510_2
    100322: "\u3000\uE813\uE811"
    100324: "\uE814\uE812"
    400000: Bip001
    400002: Bip001 Footsteps
    400004: Bip001 Head
    400006: Bip001 HeadNub
    400008: Bip001 L Calf
    400010: Bip001 L Clavicle
    400012: Bip001 L Finger0
    400014: Bip001 L Finger01
    400016: Bip001 L Finger0Nub
    400018: Bip001 L Finger1
    400020: Bip001 L Finger11
    400022: Bip001 L Finger1Nub
    400024: Bip001 L Foot
    400026: Bip001 L Forearm
    400028: Bip001 L Hand
    400030: Bip001 L Thigh
    400032: Bip001 L Toe0
    400034: Bip001 L Toe0Nub
    400036: Bip001 L UpperArm
    400038: Bip001 Neck
    400040: Bip001 Pelvis
    400042: Bip001 Prop1
    400044: Bip001 R Calf
    400046: Bip001 R Clavicle
    400048: Bip001 R Finger0
    400050: Bip001 R Finger01
    400052: Bip001 R Finger0Nub
    400054: Bip001 R Finger1
    400056: Bip001 R Finger11
    400058: Bip001 R Finger1Nub
    400060: Bip001 R Foot
    400062: Bip001 R Forearm
    400064: Bip001 R Hand
    400066: Bip001 R Thigh
    400068: Bip001 R Toe0
    400070: Bip001 R Toe0Nub
    400072: Bip001 R UpperArm
    400074: Bip001 Spine
    400076: Bip001 Spine1
    400078: Bone001
    400080: Bone002
    400082: Bone003
    400084: Bone004
    400086: Bone005
    400088: Bone006
    400090: Bone007
    400092: Bone008
    400094: Bone009
    400096: Bone010
    400098: Bone011
    400100: Bone012
    400102: Bone013
    400104: Bone014
    400106: Bone015
    400108: Bone016
    400110: Bone017
    400112: Bone018
    400114: Bone019
    400116: Bone020
    400118: Bone021
    400120: Bone022
    400122: Bone023
    400124: Bone024
    400126: Bone025
    400128: Bone026
    400130: Bone027
    400132: Bone028
    400134: //RootNode
    400136: pet_401
    400138: weapon401_1
    400140: Bip001 L Finger2
    400142: Bip001 L Finger2Nub
    400144: Bip001 R Finger2
    400146: Bip001 R Finger2Nub
    400148: Bone029
    400150: Bone030
    400152: Bone031
    400154: Bone032
    400156: Bone033
    400158: Bone034
    400160: Bone035
    400162: Bone036
    400164: Bone037
    400166: Bone038
    400168: Bone039
    400170: Bone040
    400172: Bone041
    400174: Bone042
    400176: Bone043
    400178: model507
    400180: Bone009(mirrored)
    400182: Bone010(mirrored)
    400184: Bone011(mirrored)
    400186: Bone012(mirrored)
    400188: Bone013(mirrored)
    400190: Bone044
    400192: Bone045
    400194: Bone046
    400196: Bone047
    400198: Bone048
    400200: Bone049
    400202: Bone050
    400204: Bone051
    400206: Bone052
    400208: Bone053
    400210: Bone054
    400212: Bone055
    400214: Bone056
    400216: Bone056(mirrored)
    400218: Bone057
    400220: Bone057(mirrored)
    400222: Bone058
    400224: Bone058(mirrored)
    400226: Bone059
    400228: Bone059(mirrored)
    400230: Bone060
    400232: Bone061
    400234: Bone062
    400236: Bone063
    400238: pet_508
    400240: Point001
    400242: weapon508_1
    400244: wuqi01
    400246: wuqi02
    400248: wuqi03
    400250: wuqi04
    400252: wuqi05
    400254: wuqi06
    400256: wuqi07
    400258: wuqi08
    400260: wuqi_xuni
    400262: model508
    400264: weapon508
    400266: Bip001 L Toe01
    400268: Bip001 L Toe02
    400270: Bone005(mirrored)
    400272: model509
    400274: Particle View 001
    400276: Particle View 001 1
    400278: Particle View 001 2
    400280: Particle View 001 3
    400282: Particle View 001 4
    400284: Particle View 001 5
    400286: Bip001 Xtra01
    400288: Bip001 Xtra01Nub
    400290: Bip001 Xtra01Opp
    400292: Bip001 Xtra01OppNub
    400294: Bone004(mirrored)
    400296: Bone020(mirrored)
    400298: Bone021(mirrored)
    400300: Bone025(mirrored)
    400302: Bone026(mirrored)
    400304: Bone027(mirrored)
    400306: Bone029(mirrored)
    400308: Bone030(mirrored)
    400310: Bone032(mirrored)
    400312: Bone033(mirrored)
    400314: Dummy001
    400316: model510
    400318: weapon510_1
    400320: weapon510_2
    400322: "\u3000\uE813\uE811"
    400324: "\uE814\uE812"
    4300000: pet_401
    4300002: weapon401_1
    4300004: model507
    4300006: pet_508
    4300008: weapon508_1
    4300010: model508
    4300012: weapon508
    4300014: model509
    4300016: weapon510_2
    4300018: model510
    4300020: weapon510_1
    9500000: //RootNode
    13700000: pet_401
    13700002: weapon401_1
    13700004: model507
    13700006: pet_508
    13700008: weapon508_1
    13700010: model508
    13700012: weapon508
    13700014: model509
    13700016: model510
    13700018: weapon510_1
    13700020: weapon510_2
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
