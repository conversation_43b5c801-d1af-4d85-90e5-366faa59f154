local CShareCtrl = class("CShareCtrl", CCtrlBase)
CShareCtrl.g_Test = true
function CShareCtrl.ctor(self)
	CCtrlBase.ctor(self)

	local gameObject = UnityEngine.GameObject.Find("GameRoot/ShareSDK")
	self.m_ShareSDK = gameObject:GetComponent(classtype.ShareSDK)
	self.m_ShareManager = gameObject:GetComponent(classtype.ShareSDKManager)
	self.m_ShareManager:InitShare(self.m_ShareSDK, callback(self, "OnCallback"))
	
	if QUICKSDK then
		self.m_ShareManager:Quick_Login_call(self.m_ShareSDK,callback(self, "Quick_Login_call"))
		self.m_ShareManager:Quick_exit_call(self.m_ShareSDK,callback(self, "Quick_exit_call"))
		self.m_ShareManager:Quick_loginout_call(self.m_ShareSDK,callback(self, "Quick_loginout_call"))
		self.m_ShareManager:Quick_switch_call(self.m_ShareSDK,callback(self, "Quick_switch_call"))
		self.m_ShareManager:Quick_cancellation_call(self.m_ShareSDK,callback(self, "Quick_cancellation_call"))
	end
	if GoogleSDK then
		self.m_ShareManager.Google_Login_call(function(sub)
			C_api.Utils.LogError("Csharsdk:Google登录回调Lua1111"..sub)
			CShareCtrl:Google_Login_call(sub)
		end)
		--以下待完善
		--self.m_ShareManager:Google_exit_call(self.m_ShareSDK,callback(self, "Google_exit_call"))
		--self.m_ShareManager:Google_loginout_call(self.m_ShareSDK,callback(self, "Google_loginout_call"))
		--self.m_ShareManager:Google_switch_call(self.m_ShareSDK,callback(self, "Google_switch_call"))
		--self.m_ShareManager:Google_cancellation_call(self.m_ShareSDK,callback(self, "Google_cancellation_call"))
	end
--	self.m_ShareManager:Quick_Login(self.m_ShareSDK,callback(self, "Quick_Login"))
--	self.m_ShareManager:Quick_Pay(self.m_ShareSDK,callback(self, "Quick_Pay"))
--	self.m_ShareManager:Quick_CreateRole(self.m_ShareSDK,callback(self, "Quick_CreateRole"))
--	self.m_ShareManager:Quick_UpdateRole(self.m_ShareSDK,callback(self, "Quick_UpdateRole"))
--	self.m_ShareManager:Quick_EnterGame(self.m_ShareSDK,callback(self, "Quick_EnterGame"))
	--print("ShareCallback ctor")
--	data.itemdata[0] = data.itemdata.PARTNER_CHIP
--	table.insert(data.itemdata, 1, "PARTNER_CHIP")
--	table.sort(data.itemdata)
--	local i = 0
--	for k,v in pairs(data.itemdata) do
--		print(k,"data.itemdata")
--	end
	print("jit", jit.status())
	
	self.m_SupportPlat = {
		[enum.Share.PlatformType.Unknown] = "",
		[enum.Share.PlatformType.WeChat] = "com.tencent.mm",
		[enum.Share.PlatformType.WeChatMoments] = "com.tencent.mm",
		[enum.Share.PlatformType.SinaWeibo] = "",
	}

end

function CShareCtrl.OnCallback(self, iShareType, iReqID, iResponseState, iPlatformType, sData)
	print("OnShareCallback:", iShareType, iReqID, iResponseState, iPlatformType, sData)
	self:OnEvent(iReqID, {type=iShareType, response=iResponseState, result=decodejson(sData)})
end


--GoogleSdk回调
function CShareCtrl.Google_Login_call(self,sub)
    print("Google_Login_call:", sub)
	C_api.Utils.LogError("Csharsdk:Google登录回调Lua"..sub)
	local oView = CLoginView:GetView()
	CLoginAccountPage:Google_Login_call(sub)
    oView:ShowServerPage()
end

---Quick �ص�C#
function CShareCtrl.Quick_Login_call(self,accName,pwd)
    print("Quick_Login_call:", accName, pwd)
	local oView = CLoginView:GetView()
	CLoginAccountPage:QuickLogin(accName,pwd)
    oView:ShowServerPage()
end

function CShareCtrl.Quick_exit_call(self,accName,pwd)
    print("Quick_exit_call:", accName, pwd)
	Utils.QuitGame()
end

function CShareCtrl.Quick_loginout_call(self,accName,pwd)
    print("Quick_loginout_call:", accName, pwd)
	if Utils.IsPlayingCG() then
		return
	end
	if g_CreateRoleCtrl:IsInitDone() then
		CLoginView:ShowView()
		g_CreateRoleCtrl:EndCreateRole()
		g_UploadDataCtrl:CreateRoleUpload({time=g_CreateRoleCtrl.m_ShowTime, click= "���ص�¼����"})
	end
    --g_LoginCtrl:ChangeAccount()
end

--�л��˺�
function CShareCtrl.Quick_switch_call(self,accName,pwd)
    --self:CloseView()
	g_LoginCtrl:Logout()
end

--ע��
function CShareCtrl.Quick_cancellation_call(self,accName,pwd)
    --ע��ȷ�ϵ���
    --local dialog = {
        --msg = "Log out",
        --okCallback = function ()
            --self:CloseView()
	        g_LoginCtrl:Logout()
        --end,
        --okStr = "ok",
        --forceConfirm = true,
        --hideCancel = true
    --}
    --g_WindowTipCtrl:SetWindowConfirm(dialog)
end

--function CShareCtrl.Quick_Login(self,accName,pwd)
--    print("Quick_Login:", accName, pwd)
--end

--function CShareCtrl.Quick_Pay(self,accName,pwd)
--    print("Quick_Pay:", accName, pwd)
--end

--function CShareCtrl.Quick_CreateRole(self,accName,pwd)
--    print("Quick_CreateRole:", accName, pwd)
--end

--function CShareCtrl.Quick_UpdateRole(self,accName,pwd)
--    print("Quick_UpdateRole:", accName, pwd)
--end

--function CShareCtrl.Quick_UpdateRole(self,accName,pwd)
--    print("Quick_UpdateRole:", accName, pwd)
--end

---Quick �ص�C#
function CShareCtrl.IsSupportedPlatfromType(self, iPlatformType)
	if Utils.IsEditor() then
		if CShareCtrl.g_Test then
			return true
		end
		return false
	end
	return self.m_SupportPlat[iPlatformType] ~= nil
end

function CShareCtrl.IsClientInstalled(self, iPlatformType)
	if Utils.IsEditor() then
		if CShareCtrl.g_Test then
			return true
		end
		return false
	end
	if iPlatformType == enum.Share.PlatformType.Unknown then
		return true
	else
		return self.m_ShareSDK:IsClientValid(iPlatformType)
	end
end

function CShareCtrl.IsShowShare(self)
	if Utils.IsPC() and not Utils.IsEditor() then
		return false
	elseif Utils.IsIOS() then
		return false
	else
		return true
	end
end

function CShareCtrl.ShareImage(self, sPath, sDesc, cb, closecb)
	local function onshare(platid)
		Utils.AddTimer(function() cb(platid) end, 0, 0)
		if platid and not Utils.IsPC() then
			self:ShareContent({image_path = sPath}, platid, cb)
		end
	end
	CShareView:ShowView(function(oView)
		oView:SetShareCb(onshare)
		oView:SetCloseCb(closecb)
	end)
end


function CShareCtrl.CreateContent(self, dContent)
	local content = Share.ShareContent.New()
	if dContent.title then
		content:SetTitle(dContent.title)
	end
	if dContent.desc then
		content:SetText(dContent.desc)
	end
	if dContent.url then
		content:SetUrl(dContent.SetUrl)
		content:SetShareType(enum.Share.ContentType.Webpage)
	else
		if dContent.image_path then
			if dContent.image_path:find("^http") ~= nil then
				content:SetImageUrl(dContent.image_path)
			else
				content:SetImagePath(dContent.image_path)
			end
			content:SetShareType(enum.Share.ContentType.Image)
		end
	end
	return content
end

function CShareCtrl.ShareContent(self, dContent, iPlatformType, cb)
	local iReqID = self.m_ShareSDK:ShareContent(iPlatformType, self:CreateContent(dContent))
	print("ShareContent:", iReqID)
	-- self:AddCtrlEvent(iReqID, cb)
	return iReqID
end

function CShareCtrl.ShowPlatformList(self, dContent, cb)
	local content = self:CreateContent(dContent)
	local iReqID = self.m_ShareSDK:ShowPlatformList(self:GetPlatformArray(), content, 0, 0)
	print("ShowPlatformList:", iReqID)
	-- self:AddCtrlEvent(iReqID, cb)
	return iReqID
end

function CShareCtrl.GetPlatformArray(self)
	local array = Utils.ListToArray(table.keys(self.m_SupportPlat), classtype.Int)
	return array
end

return CShareCtrl