%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: magic_eff_115401_att_01
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0.083333336
        value: 0
        inSlope: 2.625
        outSlope: 2.625
        tangentMode: 10
      - time: 0.35
        value: 0.7
        inSlope: 2.625
        outSlope: 2.625
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: material._Alpha
    path: qiu_half01 (2)
    classID: 23
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0.083333336
        value: 0
        inSlope: 3.7500002
        outSlope: 3.7500002
        tangentMode: 10
      - time: 0.35
        value: 1
        inSlope: 3.7500002
        outSlope: 3.7500002
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: material._Alpha
    path: qiu_half01 (2)/qiu_half01 (5)
    classID: 23
    script: {fileID: 0}
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - path: 1066261484
      attribute: 2333735666
      script: {fileID: 0}
      classID: 23
      customType: 22
      isPPtrCurve: 0
    - path: 2300454202
      attribute: 2333735666
      script: {fileID: 0}
      classID: 23
      customType: 22
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 0.35
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0.083333336
        value: 0
        inSlope: 2.625
        outSlope: 2.625
        tangentMode: 10
      - time: 0.35
        value: 0.7
        inSlope: 2.625
        outSlope: 2.625
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: material._Alpha
    path: qiu_half01 (2)
    classID: 23
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0.083333336
        value: 0
        inSlope: 3.7500002
        outSlope: 3.7500002
        tangentMode: 10
      - time: 0.35
        value: 1
        inSlope: 3.7500002
        outSlope: 3.7500002
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: material._Alpha
    path: qiu_half01 (2)/qiu_half01 (5)
    classID: 23
    script: {fileID: 0}
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_GenerateMotionCurves: 0
  m_Events: []
