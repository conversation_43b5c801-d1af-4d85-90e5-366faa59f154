Material doesn't have a color property '_Color'
RtlLookupFunctionEntry returned NULL function. Aborting stack walk.
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000000051FCBB4B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000051FCBA34 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x000000005208F715 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000A63376A3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000A6337365 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x000000005208FE23 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x000000005208F874 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x000000005208FAA7 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FF88B7B64D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FF88B708A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x00000001408495BB (Unity) Material::GetColor
0x000000014146C2BC (Unity) Material_CUSTOM_INTERNAL_CALL_GetColorImpl
0x00000000A633724B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.Material:INTERNAL_CALL_GetColorImpl (UnityEngine.Material,int,UnityEngine.Color&)
0x00000000A633709C (Mono JIT Code) [ShaderBindings.gen.cs:339] UnityEngine.Material:GetColorImpl (int) 
0x00000000A6336F2B (Mono JIT Code) [Graphics.cs:806] UnityEngine.Material:GetColor (int) 
0x00000000A6336DD0 (Mono JIT Code) [Graphics.cs:805] UnityEngine.Material:GetColor (string) 
0x00000000A63369AE (Mono JIT Code) [ShaderBindings.gen.cs:270] UnityEngine.Material:get_color () 
0x00000000A6336B79 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_Color__this__ (object,intptr,intptr,intptr)
0x00007FF88B7B64D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FF88B708A31 (mono) [object.c:2625] mono_runtime_invoke 
0x00007FF88B75FB58 (mono) [debugger-agent.c:5315] do_invoke_method 
0x00007FF88B763A8E (mono) [debugger-agent.c:5404] invoke_method 
0x00007FF88B764F29 (mono) [debugger-agent.c:2414] suspend_current 
0x00007FF88B763BA4 (mono) [debugger-agent.c:5445] invoke_method 
0x00007FF88B764F29 (mono) [debugger-agent.c:2414] suspend_current 
0x00007FF88B763BA4 (mono) [debugger-agent.c:5445] invoke_method 
0x00007FF88B764F29 (mono) [debugger-agent.c:2414] suspend_current 
0x00007FF88B763BA4 (mono) [debugger-agent.c:5445] invoke_method 
0x00007FF88B764F29 (mono) [debugger-agent.c:2414] suspend_current 
0x00007FF88B765363 (mono) [debugger-agent.c:3101] process_event 
0x00007FF88B765E2A (mono) [debugger-agent.c:3999] process_breakpoint_inner 
0x00007FF88B765EF0 (mono) [debugger-agent.c:4021] process_breakpoint 
<Missing stacktrace information>


Material doesn't have a color property '_Color'
RtlLookupFunctionEntry returned NULL function. Aborting stack walk.
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x00000001408495BB (Unity) Material::GetColor
0x000000014146C2BC (Unity) Material_CUSTOM_INTERNAL_CALL_GetColorImpl
0x00000000A633724B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.Material:INTERNAL_CALL_GetColorImpl (UnityEngine.Material,int,UnityEngine.Color&)
0x00000000A633709C (Mono JIT Code) [ShaderBindings.gen.cs:339] UnityEngine.Material:GetColorImpl (int) 
0x00000000A6336F2B (Mono JIT Code) [Graphics.cs:806] UnityEngine.Material:GetColor (int) 
0x00000000A6336DD0 (Mono JIT Code) [Graphics.cs:805] UnityEngine.Material:GetColor (string) 
0x00000000A63369AE (Mono JIT Code) [ShaderBindings.gen.cs:270] UnityEngine.Material:get_color () 
0x00000000A6336B79 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_Color__this__ (object,intptr,intptr,intptr)
0x00007FF88B7B64D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FF88B708A31 (mono) [object.c:2625] mono_runtime_invoke 
0x00007FF88B75FB58 (mono) [debugger-agent.c:5315] do_invoke_method 
0x00007FF88B763A8E (mono) [debugger-agent.c:5404] invoke_method 
0x00007FF88B764F29 (mono) [debugger-agent.c:2414] suspend_current 
0x00007FF88B763BA4 (mono) [debugger-agent.c:5445] invoke_method 
0x00007FF88B764F29 (mono) [debugger-agent.c:2414] suspend_current 
0x00007FF88B763BA4 (mono) [debugger-agent.c:5445] invoke_method 
0x00007FF88B764F29 (mono) [debugger-agent.c:2414] suspend_current 
0x00007FF88B763BA4 (mono) [debugger-agent.c:5445] invoke_method 
0x00007FF88B764F29 (mono) [debugger-agent.c:2414] suspend_current 
0x00007FF88B765363 (mono) [debugger-agent.c:3101] process_event 
0x00007FF88B765E2A (mono) [debugger-agent.c:3999] process_breakpoint_inner 
0x00007FF88B765EF0 (mono) [debugger-agent.c:4021] process_breakpoint 
<Missing stacktrace information>


Material doesn't have a color property '_Color'
RtlLookupFunctionEntry returned NULL function. Aborting stack walk.
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000000051FCBB4B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000051FCBA34 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x000000005208F715 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000A63376A3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000A6337365 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x000000005208FE23 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x000000005208F874 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x000000005208FAA7 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FF88B7B64D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FF88B708A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x00000001408495BB (Unity) Material::GetColor
0x000000014146C2BC (Unity) Material_CUSTOM_INTERNAL_CALL_GetColorImpl
0x00000000A633724B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.Material:INTERNAL_CALL_GetColorImpl (UnityEngine.Material,int,UnityEngine.Color&)
0x00000000A633709C (Mono JIT Code) [ShaderBindings.gen.cs:339] UnityEngine.Material:GetColorImpl (int) 
0x00000000A6336F2B (Mono JIT Code) [Graphics.cs:806] UnityEngine.Material:GetColor (int) 
0x00000000A6336DD0 (Mono JIT Code) [Graphics.cs:805] UnityEngine.Material:GetColor (string) 
0x00000000A63369AE (Mono JIT Code) [ShaderBindings.gen.cs:270] UnityEngine.Material:get_color () 
0x00000000A6336B79 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_Color__this__ (object,intptr,intptr,intptr)
0x00007FF88B7B64D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FF88B708A31 (mono) [object.c:2625] mono_runtime_invoke 
0x00007FF88B75FB58 (mono) [debugger-agent.c:5315] do_invoke_method 
0x00007FF88B763A8E (mono) [debugger-agent.c:5404] invoke_method 
0x00007FF88B764F29 (mono) [debugger-agent.c:2414] suspend_current 
0x00007FF88B763BA4 (mono) [debugger-agent.c:5445] invoke_method 
0x00007FF88B764F29 (mono) [debugger-agent.c:2414] suspend_current 
0x00007FF88B763BA4 (mono) [debugger-agent.c:5445] invoke_method 
0x00007FF88B764F29 (mono) [debugger-agent.c:2414] suspend_current 
0x00007FF88B763BA4 (mono) [debugger-agent.c:5445] invoke_method 
0x00007FF88B764F29 (mono) [debugger-agent.c:2414] suspend_current 
0x00007FF88B765363 (mono) [debugger-agent.c:3101] process_event 
0x00007FF88B765E2A (mono) [debugger-agent.c:3999] process_breakpoint_inner 
0x00007FF88B765EF0 (mono) [debugger-agent.c:4021] process_breakpoint 
<Missing stacktrace information>


Material doesn't have a color property '_Color'
RtlLookupFunctionEntry returned NULL function. Aborting stack walk.
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x00000001408495BB (Unity) Material::GetColor
0x000000014146C2BC (Unity) Material_CUSTOM_INTERNAL_CALL_GetColorImpl
0x00000000A633724B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.Material:INTERNAL_CALL_GetColorImpl (UnityEngine.Material,int,UnityEngine.Color&)
0x00000000A633709C (Mono JIT Code) [ShaderBindings.gen.cs:339] UnityEngine.Material:GetColorImpl (int) 
0x00000000A6336F2B (Mono JIT Code) [Graphics.cs:806] UnityEngine.Material:GetColor (int) 
0x00000000A6336DD0 (Mono JIT Code) [Graphics.cs:805] UnityEngine.Material:GetColor (string) 
0x00000000A63369AE (Mono JIT Code) [ShaderBindings.gen.cs:270] UnityEngine.Material:get_color () 
0x00000000A6336B79 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_Color__this__ (object,intptr,intptr,intptr)
0x00007FF88B7B64D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FF88B708A31 (mono) [object.c:2625] mono_runtime_invoke 
0x00007FF88B75FB58 (mono) [debugger-agent.c:5315] do_invoke_method 
0x00007FF88B763A8E (mono) [debugger-agent.c:5404] invoke_method 
0x00007FF88B764F29 (mono) [debugger-agent.c:2414] suspend_current 
0x00007FF88B763BA4 (mono) [debugger-agent.c:5445] invoke_method 
0x00007FF88B764F29 (mono) [debugger-agent.c:2414] suspend_current 
0x00007FF88B763BA4 (mono) [debugger-agent.c:5445] invoke_method 
0x00007FF88B764F29 (mono) [debugger-agent.c:2414] suspend_current 
0x00007FF88B763BA4 (mono) [debugger-agent.c:5445] invoke_method 
0x00007FF88B764F29 (mono) [debugger-agent.c:2414] suspend_current 
0x00007FF88B765363 (mono) [debugger-agent.c:3101] process_event 
0x00007FF88B765E2A (mono) [debugger-agent.c:3999] process_breakpoint_inner 
0x00007FF88B765EF0 (mono) [debugger-agent.c:4021] process_breakpoint 
<Missing stacktrace information>


