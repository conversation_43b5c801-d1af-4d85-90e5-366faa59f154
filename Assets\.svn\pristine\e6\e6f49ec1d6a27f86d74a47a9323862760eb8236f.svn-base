local CHyaTestView = class("CHyaTestView", CViewBase)

function CHyaTestView.ctor(self, cb)
	CViewBase.ctor(self, "UI/Test/CHyaTestView.prefab", cb)
	--界面设置
	self.m_DepthType = "Dialog"
	--self.m_ExtendClose = "ClickOut"
end

function CHyaTestView.OnCreateView(self)
    --获取UI组件
    self.m_TieleLabel=self:NewUI(1, CLabel)
    self.m_button=self:NewUI(2, CButton)
    self.m_TieleLabel:SetText("测试界面")
    self.m_button:AddUIEvent("click", callback(self, "OnButtonClick"))
end

function CHyaTestView.OnShowView(self)
    print("测试界面显示了")
end

function CHyaTestView.OnHideView(self)
    print("测试界面隐藏了")
end

function CHyaTestView.OnButtonClick(self)
    print("按钮被点击了,关闭界面")
    g_NotifyCtrl:FloatMsg("点击了按钮")
    Share.ShareSDKManager.Google_Pay("gat.75dia")
    self:CloseView()
end


return CHyaTestView