with:710  hight:318
[logic/misc/CShareCtrl.lua:28]:jit    true    SSE2    SSE3    SSE4.1    BMI2    fold    cse    dce    fwd    dse    narrow    loop    abc    sink    fuse
[logic/login/CLoginCtrl.lua:5]:公告初始化...
[logic/base/CHttpCtrl.lua:32]:http get ->    https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials&client_id=rlU84dprH6I7sMjBkqM0gizl&client_secret=c15395c2433c4a904d22aa27eab8321b&
[logic/ui/CViewCtrl.lua:94]:CNotifyView ShowView
[logic/ui/CViewCtrl.lua:94]:CBottomView ShowView
[logic/ui/CViewBase.lua:125]:CNotifyView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x3febe2e8"
|  json_result = true
|  timer = 16
}
[logic/audio/CSpeechCtrl.lua:72]:OnTokenGet err
[logic/ui/CViewBase.lua:125]:CBottomView LoadDone!
[logic/base/CResCtrl.lua:199]:-->resource init done!!! 12
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {}
[logic/ui/CViewCtrl.lua:94]:CLoginView ShowView
[logic/base/CHttpCtrl.lua:32]:http get ->    http://**************:88/Note/note2408.json
[logic/ui/CViewBase.lua:125]:CLoginView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x6d74e160"
|  json_result = true
|  timer = 58
}
[core/table.lua:94]:cb tResult-> = {
|  info = {
|  |  role_list = {}
|  |  server_info = {
|  |  |  groups = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 1
|  |  |  |  |  name = "1-10区"
|  |  |  |  }
|  |  |  }
|  |  |  infoList = {
|  |  |  |  [1] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]服务器在8月16日22点00分进行不停机维护，解决ios无法使用可选御灵道具，0点掉线问题！[-]"
|  |  |  |  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "维护公告"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]欢迎来到《龙痕守护（妖怪物语0.05折）》！[-]"
|  |  |  |  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "开服了"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]单日累充活动[-]"
|  |  |  |  |  |  |  |  title = "【活动类型】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [10] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*120[-]"
|  |  |  |  |  |  |  |  title = "单日充值1500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [11] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1[-]"
|  |  |  |  |  |  |  |  title = "单日充值2500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [12] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*5、万能碎片*500、原石盒子*20、附魔材料盒子*20[-]"
|  |  |  |  |  |  |  |  title = "单日充值5000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [13] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.每日均可参与，高档位可领所有低档位；
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  |  |  |  title = "【活动时间】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "[ff0000]单日真实充值达到指定金额，即可领取该档位的奖励，本活动需要对应真实折后充值参与（高档位可以领取低档位的奖励)，每日活动重置[-]"
|  |  |  |  |  |  |  |  title = "【活动说明】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  |  |  |  title = "【领取方式】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [5] = {
|  |  |  |  |  |  |  |  text = "[ff0000]武器原石*5、星梦矿*10、金币*20000[-]"
|  |  |  |  |  |  |  |  title = "单日充值50元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [6] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*10、体力药水*1、鲜肉包*30、金币*100000[-]"
|  |  |  |  |  |  |  |  title = "单日充值150元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [7] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*20、体力药水*2、焦糖包*60、金币*200000[-]"
|  |  |  |  |  |  |  |  title = "单日充值250元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [8] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1[-]"
|  |  |  |  |  |  |  |  title = "单日充值500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [9] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3[-]"
|  |  |  |  |  |  |  |  title = "单日充值1000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "单日累充"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久累计充值活动[-]"
|  |  |  |  |  |  |  |  title = "活动名称："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [10] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*400、一发入魂碎片*100、原石盒子*40、附魔材料盒子*40、金币*3000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值7000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [11] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*700、一发入魂碎片*200、原石盒子*50、附魔材料盒子*50、金币*5000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值10000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [12] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.各档位奖励永久仅可获得一次返利，高档位可领所有低档位。
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额，活动解释权归游戏方所有。[-]"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  |  |  |  title = "领取方式："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "[ff0000]活动为永久累计真实充值，高档位可领取低档位所有物品（每个档位奖励仅可领取一次）[-]"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "[ff0000]梦觉书·零*50、星梦矿*50、金币*10000[-]"
|  |  |  |  |  |  |  |  title = "累计充值300元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [5] = {
|  |  |  |  |  |  |  |  text = "[ff0000]2星精英伙伴*10、原石盒子*5、附魔材料盒子*5、金币*50000[-]"
|  |  |  |  |  |  |  |  title = "累计充值500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [6] = {
|  |  |  |  |  |  |  |  text = "[ff0000]2星精英伙伴*20、原石盒子*10、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  |  |  |  title = "累计充值1000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [7] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*1、原石盒子*30、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  |  |  |  title = "累计充值2000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [8] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*100、一发入魂碎片*100、原石盒子*20、附魔材料盒子*20、金币*1000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值3000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [9] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*200、一发入魂碎片*200、原石盒子*30、附魔材料盒子*30、金币*2000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值5000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "累计充值"
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]所有区服开服之日起3天内，第一个累计实际真实充值金额1000元的玩家，可获得该区服务器冠名权+3星传说伙伴任选*1（需提供具体道具名，伙伴包括：亦候、袁雀、白殊、莹月、奉主夜鹤、浮屠僧）[-]"
|  |  |  |  |  |  |  |  title = "服务器冠名活动："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.达成条件的玩家需向客服申请，核实后将于5个工作日内完成冠名及发放奖励至玩家邮箱；
2.资格获得者可以自定义自己所在区服的服务器名称，冠名后不可修改，请慎重取名；
3.更改后，该区服名称永久有效；
4.服务器命名规则：不超过4个汉字，不支持符号、英文，禁止色情反动、诋毁他人等不文明或破坏社会风气的词语。
后面满足条件的玩家无法获得冠名权限但新服前三名满足条件的玩家都可以联系客服领取到冠名奖励呦！

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "冠名活动"
|  |  |  |  }
|  |  |  }
|  |  |  ports = {}
|  |  |  role_list = ""
|  |  |  servers = {
|  |  |  |  [1] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 2
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "流年岁月"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = **********
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7211
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10002"
|  |  |  |  |  start_time = **********
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 11
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护10区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1724515200
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8111
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10011"
|  |  |  |  |  start_time = 1724515200
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 12
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护11区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1724774400
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8211
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10012"
|  |  |  |  |  start_time = 1724774400
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 13
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护12区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1725033600
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8311
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10013"
|  |  |  |  |  start_time = 1725033600
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [13] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 14
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护13区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1725552000
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8411
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10014"
|  |  |  |  |  start_time = 1725552000
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [14] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 15
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护14区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1726156800
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8511
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10015"
|  |  |  |  |  start_time = 1726156800
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [15] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 16
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护15区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1726761600
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8611
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10016"
|  |  |  |  |  start_time = 1726761600
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [16] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 17
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护16区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1727366400
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8711
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10017"
|  |  |  |  |  start_time = 1727366400
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 3
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "诗情画意"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1723269300
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7211
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10003"
|  |  |  |  |  start_time = 1723269300
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 4
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "娱乐至上"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1723305600
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7211
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10004"
|  |  |  |  |  start_time = 1723305600
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 5
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "夜色真美"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1723392000
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7211
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10005"
|  |  |  |  |  start_time = 1723392000
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 6
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护5区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1723478400
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7211
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10006"
|  |  |  |  |  start_time = 1723478400
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 7
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "修罗世界"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1723651200
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7711
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10007"
|  |  |  |  |  start_time = 1723651200
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 8
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护7区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1723824000
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7811
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10008"
|  |  |  |  |  start_time = 1723824000
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 9
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护8区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = **********
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7911
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10009"
|  |  |  |  |  start_time = **********
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护9区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1724256000
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10010"
|  |  |  |  |  start_time = 1724256000
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  }
|  |  }
|  |  token = ""
|  }
}
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {
|  [1] = {
|  |  group_id = 1
|  |  id = 1
|  |  name = "1-10区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  id = 2
|  |  |  |  ip = "**************"
|  |  |  |  name = "流年岁月"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10002"
|  |  |  |  open_time = **********
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7211
|  |  |  |  }
|  |  |  |  server_id = 2
|  |  |  |  start_time = **********
|  |  |  |  state = 1
|  |  |  }
|  |  |  [10] = {
|  |  |  |  group = 1
|  |  |  |  id = 11
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护10区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10011"
|  |  |  |  open_time = 1724515200
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8111
|  |  |  |  }
|  |  |  |  server_id = 11
|  |  |  |  start_time = 1724515200
|  |  |  |  state = 1
|  |  |  }
|  |  |  [11] = {
|  |  |  |  group = 1
|  |  |  |  id = 12
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护11区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10012"
|  |  |  |  open_time = 1724774400
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8211
|  |  |  |  }
|  |  |  |  server_id = 12
|  |  |  |  start_time = 1724774400
|  |  |  |  state = 1
|  |  |  }
|  |  |  [12] = {
|  |  |  |  group = 1
|  |  |  |  id = 13
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护12区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10013"
|  |  |  |  open_time = 1725033600
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8311
|  |  |  |  }
|  |  |  |  server_id = 13
|  |  |  |  start_time = 1725033600
|  |  |  |  state = 1
|  |  |  }
|  |  |  [13] = {
|  |  |  |  group = 1
|  |  |  |  id = 14
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护13区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10014"
|  |  |  |  open_time = 1725552000
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8411
|  |  |  |  }
|  |  |  |  server_id = 14
|  |  |  |  start_time = 1725552000
|  |  |  |  state = 1
|  |  |  }
|  |  |  [14] = {
|  |  |  |  group = 1
|  |  |  |  id = 15
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护14区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10015"
|  |  |  |  open_time = 1726156800
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8511
|  |  |  |  }
|  |  |  |  server_id = 15
|  |  |  |  start_time = 1726156800
|  |  |  |  state = 1
|  |  |  }
|  |  |  [15] = {
|  |  |  |  group = 1
|  |  |  |  id = 16
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护15区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10016"
|  |  |  |  open_time = 1726761600
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8611
|  |  |  |  }
|  |  |  |  server_id = 16
|  |  |  |  start_time = 1726761600
|  |  |  |  state = 1
|  |  |  }
|  |  |  [16] = {
|  |  |  |  group = 1
|  |  |  |  id = 17
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护16区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10017"
|  |  |  |  open_time = 1727366400
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8711
|  |  |  |  }
|  |  |  |  server_id = 17
|  |  |  |  start_time = 1727366400
|  |  |  |  state = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  group = 1
|  |  |  |  id = 3
|  |  |  |  ip = "**************"
|  |  |  |  name = "诗情画意"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10003"
|  |  |  |  open_time = 1723269300
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7211
|  |  |  |  }
|  |  |  |  server_id = 3
|  |  |  |  start_time = 1723269300
|  |  |  |  state = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  group = 1
|  |  |  |  id = 4
|  |  |  |  ip = "**************"
|  |  |  |  name = "娱乐至上"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10004"
|  |  |  |  open_time = 1723305600
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7211
|  |  |  |  }
|  |  |  |  server_id = 4
|  |  |  |  start_time = 1723305600
|  |  |  |  state = 1
|  |  |  }
|  |  |  [4] = {
|  |  |  |  group = 1
|  |  |  |  id = 5
|  |  |  |  ip = "**************"
|  |  |  |  name = "夜色真美"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10005"
|  |  |  |  open_time = 1723392000
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7211
|  |  |  |  }
|  |  |  |  server_id = 5
|  |  |  |  start_time = 1723392000
|  |  |  |  state = 1
|  |  |  }
|  |  |  [5] = {
|  |  |  |  group = 1
|  |  |  |  id = 6
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护5区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10006"
|  |  |  |  open_time = 1723478400
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7211
|  |  |  |  }
|  |  |  |  server_id = 6
|  |  |  |  start_time = 1723478400
|  |  |  |  state = 1
|  |  |  }
|  |  |  [6] = {
|  |  |  |  group = 1
|  |  |  |  id = 7
|  |  |  |  ip = "**************"
|  |  |  |  name = "修罗世界"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10007"
|  |  |  |  open_time = 1723651200
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7711
|  |  |  |  }
|  |  |  |  server_id = 7
|  |  |  |  start_time = 1723651200
|  |  |  |  state = 1
|  |  |  }
|  |  |  [7] = {
|  |  |  |  group = 1
|  |  |  |  id = 8
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护7区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10008"
|  |  |  |  open_time = 1723824000
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7811
|  |  |  |  }
|  |  |  |  server_id = 8
|  |  |  |  start_time = 1723824000
|  |  |  |  state = 1
|  |  |  }
|  |  |  [8] = {
|  |  |  |  group = 1
|  |  |  |  id = 9
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护8区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10009"
|  |  |  |  open_time = **********
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7911
|  |  |  |  }
|  |  |  |  server_id = 9
|  |  |  |  start_time = **********
|  |  |  |  state = 1
|  |  |  }
|  |  |  [9] = {
|  |  |  |  group = 1
|  |  |  |  id = 10
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护9区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10010"
|  |  |  |  open_time = 1724256000
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8011
|  |  |  |  }
|  |  |  |  server_id = 10
|  |  |  |  start_time = 1724256000
|  |  |  |  state = 1
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:设置公告: = {
|  [1] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]服务器在8月16日22点00分进行不停机维护，解决ios无法使用可选御灵道具，0点掉线问题！[-]"
|  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "维护公告"
|  }
|  [2] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]欢迎来到《龙痕守护（妖怪物语0.05折）》！[-]"
|  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "开服了"
|  }
|  [3] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]单日累充活动[-]"
|  |  |  |  |  title = "【活动类型】"
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*120[-]"
|  |  |  |  |  title = "单日充值1500元："
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1[-]"
|  |  |  |  |  title = "单日充值2500元："
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*5、万能碎片*500、原石盒子*20、附魔材料盒子*20[-]"
|  |  |  |  |  title = "单日充值5000元："
|  |  |  |  }
|  |  |  |  [13] = {
|  |  |  |  |  text = "[ff0000]1.每日均可参与，高档位可领所有低档位；
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  title = "【活动时间】"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "[ff0000]单日真实充值达到指定金额，即可领取该档位的奖励，本活动需要对应真实折后充值参与（高档位可以领取低档位的奖励)，每日活动重置[-]"
|  |  |  |  |  title = "【活动说明】"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  title = "【领取方式】"
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  text = "[ff0000]武器原石*5、星梦矿*10、金币*20000[-]"
|  |  |  |  |  title = "单日充值50元："
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*10、体力药水*1、鲜肉包*30、金币*100000[-]"
|  |  |  |  |  title = "单日充值150元："
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*20、体力药水*2、焦糖包*60、金币*200000[-]"
|  |  |  |  |  title = "单日充值250元："
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  text = "[ff0000]装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1[-]"
|  |  |  |  |  title = "单日充值500元："
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  text = "[ff0000]装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3[-]"
|  |  |  |  |  title = "单日充值1000元："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "单日累充"
|  }
|  [4] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]永久累计充值活动[-]"
|  |  |  |  |  title = "活动名称："
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  text = "[ff0000]万能碎片*400、一发入魂碎片*100、原石盒子*40、附魔材料盒子*40、金币*3000000[-]"
|  |  |  |  |  title = "累计充值7000元："
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  text = "[ff0000]万能碎片*700、一发入魂碎片*200、原石盒子*50、附魔材料盒子*50、金币*5000000[-]"
|  |  |  |  |  title = "累计充值10000元："
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  text = "[ff0000]1.各档位奖励永久仅可获得一次返利，高档位可领所有低档位。
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额，活动解释权归游戏方所有。[-]"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  title = "领取方式："
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "[ff0000]活动为永久累计真实充值，高档位可领取低档位所有物品（每个档位奖励仅可领取一次）[-]"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "[ff0000]梦觉书·零*50、星梦矿*50、金币*10000[-]"
|  |  |  |  |  title = "累计充值300元："
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  text = "[ff0000]2星精英伙伴*10、原石盒子*5、附魔材料盒子*5、金币*50000[-]"
|  |  |  |  |  title = "累计充值500元："
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  text = "[ff0000]2星精英伙伴*20、原石盒子*10、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  title = "累计充值1000元："
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*1、原石盒子*30、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  title = "累计充值2000元："
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  text = "[ff0000]万能碎片*100、一发入魂碎片*100、原石盒子*20、附魔材料盒子*20、金币*1000000[-]"
|  |  |  |  |  title = "累计充值3000元："
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  text = "[ff0000]万能碎片*200、一发入魂碎片*200、原石盒子*30、附魔材料盒子*30、金币*2000000[-]"
|  |  |  |  |  title = "累计充值5000元："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "累计充值"
|  }
|  [5] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]所有区服开服之日起3天内，第一个累计实际真实充值金额1000元的玩家，可获得该区服务器冠名权+3星传说伙伴任选*1（需提供具体道具名，伙伴包括：亦候、袁雀、白殊、莹月、奉主夜鹤、浮屠僧）[-]"
|  |  |  |  |  title = "服务器冠名活动："
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]1.达成条件的玩家需向客服申请，核实后将于5个工作日内完成冠名及发放奖励至玩家邮箱；
2.资格获得者可以自定义自己所在区服的服务器名称，冠名后不可修改，请慎重取名；
3.更改后，该区服名称永久有效；
4.服务器命名规则：不超过4个汉字，不支持符号、英文，禁止色情反动、诋毁他人等不文明或破坏社会风气的词语。
后面满足条件的玩家无法获得冠名权限但新服前三名满足条件的玩家都可以联系客服领取到冠名奖励呦！

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "冠名活动"
|  }
}
[core/table.lua:94]:http post -> = {
|  headers = {
|  |  Content-Type = "application/x-www-form-urlencoded"
|  }
|  url = "http://**************:8080/common/accountList"
}
[core/global.lua:59]:<color=#ffeb04>CLoginServerPage.SetServer</color>
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x4986fce8"
|  json_result = true
|  timer = 64
}
[core/table.lua:94]:cb tResult-> = {
|  code = 0
|  data = {
|  |  [1] = {
|  |  |  account = "3805916"
|  |  |  grade = 66
|  |  |  icon = 130
|  |  |  name = "废柴v寿司"
|  |  |  now_server = "gs10004"
|  |  |  pid = 40009
|  |  |  platform = 1
|  |  }
|  }
|  msg = "操作成功"
}
[logic/login/CLoginCtrl.lua:154]:ConnectServer =     table:0x6D755C80    table:0x69108858
[net/CNetCtrl.lua:114]:Test连接    **************    7211
[core/table.lua:94]:-->Net Receive: login.GS2CHello = {
|  time = **********
}
[core/table.lua:94]:<--Net Send: login.C2GSQueryLogin = {
|  res_file_version = {}
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2024/08/21 13:38:16
[net/netlogin.lua:208]:netlogin.GS2CQueryLogin-->
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 1001
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginAccount = {
|  account = "3805916"
|  client_svn_version = 96
|  client_version = "1.15.0.0"
|  device = "B760M GAMING (JGINYUE)"
|  imei = ""
|  is_qrcode = 0
|  mac = "00-E0-1A-9A-48-AB"
|  os = "Windows 10  (10.0.0) 64bit"
|  platform = 3
|  udid = "6370fc50ee13b3bf1fd91cc507270f729c390721"
}
[core/global.lua:59]:<color=#ffeb04>更新代码: 1049</color>
[logic/base/Utils.lua:400]:local framever, dllver, resver = C_api.Utils.GetResVersion()
if resver >= 96 or Utils.IsEditor() then
	function CWelfareCtrl.IsOpenCostSave(self)
		local b = false
		local t = g_TimeCtrl:GetTimeS()	
		if self.m_CostSaveStartTime ~= 0 and self.m_CostSaveEndTime ~= 0 and t > self.m_CostSaveStartTime and t < self.m_CostSaveEndTime then
			b = true
		end
		return b
	end
	function CPartnerHireView.IsCanYFRH(self, iParID)
		local d = data.partnerhiredata.DATA
		if table.index(d[iParID]["recommand_list"], "一发入魂") then
			return self:IsCanSSRDraw()
		end
	end
	function CPartnerEquipPage.ChangePart(self)
		local oItem = g_ItemCtrl:GetItem(self.m_CurItemID)
		if oItem and oItem:GetValue("level") == define.Partner.ParEquip.MaxLevel and oItem:GetValue("star") < define.Partner.ParEquip.MaxStar then
			if self.m_CurPart ~= self.m_UpStarPart and self.m_CurPart ~= self.m_UpStonePart then
				self.m_UpStarBtn:SetSelected(true)
				self:SwitchPart()
			end
		end
	end
	data.netdata.BAN["proto"]["herobox"]["item"]["GS2CItemQuickUse"] = true
end

[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 1001
}
[core/table.lua:94]:-->Net Receive: login.GS2CCheckInviteCodeResult = {
|  msg = "邀请码验证已关闭"
|  result = 1
}
[core/table.lua:94]:-->Net Receive: login.GS2CLoginAccount = {
|  account = "3805916"
}
[core/global.lua:59]:<color=#ffeb04>读取系统设置本地数据，帐号为  3805916</color>
[core/global.lua:59]:<color=#ffeb04>3805916 的系统设置本地数据为 nil，使用默认数据</color>
[core/global.lua:59]:<color=#ffeb04>=00000=  userdata: 0x498bcd80 nil</color>
[core/global.lua:59]:<color=#ffeb04>读取地图： 6100</color>
[core/global.lua:59]:<color=#ffeb04>Unity2017.4.30f1 localScale</color>
[logic/ui/CViewCtrl.lua:104]:CLoginView     CloseView
[logic/base/CResCtrl.lua:599]:res gc step start!
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[core/global.lua:59]:<color=#ffeb04>地图加载完成: 6100 ,当前地图: nil</color>
[core/table.lua:94]:Table = {}
[logic/ui/CViewCtrl.lua:94]:CCreateRoleView ShowView
[logic/misc/CUploadDataCtrl.lua:9]:流派1
[core/table.lua:94]:http post -> = {
|  headers = {
|  |  Content-Type = "application/x-www-form-urlencoded"
|  }
|  url = "http://**************:88/clientdata/"
}
[logic/misc/CUploadDataCtrl.lua:9]:性别male
[core/table.lua:94]:http post -> = {
|  headers = {
|  |  Content-Type = "application/x-www-form-urlencoded"
|  }
|  url = "http://**************:88/clientdata/"
}
[logic/misc/CUploadDataCtrl.lua:9]:职业2
[core/table.lua:94]:http post -> = {
|  headers = {
|  |  Content-Type = "application/x-www-form-urlencoded"
|  }
|  url = "http://**************:88/clientdata/"
}
[logic/ui/CViewBase.lua:125]:CCreateRoleView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  timer = 100
}
[core/table.lua:94]:CommonProcess-> = {
|  timer = 113
}
[core/table.lua:94]:CommonProcess-> = {
|  timer = 116
}
[logic/model/CModel.lua:181]:造型:    131    ,没有武器    2000
[logic/base/CResCtrl.lua:626]:res gc finish!
[logic/ui/CViewCtrl.lua:94]:CLoginView ShowView
[logic/ui/CViewCtrl.lua:104]:CCreateRoleView     CloseView
[logic/misc/CUploadDataCtrl.lua:9]:返回登录界面
[core/table.lua:94]:http post -> = {
|  headers = {
|  |  Content-Type = "application/x-www-form-urlencoded"
|  }
|  url = "http://**************:88/clientdata/"
}
[logic/base/CHttpCtrl.lua:32]:http get ->    http://**************:88/Note/note2408.json
[logic/ui/CViewBase.lua:125]:CLoginView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  timer = 134
}
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x413bfa98"
|  json_result = true
|  timer = 158
}
[core/table.lua:94]:cb tResult-> = {
|  info = {
|  |  role_list = {}
|  |  server_info = {
|  |  |  groups = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 1
|  |  |  |  |  name = "1-10区"
|  |  |  |  }
|  |  |  }
|  |  |  infoList = {
|  |  |  |  [1] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]服务器在8月16日22点00分进行不停机维护，解决ios无法使用可选御灵道具，0点掉线问题！[-]"
|  |  |  |  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "维护公告"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]欢迎来到《龙痕守护（妖怪物语0.05折）》！[-]"
|  |  |  |  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "开服了"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]单日累充活动[-]"
|  |  |  |  |  |  |  |  title = "【活动类型】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [10] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*120[-]"
|  |  |  |  |  |  |  |  title = "单日充值1500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [11] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1[-]"
|  |  |  |  |  |  |  |  title = "单日充值2500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [12] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*5、万能碎片*500、原石盒子*20、附魔材料盒子*20[-]"
|  |  |  |  |  |  |  |  title = "单日充值5000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [13] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.每日均可参与，高档位可领所有低档位；
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  |  |  |  title = "【活动时间】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "[ff0000]单日真实充值达到指定金额，即可领取该档位的奖励，本活动需要对应真实折后充值参与（高档位可以领取低档位的奖励)，每日活动重置[-]"
|  |  |  |  |  |  |  |  title = "【活动说明】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  |  |  |  title = "【领取方式】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [5] = {
|  |  |  |  |  |  |  |  text = "[ff0000]武器原石*5、星梦矿*10、金币*20000[-]"
|  |  |  |  |  |  |  |  title = "单日充值50元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [6] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*10、体力药水*1、鲜肉包*30、金币*100000[-]"
|  |  |  |  |  |  |  |  title = "单日充值150元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [7] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*20、体力药水*2、焦糖包*60、金币*200000[-]"
|  |  |  |  |  |  |  |  title = "单日充值250元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [8] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1[-]"
|  |  |  |  |  |  |  |  title = "单日充值500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [9] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3[-]"
|  |  |  |  |  |  |  |  title = "单日充值1000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "单日累充"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久累计充值活动[-]"
|  |  |  |  |  |  |  |  title = "活动名称："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [10] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*400、一发入魂碎片*100、原石盒子*40、附魔材料盒子*40、金币*3000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值7000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [11] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*700、一发入魂碎片*200、原石盒子*50、附魔材料盒子*50、金币*5000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值10000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [12] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.各档位奖励永久仅可获得一次返利，高档位可领所有低档位。
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额，活动解释权归游戏方所有。[-]"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  |  |  |  title = "领取方式："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "[ff0000]活动为永久累计真实充值，高档位可领取低档位所有物品（每个档位奖励仅可领取一次）[-]"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "[ff0000]梦觉书·零*50、星梦矿*50、金币*10000[-]"
|  |  |  |  |  |  |  |  title = "累计充值300元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [5] = {
|  |  |  |  |  |  |  |  text = "[ff0000]2星精英伙伴*10、原石盒子*5、附魔材料盒子*5、金币*50000[-]"
|  |  |  |  |  |  |  |  title = "累计充值500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [6] = {
|  |  |  |  |  |  |  |  text = "[ff0000]2星精英伙伴*20、原石盒子*10、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  |  |  |  title = "累计充值1000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [7] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*1、原石盒子*30、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  |  |  |  title = "累计充值2000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [8] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*100、一发入魂碎片*100、原石盒子*20、附魔材料盒子*20、金币*1000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值3000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [9] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*200、一发入魂碎片*200、原石盒子*30、附魔材料盒子*30、金币*2000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值5000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "累计充值"
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]所有区服开服之日起3天内，第一个累计实际真实充值金额1000元的玩家，可获得该区服务器冠名权+3星传说伙伴任选*1（需提供具体道具名，伙伴包括：亦候、袁雀、白殊、莹月、奉主夜鹤、浮屠僧）[-]"
|  |  |  |  |  |  |  |  title = "服务器冠名活动："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.达成条件的玩家需向客服申请，核实后将于5个工作日内完成冠名及发放奖励至玩家邮箱；
2.资格获得者可以自定义自己所在区服的服务器名称，冠名后不可修改，请慎重取名；
3.更改后，该区服名称永久有效；
4.服务器命名规则：不超过4个汉字，不支持符号、英文，禁止色情反动、诋毁他人等不文明或破坏社会风气的词语。
后面满足条件的玩家无法获得冠名权限但新服前三名满足条件的玩家都可以联系客服领取到冠名奖励呦！

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "冠名活动"
|  |  |  |  }
|  |  |  }
|  |  |  ports = {}
|  |  |  role_list = ""
|  |  |  servers = {
|  |  |  |  [1] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 2
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "流年岁月"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = **********
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7211
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10002"
|  |  |  |  |  start_time = **********
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 11
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护10区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1724515200
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8111
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10011"
|  |  |  |  |  start_time = 1724515200
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 12
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护11区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1724774400
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8211
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10012"
|  |  |  |  |  start_time = 1724774400
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 13
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护12区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1725033600
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8311
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10013"
|  |  |  |  |  start_time = 1725033600
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [13] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 14
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护13区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1725552000
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8411
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10014"
|  |  |  |  |  start_time = 1725552000
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [14] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 15
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护14区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1726156800
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8511
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10015"
|  |  |  |  |  start_time = 1726156800
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [15] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 16
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护15区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1726761600
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8611
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10016"
|  |  |  |  |  start_time = 1726761600
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [16] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 17
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护16区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1727366400
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8711
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10017"
|  |  |  |  |  start_time = 1727366400
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 3
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "诗情画意"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1723269300
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7211
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10003"
|  |  |  |  |  start_time = 1723269300
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 4
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "娱乐至上"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1723305600
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7211
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10004"
|  |  |  |  |  start_time = 1723305600
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 5
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "夜色真美"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1723392000
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7211
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10005"
|  |  |  |  |  start_time = 1723392000
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 6
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护5区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1723478400
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7211
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10006"
|  |  |  |  |  start_time = 1723478400
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 7
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "修罗世界"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1723651200
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7711
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10007"
|  |  |  |  |  start_time = 1723651200
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 8
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护7区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1723824000
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7811
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10008"
|  |  |  |  |  start_time = 1723824000
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 9
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护8区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = **********
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7911
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10009"
|  |  |  |  |  start_time = **********
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 10
|  |  |  |  |  ip = "**************"
|  |  |  |  |  name = "守护9区"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1724256000
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 8011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10010"
|  |  |  |  |  start_time = 1724256000
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  }
|  |  }
|  |  token = ""
|  }
}
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {
|  [1] = {
|  |  group_id = 1
|  |  id = 1
|  |  name = "1-10区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  id = 2
|  |  |  |  ip = "**************"
|  |  |  |  name = "流年岁月"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10002"
|  |  |  |  open_time = **********
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7211
|  |  |  |  }
|  |  |  |  server_id = 2
|  |  |  |  start_time = **********
|  |  |  |  state = 1
|  |  |  }
|  |  |  [10] = {
|  |  |  |  group = 1
|  |  |  |  id = 11
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护10区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10011"
|  |  |  |  open_time = 1724515200
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8111
|  |  |  |  }
|  |  |  |  server_id = 11
|  |  |  |  start_time = 1724515200
|  |  |  |  state = 1
|  |  |  }
|  |  |  [11] = {
|  |  |  |  group = 1
|  |  |  |  id = 12
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护11区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10012"
|  |  |  |  open_time = 1724774400
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8211
|  |  |  |  }
|  |  |  |  server_id = 12
|  |  |  |  start_time = 1724774400
|  |  |  |  state = 1
|  |  |  }
|  |  |  [12] = {
|  |  |  |  group = 1
|  |  |  |  id = 13
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护12区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10013"
|  |  |  |  open_time = 1725033600
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8311
|  |  |  |  }
|  |  |  |  server_id = 13
|  |  |  |  start_time = 1725033600
|  |  |  |  state = 1
|  |  |  }
|  |  |  [13] = {
|  |  |  |  group = 1
|  |  |  |  id = 14
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护13区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10014"
|  |  |  |  open_time = 1725552000
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8411
|  |  |  |  }
|  |  |  |  server_id = 14
|  |  |  |  start_time = 1725552000
|  |  |  |  state = 1
|  |  |  }
|  |  |  [14] = {
|  |  |  |  group = 1
|  |  |  |  id = 15
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护14区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10015"
|  |  |  |  open_time = 1726156800
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8511
|  |  |  |  }
|  |  |  |  server_id = 15
|  |  |  |  start_time = 1726156800
|  |  |  |  state = 1
|  |  |  }
|  |  |  [15] = {
|  |  |  |  group = 1
|  |  |  |  id = 16
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护15区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10016"
|  |  |  |  open_time = 1726761600
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8611
|  |  |  |  }
|  |  |  |  server_id = 16
|  |  |  |  start_time = 1726761600
|  |  |  |  state = 1
|  |  |  }
|  |  |  [16] = {
|  |  |  |  group = 1
|  |  |  |  id = 17
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护16区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10017"
|  |  |  |  open_time = 1727366400
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8711
|  |  |  |  }
|  |  |  |  server_id = 17
|  |  |  |  start_time = 1727366400
|  |  |  |  state = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  group = 1
|  |  |  |  id = 3
|  |  |  |  ip = "**************"
|  |  |  |  name = "诗情画意"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10003"
|  |  |  |  open_time = 1723269300
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7211
|  |  |  |  }
|  |  |  |  server_id = 3
|  |  |  |  start_time = 1723269300
|  |  |  |  state = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  group = 1
|  |  |  |  id = 4
|  |  |  |  ip = "**************"
|  |  |  |  name = "娱乐至上"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10004"
|  |  |  |  open_time = 1723305600
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7211
|  |  |  |  }
|  |  |  |  server_id = 4
|  |  |  |  start_time = 1723305600
|  |  |  |  state = 1
|  |  |  }
|  |  |  [4] = {
|  |  |  |  group = 1
|  |  |  |  id = 5
|  |  |  |  ip = "**************"
|  |  |  |  name = "夜色真美"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10005"
|  |  |  |  open_time = 1723392000
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7211
|  |  |  |  }
|  |  |  |  server_id = 5
|  |  |  |  start_time = 1723392000
|  |  |  |  state = 1
|  |  |  }
|  |  |  [5] = {
|  |  |  |  group = 1
|  |  |  |  id = 6
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护5区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10006"
|  |  |  |  open_time = 1723478400
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7211
|  |  |  |  }
|  |  |  |  server_id = 6
|  |  |  |  start_time = 1723478400
|  |  |  |  state = 1
|  |  |  }
|  |  |  [6] = {
|  |  |  |  group = 1
|  |  |  |  id = 7
|  |  |  |  ip = "**************"
|  |  |  |  name = "修罗世界"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10007"
|  |  |  |  open_time = 1723651200
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7711
|  |  |  |  }
|  |  |  |  server_id = 7
|  |  |  |  start_time = 1723651200
|  |  |  |  state = 1
|  |  |  }
|  |  |  [7] = {
|  |  |  |  group = 1
|  |  |  |  id = 8
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护7区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10008"
|  |  |  |  open_time = 1723824000
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7811
|  |  |  |  }
|  |  |  |  server_id = 8
|  |  |  |  start_time = 1723824000
|  |  |  |  state = 1
|  |  |  }
|  |  |  [8] = {
|  |  |  |  group = 1
|  |  |  |  id = 9
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护8区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10009"
|  |  |  |  open_time = **********
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7911
|  |  |  |  }
|  |  |  |  server_id = 9
|  |  |  |  start_time = **********
|  |  |  |  state = 1
|  |  |  }
|  |  |  [9] = {
|  |  |  |  group = 1
|  |  |  |  id = 10
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护9区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10010"
|  |  |  |  open_time = 1724256000
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8011
|  |  |  |  }
|  |  |  |  server_id = 10
|  |  |  |  start_time = 1724256000
|  |  |  |  state = 1
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:设置公告: = {
|  [1] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]服务器在8月16日22点00分进行不停机维护，解决ios无法使用可选御灵道具，0点掉线问题！[-]"
|  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "维护公告"
|  }
|  [2] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]欢迎来到《龙痕守护（妖怪物语0.05折）》！[-]"
|  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "开服了"
|  }
|  [3] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]单日累充活动[-]"
|  |  |  |  |  title = "【活动类型】"
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*120[-]"
|  |  |  |  |  title = "单日充值1500元："
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1[-]"
|  |  |  |  |  title = "单日充值2500元："
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*5、万能碎片*500、原石盒子*20、附魔材料盒子*20[-]"
|  |  |  |  |  title = "单日充值5000元："
|  |  |  |  }
|  |  |  |  [13] = {
|  |  |  |  |  text = "[ff0000]1.每日均可参与，高档位可领所有低档位；
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  title = "【活动时间】"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "[ff0000]单日真实充值达到指定金额，即可领取该档位的奖励，本活动需要对应真实折后充值参与（高档位可以领取低档位的奖励)，每日活动重置[-]"
|  |  |  |  |  title = "【活动说明】"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  title = "【领取方式】"
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  text = "[ff0000]武器原石*5、星梦矿*10、金币*20000[-]"
|  |  |  |  |  title = "单日充值50元："
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*10、体力药水*1、鲜肉包*30、金币*100000[-]"
|  |  |  |  |  title = "单日充值150元："
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*20、体力药水*2、焦糖包*60、金币*200000[-]"
|  |  |  |  |  title = "单日充值250元："
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  text = "[ff0000]装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1[-]"
|  |  |  |  |  title = "单日充值500元："
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  text = "[ff0000]装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3[-]"
|  |  |  |  |  title = "单日充值1000元："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "单日累充"
|  }
|  [4] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]永久累计充值活动[-]"
|  |  |  |  |  title = "活动名称："
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  text = "[ff0000]万能碎片*400、一发入魂碎片*100、原石盒子*40、附魔材料盒子*40、金币*3000000[-]"
|  |  |  |  |  title = "累计充值7000元："
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  text = "[ff0000]万能碎片*700、一发入魂碎片*200、原石盒子*50、附魔材料盒子*50、金币*5000000[-]"
|  |  |  |  |  title = "累计充值10000元："
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  text = "[ff0000]1.各档位奖励永久仅可获得一次返利，高档位可领所有低档位。
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额，活动解释权归游戏方所有。[-]"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  title = "领取方式："
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "[ff0000]活动为永久累计真实充值，高档位可领取低档位所有物品（每个档位奖励仅可领取一次）[-]"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "[ff0000]梦觉书·零*50、星梦矿*50、金币*10000[-]"
|  |  |  |  |  title = "累计充值300元："
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  text = "[ff0000]2星精英伙伴*10、原石盒子*5、附魔材料盒子*5、金币*50000[-]"
|  |  |  |  |  title = "累计充值500元："
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  text = "[ff0000]2星精英伙伴*20、原石盒子*10、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  title = "累计充值1000元："
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*1、原石盒子*30、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  title = "累计充值2000元："
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  text = "[ff0000]万能碎片*100、一发入魂碎片*100、原石盒子*20、附魔材料盒子*20、金币*1000000[-]"
|  |  |  |  |  title = "累计充值3000元："
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  text = "[ff0000]万能碎片*200、一发入魂碎片*200、原石盒子*30、附魔材料盒子*30、金币*2000000[-]"
|  |  |  |  |  title = "累计充值5000元："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "累计充值"
|  }
|  [5] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]所有区服开服之日起3天内，第一个累计实际真实充值金额1000元的玩家，可获得该区服务器冠名权+3星传说伙伴任选*1（需提供具体道具名，伙伴包括：亦候、袁雀、白殊、莹月、奉主夜鹤、浮屠僧）[-]"
|  |  |  |  |  title = "服务器冠名活动："
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]1.达成条件的玩家需向客服申请，核实后将于5个工作日内完成冠名及发放奖励至玩家邮箱；
2.资格获得者可以自定义自己所在区服的服务器名称，冠名后不可修改，请慎重取名；
3.更改后，该区服名称永久有效；
4.服务器命名规则：不超过4个汉字，不支持符号、英文，禁止色情反动、诋毁他人等不文明或破坏社会风气的词语。
后面满足条件的玩家无法获得冠名权限但新服前三名满足条件的玩家都可以联系客服领取到冠名奖励呦！

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "冠名活动"
|  }
}
[core/table.lua:94]:http post -> = {
|  headers = {
|  |  Content-Type = "application/x-www-form-urlencoded"
|  }
|  url = "http://**************:8080/common/accountList"
}
[core/global.lua:59]:<color=#ffeb04>CLoginServerPage.SetServer</color>
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x6d79b6a0"
|  json_result = true
|  timer = 164
}
[core/table.lua:94]:cb tResult-> = {
|  code = 0
|  data = {
|  |  [1] = {
|  |  |  account = "3805916"
|  |  |  grade = 66
|  |  |  icon = 130
|  |  |  name = "废柴v寿司"
|  |  |  now_server = "gs10004"
|  |  |  pid = 40009
|  |  |  platform = 1
|  |  }
|  }
|  msg = "操作成功"
}
[logic/ui/CViewCtrl.lua:94]:CSelectServerView ShowView
[core/table.lua:94]:GroupServers------------------------> = {
|  [1] = {
|  |  group_id = 1
|  |  id = 1
|  |  name = "1-10区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  id = 2
|  |  |  |  ip = "**************"
|  |  |  |  name = "流年岁月"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10002"
|  |  |  |  open_time = **********
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7211
|  |  |  |  }
|  |  |  |  server_id = 2
|  |  |  |  start_time = **********
|  |  |  |  state = 1
|  |  |  }
|  |  |  [10] = {
|  |  |  |  group = 1
|  |  |  |  id = 11
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护10区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10011"
|  |  |  |  open_time = 1724515200
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8111
|  |  |  |  }
|  |  |  |  server_id = 11
|  |  |  |  start_time = 1724515200
|  |  |  |  state = 1
|  |  |  }
|  |  |  [11] = {
|  |  |  |  group = 1
|  |  |  |  id = 12
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护11区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10012"
|  |  |  |  open_time = 1724774400
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8211
|  |  |  |  }
|  |  |  |  server_id = 12
|  |  |  |  start_time = 1724774400
|  |  |  |  state = 1
|  |  |  }
|  |  |  [12] = {
|  |  |  |  group = 1
|  |  |  |  id = 13
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护12区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10013"
|  |  |  |  open_time = 1725033600
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8311
|  |  |  |  }
|  |  |  |  server_id = 13
|  |  |  |  start_time = 1725033600
|  |  |  |  state = 1
|  |  |  }
|  |  |  [13] = {
|  |  |  |  group = 1
|  |  |  |  id = 14
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护13区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10014"
|  |  |  |  open_time = 1725552000
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8411
|  |  |  |  }
|  |  |  |  server_id = 14
|  |  |  |  start_time = 1725552000
|  |  |  |  state = 1
|  |  |  }
|  |  |  [14] = {
|  |  |  |  group = 1
|  |  |  |  id = 15
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护14区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10015"
|  |  |  |  open_time = 1726156800
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8511
|  |  |  |  }
|  |  |  |  server_id = 15
|  |  |  |  start_time = 1726156800
|  |  |  |  state = 1
|  |  |  }
|  |  |  [15] = {
|  |  |  |  group = 1
|  |  |  |  id = 16
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护15区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10016"
|  |  |  |  open_time = 1726761600
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8611
|  |  |  |  }
|  |  |  |  server_id = 16
|  |  |  |  start_time = 1726761600
|  |  |  |  state = 1
|  |  |  }
|  |  |  [16] = {
|  |  |  |  group = 1
|  |  |  |  id = 17
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护16区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10017"
|  |  |  |  open_time = 1727366400
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8711
|  |  |  |  }
|  |  |  |  server_id = 17
|  |  |  |  start_time = 1727366400
|  |  |  |  state = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  group = 1
|  |  |  |  id = 3
|  |  |  |  ip = "**************"
|  |  |  |  name = "诗情画意"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10003"
|  |  |  |  open_time = 1723269300
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7211
|  |  |  |  }
|  |  |  |  server_id = 3
|  |  |  |  start_time = 1723269300
|  |  |  |  state = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  group = 1
|  |  |  |  id = 4
|  |  |  |  ip = "**************"
|  |  |  |  name = "娱乐至上"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10004"
|  |  |  |  open_time = 1723305600
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7211
|  |  |  |  }
|  |  |  |  server_id = 4
|  |  |  |  start_time = 1723305600
|  |  |  |  state = 1
|  |  |  }
|  |  |  [4] = {
|  |  |  |  group = 1
|  |  |  |  id = 5
|  |  |  |  ip = "**************"
|  |  |  |  name = "夜色真美"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10005"
|  |  |  |  open_time = 1723392000
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7211
|  |  |  |  }
|  |  |  |  server_id = 5
|  |  |  |  start_time = 1723392000
|  |  |  |  state = 1
|  |  |  }
|  |  |  [5] = {
|  |  |  |  group = 1
|  |  |  |  id = 6
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护5区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10006"
|  |  |  |  open_time = 1723478400
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7211
|  |  |  |  }
|  |  |  |  server_id = 6
|  |  |  |  start_time = 1723478400
|  |  |  |  state = 1
|  |  |  }
|  |  |  [6] = {
|  |  |  |  group = 1
|  |  |  |  id = 7
|  |  |  |  ip = "**************"
|  |  |  |  name = "修罗世界"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10007"
|  |  |  |  open_time = 1723651200
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7711
|  |  |  |  }
|  |  |  |  server_id = 7
|  |  |  |  start_time = 1723651200
|  |  |  |  state = 1
|  |  |  }
|  |  |  [7] = {
|  |  |  |  group = 1
|  |  |  |  id = 8
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护7区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10008"
|  |  |  |  open_time = 1723824000
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7811
|  |  |  |  }
|  |  |  |  server_id = 8
|  |  |  |  start_time = 1723824000
|  |  |  |  state = 1
|  |  |  }
|  |  |  [8] = {
|  |  |  |  group = 1
|  |  |  |  id = 9
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护8区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10009"
|  |  |  |  open_time = **********
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7911
|  |  |  |  }
|  |  |  |  server_id = 9
|  |  |  |  start_time = **********
|  |  |  |  state = 1
|  |  |  }
|  |  |  [9] = {
|  |  |  |  group = 1
|  |  |  |  id = 10
|  |  |  |  ip = "**************"
|  |  |  |  name = "守护9区"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10010"
|  |  |  |  open_time = 1724256000
|  |  |  |  ports = {
|  |  |  |  |  [1] = 8011
|  |  |  |  }
|  |  |  |  server_id = 10
|  |  |  |  start_time = 1724256000
|  |  |  |  state = 1
|  |  |  }
|  |  }
|  }
}
[logic/ui/CViewBase.lua:125]:CSelectServerView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[logic/login/CLoginCtrl.lua:154]:ConnectServer =     table:0x68FF9770    table:0x441447F0
[net/CNetCtrl.lua:114]:Test连接    **************    7211
[core/table.lua:94]:-->Net Receive: login.GS2CHello = {
|  time = **********
}
[core/table.lua:94]:<--Net Send: login.C2GSQueryLogin = {
|  res_file_version = {}
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2024/08/21 13:38:21
[net/netlogin.lua:208]:netlogin.GS2CQueryLogin-->
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 1001
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginAccount = {
|  account = "3805916"
|  client_svn_version = 96
|  client_version = "1.15.0.0"
|  device = "B760M GAMING (JGINYUE)"
|  imei = ""
|  is_qrcode = 0
|  mac = "00-E0-1A-9A-48-AB"
|  os = "Windows 10  (10.0.0) 64bit"
|  platform = 1
|  udid = "6370fc50ee13b3bf1fd91cc507270f729c390721"
}
[core/global.lua:59]:<color=#ffeb04>更新代码: 1049</color>
[logic/base/Utils.lua:400]:local framever, dllver, resver = C_api.Utils.GetResVersion()
if resver >= 96 or Utils.IsEditor() then
	function CWelfareCtrl.IsOpenCostSave(self)
		local b = false
		local t = g_TimeCtrl:GetTimeS()	
		if self.m_CostSaveStartTime ~= 0 and self.m_CostSaveEndTime ~= 0 and t > self.m_CostSaveStartTime and t < self.m_CostSaveEndTime then
			b = true
		end
		return b
	end
	function CPartnerHireView.IsCanYFRH(self, iParID)
		local d = data.partnerhiredata.DATA
		if table.index(d[iParID]["recommand_list"], "一发入魂") then
			return self:IsCanSSRDraw()
		end
	end
	function CPartnerEquipPage.ChangePart(self)
		local oItem = g_ItemCtrl:GetItem(self.m_CurItemID)
		if oItem and oItem:GetValue("level") == define.Partner.ParEquip.MaxLevel and oItem:GetValue("star") < define.Partner.ParEquip.MaxStar then
			if self.m_CurPart ~= self.m_UpStarPart and self.m_CurPart ~= self.m_UpStonePart then
				self.m_UpStarBtn:SetSelected(true)
				self:SwitchPart()
			end
		end
	end
	data.netdata.BAN["proto"]["herobox"]["item"]["GS2CItemQuickUse"] = true
end

[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 1001
}
[core/table.lua:94]:-->Net Receive: login.GS2CCheckInviteCodeResult = {
|  msg = "邀请码验证已关闭"
|  result = 1
}
[core/table.lua:94]:-->Net Receive: login.GS2CLoginAccount = {
|  account = "3805916"
|  role_list = {
|  |  [1] = {
|  |  |  grade = 66
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 130
|  |  |  |  weapon = 2001
|  |  |  }
|  |  |  pid = 40009
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginRole = {
|  account = "3805916"
|  pid = 40009
}
[core/global.lua:59]:<color=#ffeb04>读取系统设置本地数据，帐号为  3805916</color>
[core/global.lua:59]:<color=#ffeb04>3805916 的系统设置本地数据为 nil，使用默认数据</color>
[core/table.lua:94]:-->Net Receive: login.GS2CLoginRole = {
|  account = "3805916"
|  pid = 40009
|  role = {
|  |  abnormal_attr_ratio = 1527
|  |  active = 1104
|  |  arenamedal = 10
|  |  attack = 2204
|  |  bcmd = {
|  |  |  [1] = {
|  |  |  |  idx = 208
|  |  |  }
|  |  |  [10] = {
|  |  |  |  idx = 105
|  |  |  }
|  |  |  [11] = {
|  |  |  |  cmd = "封印"
|  |  |  |  idx = 202
|  |  |  }
|  |  |  [12] = {
|  |  |  |  idx = 107
|  |  |  }
|  |  |  [13] = {
|  |  |  |  cmd = "禁疗"
|  |  |  |  idx = 204
|  |  |  }
|  |  |  [14] = {
|  |  |  |  idx = 205
|  |  |  }
|  |  |  [15] = {
|  |  |  |  idx = 206
|  |  |  }
|  |  |  [16] = {
|  |  |  |  idx = 207
|  |  |  }
|  |  |  [2] = {
|  |  |  |  cmd = "集火"
|  |  |  |  idx = 201
|  |  |  }
|  |  |  [3] = {
|  |  |  |  idx = 106
|  |  |  }
|  |  |  [4] = {
|  |  |  |  cmd = "减速"
|  |  |  |  idx = 203
|  |  |  }
|  |  |  [5] = {
|  |  |  |  idx = 108
|  |  |  }
|  |  |  [6] = {
|  |  |  |  cmd = "治疗"
|  |  |  |  idx = 101
|  |  |  }
|  |  |  [7] = {
|  |  |  |  cmd = "复活"
|  |  |  |  idx = 102
|  |  |  }
|  |  |  [8] = {
|  |  |  |  cmd = "加速"
|  |  |  |  idx = 103
|  |  |  }
|  |  |  [9] = {
|  |  |  |  cmd = "解封"
|  |  |  |  idx = 104
|  |  |  }
|  |  }
|  |  coin = 11544272
|  |  critical_damage = 19531
|  |  critical_ratio = 3639
|  |  cure_critical_ratio = 500
|  |  defense = 612
|  |  energy = 170
|  |  exp = 2334420
|  |  goldcoin = 44500
|  |  grade = 66
|  |  kp_sdk_info = {
|  |  |  create_time = 1723313571
|  |  |  upgrade_time = 1724158754
|  |  }
|  |  mask = "7e6683d1ffffffe"
|  |  max_hp = 20567
|  |  medal = 5686
|  |  model_info = {
|  |  |  color = {
|  |  |  |  [1] = 0
|  |  |  }
|  |  |  shape = 130
|  |  |  weapon = 2001
|  |  }
|  |  name = "废柴v寿司"
|  |  open_day = 11
|  |  org_fuben_cnt = 2
|  |  org_offer = 36852
|  |  power = 11704
|  |  res_abnormal_ratio = 1845
|  |  res_critical_ratio = 1517
|  |  school = 2
|  |  school_branch = 1
|  |  sex = 1
|  |  show_id = 40009
|  |  skill_point = 16
|  |  skin = 67
|  |  speed = 1045
|  |  systemsetting = {
|  |  |  huntsetting = {
|  |  |  |  auto_sale = 1
|  |  |  }
|  |  |  teamsetting = {
|  |  |  |  barrage = 2
|  |  |  }
|  |  }
|  |  title_info = {
|  |  |  [1] = {
|  |  |  |  create_time = **********
|  |  |  |  left_time = **********
|  |  |  |  name = "1段"
|  |  |  |  tid = 1001
|  |  |  }
|  |  |  [2] = {
|  |  |  |  create_time = **********
|  |  |  |  left_time = **********
|  |  |  |  name = "谁与争锋"
|  |  |  |  tid = 1026
|  |  |  }
|  |  }
|  |  trapmine_point = 50
|  |  travel_score = 115
|  |  upvote_amount = 6
|  }
|  role_token = "**************"
|  xg_account = "bus40009"
}
[logic/ui/CViewCtrl.lua:94]:CMainMenuView ShowView
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 1527
|  active = 1104
|  arenamedal = 10
|  attack = 2204
|  bcmd = {
|  |  [1] = {
|  |  |  idx = 208
|  |  }
|  |  [10] = {
|  |  |  idx = 105
|  |  }
|  |  [11] = {
|  |  |  cmd = "封印"
|  |  |  idx = 202
|  |  }
|  |  [12] = {
|  |  |  idx = 107
|  |  }
|  |  [13] = {
|  |  |  cmd = "禁疗"
|  |  |  idx = 204
|  |  }
|  |  [14] = {
|  |  |  idx = 205
|  |  }
|  |  [15] = {
|  |  |  idx = 206
|  |  }
|  |  [16] = {
|  |  |  idx = 207
|  |  }
|  |  [2] = {
|  |  |  cmd = "集火"
|  |  |  idx = 201
|  |  }
|  |  [3] = {
|  |  |  idx = 106
|  |  }
|  |  [4] = {
|  |  |  cmd = "减速"
|  |  |  idx = 203
|  |  }
|  |  [5] = {
|  |  |  idx = 108
|  |  }
|  |  [6] = {
|  |  |  cmd = "治疗"
|  |  |  idx = 101
|  |  }
|  |  [7] = {
|  |  |  cmd = "复活"
|  |  |  idx = 102
|  |  }
|  |  [8] = {
|  |  |  cmd = "加速"
|  |  |  idx = 103
|  |  }
|  |  [9] = {
|  |  |  cmd = "解封"
|  |  |  idx = 104
|  |  }
|  }
|  camp = 0
|  chatself = false
|  chubeiexp = 0
|  coin = 11544272
|  coin_over = 0
|  color_coin = 0
|  critical_damage = 19531
|  critical_ratio = 3639
|  cure_critical_ratio = 500
|  defense = 612
|  energy = 170
|  exp = 2334420
|  followers = {}
|  goldcoin = 44500
|  grade = 66
|  hp = 0
|  kp_sdk_info = {
|  |  create_time = 1723313571
|  |  upgrade_time = 1724158754
|  }
|  max_hp = 20567
|  medal = 5686
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 130
|  |  weapon = 2001
|  }
|  name = "废柴v寿司"
|  open_day = 11
|  org_fuben_cnt = 2
|  org_offer = 36852
|  power = 11704
|  res_abnormal_ratio = 1845
|  res_critical_ratio = 1517
|  school = 2
|  school_branch = 1
|  sex = 1
|  show_id = 40009
|  skill_point = 16
|  skin = 67
|  speed = 1045
|  systemsetting = {
|  |  huntsetting = {
|  |  |  auto_sale = 1
|  |  }
|  |  teamsetting = {
|  |  |  barrage = 2
|  |  }
|  }
|  title_info = {
|  |  [1] = {
|  |  |  create_time = **********
|  |  |  left_time = **********
|  |  |  name = "1段"
|  |  |  tid = 1001
|  |  }
|  |  [2] = {
|  |  |  create_time = **********
|  |  |  left_time = **********
|  |  |  name = "谁与争锋"
|  |  |  tid = 1026
|  |  }
|  }
|  trapmine_point = 50
|  travel_score = 115
|  upvote_amount = 6
}
[logic/misc/CTimeCtrl.lua:64]:HeartBeat-->开启心跳包检测
[core/table.lua:94]:<--Net Send: title.C2GSTitleInfoList = {}
[core/table.lua:94]:-->Net Receive: player.GS2CGamePushSetting = {}
[core/table.lua:94]:-->Net Receive: player.GS2CShapeList = {
|  shapes = {
|  |  [1] = 130
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CGuidanceInfo = {
|  guidanceinfo = {
|  |  key = {
|  |  |  [1] = 7000041
|  |  |  [10] = 2002099
|  |  |  [11] = 7000081
|  |  |  [12] = 2006099
|  |  |  [13] = 1003099
|  |  |  [14] = 3006099
|  |  |  [15] = 3007099
|  |  |  [16] = 3008099
|  |  |  [17] = 3003099
|  |  |  [18] = 7000091
|  |  |  [19] = 3004099
|  |  |  [2] = 7000051
|  |  |  [20] = 3005099
|  |  |  [21] = 2004099
|  |  |  [22] = 1004099
|  |  |  [23] = 3009099
|  |  |  [24] = 1005099
|  |  |  [25] = 3012099
|  |  |  [26] = 3013099
|  |  |  [27] = 3014099
|  |  |  [28] = 3015099
|  |  |  [29] = 7000071
|  |  |  [3] = 6010002
|  |  |  [30] = 3010099
|  |  |  [31] = 3011099
|  |  |  [32] = 1006099
|  |  |  [33] = 3016099
|  |  |  [34] = 1010099
|  |  |  [35] = 1007099
|  |  |  [36] = 3019099
|  |  |  [37] = 1012099
|  |  |  [38] = 2005099
|  |  |  [39] = 1008099
|  |  |  [4] = 1027099
|  |  |  [40] = 3020099
|  |  |  [41] = 1009099
|  |  |  [42] = 1011099
|  |  |  [43] = 6010033
|  |  |  [44] = 3017099
|  |  |  [45] = 3018099
|  |  |  [46] = 5003099
|  |  |  [47] = 1015099
|  |  |  [48] = 3028099
|  |  |  [49] = 3026099
|  |  |  [5] = 1028099
|  |  |  [50] = 3027099
|  |  |  [51] = 1013099
|  |  |  [52] = 1014099
|  |  |  [53] = 5007099
|  |  |  [54] = 1016099
|  |  |  [55] = 3029099
|  |  |  [56] = 3030099
|  |  |  [57] = 7000111
|  |  |  [58] = 1017099
|  |  |  [59] = 5010099
|  |  |  [6] = 2001099
|  |  |  [60] = 5008099
|  |  |  [61] = 3023099
|  |  |  [62] = 3024099
|  |  |  [63] = 3025099
|  |  |  [64] = 1018099
|  |  |  [65] = 1019099
|  |  |  [66] = 1020099
|  |  |  [67] = 1021099
|  |  |  [68] = 1022099
|  |  |  [69] = 1023099
|  |  |  [7] = 7000061
|  |  |  [70] = 1024099
|  |  |  [71] = 5006099
|  |  |  [72] = 5011099
|  |  |  [73] = 5012099
|  |  |  [74] = 3032099
|  |  |  [75] = 1026099
|  |  |  [76] = 1025099
|  |  |  [77] = 2003099
|  |  |  [78] = 5009099
|  |  |  [79] = 5004099
|  |  |  [8] = 1001099
|  |  |  [80] = 3021099
|  |  |  [9] = 3002099
|  |  }
|  |  version = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>>>>>>>>>>>>>>>>>>>>> GS2CGuidanceInfo </color>
[core/table.lua:94]:-->Net Receive: travel.GS2CLoginTravelPartner = {
|  mine_invite = {
|  |  [1] = {
|  |  |  frd_pid = 40035
|  |  |  invite_time = 1723974575
|  |  }
|  }
|  pos_info = {
|  |  [1] = {
|  |  |  par_grade = 43
|  |  |  par_model = {
|  |  |  |  shape = 502
|  |  |  |  skin = 205020
|  |  |  }
|  |  |  par_name = "马面面"
|  |  |  par_star = 1
|  |  |  parid = 3
|  |  |  pos = 1
|  |  }
|  |  [2] = {
|  |  |  par_grade = 56
|  |  |  par_model = {
|  |  |  |  shape = 405
|  |  |  |  skin = 204050
|  |  |  }
|  |  |  par_name = "莹月"
|  |  |  par_star = 5
|  |  |  parid = 10
|  |  |  pos = 2
|  |  }
|  |  [3] = {
|  |  |  par_awake = 1
|  |  |  par_grade = 35
|  |  |  par_model = {
|  |  |  |  shape = 314
|  |  |  |  skin = 203140
|  |  |  }
|  |  |  par_name = "执阳"
|  |  |  par_star = 2
|  |  |  parid = 17
|  |  |  pos = 3
|  |  }
|  |  [4] = {
|  |  |  par_grade = 28
|  |  |  par_model = {
|  |  |  |  shape = 511
|  |  |  |  skin = 205110
|  |  |  }
|  |  |  par_name = "片雾"
|  |  |  par_star = 3
|  |  |  parid = 11
|  |  |  pos = 4
|  |  }
|  }
|  travel_content = {
|  |  [1] = {
|  |  |  content = "[11977c]马面面[6f4f2c]经过一番撒娇打滚求抱抱举高高的卖萌之后，成功将大章鱼卖出并获得了[883737]经验*2000 [6f4f2c]。"
|  |  |  travel_time = 1724135290
|  |  }
|  |  [10] = {
|  |  |  content = "[6f4f2c]倏忽听到青翼轻笑一声，抬头只见青翼用手轻拍着绯翼的腰身，[11977c]莹月[6f4f2c]眼底闪过一抹诧异，收取[883737]经验*2000 [6f4f2c]的手顿了顿。"
|  |  |  travel_time = 1724142490
|  |  }
|  |  [11] = {
|  |  |  content = "[6f4f2c]倏忽听到青翼轻笑一声，抬头只见青翼用手轻拍着绯翼的腰身，[11977c]执阳[6f4f2c]眼底闪过一抹诧异，收取[883737]经验*2000 [6f4f2c]的手顿了顿。"
|  |  |  travel_time = 1724142490
|  |  }
|  |  [12] = {
|  |  |  content = "[6f4f2c]倏忽听到青翼轻笑一声，抬头只见青翼用手轻拍着绯翼的腰身，[11977c]片雾[6f4f2c]眼底闪过一抹诧异，收取[883737]经验*2000 [6f4f2c]的手顿了顿。"
|  |  |  travel_time = 1724142490
|  |  }
|  |  [13] = {
|  |  |  content = "[6f4f2c]喵小玉惊讶地抬起猫头，看了那堆鱼好一会，忽然扑上去大哭。[11977c]马面面[6f4f2c]带着“猫会哭”的疑问和获得的[883737]破坏符石·Ⅱ*1 [6f4f2c]恍恍惚惚的离开了。"
|  |  |  travel_time = 1724146090
|  |  }
|  |  [14] = {
|  |  |  content = "[6f4f2c]喵小玉惊讶地抬起猫头，看了那堆鱼好一会，忽然扑上去大哭。[11977c]莹月[6f4f2c]带着“猫会哭”的疑问和获得的[883737]鲜肉包*2 [6f4f2c]恍恍惚惚的离开了。"
|  |  |  travel_time = 1724146090
|  |  }
|  |  [15] = {
|  |  |  content = "[6f4f2c]喵小玉惊讶地抬起猫头，看了那堆鱼好一会，忽然扑上去大哭。[11977c]执阳[6f4f2c]带着“猫会哭”的疑问和获得的[883737]破坏符石·Ⅰ*1 [6f4f2c]恍恍惚惚的离开了。"
|  |  |  travel_time = 1724146090
|  |  }
|  |  [16] = {
|  |  |  content = "[6f4f2c]喵小玉惊讶地抬起猫头，看了那堆鱼好一会，忽然扑上去大哭。[11977c]片雾[6f4f2c]带着“猫会哭”的疑问和获得的[883737]鲜肉包*1 [6f4f2c]恍恍惚惚的离开了。"
|  |  |  travel_time = 1724146090
|  |  }
|  |  [17] = {
|  |  |  content = "[6f4f2c]月黑风高捡漏夜，[11977c]马面面[6f4f2c]意外捡到了一本黑色笔记本，将其售卖后获得了[883737]经验*2000 [6f4f2c]。"
|  |  |  travel_time = 1724149690
|  |  }
|  |  [18] = {
|  |  |  content = "[6f4f2c]月黑风高捡漏夜，[11977c]莹月[6f4f2c]意外捡到了一本黑色笔记本，将其售卖后获得了[883737]经验*2000 [6f4f2c]。"
|  |  |  travel_time = 1724149690
|  |  }
|  |  [19] = {
|  |  |  content = "[6f4f2c]月黑风高捡漏夜，[11977c]执阳[6f4f2c]意外捡到了一本黑色笔记本，将其售卖后获得了[883737]经验*2000 [6f4f2c]。"
|  |  |  travel_time = 1724149690
|  |  }
|  |  [2] = {
|  |  |  content = "[11977c]莹月[6f4f2c]经过一番撒娇打滚求抱抱举高高的卖萌之后，成功将大章鱼卖出并获得了[883737]经验*2000 [6f4f2c]。"
|  |  |  travel_time = 1724135290
|  |  }
|  |  [20] = {
|  |  |  content = "[6f4f2c]月黑风高捡漏夜，[11977c]片雾[6f4f2c]意外捡到了一本黑色笔记本，将其售卖后获得了[883737]经验*2000 [6f4f2c]。"
|  |  |  travel_time = 1724149690
|  |  }
|  |  [21] = {
|  |  |  content = "[6f4f2c]给了航海士一脑门子巴掌后成功将对方从幻境中救出，[11977c]马面面[6f4f2c]获得了[883737]人灵决·低*1 [6f4f2c]。"
|  |  |  travel_time = 1724153290
|  |  }
|  |  [22] = {
|  |  |  content = "[6f4f2c]给了航海士一脑门子巴掌后成功将对方从幻境中救出，[11977c]莹月[6f4f2c]获得了[883737]鲜肉包*2 [6f4f2c]。"
|  |  |  travel_time = 1724153290
|  |  }
|  |  [23] = {
|  |  |  content = "[6f4f2c]给了航海士一脑门子巴掌后成功将对方从幻境中救出，[11977c]执阳[6f4f2c]获得了[883737]水晶*40 [6f4f2c]。"
|  |  |  travel_time = 1724153290
|  |  }
|  |  [24] = {
|  |  |  content = "[6f4f2c]给了航海士一脑门子巴掌后成功将对方从幻境中救出，[11977c]片雾[6f4f2c]获得了[883737]光辉符石·Ⅱ*1 [6f4f2c]。"
|  |  |  travel_time = 1724153290
|  |  }
|  |  [3] = {
|  |  |  content = "[11977c]执阳[6f4f2c]经过一番撒娇打滚求抱抱举高高的卖萌之后，成功将大章鱼卖出并获得了[883737]经验*2000 [6f4f2c]。"
|  |  |  travel_time = 1724135290
|  |  }
|  |  [4] = {
|  |  |  content = "[11977c]片雾[6f4f2c]经过一番撒娇打滚求抱抱举高高的卖萌之后，成功将大章鱼卖出并获得了[883737]经验*2000 [6f4f2c]。"
|  |  |  travel_time = 1724135290
|  |  }
|  |  [5] = {
|  |  |  content = "[6f4f2c]小狸猫夹着尾巴，沮丧着从木桶里爬了出来。[11977c]马面面[6f4f2c]急忙将这只不怕死的小狸猫送回岸上，获得了绿狸的感谢和[883737]圣灵符石·Ⅰ*1 [6f4f2c]。"
|  |  |  travel_time = 1724138890
|  |  }
|  |  [6] = {
|  |  |  content = "[6f4f2c]小狸猫夹着尾巴，沮丧着从木桶里爬了出来。[11977c]莹月[6f4f2c]急忙将这只不怕死的小狸猫送回岸上，获得了绿狸的感谢和[883737]禁忌符石·Ⅰ*1 [6f4f2c]。"
|  |  |  travel_time = 1724138890
|  |  }
|  |  [7] = {
|  |  |  content = "[6f4f2c]小狸猫夹着尾巴，沮丧着从木桶里爬了出来。[11977c]执阳[6f4f2c]急忙将这只不怕死的小狸猫送回岸上，获得了绿狸的感谢和[883737]水晶*20 [6f4f2c]。"
|  |  |  travel_time = 1724138890
|  |  }
|  |  [8] = {
|  |  |  content = "[6f4f2c]小狸猫夹着尾巴，沮丧着从木桶里爬了出来。[11977c]片雾[6f4f2c]急忙将这只不怕死的小狸猫送回岸上，获得了绿狸的感谢和[883737]鲜肉包*2 [6f4f2c]。"
|  |  |  travel_time = 1724138890
|  |  }
|  |  [9] = {
|  |  |  content = "[6f4f2c]倏忽听到青翼轻笑一声，抬头只见青翼用手轻拍着绯翼的腰身，[11977c]马面面[6f4f2c]眼底闪过一抹诧异，收取[883737]经验*2000 [6f4f2c]的手顿了顿。"
|  |  |  travel_time = 1724142490
|  |  }
|  }
|  travel_partner = {
|  |  reward = 1
|  |  server_time = **********
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CServerGradeInfo = {
|  days = 1
|  server_grade = 65
}
[core/table.lua:94]:-->Net Receive: scene.GS2CShowScene = {
|  map_id = 200000
|  scene_id = 14
|  scene_name = "矿石之城"
}
[core/global.lua:59]:<color=#ffeb04>CResCtrl.MoveToSecondary</color>
[core/table.lua:94]:-->Net Receive: task.GS2CLoginTask = {
|  taskdata = {
|  |  [1] = {
|  |  |  acceptnpc = 5012
|  |  |  accepttime = 1723977453
|  |  |  clientnpc = {
|  |  |  |  [1] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 302
|  |  |  |  |  }
|  |  |  |  |  name = "重华"
|  |  |  |  |  npcid = 908
|  |  |  |  |  npctype = 80004
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 22000
|  |  |  |  |  |  y = 3000
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  detaildesc = "击败重华获得战斗胜利！"
|  |  |  name = "技能点任务"
|  |  |  patrolinfo = {}
|  |  |  pickiteminfo = {}
|  |  |  placeinfo = {}
|  |  |  shapeinfo = {}
|  |  |  statusinfo = {
|  |  |  |  note = "Accept Task"
|  |  |  |  status = 1
|  |  |  }
|  |  |  submitnpc = 80004
|  |  |  target = 80004
|  |  |  targetdesc = "挑战重华"
|  |  |  taskid = 1004
|  |  |  taskitem = {}
|  |  |  tasktype = 4
|  |  |  traceinfo = {}
|  |  |  type = 8
|  |  }
|  |  [2] = {
|  |  |  acceptnpc = 85021
|  |  |  clientnpc = {
|  |  |  |  [1] = {
|  |  |  |  |  map_id = 101000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 800
|  |  |  |  |  }
|  |  |  |  |  name = "黑"
|  |  |  |  |  npcid = 907
|  |  |  |  |  npctype = 85021
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 5200
|  |  |  |  |  |  y = 23900
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  detaildesc = "[159a80]帝都统帅部前的黑[654a33]要求把今年武道大会的通告交给[159a80]矿石之城的峰[654a33]。
[c54420]◇请根据上述提示找到关键建筑旁的人物完成委托！"
|  |  |  name = "小萌的委托"
|  |  |  needitem = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 1
|  |  |  |  |  itemid = 11603
|  |  |  |  }
|  |  |  }
|  |  |  patrolinfo = {}
|  |  |  pickiteminfo = {}
|  |  |  placeinfo = {}
|  |  |  shapeinfo = {}
|  |  |  statusinfo = {
|  |  |  |  note = "CreateTask"
|  |  |  |  status = 2
|  |  |  }
|  |  |  submitnpc = 5016
|  |  |  target = 85021
|  |  |  targetdesc = "女王的吩咐"
|  |  |  taskid = 2008
|  |  |  taskitem = {}
|  |  |  tasktype = 1
|  |  |  traceinfo = {}
|  |  |  type = 9
|  |  }
|  |  [3] = {
|  |  |  acceptnpc = 120077
|  |  |  accepttime = 1723834934
|  |  |  clientnpc = {
|  |  |  |  [1] = {
|  |  |  |  |  map_id = 206000
|  |  |  |  |  model_info = {
|  |  |  |  |  |  scale = 1
|  |  |  |  |  |  shape = 514
|  |  |  |  |  }
|  |  |  |  |  name = "曼珠沙华"
|  |  |  |  |  npcid = 909
|  |  |  |  |  npctype = 120077
|  |  |  |  |  pos_info = {
|  |  |  |  |  |  x = 15940
|  |  |  |  |  |  y = 12400
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  detaildesc = "似乎忘记了什么……询问曼珠沙华冥界的出口在哪。"
|  |  |  name = "沙华的委托"
|  |  |  partnertaskinfo = {
|  |  |  |  name = "曼珠沙华"
|  |  |  |  parid = 514
|  |  |  }
|  |  |  patrolinfo = {}
|  |  |  pickiteminfo = {}
|  |  |  placeinfo = {
|  |  |  |  mapid = 206000
|  |  |  |  pos_x = 8
|  |  |  |  pos_y = 6
|  |  |  }
|  |  |  shapeinfo = {}
|  |  |  statusinfo = {
|  |  |  |  note = "提交任务"
|  |  |  |  status = 5
|  |  |  }
|  |  |  submitnpc = 120077
|  |  |  target = 120077
|  |  |  targetdesc = "寻找出口"
|  |  |  taskid = 62075
|  |  |  taskitem = {}
|  |  |  tasktype = 3
|  |  |  traceinfo = {}
|  |  |  type = 12
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>login task</color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  ChapterFb = ""
|  |  clientExtStr = ""
|  |  name = "技能点任务"
|  |  submitNpcId = 80004
|  |  submitRewardStr = {
|  |  |  [1] = "R1071"
|  |  }
|  |  tips = 0
|  |  type = 8
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5012
|  |  accepttime = 1723977453
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 302
|  |  |  |  }
|  |  |  |  name = "重华"
|  |  |  |  npcid = 908
|  |  |  |  npctype = 80004
|  |  |  |  pos_info = {
|  |  |  |  |  x = 22000
|  |  |  |  |  y = 3000
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "击败重华获得战斗胜利！"
|  |  isdone = 0
|  |  name = "技能点任务"
|  |  patrolinfo = {}
|  |  pickiteminfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "Accept Task"
|  |  |  status = 1
|  |  }
|  |  submitnpc = 80004
|  |  target = 80004
|  |  targetdesc = "挑战重华"
|  |  taskid = 1004
|  |  taskitem = {}
|  |  tasktype = 4
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 8
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 1
|  |  icon = "pic_jineng_tubiao"
|  |  id = 8
|  |  menu_index = 4
|  |  menu_show_index = 4
|  |  menu_show_index_sort = 1
|  |  name = "考验"
|  }
}
[core/global.lua:59]:<color=#ffeb04>login task</color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  ChapterFb = ""
|  |  clientExtStr = ""
|  |  name = "小萌的委托"
|  |  submitNpcId = 5016
|  |  submitRewardStr = {
|  |  |  [1] = "R1001"
|  |  }
|  |  tips = 0
|  |  type = 9
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 85021
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 800
|  |  |  |  }
|  |  |  |  name = "黑"
|  |  |  |  npcid = 907
|  |  |  |  npctype = 85021
|  |  |  |  pos_info = {
|  |  |  |  |  x = 5200
|  |  |  |  |  y = 23900
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "[159a80]帝都统帅部前的黑[654a33]要求把今年武道大会的通告交给[159a80]矿石之城的峰[654a33]。
[c54420]◇请根据上述提示找到关键建筑旁的人物完成委托！"
|  |  isdone = 0
|  |  name = "小萌的委托"
|  |  needitem = {
|  |  |  [1] = {
|  |  |  |  amount = 1
|  |  |  |  itemid = 11603
|  |  |  }
|  |  }
|  |  patrolinfo = {}
|  |  pickiteminfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 5016
|  |  target = 85021
|  |  targetdesc = "女王的吩咐"
|  |  taskid = 2008
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 9
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 1
|  |  icon = "pic_sjweituo"
|  |  id = 9
|  |  menu_index = 5
|  |  menu_show_index = 5
|  |  menu_show_index_sort = 1
|  |  name = "日常"
|  }
}
[core/global.lua:59]:<color=#ffeb04>login task</color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  ChapterFb = ""
|  |  clientExtStr = ""
|  |  name = "沙华的委托"
|  |  submitNpcId = 120077
|  |  submitRewardStr = {}
|  |  tips = 0
|  |  type = 12
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 120077
|  |  accepttime = 1723834934
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 206000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 514
|  |  |  |  }
|  |  |  |  name = "曼珠沙华"
|  |  |  |  npcid = 909
|  |  |  |  npctype = 120077
|  |  |  |  pos_info = {
|  |  |  |  |  x = 15940
|  |  |  |  |  y = 12400
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "似乎忘记了什么……询问曼珠沙华冥界的出口在哪。"
|  |  isdone = 0
|  |  name = "沙华的委托"
|  |  partnertaskinfo = {
|  |  |  name = "曼珠沙华"
|  |  |  parid = 514
|  |  }
|  |  patrolinfo = {}
|  |  pickiteminfo = {}
|  |  placeinfo = {
|  |  |  mapid = 206000
|  |  |  pos_x = 8
|  |  |  pos_y = 6
|  |  }
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "提交任务"
|  |  |  status = 5
|  |  }
|  |  submitnpc = 120077
|  |  target = 120077
|  |  targetdesc = "寻找出口"
|  |  taskid = 62075
|  |  taskitem = {}
|  |  tasktype = 3
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 12
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "tujian"
|  |  id = 12
|  |  menu_index = 0
|  |  menu_show_index = 3
|  |  menu_show_index_sort = 2
|  |  name = "伙伴支线"
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CTeachProgress = {}
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshPartnerTask = {
|  partnertask_progress = {
|  |  [1] = {
|  |  |  parid = 417
|  |  |  status = 1
|  |  |  taskid = 62173
|  |  }
|  |  [10] = {
|  |  |  parid = 409
|  |  |  status = 3
|  |  }
|  |  [11] = {
|  |  |  parid = 504
|  |  |  status = 1
|  |  |  taskid = 62060
|  |  }
|  |  [12] = {
|  |  |  parid = 401
|  |  |  status = 1
|  |  |  taskid = 62148
|  |  }
|  |  [13] = {
|  |  |  parid = 306
|  |  |  status = 3
|  |  }
|  |  [14] = {
|  |  |  parid = 403
|  |  |  status = 1
|  |  |  taskid = 62169
|  |  }
|  |  [15] = {
|  |  |  parid = 404
|  |  |  status = 1
|  |  |  taskid = 62153
|  |  }
|  |  [16] = {
|  |  |  parid = 405
|  |  |  status = 3
|  |  }
|  |  [17] = {
|  |  |  parid = 502
|  |  |  status = 1
|  |  |  taskid = 62090
|  |  }
|  |  [18] = {
|  |  |  parid = 503
|  |  |  status = 1
|  |  |  taskid = 62115
|  |  }
|  |  [19] = {
|  |  |  parid = 312
|  |  |  status = 1
|  |  |  taskid = 62131
|  |  }
|  |  [2] = {
|  |  |  parid = 514
|  |  |  status = 2
|  |  |  taskid = 62075
|  |  }
|  |  [20] = {
|  |  |  parid = 313
|  |  |  status = 1
|  |  |  taskid = 62195
|  |  }
|  |  [21] = {
|  |  |  parid = 506
|  |  |  status = 1
|  |  |  taskid = 62095
|  |  }
|  |  [22] = {
|  |  |  parid = 501
|  |  |  status = 3
|  |  }
|  |  [23] = {
|  |  |  parid = 412
|  |  |  status = 3
|  |  }
|  |  [24] = {
|  |  |  parid = 509
|  |  |  status = 1
|  |  |  taskid = 62070
|  |  }
|  |  [25] = {
|  |  |  parid = 510
|  |  |  status = 3
|  |  }
|  |  [26] = {
|  |  |  parid = 511
|  |  |  status = 3
|  |  }
|  |  [3] = {
|  |  |  parid = 415
|  |  |  status = 1
|  |  |  taskid = 62180
|  |  }
|  |  [4] = {
|  |  |  parid = 410
|  |  |  status = 1
|  |  |  taskid = 62159
|  |  }
|  |  [5] = {
|  |  |  parid = 413
|  |  |  status = 1
|  |  |  taskid = 62167
|  |  }
|  |  [6] = {
|  |  |  parid = 505
|  |  |  status = 1
|  |  |  taskid = 62065
|  |  }
|  |  [7] = {
|  |  |  parid = 407
|  |  |  status = 3
|  |  }
|  |  [8] = {
|  |  |  parid = 314
|  |  |  status = 1
|  |  |  taskid = 62135
|  |  }
|  |  [9] = {
|  |  |  parid = 302
|  |  |  status = 1
|  |  |  taskid = 62080
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  activepoint = 35
|  day_task = {
|  |  [1] = 1020
|  |  [10] = 3008
|  |  [11] = 3009
|  |  [12] = 1004
|  |  [13] = 1005
|  |  [14] = 1006
|  |  [15] = 1007
|  |  [16] = 1018
|  |  [17] = 1011
|  |  [18] = 1012
|  |  [19] = 2001
|  |  [2] = 1022
|  |  [20] = 2003
|  |  [3] = 1024
|  |  [4] = 1002
|  |  [5] = 1003
|  |  [6] = 1016
|  |  [7] = 3005
|  |  [8] = 1017
|  |  [9] = 1001
|  }
}
[core/table.lua:94]:-->Net Receive: state.GS2CLoginState = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginHuodongInfo = {
|  convoyinfo = {
|  |  convoy_partner = 1016
|  |  end_time = 1724190416
|  |  pool_info = {
|  |  |  [1] = {
|  |  |  |  level = 1
|  |  |  |  partnerid = 1504
|  |  |  |  pos = 1
|  |  |  |  rewardid = 1001
|  |  |  }
|  |  |  [2] = {
|  |  |  |  level = 2
|  |  |  |  partnerid = 1016
|  |  |  |  pos = 2
|  |  |  |  rewardid = 1002
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [3] = {
|  |  |  |  level = 3
|  |  |  |  partnerid = 1009
|  |  |  |  pos = 3
|  |  |  |  rewardid = 1003
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [4] = {
|  |  |  |  level = 4
|  |  |  |  partnerid = 1011
|  |  |  |  pos = 4
|  |  |  |  rewardid = 1004
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [5] = {
|  |  |  |  level = 5
|  |  |  |  partnerid = 1020
|  |  |  |  pos = 5
|  |  |  |  rewardid = 1005
|  |  |  |  weight = 25
|  |  |  }
|  |  }
|  |  refresh_cost = 5
|  |  refresh_time = 4
|  |  selected_pos = 2
|  |  status = 1
|  |  target_npc = 5016
|  }
|  dailytrain = {
|  |  reward_times = 60
|  }
|  hireinfo = {
|  |  [1] = {
|  |  |  parid = 514
|  |  |  times = 1
|  |  }
|  |  [2] = {
|  |  |  parid = 403
|  |  |  times = 1
|  |  }
|  |  [3] = {
|  |  |  parid = 404
|  |  |  times = 1
|  |  }
|  |  [4] = {
|  |  |  parid = 501
|  |  |  times = 1
|  |  }
|  |  [5] = {
|  |  |  parid = 502
|  |  |  times = 1
|  |  }
|  |  [6] = {
|  |  |  parid = 312
|  |  |  times = 1
|  |  }
|  |  [7] = {
|  |  |  parid = 409
|  |  |  times = 1
|  |  }
|  |  [8] = {
|  |  |  parid = 412
|  |  |  times = 1
|  |  }
|  |  [9] = {
|  |  |  parid = 511
|  |  |  times = 1
|  |  }
|  }
|  huntinfo = {
|  |  freeinfo = {
|  |  |  [1] = {
|  |  |  |  last_freetime = 1724084325
|  |  |  |  level = 4
|  |  |  }
|  |  }
|  |  npcinfo = {
|  |  |  [1] = {
|  |  |  |  level = 1
|  |  |  |  status = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  level = 2
|  |  |  }
|  |  |  [3] = {
|  |  |  |  level = 3
|  |  |  |  status = 1
|  |  |  }
|  |  |  [4] = {
|  |  |  |  level = 4
|  |  |  }
|  |  |  [5] = {
|  |  |  |  level = 5
|  |  |  }
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:94]:CConvoyingView ShowView
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginChapterInfo = {
|  extrareward_info = {
|  |  [1] = {
|  |  |  chapter = 11
|  |  |  level = 2
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [10] = {
|  |  |  chapter = 5
|  |  |  level = 4
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [11] = {
|  |  |  chapter = 4
|  |  |  level = 4
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [12] = {
|  |  |  chapter = 3
|  |  |  level = 4
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [13] = {
|  |  |  chapter = 9
|  |  |  level = 8
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [14] = {
|  |  |  chapter = 2
|  |  |  level = 6
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [15] = {
|  |  |  chapter = 8
|  |  |  level = 8
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [16] = {
|  |  |  chapter = 5
|  |  |  level = 8
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [17] = {
|  |  |  chapter = 6
|  |  |  level = 8
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [18] = {
|  |  |  chapter = 3
|  |  |  level = 8
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [19] = {
|  |  |  chapter = 4
|  |  |  level = 8
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  chapter = 10
|  |  |  level = 4
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [20] = {
|  |  |  chapter = 8
|  |  |  level = 6
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [21] = {
|  |  |  chapter = 7
|  |  |  level = 6
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [22] = {
|  |  |  chapter = 9
|  |  |  level = 2
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [23] = {
|  |  |  chapter = 9
|  |  |  level = 6
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [24] = {
|  |  |  chapter = 4
|  |  |  level = 6
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [25] = {
|  |  |  chapter = 8
|  |  |  level = 2
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [26] = {
|  |  |  chapter = 10
|  |  |  level = 8
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [27] = {
|  |  |  chapter = 5
|  |  |  level = 6
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [28] = {
|  |  |  chapter = 7
|  |  |  level = 8
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [29] = {
|  |  |  chapter = 10
|  |  |  level = 6
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  chapter = 3
|  |  |  level = 2
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [30] = {
|  |  |  chapter = 2
|  |  |  level = 4
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [31] = {
|  |  |  chapter = 6
|  |  |  level = 6
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [32] = {
|  |  |  chapter = 10
|  |  |  level = 2
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [33] = {
|  |  |  chapter = 1
|  |  |  level = 8
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [34] = {
|  |  |  chapter = 2
|  |  |  level = 8
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [35] = {
|  |  |  chapter = 4
|  |  |  level = 2
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [36] = {
|  |  |  chapter = 1
|  |  |  level = 6
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [37] = {
|  |  |  chapter = 7
|  |  |  level = 2
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [38] = {
|  |  |  chapter = 3
|  |  |  level = 6
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [39] = {
|  |  |  chapter = 5
|  |  |  level = 2
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  chapter = 1
|  |  |  level = 4
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [40] = {
|  |  |  chapter = 6
|  |  |  level = 2
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  chapter = 2
|  |  |  level = 2
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  chapter = 9
|  |  |  level = 4
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [7] = {
|  |  |  chapter = 8
|  |  |  level = 4
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [8] = {
|  |  |  chapter = 7
|  |  |  level = 4
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  |  [9] = {
|  |  |  chapter = 6
|  |  |  level = 4
|  |  |  reward_status = 1
|  |  |  type = 1
|  |  }
|  }
|  finalchapter = {
|  |  [1] = {
|  |  |  chapter = 11
|  |  |  level = 4
|  |  |  open = 1
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  chapter = 6
|  |  |  level = 3
|  |  |  open = 1
|  |  |  type = 2
|  |  }
|  }
|  totalstar_info = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  reward_status = 3
|  |  |  star = 24
|  |  |  type = 1
|  |  }
|  |  [10] = {
|  |  |  chapter = 10
|  |  |  reward_status = 7
|  |  |  star = 24
|  |  |  type = 1
|  |  }
|  |  [11] = {
|  |  |  chapter = 11
|  |  |  reward_status = 1
|  |  |  star = 9
|  |  |  type = 1
|  |  }
|  |  [12] = {
|  |  |  chapter = 1
|  |  |  reward_status = 3
|  |  |  star = 9
|  |  |  type = 2
|  |  }
|  |  [13] = {
|  |  |  chapter = 2
|  |  |  reward_status = 3
|  |  |  star = 9
|  |  |  type = 2
|  |  }
|  |  [14] = {
|  |  |  chapter = 3
|  |  |  reward_status = 3
|  |  |  star = 9
|  |  |  type = 2
|  |  }
|  |  [15] = {
|  |  |  chapter = 4
|  |  |  reward_status = 3
|  |  |  star = 9
|  |  |  type = 2
|  |  }
|  |  [16] = {
|  |  |  chapter = 5
|  |  |  reward_status = 3
|  |  |  star = 9
|  |  |  type = 2
|  |  }
|  |  [17] = {
|  |  |  chapter = 6
|  |  |  reward_status = 1
|  |  |  star = 6
|  |  |  type = 2
|  |  }
|  |  [2] = {
|  |  |  chapter = 2
|  |  |  reward_status = 7
|  |  |  star = 24
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  chapter = 3
|  |  |  reward_status = 7
|  |  |  star = 24
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  chapter = 4
|  |  |  reward_status = 7
|  |  |  star = 24
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  chapter = 5
|  |  |  reward_status = 7
|  |  |  star = 24
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  chapter = 6
|  |  |  reward_status = 7
|  |  |  star = 24
|  |  |  type = 1
|  |  }
|  |  [7] = {
|  |  |  chapter = 7
|  |  |  reward_status = 7
|  |  |  star = 24
|  |  |  type = 1
|  |  }
|  |  [8] = {
|  |  |  chapter = 8
|  |  |  reward_status = 7
|  |  |  star = 24
|  |  |  type = 1
|  |  }
|  |  [9] = {
|  |  |  chapter = 9
|  |  |  reward_status = 7
|  |  |  star = 24
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterScene = {
|  eid = 16
|  pos_info = {
|  |  face_y = 75223
|  |  x = 28775
|  |  y = 16245
|  }
|  scene_id = 14
}
[core/global.lua:59]:<color=#ffeb04>result  nil</color>
[core/global.lua:59]:<color=#ffeb04>=00000=  userdata: 0x498bcd80 nil</color>
[logic/ui/CSprite.lua:159]:DynamicSprite dSprInfo    Item    10012
[logic/ui/CViewBase.lua:125]:CMainMenuView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/global.lua:59]:<color=#ffeb04>读取地图： 2000</color>
[core/global.lua:59]:<color=#ffeb04>Unity2017.4.30f1 localScale</color>
[logic/ui/CViewCtrl.lua:104]:CLoginView     CloseView
[logic/base/CResCtrl.lua:599]:res gc step start!
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[core/global.lua:59]:<color=#ffeb04>地图加载完成: 2000 ,当前地图: 6100</color>
[core/global.lua:59]:<color=#ffeb04>删除地图: 6100</color>
[core/table.lua:94]:Table = {}
[core/table.lua:94]:-->Net Receive: handbook.GS2CLoginBookList = {
|  book_list = {
|  |  [1] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 314171
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 114171
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100417
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [10] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 314051
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 114051
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100405
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [11] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 315031
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115031
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100503
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [12] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 313021
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = {
|  |  |  |  |  |  [1] = 113023
|  |  |  |  |  }
|  |  |  |  |  id = 313022
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 113021
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100302
|  |  |  red_point = 2
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [13] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 315061
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  id = 315062
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115061
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100506
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [14] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 314091
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 114091
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100409
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [15] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 314011
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 114011
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100401
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [16] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = {
|  |  |  |  |  |  [1] = 113062
|  |  |  |  |  }
|  |  |  |  |  id = 313061
|  |  |  |  |  read = 1
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = {
|  |  |  |  |  |  [1] = 113063
|  |  |  |  |  }
|  |  |  |  |  id = 313062
|  |  |  |  |  read = 1
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 113061
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100306
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [17] = {
|  |  |  condition = {
|  |  |  |  [1] = 400033
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 400019
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [18] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = {
|  |  |  |  |  |  [1] = 114042
|  |  |  |  |  }
|  |  |  |  |  id = 314041
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 114041
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100404
|  |  |  red_point = 2
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [19] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = {
|  |  |  |  |  |  [1] = 115012
|  |  |  |  |  }
|  |  |  |  |  id = 315011
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  id = 315012
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115011
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100501
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [2] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 315141
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115141
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100514
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [20] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = {
|  |  |  |  |  |  [1] = 115022
|  |  |  |  |  }
|  |  |  |  |  id = 315021
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115021
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100502
|  |  |  red_point = 2
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [21] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 314071
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 114071
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100407
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [22] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 313121
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  id = 313122
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 113121
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100312
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [23] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 313131
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 113131
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100313
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [24] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 313141
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = {
|  |  |  |  |  |  [1] = 113143
|  |  |  |  |  }
|  |  |  |  |  id = 313142
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  id = 313143
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 113141
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100314
|  |  |  red_point = 2
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [25] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 314101
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 114101
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100410
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [26] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 314121
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 114121
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100412
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [27] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 315091
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115091
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100509
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [28] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = {
|  |  |  |  |  |  [1] = 115102
|  |  |  |  |  }
|  |  |  |  |  id = 315101
|  |  |  |  |  read = 1
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115101
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100510
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [29] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 315111
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115111
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100511
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [3] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 315041
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = {
|  |  |  |  |  |  [1] = 115043
|  |  |  |  |  }
|  |  |  |  |  id = 315042
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115041
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100504
|  |  |  red_point = 3
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [4] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 314031
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 114031
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100403
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [5] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = {
|  |  |  |  |  |  [1] = 114132
|  |  |  |  |  }
|  |  |  |  |  id = 314131
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 114131
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100413
|  |  |  red_point = 2
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [6] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 315051
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115051
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100505
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [7] = {
|  |  |  condition = {
|  |  |  |  [1] = 400017
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 400009
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [8] = {
|  |  |  condition = {
|  |  |  |  [1] = 400035
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 400020
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [9] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  condition = {
|  |  |  |  |  |  [1] = 114152
|  |  |  |  |  }
|  |  |  |  |  id = 314151
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 114151
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100415
|  |  |  red_point = 2
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  }
|  red_points = {
|  |  [1] = {
|  |  |  book_type = 1
|  |  |  red_point = 1
|  |  }
|  |  [2] = {
|  |  |  book_type = 2
|  |  }
|  |  [3] = {
|  |  |  book_type = 3
|  |  |  red_point = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: team.GS2CInviteInfo = {}
[core/table.lua:94]:-->Net Receive: mail.GS2CLoginMail = {}
[core/table.lua:94]:-->Net Receive: friend.GS2CLoginFriend = {
|  friend_onlinestatus_list = {
|  |  [1] = {
|  |  |  pid = 40035
|  |  }
|  |  [2] = {
|  |  |  pid = 40006
|  |  }
|  |  [3] = {
|  |  |  pid = 40034
|  |  }
|  |  [4] = {
|  |  |  pid = 40023
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>好友版本一致，无需更新</color>
[core/table.lua:94]:<--Net Send: friend.C2GSSimpleFriendList = {
|  pidlist = {
|  |  [1] = 40035
|  |  [2] = 40006
|  |  [3] = 40034
|  |  [4] = 40023
|  }
}
[core/table.lua:94]:-->Net Receive: friend.GS2CApplyList = {}
[core/table.lua:94]:-->Net Receive: friend.GS2CFriendSetting = {
|  setting = {
|  |  notify = 1
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CRankBack = {
|  endtime = 1724511539
|  starttime = 1723215600
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CDailySignInfo = {
|  sign_info = {
|  |  [1] = {
|  |  |  key = "week"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CDrawCardUI = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CGradeGiftInfo = {
|  buy_gift = {
|  |  items = {
|  |  |  [1] = {
|  |  |  |  amount = 6
|  |  |  |  sid = 13270
|  |  |  |  virtual = 13270
|  |  |  }
|  |  |  [2] = {
|  |  |  |  amount = 1280
|  |  |  |  sid = 1003
|  |  |  |  virtual = 1003
|  |  |  }
|  |  |  [3] = {
|  |  |  |  amount = 800000
|  |  |  |  sid = 1002
|  |  |  |  virtual = 1002
|  |  |  }
|  |  }
|  |  type = 1
|  }
|  discount = 25
|  endtime = 1724464672
|  free_gift = {
|  |  done = 1
|  |  items = {
|  |  |  [1] = {
|  |  |  |  amount = 1
|  |  |  |  sid = 13270
|  |  |  |  virtual = 13270
|  |  |  }
|  |  |  [2] = {
|  |  |  |  amount = 128888
|  |  |  |  sid = 1002
|  |  |  |  virtual = 1002
|  |  |  }
|  |  }
|  }
|  grade = 65
|  ios_payid = "com.kaopu.ylq.appstore.lb.128"
|  now_price = 128
|  old_price = 512
|  payid = "com.kaopu.ylq.lb.128"
|  status = 1
}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcFightInfoList = {
|  info_list = {
|  |  [1] = {
|  |  |  npc_type = 10024
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [10] = {
|  |  |  npc_type = 10007
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [11] = {
|  |  |  npc_type = 10008
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [12] = {
|  |  |  npc_type = 10009
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [13] = {
|  |  |  npc_type = 10011
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [14] = {
|  |  |  npc_type = 10012
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [15] = {
|  |  |  npc_type = 10013
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [16] = {
|  |  |  npc_type = 10000
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [17] = {
|  |  |  npc_type = 10023
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [18] = {
|  |  |  fight = 3
|  |  |  npc_type = 10014
|  |  |  rewards = {
|  |  |  |  [1] = 1024
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [19] = {
|  |  |  fight = 1
|  |  |  npc_type = 10015
|  |  |  rewards = {
|  |  |  |  [1] = 1012
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [2] = {
|  |  |  npc_type = 10002
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [20] = {
|  |  |  npc_type = 10016
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [21] = {
|  |  |  npc_type = 10017
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [22] = {
|  |  |  npc_type = 10018
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [23] = {
|  |  |  npc_type = 10019
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [24] = {
|  |  |  npc_type = 10020
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [25] = {
|  |  |  npc_type = 10021
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [26] = {
|  |  |  npc_type = 10022
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [3] = {
|  |  |  npc_type = 10010
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [4] = {
|  |  |  npc_type = 10001
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [5] = {
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [6] = {
|  |  |  npc_type = 10003
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [7] = {
|  |  |  npc_type = 10004
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [8] = {
|  |  |  npc_type = 10005
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [9] = {
|  |  |  fight = 8
|  |  |  npc_type = 10006
|  |  |  rewards = {
|  |  |  |  [1] = 1009
|  |  |  }
|  |  |  total = 10
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginTrapmine = {
|  rare_monster = {
|  |  [1] = {
|  |  |  map_id = 202000
|  |  |  partypes = {
|  |  |  |  [1] = 312
|  |  |  |  [2] = 401
|  |  |  |  [3] = 316
|  |  |  }
|  |  }
|  |  [2] = {
|  |  |  map_id = 204000
|  |  |  partypes = {
|  |  |  |  [1] = 404
|  |  |  |  [2] = 402
|  |  |  |  [3] = 403
|  |  |  }
|  |  }
|  |  [3] = {
|  |  |  map_id = 205000
|  |  |  partypes = {
|  |  |  |  [1] = 508
|  |  |  |  [2] = 509
|  |  |  |  [3] = 513
|  |  |  }
|  |  }
|  |  [4] = {
|  |  |  map_id = 206000
|  |  |  partypes = {
|  |  |  |  [1] = 410
|  |  |  |  [2] = 414
|  |  |  |  [3] = 415
|  |  |  }
|  |  }
|  |  [5] = {
|  |  |  map_id = 210400
|  |  |  partypes = {
|  |  |  |  [1] = 505
|  |  |  |  [2] = 506
|  |  |  |  [3] = 507
|  |  |  }
|  |  }
|  |  [6] = {
|  |  |  map_id = 200000
|  |  |  partypes = {
|  |  |  |  [1] = 416
|  |  |  |  [2] = 501
|  |  |  |  [3] = 502
|  |  |  }
|  |  }
|  |  [7] = {
|  |  |  map_id = 201000
|  |  |  partypes = {
|  |  |  |  [1] = 308
|  |  |  |  [2] = 301
|  |  |  |  [3] = 302
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHeroBoxRecord = {
|  npcid = {
|  |  [1] = 841
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraWarState = {
|  state = 1
|  time = 1724256000
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraQueueStatus = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeScore = {
|  cur_id = 1
|  end_time = 1724511539
|  score_info = {
|  |  buy_info = {
|  |  |  [1] = {
|  |  |  |  buy_times = 48
|  |  |  |  id = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  buy_times = 106
|  |  |  |  id = 2
|  |  |  }
|  |  |  [3] = {
|  |  |  |  buy_times = 50
|  |  |  |  id = 4
|  |  |  }
|  |  |  [4] = {
|  |  |  |  buy_times = 50
|  |  |  |  id = 6
|  |  |  }
|  |  |  [5] = {
|  |  |  |  buy_times = 199
|  |  |  |  id = 8
|  |  |  }
|  |  }
|  |  score = 423
|  }
|  start_time = 1723215600
|  status = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHDDayChargeInfo = {
|  code = 1
|  endtime = 1724511539
|  list = {
|  |  [1] = {
|  |  |  id = 1
|  |  |  receive = 1
|  |  }
|  |  [2] = {
|  |  |  id = 2
|  |  |  receive = 1
|  |  }
|  |  [3] = {
|  |  |  id = 3
|  |  |  receive = 1
|  |  }
|  |  [4] = {
|  |  |  id = 4
|  |  |  receive = 1
|  |  }
|  |  [5] = {
|  |  |  id = 5
|  |  |  receive = 1
|  |  }
|  }
|  progress = 11
|  starttime = 1723215600
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHDAddChargeInfo = {
|  endtime = 1724252339
|  list = {
|  |  [1] = {
|  |  |  id = 1
|  |  }
|  |  [10] = {
|  |  |  id = 10
|  |  }
|  |  [11] = {
|  |  |  id = 11
|  |  }
|  |  [12] = {
|  |  |  id = 12
|  |  }
|  |  [2] = {
|  |  |  id = 2
|  |  }
|  |  [3] = {
|  |  |  id = 3
|  |  }
|  |  [4] = {
|  |  |  id = 4
|  |  }
|  |  [5] = {
|  |  |  id = 5
|  |  }
|  |  [6] = {
|  |  |  id = 6
|  |  }
|  |  [7] = {
|  |  |  id = 7
|  |  }
|  |  [8] = {
|  |  |  id = 8
|  |  }
|  |  [9] = {
|  |  |  id = 9
|  |  }
|  }
|  starttime = 1724166000
}
[core/table.lua:94]:-->Net Receive: huodong.GS2COnlineGift = {
|  onlinetime = 25422
|  reward = {
|  |  [1] = {
|  |  |  random_reward = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 3
|  |  |  |  |  sid = 27411
|  |  |  |  }
|  |  |  }
|  |  |  rewardid = 1
|  |  |  stable_reward = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 2
|  |  |  |  |  sid = 10030
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  amount = 1
|  |  |  |  |  sid = 1002
|  |  |  |  }
|  |  |  }
|  |  }
|  |  [2] = {
|  |  |  random_reward = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 4
|  |  |  |  |  sid = 18300
|  |  |  |  }
|  |  |  }
|  |  |  rewardid = 2
|  |  |  stable_reward = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 3
|  |  |  |  |  sid = 10030
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  amount = 1
|  |  |  |  |  sid = 1002
|  |  |  |  }
|  |  |  }
|  |  }
|  |  [3] = {
|  |  |  random_reward = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 3
|  |  |  |  |  sid = 27409
|  |  |  |  }
|  |  |  }
|  |  |  rewardid = 3
|  |  |  stable_reward = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 1
|  |  |  |  |  sid = 1003
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  amount = 1
|  |  |  |  |  sid = 1002
|  |  |  |  }
|  |  |  }
|  |  }
|  |  [4] = {
|  |  |  random_reward = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 2
|  |  |  |  |  sid = 1003
|  |  |  |  }
|  |  |  }
|  |  |  rewardid = 4
|  |  |  stable_reward = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 5
|  |  |  |  |  sid = 10030
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  amount = 1
|  |  |  |  |  sid = 1002
|  |  |  |  }
|  |  |  }
|  |  }
|  |  [5] = {
|  |  |  random_reward = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 4
|  |  |  |  |  sid = 1003
|  |  |  |  }
|  |  |  }
|  |  |  rewardid = 5
|  |  |  stable_reward = {
|  |  |  |  [1] = {
|  |  |  |  |  amount = 1
|  |  |  |  |  sid = 10024
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  amount = 1
|  |  |  |  |  sid = 1002
|  |  |  |  }
|  |  |  }
|  |  }
|  }
|  status = 31
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CNewFieldBoss = {
|  bossid = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 37298
|  |  |  type = "zsk"
|  |  |  val = 2
|  |  }
|  |  [2] = {
|  |  |  left_count = 20
|  |  |  next_time = 37298
|  |  |  type = "yk"
|  |  |  val = 2
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_20"
|  |  |  val = 2
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_40"
|  |  |  val = 2
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_60"
|  |  |  val = 2
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_55"
|  |  |  val = 2
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_50"
|  |  |  val = 2
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_65"
|  |  |  val = 2
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  |  val = 2
|  |  }
|  }
|  czjj_is_buy = 1
|  mask = "e"
}
[core/table.lua:94]:CNetCtrl解析mask: GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 37298
|  |  |  type = "zsk"
|  |  |  val = 2
|  |  }
|  |  [2] = {
|  |  |  left_count = 20
|  |  |  next_time = 37298
|  |  |  type = "yk"
|  |  |  val = 2
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_20"
|  |  |  val = 2
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_40"
|  |  |  val = 2
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_60"
|  |  |  val = 2
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_55"
|  |  |  val = 2
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_50"
|  |  |  val = 2
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_65"
|  |  |  val = 2
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  |  val = 2
|  |  }
|  }
|  czjj_is_buy = 1
}
[core/table.lua:94]:-->Net Receive: store.GS2CPayForColorCoinInfo = {
|  colorcoin_list = {
|  |  [1] = {
|  |  |  key = "goldcoinstore_1012"
|  |  }
|  |  [10] = {
|  |  |  key = "goldcoinstore_2011"
|  |  }
|  |  [11] = {
|  |  |  key = "goldcoinstore_2012"
|  |  }
|  |  [12] = {
|  |  |  key = "goldcoinstore_2013"
|  |  }
|  |  [13] = {
|  |  |  key = "goldcoinstore_2014"
|  |  }
|  |  [14] = {
|  |  |  key = "goldcoinstore_2015"
|  |  }
|  |  [15] = {
|  |  |  key = "goldcoinstore_1015"
|  |  }
|  |  [16] = {
|  |  |  key = "goldcoinstore_1005"
|  |  }
|  |  [17] = {
|  |  |  key = "goldcoinstore_1006"
|  |  |  val = 12
|  |  }
|  |  [18] = {
|  |  |  key = "goldcoinstore_1007"
|  |  |  val = 3
|  |  }
|  |  [19] = {
|  |  |  key = "goldcoinstore_1008"
|  |  |  val = 1
|  |  }
|  |  [2] = {
|  |  |  key = "goldcoinstore_1013"
|  |  }
|  |  [20] = {
|  |  |  key = "goldcoinstore_1009"
|  |  |  val = 1
|  |  }
|  |  [21] = {
|  |  |  key = "goldcoinstore_1010"
|  |  }
|  |  [22] = {
|  |  |  key = "goldcoinstore_1011"
|  |  }
|  |  [3] = {
|  |  |  key = "goldcoinstore_1014"
|  |  }
|  |  [4] = {
|  |  |  key = "goldcoinstore_2005"
|  |  }
|  |  [5] = {
|  |  |  key = "goldcoinstore_2006"
|  |  }
|  |  [6] = {
|  |  |  key = "goldcoinstore_2007"
|  |  }
|  |  [7] = {
|  |  |  key = "goldcoinstore_2008"
|  |  }
|  |  [8] = {
|  |  |  key = "goldcoinstore_2009"
|  |  }
|  |  [9] = {
|  |  |  key = "goldcoinstore_2010"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeRewrad = {
|  end_time = 1724252339
|  reward_info = {
|  |  [1] = {
|  |  |  left_amount = 2
|  |  |  rmb = 328
|  |  }
|  |  [2] = {
|  |  |  left_amount = 2
|  |  |  rmb = 648
|  |  }
|  }
|  schedule = 1
|  start_time = 1724166000
}
[core/table.lua:94]:-->Net Receive: huodong.GS2COneRMBGift = {
|  endtime = 1724252339
|  gift = {
|  |  [1] = {
|  |  |  key = 1
|  |  }
|  |  [2] = {
|  |  |  key = 2
|  |  }
|  }
|  starttime = 1724166000
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CRefreshRewardBack = {
|  info = {
|  |  [1] = {
|  |  |  left = 1
|  |  |  sid = 2002
|  |  |  vip = 30
|  |  }
|  |  [2] = {
|  |  |  left = 1
|  |  |  sid = 2003
|  |  |  vip = 20
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliReddot = {
|  cday = 4
|  flag = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTimeResumeInfo = {
|  end_time = 1724252339
|  plan_id = 1
|  start_time = 1724166000
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CRefreshTimeResume = {
|  resume_amount = 140772
|  rewardinfo = {
|  |  [1] = {
|  |  |  reward = 10001
|  |  |  status = 1
|  |  }
|  |  [10] = {
|  |  |  reward = 10010
|  |  |  status = 1
|  |  }
|  |  [2] = {
|  |  |  reward = 10002
|  |  |  status = 1
|  |  }
|  |  [3] = {
|  |  |  reward = 10003
|  |  |  status = 1
|  |  }
|  |  [4] = {
|  |  |  reward = 10004
|  |  |  status = 1
|  |  }
|  |  [5] = {
|  |  |  reward = 10005
|  |  |  status = 1
|  |  }
|  |  [6] = {
|  |  |  reward = 10006
|  |  |  status = 1
|  |  }
|  |  [7] = {
|  |  |  reward = 10007
|  |  |  status = 1
|  |  }
|  |  [8] = {
|  |  |  reward = 10008
|  |  |  status = 1
|  |  }
|  |  [9] = {
|  |  |  reward = 10009
|  |  |  status = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayBuy = {
|  already_buy = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 4
|  |  [4] = 3
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  }
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CHistoryCharge = {
|  degree = 15158
|  getlist = {
|  |  [1] = 10001
|  |  [2] = 10002
|  |  [3] = 10003
|  |  [4] = 10004
|  |  [5] = 10005
|  }
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliPoint = {
|  point = 556
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawCnt = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliTime = {
|  endtime = 1724511539
|  starttime = 1723215600
}
[core/table.lua:94]:-->Net Receive: house.GS2CEnterHouse = {
|  buff_info = {}
|  furniture_info = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  lock_status = 1
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  type = 2
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  type = 3
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  type = 4
|  |  }
|  |  [5] = {
|  |  |  level = 1
|  |  |  type = 5
|  |  }
|  }
|  handle_type = 1
|  item_info = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  create_time = 1724186850
|  |  |  id = 1
|  |  |  itemlevel = 4
|  |  |  name = "大嘴饮料"
|  |  |  sid = 30607
|  |  }
|  }
|  max_train = 1
|  max_warm_degree = 1000
|  owner_pid = 40009
|  partner_info = {
|  |  [1] = {
|  |  |  love_level = 9
|  |  |  love_ship = 2203
|  |  |  train_type = 4
|  |  |  type = 1004
|  |  |  unchain_level = {
|  |  |  |  [1] = 5
|  |  |  }
|  |  }
|  |  [2] = {
|  |  |  love_level = 6
|  |  |  love_ship = 286
|  |  |  type = 1001
|  |  |  unchain_level = {
|  |  |  |  [1] = 5
|  |  |  }
|  |  }
|  |  [3] = {
|  |  |  love_level = 9
|  |  |  love_ship = 2285
|  |  |  type = 1002
|  |  |  unchain_level = {
|  |  |  |  [1] = 5
|  |  |  }
|  |  }
|  |  [4] = {
|  |  |  love_level = 7
|  |  |  love_ship = 434
|  |  |  type = 1003
|  |  |  unchain_level = {
|  |  |  |  [1] = 5
|  |  |  }
|  |  }
|  }
|  talent_level = 2
|  warm_degree = 302
}
[core/table.lua:94]:-->Net Receive: house.GS2CPartnerExchangeUI = {
|  handle_type = 1
|  love_cnt = 10
|  max_gift_cnt = 10
|  max_love_cnt = 10
|  partner_gift_cnt = 10
}
[core/table.lua:94]:-->Net Receive: house.GS2COpenWorkDesk = {
|  desk_info = {
|  |  [1] = {
|  |  |  item_sid = 30607
|  |  |  lock_status = 1
|  |  |  pos = 1
|  |  |  status = 3
|  |  }
|  |  [2] = {
|  |  |  item_sid = 30606
|  |  |  lock_status = 1
|  |  |  pos = 2
|  |  |  status = 3
|  |  }
|  |  [3] = {
|  |  |  pos = 3
|  |  |  status = 1
|  |  }
|  |  [4] = {
|  |  |  lock_status = 1
|  |  |  pos = 4
|  |  |  status = 1
|  |  }
|  }
|  handle_type = 1
|  owner_pid = 40009
|  talent_level = 2
|  talent_schedule = 3850
}
[core/table.lua:94]:-->Net Receive: player.GS2CTodayInfo = {
|  info = {
|  |  mask = "10"
|  |  shimen_finish = 2
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: todyInfo = {
|  shimen_finish = 2
}
[core/table.lua:94]:-->Net Receive: player.GS2CGameShare = {
|  game_share = {
|  |  [1] = {
|  |  |  type = "picture_share"
|  |  }
|  |  [2] = {
|  |  |  type = "skin_share"
|  |  }
|  |  [3] = {
|  |  |  type = "draw_card_share"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginRewardInfo = {
|  breed_val = 1284
|  login_day = 11
|  rewarded_day = 2047
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  mask = "1997c2e0000000"
|  |  org_id = 4
|  |  org_leader = "废柴v寿司"
|  |  org_level = 1
|  |  org_pos = 1
|  |  org_status = 2
|  |  orgname = "玩了个寂寞"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  give_org_equip = {}
|  give_org_wish = {}
|  is_equip_wish = 0
|  is_org_wish = 0
|  org_build_status = 0
|  org_build_time = 0
|  org_id = 4
|  org_leader = "废柴v寿司"
|  org_level = 1
|  org_pos = 1
|  org_red_packet = 0
|  org_sign_reward = 0
|  org_status = 2
|  orgname = "玩了个寂寞"
}
[core/table.lua:94]:-->Net Receive: org.GS2COrgMainInfo = {
|  info = {
|  |  active_point = 574
|  |  cash = 6600
|  |  exp = 6600
|  |  flagbgid = 10011
|  |  leadername = "废柴v寿司"
|  |  level = 1
|  |  mail_rest = 3
|  |  memcnt = 1
|  |  name = "玩了个寂寞"
|  |  online_count = 1
|  |  orgid = 4
|  |  prestige = 4000
|  |  rank = 2
|  |  sflag = "懒"
|  |  sign_degree = 1
|  }
}
[core/table.lua:94]:-->Net Receive: org.GS2CControlRedPacketUI = {
|  redlist = {
|  |  [1] = false
|  |  [2] = false
|  |  [3] = false
|  }
}
[logic/ui/CViewCtrl.lua:94]:CMainMenuRedPackedView ShowView
[core/table.lua:94]:-->Net Receive: item.GS2CLoginItem = {
|  itemdata = {
|  |  [1] = {
|  |  |  amount = 21
|  |  |  create_time = 1724187113
|  |  |  id = 1
|  |  |  itemlevel = 5
|  |  |  name = "扫荡券"
|  |  |  sid = 10030
|  |  }
|  |  [10] = {
|  |  |  amount = 84
|  |  |  create_time = 1723997215
|  |  |  id = 10
|  |  |  itemlevel = 2
|  |  |  name = "红包"
|  |  |  sid = 11501
|  |  }
|  |  [100] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 539
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 539
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723913958
|  |  |  id = 289
|  |  |  itemlevel = 2
|  |  |  name = "秽风绝弓"
|  |  |  power = 539
|  |  |  sid = 3202700
|  |  }
|  |  [101] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "res_critical_ratio"
|  |  |  |  |  value = 270
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 182
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 317
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723811277
|  |  |  id = 290
|  |  |  itemlevel = 3
|  |  |  name = "歃血裘"
|  |  |  power = 317
|  |  |  sid = 4104500
|  |  }
|  |  [102] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 1520
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  value = 118
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 363
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723742938
|  |  |  id = 291
|  |  |  itemlevel = 4
|  |  |  name = "钧天链·煌"
|  |  |  power = 363
|  |  |  sid = 4005501
|  |  }
|  |  [103] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 58
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "speed"
|  |  |  |  |  value = 77
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 212
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723811229
|  |  |  id = 292
|  |  |  itemlevel = 3
|  |  |  name = "饮霜屐"
|  |  |  power = 212
|  |  |  sid = 4404500
|  |  }
|  |  [104] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "critical_damage"
|  |  |  |  |  value = 599
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 202
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 321
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723742939
|  |  |  id = 293
|  |  |  itemlevel = 4
|  |  |  name = "钧天戒·煌"
|  |  |  power = 321
|  |  |  sid = 4205501
|  |  }
|  |  [105] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 1567
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  value = 118
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 372
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723912410
|  |  |  id = 294
|  |  |  itemlevel = 4
|  |  |  name = "钧天链·煌"
|  |  |  power = 372
|  |  |  sid = 4005501
|  |  }
|  |  [106] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "critical_damage"
|  |  |  |  |  value = 576
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 197
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 312
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723912410
|  |  |  id = 266
|  |  |  itemlevel = 4
|  |  |  name = "钧天戒·煌"
|  |  |  power = 312
|  |  |  sid = 4205501
|  |  }
|  |  [107] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "critical_damage"
|  |  |  |  |  value = 565
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 198
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 311
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723505139
|  |  |  id = 267
|  |  |  itemlevel = 4
|  |  |  name = "钧天戒·煌"
|  |  |  power = 311
|  |  |  sid = 4205501
|  |  }
|  |  [108] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 1474
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  value = 115
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 352
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723668369
|  |  |  id = 268
|  |  |  itemlevel = 4
|  |  |  name = "钧天链·翼"
|  |  |  power = 352
|  |  |  sid = 4005503
|  |  }
|  |  [109] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "critical_damage"
|  |  |  |  |  value = 571
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 197
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 311
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723503972
|  |  |  id = 269
|  |  |  itemlevel = 4
|  |  |  name = "钧天戒·煌"
|  |  |  power = 311
|  |  |  sid = 4205501
|  |  }
|  |  [11] = {
|  |  |  amount = 2
|  |  |  create_time = 1723455475
|  |  |  id = 11
|  |  |  itemlevel = 3
|  |  |  name = "体力药水"
|  |  |  sid = 13301
|  |  }
|  |  [110] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 62
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "speed"
|  |  |  |  |  value = 102
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 266
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723913958
|  |  |  id = 270
|  |  |  itemlevel = 2
|  |  |  name = "铁血重靴"
|  |  |  power = 266
|  |  |  sid = 4402700
|  |  }
|  |  [111] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 1489
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  value = 118
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 356
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723505139
|  |  |  id = 271
|  |  |  itemlevel = 4
|  |  |  name = "钧天链·煌"
|  |  |  power = 356
|  |  |  sid = 4005501
|  |  }
|  |  [112] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 1489
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  value = 120
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 357
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723504024
|  |  |  id = 272
|  |  |  itemlevel = 4
|  |  |  name = "钧天链·煌"
|  |  |  power = 357
|  |  |  sid = 4005501
|  |  }
|  |  [113] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 438
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 438
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723663304
|  |  |  id = 273
|  |  |  itemlevel = 2
|  |  |  name = "隼之狩弓"
|  |  |  power = 438
|  |  |  sid = 3202600
|  |  }
|  |  [114] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "critical_damage"
|  |  |  |  |  value = 532
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 204
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 310
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1724083836
|  |  |  id = 274
|  |  |  itemlevel = 4
|  |  |  name = "钧天戒·煌"
|  |  |  power = 310
|  |  |  sid = 4205501
|  |  }
|  |  [115] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 1583
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  value = 130
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 381
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1724083836
|  |  |  id = 275
|  |  |  itemlevel = 4
|  |  |  name = "钧天链·煌"
|  |  |  power = 381
|  |  |  sid = 4005501
|  |  }
|  |  [116] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 51
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "speed"
|  |  |  |  |  value = 86
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 223
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723663304
|  |  |  id = 276
|  |  |  itemlevel = 2
|  |  |  name = "练钧猛履"
|  |  |  power = 223
|  |  |  sid = 4402600
|  |  }
|  |  [117] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "res_critical_ratio"
|  |  |  |  |  value = 89
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 3200
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 684
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723811263
|  |  |  id = 277
|  |  |  itemlevel = 3
|  |  |  name = "斩龙腰"
|  |  |  power = 684
|  |  |  sid = 4304500
|  |  }
|  |  [118] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 146
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 146
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723663724
|  |  |  id = 278
|  |  |  itemlevel = 2
|  |  |  name = "摇光流戒"
|  |  |  power = 146
|  |  |  sid = 4202600
|  |  }
|  |  [119] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 3374
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 674
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723663724
|  |  |  id = 279
|  |  |  itemlevel = 2
|  |  |  name = "血灼腰带"
|  |  |  power = 674
|  |  |  sid = 4302600
|  |  }
|  |  [12] = {
|  |  |  amount = 8
|  |  |  create_time = 1724087846
|  |  |  id = 12
|  |  |  itemlevel = 4
|  |  |  name = "一发入魂碎片"
|  |  |  sid = 13212
|  |  }
|  |  [120] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 1125
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 225
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723664120
|  |  |  id = 280
|  |  |  itemlevel = 2
|  |  |  name = "守护之坠"
|  |  |  power = 225
|  |  |  sid = 4002600
|  |  }
|  |  [121] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 153
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 153
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723664120
|  |  |  id = 281
|  |  |  itemlevel = 2
|  |  |  name = "黄泉风铠"
|  |  |  power = 153
|  |  |  sid = 4102600
|  |  }
|  |  [122] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "critical_damage"
|  |  |  |  |  value = 582
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 191
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 307
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723668393
|  |  |  id = 282
|  |  |  itemlevel = 4
|  |  |  name = "钧天戒·翼"
|  |  |  power = 307
|  |  |  sid = 4205503
|  |  }
|  |  [123] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 180
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 180
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723914306
|  |  |  id = 283
|  |  |  itemlevel = 2
|  |  |  name = "天沼时戒"
|  |  |  power = 180
|  |  |  sid = 4202700
|  |  }
|  |  [124] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 185
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 185
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723914672
|  |  |  id = 284
|  |  |  itemlevel = 2
|  |  |  name = "夷阳竹甲"
|  |  |  power = 185
|  |  |  sid = 4102700
|  |  }
|  |  [125] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 1499
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 299
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723914672
|  |  |  id = 285
|  |  |  itemlevel = 2
|  |  |  name = "坊角吊坠"
|  |  |  power = 299
|  |  |  sid = 4002700
|  |  }
|  |  [126] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 4497
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 899
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723914306
|  |  |  id = 286
|  |  |  itemlevel = 2
|  |  |  name = "撼岳腰甲"
|  |  |  power = 899
|  |  |  sid = 4302700
|  |  }
|  |  [127] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 1536
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  value = 120
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 367
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723998119
|  |  |  id = 287
|  |  |  itemlevel = 4
|  |  |  name = "钧天链·煌"
|  |  |  power = 367
|  |  |  sid = 4005501
|  |  }
|  |  [128] = {
|  |  |  amount = 4
|  |  |  create_time = 1724086536
|  |  |  id = 256
|  |  |  itemlevel = 3
|  |  |  name = "犬妖碎片"
|  |  |  sid = 20409
|  |  }
|  |  [129] = {
|  |  |  amount = 12
|  |  |  create_time = 1724131591
|  |  |  id = 257
|  |  |  itemlevel = 3
|  |  |  name = "吞天碎片"
|  |  |  sid = 20505
|  |  }
|  |  [13] = {
|  |  |  amount = 1
|  |  |  create_time = 1723455475
|  |  |  id = 13
|  |  |  itemlevel = 4
|  |  |  name = "改名卡"
|  |  |  sid = 10023
|  |  }
|  |  [130] = {
|  |  |  amount = 3
|  |  |  create_time = 1724087574
|  |  |  id = 258
|  |  |  itemlevel = 3
|  |  |  name = "重华碎片"
|  |  |  sid = 20302
|  |  }
|  |  [131] = {
|  |  |  amount = 10
|  |  |  create_time = 1724132091
|  |  |  id = 259
|  |  |  itemlevel = 3
|  |  |  name = "噬地碎片"
|  |  |  sid = 20506
|  |  }
|  |  [132] = {
|  |  |  amount = 3
|  |  |  create_time = 1724132167
|  |  |  id = 260
|  |  |  itemlevel = 3
|  |  |  name = "执阳碎片"
|  |  |  sid = 20314
|  |  }
|  |  [133] = {
|  |  |  amount = 2
|  |  |  create_time = 1723830446
|  |  |  id = 261
|  |  |  itemlevel = 3
|  |  |  name = "青竹碎片"
|  |  |  sid = 20410
|  |  }
|  |  [134] = {
|  |  |  amount = 11
|  |  |  create_time = 1724187310
|  |  |  id = 262
|  |  |  itemlevel = 4
|  |  |  name = "嘟嘟噜碎片"
|  |  |  sid = 20418
|  |  }
|  |  [135] = {
|  |  |  amount = 17
|  |  |  create_time = 1724132252
|  |  |  id = 263
|  |  |  itemlevel = 3
|  |  |  name = "古娄碎片"
|  |  |  sid = 20413
|  |  }
|  |  [136] = {
|  |  |  amount = 1
|  |  |  create_time = 1724087865
|  |  |  id = 264
|  |  |  itemlevel = 3
|  |  |  name = "莲碎片"
|  |  |  sid = 20407
|  |  }
|  |  [137] = {
|  |  |  amount = 2
|  |  |  create_time = 1723911027
|  |  |  id = 265
|  |  |  itemlevel = 3
|  |  |  name = "稻荷碎片"
|  |  |  sid = 20402
|  |  }
|  |  [138] = {
|  |  |  amount = 1
|  |  |  create_time = 1724187113
|  |  |  id = 226
|  |  |  itemlevel = 3
|  |  |  name = "绿狸碎片"
|  |  |  sid = 20404
|  |  }
|  |  [139] = {
|  |  |  amount = 3
|  |  |  create_time = 1724075928
|  |  |  id = 227
|  |  |  itemlevel = 3
|  |  |  name = "马面面碎片"
|  |  |  sid = 20502
|  |  }
|  |  [14] = {
|  |  |  amount = 3
|  |  |  create_time = 1723974463
|  |  |  id = 14
|  |  |  itemlevel = 3
|  |  |  name = "三星云母"
|  |  |  sid = 14033
|  |  }
|  |  [140] = {
|  |  |  amount = 7
|  |  |  create_time = 1724086004
|  |  |  id = 228
|  |  |  itemlevel = 3
|  |  |  name = "琥非碎片"
|  |  |  sid = 20415
|  |  }
|  |  [141] = {
|  |  |  amount = 4
|  |  |  create_time = 1723549213
|  |  |  id = 229
|  |  |  itemlevel = 3
|  |  |  name = "祁连碎片"
|  |  |  sid = 20301
|  |  }
|  |  [142] = {
|  |  |  amount = 7
|  |  |  create_time = 1723830446
|  |  |  id = 230
|  |  |  itemlevel = 3
|  |  |  name = "伊露碎片"
|  |  |  sid = 20312
|  |  }
|  |  [143] = {
|  |  |  amount = 7
|  |  |  create_time = 1724087879
|  |  |  id = 231
|  |  |  itemlevel = 3
|  |  |  name = "曼珠沙华碎片"
|  |  |  sid = 20514
|  |  }
|  |  [144] = {
|  |  |  amount = 5
|  |  |  create_time = 1724086044
|  |  |  id = 232
|  |  |  itemlevel = 3
|  |  |  name = "蛇姬碎片"
|  |  |  sid = 20403
|  |  }
|  |  [145] = {
|  |  |  amount = 5
|  |  |  create_time = 1724086547
|  |  |  id = 233
|  |  |  itemlevel = 3
|  |  |  name = "流褐碎片"
|  |  |  sid = 20316
|  |  }
|  |  [146] = {
|  |  |  amount = 5
|  |  |  create_time = 1724086143
|  |  |  id = 234
|  |  |  itemlevel = 3
|  |  |  name = "魂夕碎片"
|  |  |  sid = 20508
|  |  }
|  |  [147] = {
|  |  |  amount = 6
|  |  |  create_time = 1724132083
|  |  |  id = 235
|  |  |  itemlevel = 3
|  |  |  name = "黑烈碎片"
|  |  |  sid = 20308
|  |  }
|  |  [148] = {
|  |  |  amount = 3
|  |  |  create_time = 1723738226
|  |  |  id = 236
|  |  |  itemlevel = 4
|  |  |  name = "白殊碎片"
|  |  |  sid = 20315
|  |  }
|  |  [149] = {
|  |  |  amount = 9
|  |  |  create_time = 1724087643
|  |  |  id = 237
|  |  |  itemlevel = 4
|  |  |  name = "浮屠僧碎片"
|  |  |  sid = 20512
|  |  }
|  |  [15] = {
|  |  |  amount = 451
|  |  |  create_time = 1724139182
|  |  |  id = 15
|  |  |  itemlevel = 2
|  |  |  name = "鲜肉包"
|  |  |  sid = 14001
|  |  }
|  |  [150] = {
|  |  |  amount = 8
|  |  |  create_time = 1724087250
|  |  |  id = 238
|  |  |  itemlevel = 3
|  |  |  name = "檀碎片"
|  |  |  sid = 20313
|  |  }
|  |  [151] = {
|  |  |  amount = 8
|  |  |  create_time = 1724187333
|  |  |  id = 239
|  |  |  itemlevel = 3
|  |  |  name = "北溟碎片"
|  |  |  sid = 20401
|  |  }
|  |  [152] = {
|  |  |  amount = 5
|  |  |  create_time = 1723997269
|  |  |  id = 240
|  |  |  itemlevel = 3
|  |  |  name = "花常酒碎片"
|  |  |  sid = 20311
|  |  }
|  |  [153] = {
|  |  |  amount = 7
|  |  |  create_time = 1724086219
|  |  |  id = 241
|  |  |  itemlevel = 3
|  |  |  name = "魃女碎片"
|  |  |  sid = 20507
|  |  }
|  |  [154] = {
|  |  |  amount = 6
|  |  |  create_time = 1724132072
|  |  |  id = 242
|  |  |  itemlevel = 3
|  |  |  name = "夜叉碎片"
|  |  |  sid = 20513
|  |  }
|  |  [155] = {
|  |  |  amount = 7
|  |  |  create_time = 1724132208
|  |  |  id = 243
|  |  |  itemlevel = 3
|  |  |  name = "朵屠碎片"
|  |  |  sid = 20303
|  |  }
|  |  [156] = {
|  |  |  amount = 3
|  |  |  create_time = 1724086185
|  |  |  id = 244
|  |  |  itemlevel = 4
|  |  |  name = "黑碎片"
|  |  |  sid = 20503
|  |  }
|  |  [157] = {
|  |  |  amount = 5
|  |  |  create_time = 1723738226
|  |  |  id = 245
|  |  |  itemlevel = 3
|  |  |  name = "蓝堂昼碎片"
|  |  |  sid = 20414
|  |  }
|  |  [158] = {
|  |  |  amount = 7
|  |  |  create_time = 1724086372
|  |  |  id = 246
|  |  |  itemlevel = 3
|  |  |  name = "观绪碎片"
|  |  |  sid = 20416
|  |  }
|  |  [159] = {
|  |  |  amount = 5
|  |  |  create_time = 1724132230
|  |  |  id = 247
|  |  |  itemlevel = 3
|  |  |  name = "松姑子碎片"
|  |  |  sid = 20417
|  |  }
|  |  [16] = {
|  |  |  amount = 52
|  |  |  create_time = 1724187135
|  |  |  id = 16
|  |  |  itemlevel = 2
|  |  |  name = "二星云母"
|  |  |  sid = 14032
|  |  }
|  |  [160] = {
|  |  |  amount = 3
|  |  |  create_time = 1724086167
|  |  |  id = 248
|  |  |  itemlevel = 4
|  |  |  name = "奉主夜鹤碎片"
|  |  |  sid = 20510
|  |  }
|  |  [161] = {
|  |  |  amount = 6
|  |  |  create_time = 1724087811
|  |  |  id = 249
|  |  |  itemlevel = 3
|  |  |  name = "执夷碎片"
|  |  |  sid = 20412
|  |  }
|  |  [162] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998029
|  |  |  id = 250
|  |  |  itemlevel = 4
|  |  |  name = "袁雀碎片"
|  |  |  sid = 20306
|  |  }
|  |  [163] = {
|  |  |  amount = 5
|  |  |  create_time = 1724086310
|  |  |  id = 251
|  |  |  itemlevel = 4
|  |  |  name = "亦候碎片"
|  |  |  sid = 20305
|  |  }
|  |  [164] = {
|  |  |  amount = 10
|  |  |  create_time = 1724086195
|  |  |  id = 252
|  |  |  itemlevel = 3
|  |  |  name = "阿坊碎片"
|  |  |  sid = 20501
|  |  }
|  |  [165] = {
|  |  |  amount = 2
|  |  |  create_time = 1723738226
|  |  |  id = 253
|  |  |  itemlevel = 4
|  |  |  name = "片雾碎片"
|  |  |  sid = 20511
|  |  }
|  |  [166] = {
|  |  |  amount = 13
|  |  |  create_time = 1724187333
|  |  |  id = 254
|  |  |  itemlevel = 3
|  |  |  name = "白碎片"
|  |  |  sid = 20504
|  |  }
|  |  [167] = {
|  |  |  amount = 5
|  |  |  create_time = 1724087609
|  |  |  id = 255
|  |  |  itemlevel = 3
|  |  |  name = "判官碎片"
|  |  |  sid = 20509
|  |  }
|  |  [168] = {
|  |  |  amount = 2
|  |  |  create_time = 1724186961
|  |  |  id = 297
|  |  |  itemlevel = 3
|  |  |  name = "鬼云轮·低"
|  |  |  sid = 27411
|  |  }
|  |  [169] = {
|  |  |  amount = 3
|  |  |  create_time = 1724186967
|  |  |  id = 298
|  |  |  itemlevel = 4
|  |  |  name = "妖风铃·高"
|  |  |  sid = 27409
|  |  }
|  |  [17] = {
|  |  |  amount = 42
|  |  |  create_time = 1724132022
|  |  |  id = 17
|  |  |  itemlevel = 4
|  |  |  name = "万能碎片"
|  |  |  sid = 14002
|  |  }
|  |  [170] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998917
|  |  |  id = 299
|  |  |  itemlevel = 4
|  |  |  name = "鬼云轮·高"
|  |  |  sid = 27412
|  |  }
|  |  [171] = {
|  |  |  amount = 4
|  |  |  create_time = 1723974513
|  |  |  id = 300
|  |  |  itemlevel = 3
|  |  |  name = "梦觉书·低"
|  |  |  sid = 27402
|  |  }
|  |  [172] = {
|  |  |  amount = 5
|  |  |  create_time = 1724077301
|  |  |  id = 301
|  |  |  itemlevel = 3
|  |  |  name = "妖风铃·低"
|  |  |  sid = 27408
|  |  }
|  |  [173] = {
|  |  |  amount = 1
|  |  |  create_time = 1723889051
|  |  |  id = 302
|  |  |  itemlevel = 4
|  |  |  name = "梦觉书·高"
|  |  |  sid = 27403
|  |  }
|  |  [174] = {
|  |  |  amount = 4
|  |  |  create_time = 1724131674
|  |  |  id = 303
|  |  |  itemlevel = 3
|  |  |  name = "人灵决·低"
|  |  |  sid = 27405
|  |  }
|  |  [175] = {
|  |  |  amount = 1
|  |  |  create_time = 1723366484
|  |  |  id = 296
|  |  |  itemlevel = 2
|  |  |  name = "默认皮肤"
|  |  |  sid = 205030
|  |  }
|  |  [176] = {
|  |  |  amount = 1
|  |  |  create_time = 1723366459
|  |  |  id = 295
|  |  |  itemlevel = 2
|  |  |  name = "国家荣耀 "
|  |  |  sid = 205032
|  |  }
|  |  [177] = {
|  |  |  amount = 3
|  |  |  create_time = 1724131674
|  |  |  id = 19
|  |  |  itemlevel = 2
|  |  |  name = "光辉符石·Ⅰ"
|  |  |  sid = 330001
|  |  }
|  |  [178] = {
|  |  |  amount = 4
|  |  |  create_time = 1724137747
|  |  |  id = 20
|  |  |  itemlevel = 2
|  |  |  name = "光辉符石·Ⅲ"
|  |  |  sid = 330003
|  |  }
|  |  [179] = {
|  |  |  amount = 2
|  |  |  create_time = 1724136017
|  |  |  id = 21
|  |  |  itemlevel = 2
|  |  |  name = "破坏符石·Ⅲ"
|  |  |  sid = 310003
|  |  }
|  |  [18] = {
|  |  |  amount = 169
|  |  |  create_time = 1724218067
|  |  |  id = 18
|  |  |  itemlevel = 2
|  |  |  name = "深蓝琥珀"
|  |  |  sid = 14021
|  |  }
|  |  [180] = {
|  |  |  amount = 2
|  |  |  create_time = 1724130936
|  |  |  id = 22
|  |  |  itemlevel = 2
|  |  |  name = "破坏符石·Ⅳ"
|  |  |  sid = 310004
|  |  }
|  |  [181] = {
|  |  |  amount = 1
|  |  |  create_time = 1724132434
|  |  |  id = 23
|  |  |  itemlevel = 2
|  |  |  name = "禁忌符石·Ⅲ"
|  |  |  sid = 340003
|  |  }
|  |  [182] = {
|  |  |  amount = 1
|  |  |  create_time = 1724132159
|  |  |  id = 24
|  |  |  itemlevel = 2
|  |  |  name = "光辉符石·Ⅱ"
|  |  |  sid = 330002
|  |  }
|  |  [183] = {
|  |  |  amount = 3
|  |  |  create_time = 1724134916
|  |  |  id = 25
|  |  |  itemlevel = 2
|  |  |  name = "破坏符石·Ⅰ"
|  |  |  sid = 310001
|  |  }
|  |  [184] = {
|  |  |  amount = 4
|  |  |  create_time = 1724136072
|  |  |  id = 26
|  |  |  itemlevel = 2
|  |  |  name = "圣灵符石·Ⅱ"
|  |  |  sid = 320002
|  |  }
|  |  [185] = {
|  |  |  amount = 4
|  |  |  create_time = 1724133120
|  |  |  id = 27
|  |  |  itemlevel = 2
|  |  |  name = "破坏符石·Ⅱ"
|  |  |  sid = 310002
|  |  }
|  |  [186] = {
|  |  |  amount = 17
|  |  |  create_time = 1724135680
|  |  |  id = 28
|  |  |  itemlevel = 2
|  |  |  name = "禁忌符石·Ⅰ"
|  |  |  sid = 340001
|  |  }
|  |  [187] = {
|  |  |  amount = 7
|  |  |  create_time = 1724138720
|  |  |  id = 29
|  |  |  itemlevel = 2
|  |  |  name = "圣灵符石·Ⅰ"
|  |  |  sid = 320001
|  |  }
|  |  [188] = {
|  |  |  amount = 5
|  |  |  create_time = 1724136863
|  |  |  id = 30
|  |  |  itemlevel = 2
|  |  |  name = "禁忌符石·Ⅱ"
|  |  |  sid = 340002
|  |  }
|  |  [189] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998236
|  |  |  id = 31
|  |  |  itemlevel = 2
|  |  |  name = "流臾之须·抗暴"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7430206
|  |  }
|  |  [19] = {
|  |  |  amount = 81
|  |  |  create_time = 1724139218
|  |  |  id = 372
|  |  |  itemlevel = 3
|  |  |  name = "淬灵云晶"
|  |  |  sid = 11101
|  |  }
|  |  [190] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998236
|  |  |  id = 32
|  |  |  itemlevel = 3
|  |  |  name = "偃月无夜·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 1
|  |  |  }
|  |  |  sid = 7310302
|  |  }
|  |  [191] = {
|  |  |  amount = 1
|  |  |  create_time = 1723815986
|  |  |  id = 33
|  |  |  itemlevel = 6
|  |  |  name = "博戏鬼·抗暴"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 7
|  |  |  }
|  |  |  sid = 7030506
|  |  }
|  |  [192] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998236
|  |  |  id = 34
|  |  |  itemlevel = 4
|  |  |  name = "一阕秋·防御加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7450413
|  |  }
|  |  [193] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998236
|  |  |  id = 35
|  |  |  itemlevel = 3
|  |  |  name = "乌琪琪·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 8
|  |  |  }
|  |  |  sid = 7090302
|  |  }
|  |  [194] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998236
|  |  |  id = 36
|  |  |  itemlevel = 3
|  |  |  name = "红雨将军·暴击伤害"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 17
|  |  |  }
|  |  |  sid = 7100307
|  |  }
|  |  [195] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998236
|  |  |  id = 37
|  |  |  itemlevel = 2
|  |  |  name = "乌琪琪·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 8
|  |  |  }
|  |  |  sid = 7090211
|  |  }
|  |  [196] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998236
|  |  |  id = 38
|  |  |  itemlevel = 3
|  |  |  name = "墨菲·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 13
|  |  |  }
|  |  |  sid = 7020312
|  |  }
|  |  [197] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998244
|  |  |  id = 39
|  |  |  itemlevel = 3
|  |  |  name = "洛迦·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 16
|  |  |  }
|  |  |  sid = 7060311
|  |  }
|  |  [198] = {
|  |  |  amount = 1
|  |  |  create_time = 1723815996
|  |  |  id = 40
|  |  |  itemlevel = 6
|  |  |  name = "血衣客·暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7010505
|  |  }
|  |  [199] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998244
|  |  |  id = 41
|  |  |  itemlevel = 5
|  |  |  name = "乌琪琪·暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 8
|  |  |  }
|  |  |  sid = 7090105
|  |  }
|  |  [2] = {
|  |  |  amount = 217
|  |  |  create_time = 1724187337
|  |  |  id = 2
|  |  |  itemlevel = 4
|  |  |  name = "灵魂钥匙"
|  |  |  sid = 10040
|  |  }
|  |  [20] = {
|  |  |  amount = 10
|  |  |  create_time = 1724138838
|  |  |  id = 373
|  |  |  itemlevel = 2
|  |  |  name = "星梦矿"
|  |  |  sid = 11001
|  |  }
|  |  [200] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998244
|  |  |  id = 42
|  |  |  itemlevel = 3
|  |  |  name = "洛迦·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 16
|  |  |  }
|  |  |  sid = 7060302
|  |  }
|  |  [21] = {
|  |  |  amount = 15
|  |  |  create_time = 1724084490
|  |  |  id = 374
|  |  |  itemlevel = 3
|  |  |  name = "月莹矿"
|  |  |  sid = 11002
|  |  }
|  |  [22] = {
|  |  |  amount = 8
|  |  |  create_time = 1724073950
|  |  |  id = 375
|  |  |  itemlevel = 4
|  |  |  name = "武器原石"
|  |  |  sid = 11004
|  |  }
|  |  [23] = {
|  |  |  amount = 137
|  |  |  create_time = 1724134916
|  |  |  id = 376
|  |  |  itemlevel = 3
|  |  |  name = "合成粉尘"
|  |  |  sid = 11016
|  |  }
|  |  [24] = {
|  |  |  amount = 7
|  |  |  create_time = 1724132699
|  |  |  id = 377
|  |  |  itemlevel = 3
|  |  |  name = "项链原石"
|  |  |  sid = 11005
|  |  }
|  |  [25] = {
|  |  |  amount = 45
|  |  |  create_time = 1724134926
|  |  |  id = 378
|  |  |  itemlevel = 3
|  |  |  name = "附魔原石"
|  |  |  sid = 11010
|  |  }
|  |  [26] = {
|  |  |  amount = 6
|  |  |  create_time = 1724134925
|  |  |  id = 379
|  |  |  itemlevel = 3
|  |  |  name = "戒指原石"
|  |  |  sid = 11007
|  |  }
|  |  [27] = {
|  |  |  amount = 7
|  |  |  create_time = 1724134916
|  |  |  id = 380
|  |  |  itemlevel = 3
|  |  |  name = "腰带原石"
|  |  |  sid = 11008
|  |  }
|  |  [28] = {
|  |  |  amount = 11
|  |  |  create_time = 1724134927
|  |  |  id = 381
|  |  |  itemlevel = 3
|  |  |  name = "衣服原石"
|  |  |  sid = 11006
|  |  }
|  |  [29] = {
|  |  |  amount = 8
|  |  |  create_time = 1724071516
|  |  |  id = 382
|  |  |  itemlevel = 3
|  |  |  name = "鞋子原石"
|  |  |  sid = 11009
|  |  }
|  |  [3] = {
|  |  |  amount = 47
|  |  |  create_time = 1724203642
|  |  |  id = 3
|  |  |  itemlevel = 4
|  |  |  name = "星象图"
|  |  |  sid = 10024
|  |  |  treasure_info = {
|  |  |  |  treasure_mapid = 206000
|  |  |  |  treasure_posx = 27
|  |  |  |  treasure_posy = 5
|  |  |  }
|  |  }
|  |  [30] = {
|  |  |  amount = 2
|  |  |  create_time = 1723913354
|  |  |  id = 383
|  |  |  itemlevel = 4
|  |  |  name = "日冕矿"
|  |  |  sid = 11003
|  |  }
|  |  [31] = {
|  |  |  amount = 2
|  |  |  create_time = 1724086729
|  |  |  id = 352
|  |  |  itemlevel = 2
|  |  |  name = "4级绯红宝石"
|  |  |  sid = 18003
|  |  }
|  |  [32] = {
|  |  |  amount = 2
|  |  |  create_time = 1723998915
|  |  |  id = 353
|  |  |  itemlevel = 2
|  |  |  name = "1级绯红宝石"
|  |  |  sid = 18000
|  |  }
|  |  [33] = {
|  |  |  amount = 7
|  |  |  create_time = 1724086767
|  |  |  id = 354
|  |  |  itemlevel = 2
|  |  |  name = "3级双生宝石"
|  |  |  sid = 18202
|  |  }
|  |  [34] = {
|  |  |  amount = 3
|  |  |  create_time = 1724084605
|  |  |  id = 355
|  |  |  itemlevel = 2
|  |  |  name = "3级黄金宝石"
|  |  |  sid = 18302
|  |  }
|  |  [35] = {
|  |  |  amount = 13
|  |  |  create_time = 1724187326
|  |  |  id = 356
|  |  |  itemlevel = 2
|  |  |  name = "3级八云宝石"
|  |  |  sid = 18102
|  |  }
|  |  [36] = {
|  |  |  amount = 3
|  |  |  create_time = 1724084605
|  |  |  id = 357
|  |  |  itemlevel = 2
|  |  |  name = "2级黄金宝石"
|  |  |  sid = 18301
|  |  }
|  |  [37] = {
|  |  |  amount = 9
|  |  |  create_time = 1724084605
|  |  |  id = 358
|  |  |  itemlevel = 2
|  |  |  name = "2级八云宝石"
|  |  |  sid = 18101
|  |  }
|  |  [38] = {
|  |  |  amount = 3
|  |  |  create_time = 1724083880
|  |  |  id = 359
|  |  |  itemlevel = 2
|  |  |  name = "4级八云宝石"
|  |  |  sid = 18103
|  |  }
|  |  [39] = {
|  |  |  amount = 10
|  |  |  create_time = 1724084605
|  |  |  id = 360
|  |  |  itemlevel = 2
|  |  |  name = "2级双生宝石"
|  |  |  sid = 18201
|  |  }
|  |  [4] = {
|  |  |  amount = 10
|  |  |  create_time = 1724187326
|  |  |  id = 4
|  |  |  itemlevel = 2
|  |  |  name = "神格·攻"
|  |  |  sid = 11202
|  |  }
|  |  [40] = {
|  |  |  amount = 16
|  |  |  create_time = 1724187337
|  |  |  id = 361
|  |  |  itemlevel = 2
|  |  |  name = "2级翠星宝石"
|  |  |  sid = 18401
|  |  }
|  |  [41] = {
|  |  |  amount = 2
|  |  |  create_time = 1724083889
|  |  |  id = 362
|  |  |  itemlevel = 2
|  |  |  name = "3级绯红宝石"
|  |  |  sid = 18002
|  |  }
|  |  [42] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998080
|  |  |  id = 363
|  |  |  itemlevel = 2
|  |  |  name = "5级翠星宝石"
|  |  |  sid = 18404
|  |  }
|  |  [43] = {
|  |  |  amount = 3
|  |  |  create_time = 1724083928
|  |  |  id = 364
|  |  |  itemlevel = 2
|  |  |  name = "4级黄金宝石"
|  |  |  sid = 18303
|  |  }
|  |  [44] = {
|  |  |  amount = 3
|  |  |  create_time = 1724084605
|  |  |  id = 365
|  |  |  itemlevel = 2
|  |  |  name = "3级翠星宝石"
|  |  |  sid = 18402
|  |  }
|  |  [45] = {
|  |  |  amount = 3
|  |  |  create_time = 1724084605
|  |  |  id = 366
|  |  |  itemlevel = 2
|  |  |  name = "3级疾风宝石"
|  |  |  sid = 18502
|  |  }
|  |  [46] = {
|  |  |  amount = 9
|  |  |  create_time = 1724084605
|  |  |  id = 367
|  |  |  itemlevel = 2
|  |  |  name = "2级疾风宝石"
|  |  |  sid = 18501
|  |  }
|  |  [47] = {
|  |  |  amount = 3
|  |  |  create_time = 1724186965
|  |  |  id = 368
|  |  |  itemlevel = 2
|  |  |  name = "1级黄金宝石"
|  |  |  sid = 18300
|  |  }
|  |  [48] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998080
|  |  |  id = 369
|  |  |  itemlevel = 2
|  |  |  name = "5级双生宝石"
|  |  |  sid = 18204
|  |  }
|  |  [49] = {
|  |  |  amount = 29
|  |  |  create_time = 1724087176
|  |  |  id = 370
|  |  |  itemlevel = 2
|  |  |  name = "1级双生宝石"
|  |  |  sid = 18200
|  |  }
|  |  [5] = {
|  |  |  amount = 62
|  |  |  create_time = 1724083833
|  |  |  id = 5
|  |  |  itemlevel = 4
|  |  |  name = "橙色可选御灵"
|  |  |  sid = 13270
|  |  }
|  |  [50] = {
|  |  |  amount = 4
|  |  |  create_time = 1724086980
|  |  |  id = 371
|  |  |  itemlevel = 2
|  |  |  name = "1级八云宝石"
|  |  |  sid = 18100
|  |  }
|  |  [51] = {
|  |  |  amount = 3
|  |  |  create_time = 1724084605
|  |  |  id = 345
|  |  |  itemlevel = 2
|  |  |  name = "2级绯红宝石"
|  |  |  sid = 18001
|  |  }
|  |  [52] = {
|  |  |  amount = 1
|  |  |  create_time = 1723582948
|  |  |  id = 346
|  |  |  itemlevel = 2
|  |  |  name = "4级双生宝石"
|  |  |  sid = 18203
|  |  }
|  |  [53] = {
|  |  |  amount = 2
|  |  |  create_time = 1724083880
|  |  |  id = 347
|  |  |  itemlevel = 2
|  |  |  name = "4级翠星宝石"
|  |  |  sid = 18403
|  |  }
|  |  [54] = {
|  |  |  amount = 3
|  |  |  create_time = 1724083880
|  |  |  id = 348
|  |  |  itemlevel = 2
|  |  |  name = "4级疾风宝石"
|  |  |  sid = 18503
|  |  }
|  |  [55] = {
|  |  |  amount = 1
|  |  |  create_time = 1723652800
|  |  |  id = 349
|  |  |  itemlevel = 2
|  |  |  name = "5级绯红宝石"
|  |  |  sid = 18004
|  |  }
|  |  [56] = {
|  |  |  amount = 1
|  |  |  create_time = 1724083925
|  |  |  id = 350
|  |  |  itemlevel = 2
|  |  |  name = "5级黄金宝石"
|  |  |  sid = 18304
|  |  }
|  |  [57] = {
|  |  |  amount = 3
|  |  |  create_time = 1723911217
|  |  |  id = 351
|  |  |  itemlevel = 2
|  |  |  name = "1级疾风宝石"
|  |  |  sid = 18500
|  |  }
|  |  [58] = {
|  |  |  amount = 1
|  |  |  create_time = 1723987650
|  |  |  id = 320
|  |  |  itemlevel = 1
|  |  |  name = "双灵符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 10
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6401001
|  |  }
|  |  [59] = {
|  |  |  amount = 1
|  |  |  create_time = 1723500884
|  |  |  id = 321
|  |  |  itemlevel = 1
|  |  |  name = "双灵符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 2
|  |  |  |  stone_info = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_damage"
|  |  |  |  |  |  |  |  value = 80
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "abnormal_attr_ratio"
|  |  |  |  |  |  |  |  value = 80
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 340001
|  |  |  |  |  |  |  [2] = 340001
|  |  |  |  |  |  |  [3] = 340001
|  |  |  |  |  |  |  [4] = 340001
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_damage"
|  |  |  |  |  |  |  |  value = 200
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "abnormal_attr_ratio"
|  |  |  |  |  |  |  |  value = 150
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 340002
|  |  |  |  |  |  |  [2] = 340002
|  |  |  |  |  |  |  [3] = 340002
|  |  |  |  |  |  |  [4] = 340002
|  |  |  |  |  |  |  [5] = 340002
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_damage"
|  |  |  |  |  |  |  |  value = 60
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "abnormal_attr_ratio"
|  |  |  |  |  |  |  |  value = 40
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 3
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 340003
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6403004
|  |  }
|  |  [6] = {
|  |  |  amount = 7
|  |  |  create_time = 1724131600
|  |  |  id = 6
|  |  |  itemlevel = 2
|  |  |  name = "镜花水月"
|  |  |  sid = 10022
|  |  }
|  |  [60] = {
|  |  |  amount = 1
|  |  |  create_time = 1723494399
|  |  |  id = 322
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 12
|  |  |  |  stone_info = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 28
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 310001
|  |  |  |  |  |  |  [2] = 310001
|  |  |  |  |  |  |  [3] = 310001
|  |  |  |  |  |  |  [4] = 310001
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 50
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 310002
|  |  |  |  |  |  |  [2] = 310002
|  |  |  |  |  |  |  [3] = 310002
|  |  |  |  |  |  |  [4] = 310002
|  |  |  |  |  |  |  [5] = 310002
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101002
|  |  }
|  |  [61] = {
|  |  |  amount = 1
|  |  |  create_time = 1723494401
|  |  |  id = 323
|  |  |  itemlevel = 1
|  |  |  name = "气血符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 12
|  |  |  |  stone_info = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  value = 300
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 320001
|  |  |  |  |  |  |  [2] = 320001
|  |  |  |  |  |  |  [3] = 320001
|  |  |  |  |  |  |  [4] = 320001
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  value = 565
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 320002
|  |  |  |  |  |  |  [2] = 320002
|  |  |  |  |  |  |  [3] = 320002
|  |  |  |  |  |  |  [4] = 320002
|  |  |  |  |  |  |  [5] = 320002
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  value = 600
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 3
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 320003
|  |  |  |  |  |  |  [2] = 320003
|  |  |  |  |  |  |  [3] = 320003
|  |  |  |  |  |  |  [4] = 320003
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [4] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  value = 376
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 4
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 320004
|  |  |  |  |  |  |  [2] = 320004
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6203004
|  |  }
|  |  [62] = {
|  |  |  amount = 1
|  |  |  create_time = 1723494402
|  |  |  id = 324
|  |  |  itemlevel = 1
|  |  |  name = "防御符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 12
|  |  |  |  stone_info = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  value = 80
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "res_abnormal_ratio"
|  |  |  |  |  |  |  |  value = 80
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 330001
|  |  |  |  |  |  |  [2] = 330001
|  |  |  |  |  |  |  [3] = 330001
|  |  |  |  |  |  |  [4] = 330001
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  value = 150
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "res_abnormal_ratio"
|  |  |  |  |  |  |  |  value = 150
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 330002
|  |  |  |  |  |  |  [2] = 330002
|  |  |  |  |  |  |  [3] = 330002
|  |  |  |  |  |  |  [4] = 330002
|  |  |  |  |  |  |  [5] = 330002
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6301008
|  |  }
|  |  [63] = {
|  |  |  amount = 1
|  |  |  create_time = 1723494431
|  |  |  id = 325
|  |  |  itemlevel = 1
|  |  |  name = "气血符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 2
|  |  |  |  stone_info = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  value = 300
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 320001
|  |  |  |  |  |  |  [2] = 320001
|  |  |  |  |  |  |  [3] = 320001
|  |  |  |  |  |  |  [4] = 320001
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  value = 565
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 320002
|  |  |  |  |  |  |  [2] = 320002
|  |  |  |  |  |  |  [3] = 320002
|  |  |  |  |  |  |  [4] = 320002
|  |  |  |  |  |  |  [5] = 320002
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  value = 150
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 3
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 320003
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6202004
|  |  }
|  |  [64] = {
|  |  |  amount = 1
|  |  |  create_time = 1723366178
|  |  |  id = 326
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 1
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101003
|  |  }
|  |  [65] = {
|  |  |  amount = 1
|  |  |  create_time = 1723366430
|  |  |  id = 327
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 19
|  |  |  |  stone_info = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 28
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 310001
|  |  |  |  |  |  |  [2] = 310001
|  |  |  |  |  |  |  [3] = 310001
|  |  |  |  |  |  |  [4] = 310001
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 50
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 310002
|  |  |  |  |  |  |  [2] = 310002
|  |  |  |  |  |  |  [3] = 310002
|  |  |  |  |  |  |  [4] = 310002
|  |  |  |  |  |  |  [5] = 310002
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 65
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 3
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 310003
|  |  |  |  |  |  |  [2] = 310003
|  |  |  |  |  |  |  [3] = 310003
|  |  |  |  |  |  |  [4] = 310003
|  |  |  |  |  |  |  [5] = 310003
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [4] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 96
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 4
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 310004
|  |  |  |  |  |  |  [2] = 310004
|  |  |  |  |  |  |  [3] = 310004
|  |  |  |  |  |  |  [4] = 310004
|  |  |  |  |  |  |  [5] = 310004
|  |  |  |  |  |  |  [6] = 310004
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [5] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 23
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 5
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 310005
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  stone_level = 2
|  |  |  }
|  |  |  sid = 6103004
|  |  }
|  |  [66] = {
|  |  |  amount = 1
|  |  |  create_time = 1723441626
|  |  |  id = 328
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 9
|  |  |  |  stone_info = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 28
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 310001
|  |  |  |  |  |  |  [2] = 310001
|  |  |  |  |  |  |  [3] = 310001
|  |  |  |  |  |  |  [4] = 310001
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 50
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 310002
|  |  |  |  |  |  |  [2] = 310002
|  |  |  |  |  |  |  [3] = 310002
|  |  |  |  |  |  |  [4] = 310002
|  |  |  |  |  |  |  [5] = 310002
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 65
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 3
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 310003
|  |  |  |  |  |  |  [2] = 310003
|  |  |  |  |  |  |  [3] = 310003
|  |  |  |  |  |  |  [4] = 310003
|  |  |  |  |  |  |  [5] = 310003
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [4] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 96
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 4
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 310004
|  |  |  |  |  |  |  [2] = 310004
|  |  |  |  |  |  |  [3] = 310004
|  |  |  |  |  |  |  [4] = 310004
|  |  |  |  |  |  |  [5] = 310004
|  |  |  |  |  |  |  [6] = 310004
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [5] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 138
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 5
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 310005
|  |  |  |  |  |  |  [2] = 310005
|  |  |  |  |  |  |  [3] = 310005
|  |  |  |  |  |  |  [4] = 310005
|  |  |  |  |  |  |  [5] = 310005
|  |  |  |  |  |  |  [6] = 310005
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [6] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 203
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 6
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 310006
|  |  |  |  |  |  |  [2] = 310006
|  |  |  |  |  |  |  [3] = 310006
|  |  |  |  |  |  |  [4] = 310006
|  |  |  |  |  |  |  [5] = 310006
|  |  |  |  |  |  |  [6] = 310006
|  |  |  |  |  |  |  [7] = 310006
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  stone_level = 3
|  |  |  }
|  |  |  sid = 6103002
|  |  }
|  |  [67] = {
|  |  |  amount = 1
|  |  |  create_time = 1723441871
|  |  |  id = 329
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 15
|  |  |  |  stone_info = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 7
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 310001
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6102001
|  |  }
|  |  [68] = {
|  |  |  amount = 1
|  |  |  create_time = 1723494644
|  |  |  id = 330
|  |  |  itemlevel = 1
|  |  |  name = "防御符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 9
|  |  |  |  stone_info = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  value = 80
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "res_abnormal_ratio"
|  |  |  |  |  |  |  |  value = 80
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 330001
|  |  |  |  |  |  |  [2] = 330001
|  |  |  |  |  |  |  [3] = 330001
|  |  |  |  |  |  |  [4] = 330001
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  value = 150
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "res_abnormal_ratio"
|  |  |  |  |  |  |  |  value = 150
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 330002
|  |  |  |  |  |  |  [2] = 330002
|  |  |  |  |  |  |  [3] = 330002
|  |  |  |  |  |  |  [4] = 330002
|  |  |  |  |  |  |  [5] = 330002
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6301002
|  |  }
|  |  [69] = {
|  |  |  amount = 1
|  |  |  create_time = 1723441874
|  |  |  id = 331
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 11
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6102001
|  |  }
|  |  [7] = {
|  |  |  amount = 2
|  |  |  create_time = 1723501001
|  |  |  id = 7
|  |  |  itemlevel = 3
|  |  |  name = "紫色可选御灵"
|  |  |  sid = 13269
|  |  }
|  |  [70] = {
|  |  |  amount = 1
|  |  |  create_time = 1723437730
|  |  |  id = 332
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 8
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101003
|  |  }
|  |  [71] = {
|  |  |  amount = 1
|  |  |  create_time = 1723366432
|  |  |  id = 333
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 2
|  |  |  |  stone_info = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 28
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 310001
|  |  |  |  |  |  |  [2] = 310001
|  |  |  |  |  |  |  [3] = 310001
|  |  |  |  |  |  |  [4] = 310001
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 50
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 310002
|  |  |  |  |  |  |  [2] = 310002
|  |  |  |  |  |  |  [3] = 310002
|  |  |  |  |  |  |  [4] = 310002
|  |  |  |  |  |  |  [5] = 310002
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 65
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 3
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 310003
|  |  |  |  |  |  |  [2] = 310003
|  |  |  |  |  |  |  [3] = 310003
|  |  |  |  |  |  |  [4] = 310003
|  |  |  |  |  |  |  [5] = 310003
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [4] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 96
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 4
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 310004
|  |  |  |  |  |  |  [2] = 310004
|  |  |  |  |  |  |  [3] = 310004
|  |  |  |  |  |  |  [4] = 310004
|  |  |  |  |  |  |  [5] = 310004
|  |  |  |  |  |  |  [6] = 310004
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [5] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 138
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 5
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 310005
|  |  |  |  |  |  |  [2] = 310005
|  |  |  |  |  |  |  [3] = 310005
|  |  |  |  |  |  |  [4] = 310005
|  |  |  |  |  |  |  [5] = 310005
|  |  |  |  |  |  |  [6] = 310005
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  stone_level = 3
|  |  |  }
|  |  |  sid = 6103004
|  |  }
|  |  [72] = {
|  |  |  amount = 1
|  |  |  create_time = 1723376866
|  |  |  id = 334
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 5
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101003
|  |  }
|  |  [73] = {
|  |  |  amount = 1
|  |  |  create_time = 1723494670
|  |  |  id = 335
|  |  |  itemlevel = 1
|  |  |  name = "防御符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 19
|  |  |  |  stone_info = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  value = 80
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "res_abnormal_ratio"
|  |  |  |  |  |  |  |  value = 80
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 330001
|  |  |  |  |  |  |  [2] = 330001
|  |  |  |  |  |  |  [3] = 330001
|  |  |  |  |  |  |  [4] = 330001
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  value = 150
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "res_abnormal_ratio"
|  |  |  |  |  |  |  |  value = 150
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 330002
|  |  |  |  |  |  |  [2] = 330002
|  |  |  |  |  |  |  [3] = 330002
|  |  |  |  |  |  |  [4] = 330002
|  |  |  |  |  |  |  [5] = 330002
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  value = 200
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "res_abnormal_ratio"
|  |  |  |  |  |  |  |  value = 200
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 3
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 330003
|  |  |  |  |  |  |  [2] = 330003
|  |  |  |  |  |  |  [3] = 330003
|  |  |  |  |  |  |  [4] = 330003
|  |  |  |  |  |  |  [5] = 330003
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [4] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  value = 120
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "res_abnormal_ratio"
|  |  |  |  |  |  |  |  value = 120
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 4
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 330004
|  |  |  |  |  |  |  [2] = 330004
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  stone_level = 2
|  |  |  }
|  |  |  sid = 6302001
|  |  }
|  |  [74] = {
|  |  |  amount = 1
|  |  |  create_time = 1723500881
|  |  |  id = 336
|  |  |  itemlevel = 1
|  |  |  name = "双灵符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 12
|  |  |  |  stone_info = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_damage"
|  |  |  |  |  |  |  |  value = 80
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "abnormal_attr_ratio"
|  |  |  |  |  |  |  |  value = 80
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 340001
|  |  |  |  |  |  |  [2] = 340001
|  |  |  |  |  |  |  [3] = 340001
|  |  |  |  |  |  |  [4] = 340001
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_damage"
|  |  |  |  |  |  |  |  value = 200
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "abnormal_attr_ratio"
|  |  |  |  |  |  |  |  value = 150
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 340002
|  |  |  |  |  |  |  [2] = 340002
|  |  |  |  |  |  |  [3] = 340002
|  |  |  |  |  |  |  [4] = 340002
|  |  |  |  |  |  |  [5] = 340002
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6401004
|  |  }
|  |  [75] = {
|  |  |  amount = 1
|  |  |  create_time = 1723500879
|  |  |  id = 337
|  |  |  itemlevel = 1
|  |  |  name = "双灵符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 19
|  |  |  |  stone_info = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_damage"
|  |  |  |  |  |  |  |  value = 80
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "abnormal_attr_ratio"
|  |  |  |  |  |  |  |  value = 80
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 340001
|  |  |  |  |  |  |  [2] = 340001
|  |  |  |  |  |  |  [3] = 340001
|  |  |  |  |  |  |  [4] = 340001
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_damage"
|  |  |  |  |  |  |  |  value = 200
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "abnormal_attr_ratio"
|  |  |  |  |  |  |  |  value = 150
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 340002
|  |  |  |  |  |  |  [2] = 340002
|  |  |  |  |  |  |  [3] = 340002
|  |  |  |  |  |  |  [4] = 340002
|  |  |  |  |  |  |  [5] = 340002
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_damage"
|  |  |  |  |  |  |  |  value = 240
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "abnormal_attr_ratio"
|  |  |  |  |  |  |  |  value = 160
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 3
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 340003
|  |  |  |  |  |  |  [2] = 340003
|  |  |  |  |  |  |  [3] = 340003
|  |  |  |  |  |  |  [4] = 340003
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [4] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_damage"
|  |  |  |  |  |  |  |  value = 70
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "abnormal_attr_ratio"
|  |  |  |  |  |  |  |  value = 60
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 4
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 340004
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6402007
|  |  }
|  |  [76] = {
|  |  |  amount = 1
|  |  |  create_time = 1723500877
|  |  |  id = 338
|  |  |  itemlevel = 1
|  |  |  name = "双灵符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 9
|  |  |  |  stone_info = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_damage"
|  |  |  |  |  |  |  |  value = 80
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "abnormal_attr_ratio"
|  |  |  |  |  |  |  |  value = 80
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 340001
|  |  |  |  |  |  |  [2] = 340001
|  |  |  |  |  |  |  [3] = 340001
|  |  |  |  |  |  |  [4] = 340001
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_damage"
|  |  |  |  |  |  |  |  value = 200
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "abnormal_attr_ratio"
|  |  |  |  |  |  |  |  value = 150
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 340002
|  |  |  |  |  |  |  [2] = 340002
|  |  |  |  |  |  |  [3] = 340002
|  |  |  |  |  |  |  [4] = 340002
|  |  |  |  |  |  |  [5] = 340002
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6402001
|  |  }
|  |  [77] = {
|  |  |  amount = 1
|  |  |  create_time = 1723494669
|  |  |  id = 339
|  |  |  itemlevel = 1
|  |  |  name = "气血符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 19
|  |  |  |  stone_info = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  value = 300
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 320001
|  |  |  |  |  |  |  [2] = 320001
|  |  |  |  |  |  |  [3] = 320001
|  |  |  |  |  |  |  [4] = 320001
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  value = 565
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 320002
|  |  |  |  |  |  |  [2] = 320002
|  |  |  |  |  |  |  [3] = 320002
|  |  |  |  |  |  |  [4] = 320002
|  |  |  |  |  |  |  [5] = 320002
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  value = 750
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 3
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 320003
|  |  |  |  |  |  |  [2] = 320003
|  |  |  |  |  |  |  [3] = 320003
|  |  |  |  |  |  |  [4] = 320003
|  |  |  |  |  |  |  [5] = 320003
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [4] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  value = 1128
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 4
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 320004
|  |  |  |  |  |  |  [2] = 320004
|  |  |  |  |  |  |  [3] = 320004
|  |  |  |  |  |  |  [4] = 320004
|  |  |  |  |  |  |  [5] = 320004
|  |  |  |  |  |  |  [6] = 320004
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  stone_level = 2
|  |  |  }
|  |  |  sid = 6202002
|  |  }
|  |  [78] = {
|  |  |  amount = 1
|  |  |  create_time = 1723494655
|  |  |  id = 340
|  |  |  itemlevel = 1
|  |  |  name = "防御符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 18
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6301001
|  |  }
|  |  [79] = {
|  |  |  amount = 1
|  |  |  create_time = 1723494654
|  |  |  id = 341
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 18
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101001
|  |  }
|  |  [8] = {
|  |  |  amount = 93
|  |  |  create_time = 1724187113
|  |  |  id = 8
|  |  |  itemlevel = 2
|  |  |  name = "一星云母"
|  |  |  sid = 14031
|  |  }
|  |  [80] = {
|  |  |  amount = 1
|  |  |  create_time = 1723494653
|  |  |  id = 342
|  |  |  itemlevel = 1
|  |  |  name = "气血符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 18
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6201001
|  |  }
|  |  [81] = {
|  |  |  amount = 1
|  |  |  create_time = 1723366388
|  |  |  id = 343
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 3
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101001
|  |  }
|  |  [82] = {
|  |  |  amount = 1
|  |  |  create_time = 1723494639
|  |  |  id = 344
|  |  |  itemlevel = 1
|  |  |  name = "气血符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 9
|  |  |  |  stone_info = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  value = 300
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 320001
|  |  |  |  |  |  |  [2] = 320001
|  |  |  |  |  |  |  [3] = 320001
|  |  |  |  |  |  |  [4] = 320001
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  value = 565
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 320002
|  |  |  |  |  |  |  [2] = 320002
|  |  |  |  |  |  |  [3] = 320002
|  |  |  |  |  |  |  [4] = 320002
|  |  |  |  |  |  |  [5] = 320002
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6201004
|  |  }
|  |  [83] = {
|  |  |  amount = 1
|  |  |  create_time = 1723494432
|  |  |  id = 304
|  |  |  itemlevel = 1
|  |  |  name = "防御符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 2
|  |  |  |  stone_info = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  value = 80
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "res_abnormal_ratio"
|  |  |  |  |  |  |  |  value = 80
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 330001
|  |  |  |  |  |  |  [2] = 330001
|  |  |  |  |  |  |  [3] = 330001
|  |  |  |  |  |  |  [4] = 330001
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  value = 150
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "res_abnormal_ratio"
|  |  |  |  |  |  |  |  value = 150
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sids = {
|  |  |  |  |  |  |  [1] = 330002
|  |  |  |  |  |  |  [2] = 330002
|  |  |  |  |  |  |  [3] = 330002
|  |  |  |  |  |  |  [4] = 330002
|  |  |  |  |  |  |  [5] = 330002
|  |  |  |  |  |  }
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6301008
|  |  }
|  |  [84] = {
|  |  |  amount = 1
|  |  |  create_time = 1723987646
|  |  |  id = 305
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 10
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101001
|  |  }
|  |  [85] = {
|  |  |  amount = 1
|  |  |  create_time = 1723987647
|  |  |  id = 306
|  |  |  itemlevel = 1
|  |  |  name = "气血符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 10
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6201001
|  |  }
|  |  [86] = {
|  |  |  amount = 1
|  |  |  create_time = 1723987648
|  |  |  id = 307
|  |  |  itemlevel = 1
|  |  |  name = "防御符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 10
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6301001
|  |  }
|  |  [87] = {
|  |  |  amount = 1
|  |  |  create_time = 1723735921
|  |  |  id = 308
|  |  |  itemlevel = 1
|  |  |  name = "气血符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 15
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6201001
|  |  }
|  |  [88] = {
|  |  |  amount = 1
|  |  |  create_time = 1723735924
|  |  |  id = 309
|  |  |  itemlevel = 1
|  |  |  name = "防御符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 15
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6301001
|  |  }
|  |  [89] = {
|  |  |  amount = 1
|  |  |  create_time = 1723735925
|  |  |  id = 310
|  |  |  itemlevel = 1
|  |  |  name = "双灵符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 15
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6401001
|  |  }
|  |  [9] = {
|  |  |  amount = 1
|  |  |  create_time = 1723501674
|  |  |  id = 9
|  |  |  itemlevel = 2
|  |  |  name = "包裹"
|  |  |  sid = 11622
|  |  }
|  |  [90] = {
|  |  |  amount = 1
|  |  |  create_time = 1723987655
|  |  |  id = 311
|  |  |  itemlevel = 1
|  |  |  name = "防御符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 14
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6301001
|  |  }
|  |  [91] = {
|  |  |  amount = 1
|  |  |  create_time = 1723987657
|  |  |  id = 312
|  |  |  itemlevel = 1
|  |  |  name = "双灵符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 14
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6401001
|  |  }
|  |  [92] = {
|  |  |  amount = 1
|  |  |  create_time = 1723736415
|  |  |  id = 313
|  |  |  itemlevel = 1
|  |  |  name = "双灵符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 18
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6401001
|  |  }
|  |  [93] = {
|  |  |  amount = 1
|  |  |  create_time = 1723736504
|  |  |  id = 314
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 6
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101001
|  |  }
|  |  [94] = {
|  |  |  amount = 1
|  |  |  create_time = 1723736506
|  |  |  id = 315
|  |  |  itemlevel = 1
|  |  |  name = "气血符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 6
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6201001
|  |  }
|  |  [95] = {
|  |  |  amount = 1
|  |  |  create_time = 1723736507
|  |  |  id = 316
|  |  |  itemlevel = 1
|  |  |  name = "防御符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 6
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6301001
|  |  }
|  |  [96] = {
|  |  |  amount = 1
|  |  |  create_time = 1723736508
|  |  |  id = 317
|  |  |  itemlevel = 1
|  |  |  name = "双灵符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 6
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6401001
|  |  }
|  |  [97] = {
|  |  |  amount = 1
|  |  |  create_time = 1723987654
|  |  |  id = 318
|  |  |  itemlevel = 1
|  |  |  name = "气血符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 14
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6201001
|  |  }
|  |  [98] = {
|  |  |  amount = 1
|  |  |  create_time = 1723987653
|  |  |  id = 319
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 14
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101001
|  |  }
|  |  [99] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "critical_damage"
|  |  |  |  |  value = 571
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 206
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "lock"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 320
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723998119
|  |  |  id = 288
|  |  |  itemlevel = 4
|  |  |  name = "钧天戒·煌"
|  |  |  power = 320
|  |  |  sid = 4205501
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>loginitem</color>
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayRedDot = {}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayMain = {
|  already_get = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  }
|  cur_point = 21
|  server_day = 10
}
[core/table.lua:94]:-->Net Receive: task.GS2CLoginAchieveTask = {
|  info = {
|  |  [1] = {
|  |  |  achievetype = 2
|  |  |  describe = "在喵萌茶会战斗中胜利1次"
|  |  |  name = "喵萌茶会"
|  |  |  target = 1
|  |  |  taskid = 31518
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>LoginAchieveTask</color>
[core/table.lua:94]:-->Net Receive: item.GS2CLoginItem = {
|  itemdata = {
|  |  [1] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998244
|  |  |  id = 43
|  |  |  itemlevel = 3
|  |  |  name = "流臾之须·防御"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7430303
|  |  }
|  |  [10] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998254
|  |  |  id = 52
|  |  |  itemlevel = 4
|  |  |  name = "漓渚·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7240408
|  |  }
|  |  [100] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 142
|  |  |  itemlevel = 4
|  |  |  name = "诡象之匣·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7080412
|  |  }
|  |  [101] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084461
|  |  |  id = 143
|  |  |  itemlevel = 2
|  |  |  name = "墨菲·气血"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7020201
|  |  }
|  |  [102] = {
|  |  |  amount = 1
|  |  |  create_time = 1723815775
|  |  |  id = 144
|  |  |  itemlevel = 4
|  |  |  name = "红雨将军·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 18
|  |  |  }
|  |  |  sid = 7100402
|  |  }
|  |  [103] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 145
|  |  |  itemlevel = 2
|  |  |  name = "一阕秋·异常抵抗"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7450210
|  |  }
|  |  [104] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 146
|  |  |  itemlevel = 5
|  |  |  name = "流莺·异常抵抗"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7250110
|  |  }
|  |  [105] = {
|  |  |  amount = 1
|  |  |  create_time = 1723815810
|  |  |  id = 147
|  |  |  itemlevel = 4
|  |  |  name = "红雨将军·暴击伤害"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 18
|  |  |  }
|  |  |  sid = 7100407
|  |  }
|  |  [106] = {
|  |  |  amount = 1
|  |  |  create_time = 1723815829
|  |  |  id = 148
|  |  |  itemlevel = 4
|  |  |  name = "红雨将军·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 18
|  |  |  }
|  |  |  sid = 7100411
|  |  }
|  |  [107] = {
|  |  |  amount = 1
|  |  |  create_time = 1723815848
|  |  |  id = 149
|  |  |  itemlevel = 4
|  |  |  name = "红雨将军·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 18
|  |  |  }
|  |  |  sid = 7100408
|  |  }
|  |  [108] = {
|  |  |  amount = 1
|  |  |  create_time = 1723815949
|  |  |  id = 150
|  |  |  itemlevel = 6
|  |  |  name = "洛迦·暴击伤害"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 16
|  |  |  }
|  |  |  sid = 7060507
|  |  }
|  |  [109] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 151
|  |  |  itemlevel = 5
|  |  |  name = "博戏鬼·暴击伤害"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7030107
|  |  }
|  |  [11] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998254
|  |  |  id = 53
|  |  |  itemlevel = 2
|  |  |  name = "洛迦·异常抵抗"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 16
|  |  |  }
|  |  |  sid = 7060210
|  |  }
|  |  [110] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 152
|  |  |  itemlevel = 3
|  |  |  name = "陶磊磊·暴击伤害"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7340307
|  |  }
|  |  [111] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998297
|  |  |  id = 153
|  |  |  itemlevel = 4
|  |  |  name = "巳日时君·暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7350405
|  |  }
|  |  [112] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998297
|  |  |  id = 154
|  |  |  itemlevel = 4
|  |  |  name = "颜无月·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7220412
|  |  }
|  |  [113] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 155
|  |  |  itemlevel = 2
|  |  |  name = "流臾之须·防御"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7430203
|  |  }
|  |  [114] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998297
|  |  |  id = 156
|  |  |  itemlevel = 2
|  |  |  name = "斑曜·异常抵抗"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 20
|  |  |  }
|  |  |  sid = 7460210
|  |  }
|  |  [115] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998297
|  |  |  id = 157
|  |  |  itemlevel = 2
|  |  |  name = "黑帝·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 3
|  |  |  }
|  |  |  sid = 7050211
|  |  }
|  |  [116] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 158
|  |  |  itemlevel = 2
|  |  |  name = "红雨将军·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7100202
|  |  }
|  |  [117] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 159
|  |  |  itemlevel = 3
|  |  |  name = "一阕秋·异常抵抗"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7450310
|  |  }
|  |  [118] = {
|  |  |  amount = 1
|  |  |  create_time = 1723978450
|  |  |  id = 160
|  |  |  itemlevel = 4
|  |  |  name = "巳日时君·暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 5
|  |  |  }
|  |  |  sid = 7350405
|  |  }
|  |  [119] = {
|  |  |  amount = 1
|  |  |  create_time = 1723504261
|  |  |  id = 161
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 6
|  |  |  }
|  |  |  sid = 7050402
|  |  }
|  |  [12] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998254
|  |  |  id = 54
|  |  |  itemlevel = 3
|  |  |  name = "黑帝·防御"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 4
|  |  |  }
|  |  |  sid = 7050303
|  |  }
|  |  [120] = {
|  |  |  amount = 1
|  |  |  create_time = 1723504270
|  |  |  id = 162
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·攻击"
|  |  |  partner_soul = {
|  |  |  |  exp = 88800
|  |  |  |  level = 5
|  |  |  |  parid = 19
|  |  |  }
|  |  |  sid = 7050402
|  |  }
|  |  [121] = {
|  |  |  amount = 1
|  |  |  create_time = 1723504280
|  |  |  id = 163
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 19
|  |  |  }
|  |  |  sid = 7050405
|  |  }
|  |  [122] = {
|  |  |  amount = 1
|  |  |  create_time = 1723504290
|  |  |  id = 164
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 6
|  |  |  }
|  |  |  sid = 7050405
|  |  }
|  |  [123] = {
|  |  |  amount = 1
|  |  |  create_time = 1723504299
|  |  |  id = 165
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·暴击伤害"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 6
|  |  |  }
|  |  |  sid = 7050407
|  |  }
|  |  [124] = {
|  |  |  amount = 1
|  |  |  create_time = 1723504309
|  |  |  id = 166
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·暴击伤害"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 19
|  |  |  }
|  |  |  sid = 7050407
|  |  }
|  |  [125] = {
|  |  |  amount = 1
|  |  |  create_time = 1723504323
|  |  |  id = 167
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 6
|  |  |  }
|  |  |  sid = 7050412
|  |  }
|  |  [126] = {
|  |  |  amount = 1
|  |  |  create_time = 1723504333
|  |  |  id = 168
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 19
|  |  |  }
|  |  |  sid = 7050412
|  |  }
|  |  [127] = {
|  |  |  amount = 1
|  |  |  create_time = 1723504353
|  |  |  id = 169
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 19
|  |  |  }
|  |  |  sid = 7050411
|  |  }
|  |  [128] = {
|  |  |  amount = 1
|  |  |  create_time = 1723504364
|  |  |  id = 170
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 6
|  |  |  }
|  |  |  sid = 7050411
|  |  }
|  |  [129] = {
|  |  |  amount = 1
|  |  |  create_time = 1723668970
|  |  |  id = 171
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·攻击"
|  |  |  partner_soul = {
|  |  |  |  exp = 29600
|  |  |  |  level = 4
|  |  |  |  parid = 14
|  |  |  }
|  |  |  sid = 7050402
|  |  }
|  |  [13] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998254
|  |  |  id = 55
|  |  |  itemlevel = 3
|  |  |  name = "斑曜·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 20
|  |  |  }
|  |  |  sid = 7460311
|  |  }
|  |  [130] = {
|  |  |  amount = 1
|  |  |  create_time = 1723540060
|  |  |  id = 172
|  |  |  itemlevel = 3
|  |  |  name = "黑帝·暴击伤害"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 3
|  |  |  }
|  |  |  sid = 7050307
|  |  }
|  |  [131] = {
|  |  |  amount = 1
|  |  |  create_time = 1723668998
|  |  |  id = 173
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 14
|  |  |  }
|  |  |  sid = 7050405
|  |  }
|  |  [132] = {
|  |  |  amount = 1
|  |  |  create_time = 1723540060
|  |  |  id = 174
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·速度"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 3
|  |  |  }
|  |  |  sid = 7050404
|  |  }
|  |  [133] = {
|  |  |  amount = 1
|  |  |  create_time = 1723669047
|  |  |  id = 175
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 14
|  |  |  }
|  |  |  sid = 7050411
|  |  }
|  |  [134] = {
|  |  |  amount = 1
|  |  |  create_time = 1723669122
|  |  |  id = 176
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 9
|  |  |  }
|  |  |  sid = 7050411
|  |  }
|  |  [135] = {
|  |  |  amount = 1
|  |  |  create_time = 1723540060
|  |  |  id = 177
|  |  |  itemlevel = 4
|  |  |  name = "红雨将军·异常命中"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 18
|  |  |  }
|  |  |  sid = 7100409
|  |  }
|  |  [136] = {
|  |  |  amount = 1
|  |  |  create_time = 1723669280
|  |  |  id = 178
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 4
|  |  |  }
|  |  |  sid = 7050411
|  |  }
|  |  [137] = {
|  |  |  amount = 1
|  |  |  create_time = 1723669355
|  |  |  id = 179
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 19
|  |  |  }
|  |  |  sid = 7050408
|  |  }
|  |  [138] = {
|  |  |  amount = 1
|  |  |  create_time = 1723539404
|  |  |  id = 180
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·攻击"
|  |  |  partner_soul = {
|  |  |  |  exp = 40800
|  |  |  |  level = 4
|  |  |  |  parid = 9
|  |  |  }
|  |  |  sid = 7050402
|  |  }
|  |  [139] = {
|  |  |  amount = 1
|  |  |  create_time = 1723978460
|  |  |  id = 181
|  |  |  itemlevel = 4
|  |  |  name = "血衣客·暴击伤害"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7010407
|  |  }
|  |  [14] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998264
|  |  |  id = 56
|  |  |  itemlevel = 6
|  |  |  name = "归无处·抗暴"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7040506
|  |  }
|  |  [140] = {
|  |  |  amount = 1
|  |  |  create_time = 1723492270
|  |  |  id = 182
|  |  |  itemlevel = 6
|  |  |  name = "流臾之须·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  exp = 50000
|  |  |  |  level = 4
|  |  |  |  parid = 12
|  |  |  }
|  |  |  sid = 7430508
|  |  }
|  |  [141] = {
|  |  |  amount = 1
|  |  |  create_time = 1723539439
|  |  |  id = 183
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 9
|  |  |  }
|  |  |  sid = 7050412
|  |  }
|  |  [142] = {
|  |  |  amount = 1
|  |  |  create_time = 1723539456
|  |  |  id = 184
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·暴击"
|  |  |  partner_soul = {
|  |  |  |  exp = 70600
|  |  |  |  level = 5
|  |  |  |  parid = 9
|  |  |  }
|  |  |  sid = 7050405
|  |  }
|  |  [143] = {
|  |  |  amount = 1
|  |  |  create_time = 1723539464
|  |  |  id = 185
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 4
|  |  |  }
|  |  |  sid = 7050405
|  |  }
|  |  [144] = {
|  |  |  amount = 1
|  |  |  create_time = 1723539937
|  |  |  id = 186
|  |  |  itemlevel = 6
|  |  |  name = "陶磊磊·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7340512
|  |  }
|  |  [145] = {
|  |  |  amount = 1
|  |  |  create_time = 1723669271
|  |  |  id = 187
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·暴击伤害"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 9
|  |  |  }
|  |  |  sid = 7050407
|  |  }
|  |  [146] = {
|  |  |  amount = 1
|  |  |  create_time = 1723669231
|  |  |  id = 188
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·气血"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 6
|  |  |  }
|  |  |  sid = 7050401
|  |  }
|  |  [147] = {
|  |  |  amount = 1
|  |  |  create_time = 1723669091
|  |  |  id = 189
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·暴击伤害"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 4
|  |  |  }
|  |  |  sid = 7050407
|  |  }
|  |  [148] = {
|  |  |  amount = 1
|  |  |  create_time = 1723669008
|  |  |  id = 190
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·暴击伤害"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 14
|  |  |  }
|  |  |  sid = 7050407
|  |  }
|  |  [149] = {
|  |  |  amount = 1
|  |  |  create_time = 1723668981
|  |  |  id = 191
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 14
|  |  |  }
|  |  |  sid = 7050412
|  |  }
|  |  [15] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998264
|  |  |  id = 57
|  |  |  itemlevel = 4
|  |  |  name = "颜无月·抗暴"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7220406
|  |  }
|  |  [150] = {
|  |  |  amount = 1
|  |  |  create_time = 1723815976
|  |  |  id = 192
|  |  |  itemlevel = 6
|  |  |  name = "流臾之须·速度"
|  |  |  partner_soul = {
|  |  |  |  exp = 181400
|  |  |  |  level = 6
|  |  |  |  parid = 12
|  |  |  }
|  |  |  sid = 7430504
|  |  }
|  |  [151] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084452
|  |  |  id = 193
|  |  |  itemlevel = 3
|  |  |  name = "墨菲·速度"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7020304
|  |  }
|  |  [152] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084452
|  |  |  id = 194
|  |  |  itemlevel = 2
|  |  |  name = "锵明·异常命中"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7420209
|  |  }
|  |  [153] = {
|  |  |  amount = 1
|  |  |  create_time = 1723539429
|  |  |  id = 195
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 4
|  |  |  }
|  |  |  sid = 7050412
|  |  }
|  |  [154] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084452
|  |  |  id = 196
|  |  |  itemlevel = 2
|  |  |  name = "墨菲·防御加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7020213
|  |  }
|  |  [155] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084461
|  |  |  id = 197
|  |  |  itemlevel = 3
|  |  |  name = "漓渚·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7240302
|  |  }
|  |  [156] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998288
|  |  |  id = 198
|  |  |  itemlevel = 3
|  |  |  name = "斑曜·暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 20
|  |  |  }
|  |  |  sid = 7460305
|  |  }
|  |  [157] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998288
|  |  |  id = 199
|  |  |  itemlevel = 3
|  |  |  name = "流莺·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 11
|  |  |  }
|  |  |  sid = 7250308
|  |  }
|  |  [158] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084461
|  |  |  id = 200
|  |  |  itemlevel = 5
|  |  |  name = "墨菲·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7020102
|  |  }
|  |  [159] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084461
|  |  |  id = 201
|  |  |  itemlevel = 5
|  |  |  name = "铃音·暴击伤害"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7330107
|  |  }
|  |  [16] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998264
|  |  |  id = 58
|  |  |  itemlevel = 4
|  |  |  name = "乌琪琪·速度"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 8
|  |  |  }
|  |  |  sid = 7090404
|  |  }
|  |  [160] = {
|  |  |  amount = 1
|  |  |  create_time = 1723815967
|  |  |  id = 202
|  |  |  itemlevel = 6
|  |  |  name = "星罗·异常命中"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7410509
|  |  }
|  |  [161] = {
|  |  |  amount = 1
|  |  |  create_time = 1723831751
|  |  |  id = 203
|  |  |  itemlevel = 6
|  |  |  name = "诡象之匣·防御"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7080503
|  |  }
|  |  [162] = {
|  |  |  amount = 1
|  |  |  create_time = 1723814648
|  |  |  id = 204
|  |  |  itemlevel = 4
|  |  |  name = "流臾之须·气血"
|  |  |  partner_soul = {
|  |  |  |  exp = 202400
|  |  |  |  level = 7
|  |  |  |  parid = 12
|  |  |  }
|  |  |  sid = 7430401
|  |  }
|  |  [163] = {
|  |  |  amount = 1
|  |  |  create_time = 1723814409
|  |  |  id = 205
|  |  |  itemlevel = 4
|  |  |  name = "锵明·异常抵抗"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 10
|  |  |  }
|  |  |  sid = 7420410
|  |  }
|  |  [164] = {
|  |  |  amount = 1
|  |  |  create_time = 1723814385
|  |  |  id = 206
|  |  |  itemlevel = 4
|  |  |  name = "锵明·防御加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 10
|  |  |  }
|  |  |  sid = 7420413
|  |  }
|  |  [165] = {
|  |  |  amount = 1
|  |  |  create_time = 1723814300
|  |  |  id = 207
|  |  |  itemlevel = 4
|  |  |  name = "锵明·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 10
|  |  |  }
|  |  |  sid = 7420411
|  |  }
|  |  [166] = {
|  |  |  amount = 1
|  |  |  create_time = 1723978450
|  |  |  id = 208
|  |  |  itemlevel = 4
|  |  |  name = "偃月无夜·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 1
|  |  |  }
|  |  |  sid = 7310411
|  |  }
|  |  [167] = {
|  |  |  amount = 1
|  |  |  create_time = 1723978450
|  |  |  id = 209
|  |  |  itemlevel = 5
|  |  |  name = "黑帝·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 14
|  |  |  }
|  |  |  sid = 7050108
|  |  }
|  |  [168] = {
|  |  |  amount = 1
|  |  |  create_time = 1723831769
|  |  |  id = 210
|  |  |  itemlevel = 6
|  |  |  name = "洛迦·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 16
|  |  |  }
|  |  |  sid = 7060508
|  |  }
|  |  [169] = {
|  |  |  amount = 1
|  |  |  create_time = 1723492221
|  |  |  id = 211
|  |  |  itemlevel = 6
|  |  |  name = "萧衍·抗暴"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7230506
|  |  }
|  |  [17] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998264
|  |  |  id = 59
|  |  |  itemlevel = 6
|  |  |  name = "巳日时君·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 5
|  |  |  }
|  |  |  sid = 7350502
|  |  }
|  |  [170] = {
|  |  |  amount = 1
|  |  |  create_time = 1723978460
|  |  |  id = 212
|  |  |  itemlevel = 4
|  |  |  name = "墨菲·异常命中"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 13
|  |  |  }
|  |  |  sid = 7020409
|  |  }
|  |  [171] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 213
|  |  |  itemlevel = 4
|  |  |  name = "花月巫·暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7360405
|  |  }
|  |  [172] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998236
|  |  |  id = 214
|  |  |  itemlevel = 3
|  |  |  name = "乌琪琪·抗暴"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 8
|  |  |  }
|  |  |  sid = 7090306
|  |  }
|  |  [173] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998244
|  |  |  id = 215
|  |  |  itemlevel = 2
|  |  |  name = "偃月无夜·异常抵抗"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 1
|  |  |  }
|  |  |  sid = 7310210
|  |  }
|  |  [174] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 216
|  |  |  itemlevel = 3
|  |  |  name = "流莺·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7250302
|  |  }
|  |  [175] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998236
|  |  |  id = 217
|  |  |  itemlevel = 2
|  |  |  name = "墨菲·抗暴"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 13
|  |  |  }
|  |  |  sid = 7020206
|  |  }
|  |  [176] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998236
|  |  |  id = 218
|  |  |  itemlevel = 2
|  |  |  name = "流莺·异常抵抗"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 11
|  |  |  }
|  |  |  sid = 7250210
|  |  }
|  |  [177] = {
|  |  |  amount = 1
|  |  |  create_time = 1724073347
|  |  |  id = 219
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 2
|  |  |  }
|  |  |  sid = 7050412
|  |  }
|  |  [178] = {
|  |  |  amount = 1
|  |  |  create_time = 1724188607
|  |  |  id = 220
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·异常抵抗"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 15
|  |  |  }
|  |  |  sid = 7050410
|  |  }
|  |  [179] = {
|  |  |  amount = 1
|  |  |  create_time = 1724188595
|  |  |  id = 221
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 15
|  |  |  }
|  |  |  sid = 7050408
|  |  }
|  |  [18] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998264
|  |  |  id = 60
|  |  |  itemlevel = 3
|  |  |  name = "博戏鬼·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 7
|  |  |  }
|  |  |  sid = 7030308
|  |  }
|  |  [180] = {
|  |  |  amount = 1
|  |  |  create_time = 1724188582
|  |  |  id = 222
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 15
|  |  |  }
|  |  |  sid = 7050411
|  |  }
|  |  [181] = {
|  |  |  amount = 1
|  |  |  create_time = 1724188573
|  |  |  id = 223
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·防御加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 15
|  |  |  }
|  |  |  sid = 7050413
|  |  }
|  |  [182] = {
|  |  |  amount = 1
|  |  |  create_time = 1724188549
|  |  |  id = 224
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·防御"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 15
|  |  |  }
|  |  |  sid = 7050403
|  |  }
|  |  [183] = {
|  |  |  amount = 1
|  |  |  create_time = 1724188473
|  |  |  id = 225
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·气血"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 15
|  |  |  }
|  |  |  sid = 7050401
|  |  }
|  |  [184] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 592
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  value = 181
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 682
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723500523
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_damage"
|  |  |  |  |  |  |  |  quality = 3
|  |  |  |  |  |  |  |  value = 748
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  quality = 3
|  |  |  |  |  |  |  |  value = 313
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  level = 50
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 1
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 3
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  level = 50
|  |  |  |  |  |  plan = 2
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  gem_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 30
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sid = 18004
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 30
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sid = 18004
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 30
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 3
|  |  |  |  |  |  sid = 18004
|  |  |  |  |  }
|  |  |  |  |  [4] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 44
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 24300
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 4
|  |  |  |  |  |  sid = 18005
|  |  |  |  |  }
|  |  |  |  |  [5] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 66
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 72900
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 5
|  |  |  |  |  |  sid = 18006
|  |  |  |  |  }
|  |  |  |  |  [6] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  value = 44
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 24300
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 6
|  |  |  |  |  |  sid = 18005
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  pos = 1
|  |  |  |  stone_sid = 3206500
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  value = 461
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  value = 170
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  |  value = 48
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 384
|  |  |  itemlevel = 4
|  |  |  name = "向日·零"
|  |  |  power = 682
|  |  |  sid = 2110500
|  |  }
|  |  [185] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 1645
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  value = 114
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 386
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723742938
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "res_abnormal_ratio"
|  |  |  |  |  |  |  |  quality = 3
|  |  |  |  |  |  |  |  value = 498
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  quality = 3
|  |  |  |  |  |  |  |  value = 417
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  level = 50
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 1
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 17
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  level = 50
|  |  |  |  |  |  plan = 2
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  gem_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "res_critical_ratio"
|  |  |  |  |  |  |  |  value = 60
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sid = 18104
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "res_critical_ratio"
|  |  |  |  |  |  |  |  value = 60
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sid = 18104
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "res_critical_ratio"
|  |  |  |  |  |  |  |  value = 60
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 3
|  |  |  |  |  |  sid = 18104
|  |  |  |  |  }
|  |  |  |  |  [4] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "res_critical_ratio"
|  |  |  |  |  |  |  |  value = 70
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 24300
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 4
|  |  |  |  |  |  sid = 18105
|  |  |  |  |  }
|  |  |  |  |  [5] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "res_critical_ratio"
|  |  |  |  |  |  |  |  value = 60
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 5
|  |  |  |  |  |  sid = 18104
|  |  |  |  |  }
|  |  |  |  |  [6] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "res_critical_ratio"
|  |  |  |  |  |  |  |  value = 60
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 6
|  |  |  |  |  |  sid = 18104
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  pos = 2
|  |  |  |  stone_sid = 4005501
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  value = 1494
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  value = 110
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  |  value = 47
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 389
|  |  |  itemlevel = 4
|  |  |  name = "钧天链·煌"
|  |  |  power = 386
|  |  |  sid = 2200500
|  |  }
|  |  [186] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "res_critical_ratio"
|  |  |  |  |  value = 377
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 220
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 408
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723811277
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "abnormal_attr_ratio"
|  |  |  |  |  |  |  |  quality = 2
|  |  |  |  |  |  |  |  value = 355
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  quality = 3
|  |  |  |  |  |  |  |  value = 313
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  level = 60
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 1
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 17
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  level = 60
|  |  |  |  |  |  plan = 2
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  gem_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  |  |  value = 10
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sid = 18204
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  |  |  value = 10
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sid = 18204
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  |  |  value = 10
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 3
|  |  |  |  |  |  sid = 18204
|  |  |  |  |  }
|  |  |  |  |  [4] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  |  |  value = 10
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 4
|  |  |  |  |  |  sid = 18204
|  |  |  |  |  }
|  |  |  |  |  [5] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  |  |  value = 10
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 5
|  |  |  |  |  |  sid = 18204
|  |  |  |  |  }
|  |  |  |  |  [6] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  |  |  value = 10
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 6
|  |  |  |  |  |  sid = 18204
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  pos = 3
|  |  |  |  stone_sid = 4104600
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  value = 197
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  key = "res_critical_ratio"
|  |  |  |  |  |  value = 110
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  |  value = 47
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 385
|  |  |  itemlevel = 3
|  |  |  name = "黄泉风铠"
|  |  |  power = 408
|  |  |  sid = 2310600
|  |  }
|  |  [187] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "critical_damage"
|  |  |  |  |  value = 593
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 204
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 322
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723742939
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_damage"
|  |  |  |  |  |  |  |  quality = 3
|  |  |  |  |  |  |  |  value = 957
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "res_abnormal_ratio"
|  |  |  |  |  |  |  |  quality = 3
|  |  |  |  |  |  |  |  value = 437
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  level = 50
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 3
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "speed"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 7
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  level = 50
|  |  |  |  |  |  plan = 2
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  gem_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  value = 100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sid = 18304
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  value = 100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sid = 18304
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  value = 100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 3
|  |  |  |  |  |  sid = 18304
|  |  |  |  |  }
|  |  |  |  |  [4] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  value = 100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 4
|  |  |  |  |  |  sid = 18304
|  |  |  |  |  }
|  |  |  |  |  [5] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  value = 100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 5
|  |  |  |  |  |  sid = 18304
|  |  |  |  |  }
|  |  |  |  |  [6] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  value = 100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 6
|  |  |  |  |  |  sid = 18304
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  pos = 4
|  |  |  |  stone_sid = 4205501
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "critical_damage"
|  |  |  |  |  |  value = 740
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  value = 150
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  |  value = 47
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 388
|  |  |  itemlevel = 4
|  |  |  name = "钧天戒·煌"
|  |  |  power = 322
|  |  |  sid = 2400500
|  |  }
|  |  [188] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "res_critical_ratio"
|  |  |  |  |  value = 120
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 4032
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 866
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723811263
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "abnormal_attr_ratio"
|  |  |  |  |  |  |  |  quality = 2
|  |  |  |  |  |  |  |  value = 262
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "critical_damage"
|  |  |  |  |  |  |  |  quality = 3
|  |  |  |  |  |  |  |  value = 791
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  level = 60
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 3
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 17
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  level = 60
|  |  |  |  |  |  plan = 2
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  gem_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  value = 264
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sid = 18404
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  value = 264
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sid = 18404
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  value = 264
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 3
|  |  |  |  |  |  sid = 18404
|  |  |  |  |  }
|  |  |  |  |  [4] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  value = 264
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 4
|  |  |  |  |  |  sid = 18404
|  |  |  |  |  }
|  |  |  |  |  [5] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  value = 264
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 5
|  |  |  |  |  |  sid = 18404
|  |  |  |  |  }
|  |  |  |  |  [6] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  |  |  value = 264
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 6
|  |  |  |  |  |  sid = 18404
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  pos = 5
|  |  |  |  stone_sid = 4304600
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "res_critical_ratio"
|  |  |  |  |  |  value = 40
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  key = "maxhp"
|  |  |  |  |  |  value = 4483
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  |  value = 47
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 386
|  |  |  itemlevel = 3
|  |  |  name = "血灼腰带"
|  |  |  power = 866
|  |  |  sid = 2510600
|  |  }
|  |  [189] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 69
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "speed"
|  |  |  |  |  value = 91
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 251
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1723811229
|  |  |  equip_info = {
|  |  |  |  fuwen = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "critical_damage"
|  |  |  |  |  |  |  |  quality = 2
|  |  |  |  |  |  |  |  value = 702
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "critical_ratio"
|  |  |  |  |  |  |  |  quality = 3
|  |  |  |  |  |  |  |  value = 421
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  level = 60
|  |  |  |  |  |  plan = 1
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  fuwen_attr = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "attack"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 3
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "speed"
|  |  |  |  |  |  |  |  quality = 1
|  |  |  |  |  |  |  |  value = 7
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  level = 60
|  |  |  |  |  |  plan = 2
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  fuwen_plan = 1
|  |  |  |  gem_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "speed"
|  |  |  |  |  |  |  |  value = 19
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 1
|  |  |  |  |  |  sid = 18504
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "speed"
|  |  |  |  |  |  |  |  value = 19
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 2
|  |  |  |  |  |  sid = 18504
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "speed"
|  |  |  |  |  |  |  |  value = 19
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 3
|  |  |  |  |  |  sid = 18504
|  |  |  |  |  }
|  |  |  |  |  [4] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "speed"
|  |  |  |  |  |  |  |  value = 19
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 4
|  |  |  |  |  |  sid = 18504
|  |  |  |  |  }
|  |  |  |  |  [5] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "speed"
|  |  |  |  |  |  |  |  value = 19
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 5
|  |  |  |  |  |  sid = 18504
|  |  |  |  |  }
|  |  |  |  |  [6] = {
|  |  |  |  |  |  apply_info = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  key = "speed"
|  |  |  |  |  |  |  |  value = 19
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  key = "exp"
|  |  |  |  |  |  |  |  value = 8100
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pos = 6
|  |  |  |  |  |  sid = 18504
|  |  |  |  |  }
|  |  |  |  }
|  |  |  |  pos = 6
|  |  |  |  stone_sid = 4404600
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "defense"
|  |  |  |  |  |  value = 66
|  |  |  |  |  }
|  |  |  |  |  [2] = {
|  |  |  |  |  |  key = "speed"
|  |  |  |  |  |  value = 34
|  |  |  |  |  }
|  |  |  |  |  [3] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  |  value = 47
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 390
|  |  |  itemlevel = 3
|  |  |  name = "练钧猛履"
|  |  |  power = 251
|  |  |  sid = 2610600
|  |  }
|  |  [19] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998277
|  |  |  id = 61
|  |  |  itemlevel = 4
|  |  |  name = "花月巫·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7360408
|  |  }
|  |  [2] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998244
|  |  |  id = 44
|  |  |  itemlevel = 3
|  |  |  name = "墨菲·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 13
|  |  |  }
|  |  |  sid = 7020308
|  |  }
|  |  [20] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998277
|  |  |  id = 62
|  |  |  itemlevel = 3
|  |  |  name = "洛迦·防御"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 16
|  |  |  }
|  |  |  sid = 7060303
|  |  }
|  |  [21] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998277
|  |  |  id = 63
|  |  |  itemlevel = 6
|  |  |  name = "墨菲·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 13
|  |  |  }
|  |  |  sid = 7020511
|  |  }
|  |  [22] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998277
|  |  |  id = 64
|  |  |  itemlevel = 4
|  |  |  name = "花月巫·异常命中"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7360409
|  |  }
|  |  [23] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998277
|  |  |  id = 65
|  |  |  itemlevel = 5
|  |  |  name = "流臾之须·暴击伤害"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7430107
|  |  }
|  |  [24] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998277
|  |  |  id = 66
|  |  |  itemlevel = 2
|  |  |  name = "乌琪琪·防御加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 8
|  |  |  }
|  |  |  sid = 7090213
|  |  }
|  |  [25] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998277
|  |  |  id = 67
|  |  |  itemlevel = 4
|  |  |  name = "漓渚·异常命中"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7240409
|  |  }
|  |  [26] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998277
|  |  |  id = 68
|  |  |  itemlevel = 4
|  |  |  name = "流臾之须·异常抵抗"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7430410
|  |  }
|  |  [27] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998277
|  |  |  id = 69
|  |  |  itemlevel = 3
|  |  |  name = "巳日时君·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 5
|  |  |  }
|  |  |  sid = 7350308
|  |  }
|  |  [28] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084464
|  |  |  id = 70
|  |  |  itemlevel = 2
|  |  |  name = "墨菲·暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7020205
|  |  }
|  |  [29] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084464
|  |  |  id = 71
|  |  |  itemlevel = 5
|  |  |  name = "漓渚·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7240112
|  |  }
|  |  [3] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998244
|  |  |  id = 45
|  |  |  itemlevel = 2
|  |  |  name = "流莺·防御"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 11
|  |  |  }
|  |  |  sid = 7250203
|  |  }
|  |  [30] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084464
|  |  |  id = 72
|  |  |  itemlevel = 5
|  |  |  name = "漓渚·异常命中"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7240109
|  |  }
|  |  [31] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084464
|  |  |  id = 73
|  |  |  itemlevel = 3
|  |  |  name = "锵明·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7420308
|  |  }
|  |  [32] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084464
|  |  |  id = 74
|  |  |  itemlevel = 3
|  |  |  name = "铃音·异常命中"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7330309
|  |  }
|  |  [33] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 75
|  |  |  itemlevel = 3
|  |  |  name = "花月巫·抗暴"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7360306
|  |  }
|  |  [34] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 76
|  |  |  itemlevel = 5
|  |  |  name = "陶磊磊·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7340112
|  |  }
|  |  [35] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084461
|  |  |  id = 77
|  |  |  itemlevel = 2
|  |  |  name = "诡象之匣·气血"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7080201
|  |  }
|  |  [36] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 78
|  |  |  itemlevel = 2
|  |  |  name = "诡象之匣·异常命中"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7080209
|  |  }
|  |  [37] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 79
|  |  |  itemlevel = 2
|  |  |  name = "归无处·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7040202
|  |  }
|  |  [38] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 80
|  |  |  itemlevel = 3
|  |  |  name = "漓渚·防御"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7240303
|  |  }
|  |  [39] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 81
|  |  |  itemlevel = 2
|  |  |  name = "流莺·防御加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7250213
|  |  }
|  |  [4] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998244
|  |  |  id = 46
|  |  |  itemlevel = 2
|  |  |  name = "巳日时君·速度"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 5
|  |  |  }
|  |  |  sid = 7350204
|  |  }
|  |  [40] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 82
|  |  |  itemlevel = 5
|  |  |  name = "梓童·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7260111
|  |  }
|  |  [41] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 83
|  |  |  itemlevel = 5
|  |  |  name = "偃月无夜·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7310111
|  |  }
|  |  [42] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 84
|  |  |  itemlevel = 3
|  |  |  name = "流莺·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7250302
|  |  }
|  |  [43] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 85
|  |  |  itemlevel = 3
|  |  |  name = "陶磊磊·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7340308
|  |  }
|  |  [44] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 86
|  |  |  itemlevel = 2
|  |  |  name = "乌琪琪·暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7090205
|  |  }
|  |  [45] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998277
|  |  |  id = 87
|  |  |  itemlevel = 2
|  |  |  name = "流莺·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 11
|  |  |  }
|  |  |  sid = 7250202
|  |  }
|  |  [46] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 88
|  |  |  itemlevel = 3
|  |  |  name = "梓童·抗暴"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7260306
|  |  }
|  |  [47] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 89
|  |  |  itemlevel = 5
|  |  |  name = "乌琪琪·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7090108
|  |  }
|  |  [48] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998288
|  |  |  id = 90
|  |  |  itemlevel = 4
|  |  |  name = "博戏鬼·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 7
|  |  |  }
|  |  |  sid = 7030402
|  |  }
|  |  [49] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 91
|  |  |  itemlevel = 2
|  |  |  name = "归无处·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7040202
|  |  }
|  |  [5] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998244
|  |  |  id = 47
|  |  |  itemlevel = 3
|  |  |  name = "巳日时君·防御加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 5
|  |  |  }
|  |  |  sid = 7350313
|  |  }
|  |  [50] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998288
|  |  |  id = 92
|  |  |  itemlevel = 6
|  |  |  name = "梓童·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7260512
|  |  }
|  |  [51] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998288
|  |  |  id = 93
|  |  |  itemlevel = 3
|  |  |  name = "红雨将军·异常命中"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 17
|  |  |  }
|  |  |  sid = 7100309
|  |  }
|  |  [52] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 94
|  |  |  itemlevel = 3
|  |  |  name = "归无处·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7040312
|  |  }
|  |  [53] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998288
|  |  |  id = 95
|  |  |  itemlevel = 3
|  |  |  name = "博戏鬼·速度"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 7
|  |  |  }
|  |  |  sid = 7030304
|  |  }
|  |  [54] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 96
|  |  |  itemlevel = 3
|  |  |  name = "洛迦·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7060312
|  |  }
|  |  [55] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 97
|  |  |  itemlevel = 2
|  |  |  name = "漓渚·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7240202
|  |  }
|  |  [56] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998288
|  |  |  id = 98
|  |  |  itemlevel = 5
|  |  |  name = "墨菲·暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 13
|  |  |  }
|  |  |  sid = 7020105
|  |  }
|  |  [57] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084324
|  |  |  id = 99
|  |  |  itemlevel = 3
|  |  |  name = "星遥·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7320312
|  |  }
|  |  [58] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084329
|  |  |  id = 100
|  |  |  itemlevel = 6
|  |  |  name = "乌琪琪·暴击伤害"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7090507
|  |  }
|  |  [59] = {
|  |  |  amount = 1
|  |  |  create_time = 1723831039
|  |  |  id = 101
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·气血"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 4
|  |  |  }
|  |  |  sid = 7050401
|  |  }
|  |  [6] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998254
|  |  |  id = 48
|  |  |  itemlevel = 3
|  |  |  name = "巳日时君·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 5
|  |  |  }
|  |  |  sid = 7350312
|  |  }
|  |  [60] = {
|  |  |  amount = 1
|  |  |  create_time = 1723831105
|  |  |  id = 102
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 9
|  |  |  }
|  |  |  sid = 7050408
|  |  }
|  |  [61] = {
|  |  |  amount = 1
|  |  |  create_time = 1723831234
|  |  |  id = 103
|  |  |  itemlevel = 4
|  |  |  name = "流臾之须·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7430402
|  |  }
|  |  [62] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 104
|  |  |  itemlevel = 3
|  |  |  name = "锵明·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 10
|  |  |  }
|  |  |  sid = 7420302
|  |  }
|  |  [63] = {
|  |  |  amount = 1
|  |  |  create_time = 1723831261
|  |  |  id = 105
|  |  |  itemlevel = 4
|  |  |  name = "流臾之须·气血"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7430401
|  |  }
|  |  [64] = {
|  |  |  amount = 1
|  |  |  create_time = 1723831276
|  |  |  id = 106
|  |  |  itemlevel = 4
|  |  |  name = "流臾之须·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7430411
|  |  }
|  |  [65] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 107
|  |  |  itemlevel = 2
|  |  |  name = "漓渚·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7240212
|  |  }
|  |  [66] = {
|  |  |  amount = 1
|  |  |  create_time = 1723831322
|  |  |  id = 108
|  |  |  itemlevel = 4
|  |  |  name = "流臾之须·异常抵抗"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7430410
|  |  }
|  |  [67] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 109
|  |  |  itemlevel = 2
|  |  |  name = "陶磊磊·防御"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7340203
|  |  }
|  |  [68] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998288
|  |  |  id = 110
|  |  |  itemlevel = 2
|  |  |  name = "偃月无夜·暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 1
|  |  |  }
|  |  |  sid = 7310205
|  |  }
|  |  [69] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998297
|  |  |  id = 111
|  |  |  itemlevel = 3
|  |  |  name = "流臾之须·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7430308
|  |  }
|  |  [7] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998254
|  |  |  id = 49
|  |  |  itemlevel = 4
|  |  |  name = "红雨将军·防御"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 17
|  |  |  }
|  |  |  sid = 7100403
|  |  }
|  |  [70] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 112
|  |  |  itemlevel = 5
|  |  |  name = "流莺·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7250112
|  |  }
|  |  [71] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 113
|  |  |  itemlevel = 2
|  |  |  name = "星罗·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7410202
|  |  }
|  |  [72] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 114
|  |  |  itemlevel = 3
|  |  |  name = "流莺·暴击伤害"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7250307
|  |  }
|  |  [73] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 115
|  |  |  itemlevel = 2
|  |  |  name = "一阕秋·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7450208
|  |  }
|  |  [74] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998297
|  |  |  id = 116
|  |  |  itemlevel = 4
|  |  |  name = "花月巫·速度"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7360404
|  |  }
|  |  [75] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998297
|  |  |  id = 117
|  |  |  itemlevel = 2
|  |  |  name = "流莺·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 11
|  |  |  }
|  |  |  sid = 7250212
|  |  }
|  |  [76] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 118
|  |  |  itemlevel = 3
|  |  |  name = "流莺·防御加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7250313
|  |  }
|  |  [77] = {
|  |  |  amount = 1
|  |  |  create_time = 1723814289
|  |  |  id = 119
|  |  |  itemlevel = 4
|  |  |  name = "锵明·气血"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 10
|  |  |  }
|  |  |  sid = 7420401
|  |  }
|  |  [78] = {
|  |  |  amount = 1
|  |  |  create_time = 1723911934
|  |  |  id = 120
|  |  |  itemlevel = 4
|  |  |  name = "红雨将军·防御"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 18
|  |  |  }
|  |  |  sid = 7100403
|  |  }
|  |  [79] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998297
|  |  |  id = 121
|  |  |  itemlevel = 2
|  |  |  name = "红雨将军·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 17
|  |  |  }
|  |  |  sid = 7100202
|  |  }
|  |  [8] = {
|  |  |  amount = 1
|  |  |  create_time = 1723831855
|  |  |  id = 50
|  |  |  itemlevel = 4
|  |  |  name = "红雨将军·暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 17
|  |  |  }
|  |  |  sid = 7100405
|  |  }
|  |  [80] = {
|  |  |  amount = 1
|  |  |  create_time = 1723814397
|  |  |  id = 122
|  |  |  itemlevel = 4
|  |  |  name = "锵明·防御"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 10
|  |  |  }
|  |  |  sid = 7420403
|  |  }
|  |  [81] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 123
|  |  |  itemlevel = 2
|  |  |  name = "颜无月·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7220202
|  |  }
|  |  [82] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 124
|  |  |  itemlevel = 5
|  |  |  name = "铃音·防御"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7330103
|  |  }
|  |  [83] = {
|  |  |  amount = 1
|  |  |  create_time = 1723814658
|  |  |  id = 125
|  |  |  itemlevel = 4
|  |  |  name = "流臾之须·气血加成"
|  |  |  partner_soul = {
|  |  |  |  exp = 42000
|  |  |  |  level = 4
|  |  |  |  parid = 12
|  |  |  }
|  |  |  sid = 7430411
|  |  }
|  |  [84] = {
|  |  |  amount = 1
|  |  |  create_time = 1723814670
|  |  |  id = 126
|  |  |  itemlevel = 4
|  |  |  name = "流臾之须·防御"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 12
|  |  |  }
|  |  |  sid = 7430403
|  |  }
|  |  [85] = {
|  |  |  amount = 1
|  |  |  create_time = 1723814682
|  |  |  id = 127
|  |  |  itemlevel = 4
|  |  |  name = "流臾之须·防御加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7430413
|  |  }
|  |  [86] = {
|  |  |  amount = 1
|  |  |  create_time = 1723814694
|  |  |  id = 128
|  |  |  itemlevel = 4
|  |  |  name = "流臾之须·异常抵抗"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 12
|  |  |  }
|  |  |  sid = 7430410
|  |  }
|  |  [87] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 129
|  |  |  itemlevel = 2
|  |  |  name = "星罗·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7410211
|  |  }
|  |  [88] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998297
|  |  |  id = 130
|  |  |  itemlevel = 2
|  |  |  name = "红雨将军·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 17
|  |  |  }
|  |  |  sid = 7100212
|  |  }
|  |  [89] = {
|  |  |  amount = 1
|  |  |  create_time = 1724073324
|  |  |  id = 131
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·攻击"
|  |  |  partner_soul = {
|  |  |  |  exp = 126400
|  |  |  |  level = 6
|  |  |  |  parid = 2
|  |  |  }
|  |  |  sid = 7050402
|  |  }
|  |  [9] = {
|  |  |  amount = 1
|  |  |  create_time = 1723998254
|  |  |  id = 51
|  |  |  itemlevel = 5
|  |  |  name = "锵明·攻击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7420102
|  |  }
|  |  [90] = {
|  |  |  amount = 1
|  |  |  create_time = 1724073335
|  |  |  id = 132
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  exp = 122600
|  |  |  |  level = 6
|  |  |  |  parid = 2
|  |  |  }
|  |  |  sid = 7050408
|  |  }
|  |  [91] = {
|  |  |  amount = 1
|  |  |  create_time = 1723911934
|  |  |  id = 133
|  |  |  itemlevel = 4
|  |  |  name = "梓童·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7260412
|  |  }
|  |  [92] = {
|  |  |  amount = 1
|  |  |  create_time = 1724073369
|  |  |  id = 134
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·异常抵抗"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 2
|  |  |  }
|  |  |  sid = 7050410
|  |  }
|  |  [93] = {
|  |  |  amount = 1
|  |  |  create_time = 1724073433
|  |  |  id = 135
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·气血"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 2
|  |  |  }
|  |  |  sid = 7050401
|  |  }
|  |  [94] = {
|  |  |  amount = 1
|  |  |  create_time = 1724073441
|  |  |  id = 136
|  |  |  itemlevel = 4
|  |  |  name = "黑帝·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  |  parid = 2
|  |  |  }
|  |  |  sid = 7050411
|  |  }
|  |  [95] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084452
|  |  |  id = 137
|  |  |  itemlevel = 2
|  |  |  name = "铃音·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7330211
|  |  }
|  |  [96] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 138
|  |  |  itemlevel = 5
|  |  |  name = "颜无月·攻击加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7220112
|  |  }
|  |  [97] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 139
|  |  |  itemlevel = 3
|  |  |  name = "斑曜·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7460308
|  |  }
|  |  [98] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084452
|  |  |  id = 140
|  |  |  itemlevel = 2
|  |  |  name = "锵明·治疗暴击"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7420208
|  |  }
|  |  [99] = {
|  |  |  amount = 1
|  |  |  create_time = 1724084335
|  |  |  id = 141
|  |  |  itemlevel = 3
|  |  |  name = "乌琪琪·气血加成"
|  |  |  partner_soul = {
|  |  |  |  level = 1
|  |  |  }
|  |  |  sid = 7090311
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>loginitem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenPlanName = {
|  fuwen_name = {
|  |  [1] = {
|  |  |  name = "默认方案"
|  |  |  plan = 1
|  |  }
|  |  [2] = {
|  |  |  name = "备选方案"
|  |  |  plan = 2
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartnerList = {
|  partner_list = {
|  |  [1] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 659
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 1150
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 141
|  |  |  equip_list = {
|  |  |  |  [1] = 326
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 29500
|  |  |  grade = 20
|  |  |  hp = 4756
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 5425
|  |  |  model_info = {
|  |  |  |  shape = 302
|  |  |  |  skin = 203020
|  |  |  }
|  |  |  name = "重华"
|  |  |  parid = 1
|  |  |  partner_type = 302
|  |  |  patahp = 5425
|  |  |  power = 2161
|  |  |  res_abnormal_ratio = 850
|  |  |  res_critical_ratio = 600
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30201
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30202
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30203
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 31
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 208
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 32
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 110
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  itemid = 215
|  |  |  |  |  pos = 4
|  |  |  |  }
|  |  |  }
|  |  |  speed = 420
|  |  |  star = 3
|  |  }
|  |  [10] = {
|  |  |  abnormal_attr_ratio = 600
|  |  |  amount = 1
|  |  |  attack = 1433
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 1000
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 446
|  |  |  equip_list = {
|  |  |  |  [1] = 305
|  |  |  |  [2] = 306
|  |  |  |  [3] = 307
|  |  |  |  [4] = 320
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 903089
|  |  |  grade = 57
|  |  |  hp = 20740
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 20740
|  |  |  model_info = {
|  |  |  |  shape = 405
|  |  |  |  skin = 204050
|  |  |  }
|  |  |  name = "莹月"
|  |  |  parid = 10
|  |  |  partner_type = 405
|  |  |  patahp = 20740
|  |  |  power = 7614
|  |  |  res_abnormal_ratio = 1150
|  |  |  res_critical_ratio = 500
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 4
|  |  |  |  |  sk = 40501
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 2
|  |  |  |  |  sk = 40502
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 2
|  |  |  |  |  sk = 40503
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 42
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 119
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 122
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 205
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  itemid = 207
|  |  |  |  |  pos = 4
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  itemid = 206
|  |  |  |  |  pos = 5
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  itemid = 104
|  |  |  |  |  pos = 6
|  |  |  |  }
|  |  |  }
|  |  |  speed = 1120
|  |  |  star = 5
|  |  |  status = 32
|  |  }
|  |  [11] = {
|  |  |  abnormal_attr_ratio = 600
|  |  |  amount = 1
|  |  |  attack = 829
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 1000
|  |  |  defense = 221
|  |  |  equip_list = {
|  |  |  |  [1] = 331
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 59070
|  |  |  grade = 28
|  |  |  hp = 8143
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 8143
|  |  |  model_info = {
|  |  |  |  shape = 511
|  |  |  |  skin = 205110
|  |  |  }
|  |  |  name = "片雾"
|  |  |  parid = 11
|  |  |  partner_type = 511
|  |  |  patahp = 8143
|  |  |  power = 3426
|  |  |  res_abnormal_ratio = 850
|  |  |  res_critical_ratio = 500
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 51101
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 51102
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 51103
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 25
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 199
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 87
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 45
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  itemid = 218
|  |  |  |  |  pos = 4
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  itemid = 117
|  |  |  |  |  pos = 5
|  |  |  |  }
|  |  |  }
|  |  |  speed = 1225
|  |  |  star = 3
|  |  |  status = 32
|  |  }
|  |  [12] = {
|  |  |  abnormal_attr_ratio = 1030
|  |  |  amount = 1
|  |  |  attack = 1531
|  |  |  awake = 1
|  |  |  critical_damage = 15280
|  |  |  critical_ratio = 1330
|  |  |  cure_critical_ratio = 2700
|  |  |  defense = 590
|  |  |  equip_list = {
|  |  |  |  [1] = 322
|  |  |  |  [2] = 323
|  |  |  |  [3] = 324
|  |  |  |  [4] = 336
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 2333120
|  |  |  grade = 69
|  |  |  hp = 33047
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 33604
|  |  |  model_info = {
|  |  |  |  shape = 415
|  |  |  |  skin = 204150
|  |  |  }
|  |  |  name = "琥非"
|  |  |  parid = 12
|  |  |  partner_type = 415
|  |  |  patahp = 33604
|  |  |  power = 11953
|  |  |  res_abnormal_ratio = 1880
|  |  |  res_critical_ratio = 1000
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 4
|  |  |  |  |  sk = 41501
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 3
|  |  |  |  |  sk = 41502
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 41503
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 43
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 182
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 192
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 204
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  itemid = 126
|  |  |  |  |  pos = 4
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  itemid = 128
|  |  |  |  |  pos = 5
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  itemid = 125
|  |  |  |  |  pos = 6
|  |  |  |  }
|  |  |  }
|  |  |  speed = 1028
|  |  |  star = 5
|  |  |  status = 4
|  |  }
|  |  [13] = {
|  |  |  abnormal_attr_ratio = 1250
|  |  |  amount = 1
|  |  |  attack = 766
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 1200
|  |  |  cure_critical_ratio = 1000
|  |  |  defense = 174
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 195390
|  |  |  grade = 41
|  |  |  hp = 8277
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 8939
|  |  |  model_info = {
|  |  |  |  shape = 409
|  |  |  |  skin = 204090
|  |  |  }
|  |  |  name = "犬妖"
|  |  |  parid = 13
|  |  |  partner_type = 409
|  |  |  patahp = 8939
|  |  |  power = 3552
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 750
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40901
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40902
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40903
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 2
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 63
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 212
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 44
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  itemid = 38
|  |  |  |  |  pos = 4
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  itemid = 217
|  |  |  |  |  pos = 5
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  itemid = 98
|  |  |  |  |  pos = 6
|  |  |  |  }
|  |  |  }
|  |  |  speed = 245
|  |  |  star = 1
|  |  }
|  |  [14] = {
|  |  |  abnormal_attr_ratio = 900
|  |  |  amount = 1
|  |  |  attack = 2183
|  |  |  awake = 1
|  |  |  critical_damage = 16040
|  |  |  critical_ratio = 1950
|  |  |  cure_critical_ratio = 1200
|  |  |  defense = 426
|  |  |  equip_list = {
|  |  |  |  [1] = 319
|  |  |  |  [2] = 318
|  |  |  |  [3] = 311
|  |  |  |  [4] = 312
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 1524324
|  |  |  grade = 63
|  |  |  hp = 19613
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 19613
|  |  |  model_info = {
|  |  |  |  shape = 412
|  |  |  |  skin = 204120
|  |  |  }
|  |  |  name = "执夷"
|  |  |  parid = 14
|  |  |  partner_type = 412
|  |  |  patahp = 19613
|  |  |  power = 8599
|  |  |  res_abnormal_ratio = 1000
|  |  |  res_critical_ratio = 800
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 41201
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 41202
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 41203
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 5
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 171
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 173
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 190
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  itemid = 209
|  |  |  |  |  pos = 4
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  itemid = 175
|  |  |  |  |  pos = 5
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  itemid = 191
|  |  |  |  |  pos = 6
|  |  |  |  }
|  |  |  }
|  |  |  speed = 1260
|  |  |  star = 5
|  |  }
|  |  [15] = {
|  |  |  abnormal_attr_ratio = 1000
|  |  |  amount = 1
|  |  |  attack = 1122
|  |  |  awake = 1
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 1100
|  |  |  cure_critical_ratio = 1650
|  |  |  defense = 452
|  |  |  equip_list = {
|  |  |  |  [1] = 329
|  |  |  |  [2] = 308
|  |  |  |  [3] = 309
|  |  |  |  [4] = 310
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 676155
|  |  |  grade = 54
|  |  |  hp = 15786
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 15786
|  |  |  model_info = {
|  |  |  |  shape = 404
|  |  |  |  skin = 204040
|  |  |  }
|  |  |  name = "绿狸"
|  |  |  parid = 15
|  |  |  partner_type = 404
|  |  |  patahp = 15786
|  |  |  power = 6805
|  |  |  res_abnormal_ratio = 2150
|  |  |  res_critical_ratio = 1000
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40401
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40402
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40403
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 5
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 225
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 224
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 221
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  itemid = 220
|  |  |  |  |  pos = 4
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  itemid = 222
|  |  |  |  |  pos = 5
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  itemid = 223
|  |  |  |  |  pos = 6
|  |  |  |  }
|  |  |  }
|  |  |  speed = 604
|  |  |  star = 2
|  |  }
|  |  [16] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 697
|  |  |  critical_damage = 16280
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 1300
|  |  |  defense = 180
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 77960
|  |  |  grade = 32
|  |  |  hp = 6583
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 6912
|  |  |  model_info = {
|  |  |  |  shape = 509
|  |  |  |  skin = 205090
|  |  |  }
|  |  |  name = "判官"
|  |  |  parid = 16
|  |  |  partner_type = 509
|  |  |  patahp = 6912
|  |  |  power = 2910
|  |  |  res_abnormal_ratio = 850
|  |  |  res_critical_ratio = 400
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50901
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50902
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50903
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 6
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 150
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 210
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 42
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  itemid = 62
|  |  |  |  |  pos = 4
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  itemid = 39
|  |  |  |  |  pos = 5
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  itemid = 53
|  |  |  |  |  pos = 6
|  |  |  |  }
|  |  |  }
|  |  |  speed = 1295
|  |  |  star = 2
|  |  }
|  |  [17] = {
|  |  |  abnormal_attr_ratio = 1600
|  |  |  amount = 1
|  |  |  attack = 1079
|  |  |  awake = 1
|  |  |  critical_damage = 15800
|  |  |  critical_ratio = 1950
|  |  |  cure_critical_ratio = 1000
|  |  |  defense = 235
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 106110
|  |  |  grade = 35
|  |  |  hp = 9492
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 9492
|  |  |  model_info = {
|  |  |  |  shape = 314
|  |  |  |  skin = 203140
|  |  |  }
|  |  |  name = "执阳"
|  |  |  parid = 17
|  |  |  partner_type = 314
|  |  |  patahp = 9492
|  |  |  power = 5131
|  |  |  res_abnormal_ratio = 1000
|  |  |  res_critical_ratio = 800
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31401
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31402
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31403
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 10
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 49
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 50
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 36
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  itemid = 93
|  |  |  |  |  pos = 4
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  itemid = 121
|  |  |  |  |  pos = 5
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  itemid = 130
|  |  |  |  |  pos = 6
|  |  |  |  }
|  |  |  }
|  |  |  speed = 100
|  |  |  star = 2
|  |  |  status = 32
|  |  }
|  |  [18] = {
|  |  |  abnormal_attr_ratio = 1550
|  |  |  amount = 1
|  |  |  attack = 1522
|  |  |  awake = 1
|  |  |  critical_damage = 16040
|  |  |  critical_ratio = 1100
|  |  |  cure_critical_ratio = 1650
|  |  |  defense = 457
|  |  |  equip_list = {
|  |  |  |  [1] = 341
|  |  |  |  [2] = 342
|  |  |  |  [3] = 340
|  |  |  |  [4] = 313
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 1483469
|  |  |  grade = 63
|  |  |  hp = 17079
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 17079
|  |  |  model_info = {
|  |  |  |  shape = 413
|  |  |  |  skin = 204130
|  |  |  }
|  |  |  name = "古娄"
|  |  |  parid = 18
|  |  |  partner_type = 413
|  |  |  patahp = 17079
|  |  |  power = 7627
|  |  |  res_abnormal_ratio = 1000
|  |  |  res_critical_ratio = 1100
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 3
|  |  |  |  |  sk = 41301
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 4
|  |  |  |  |  sk = 41302
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 41303
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 10
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 144
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 147
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 149
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  itemid = 177
|  |  |  |  |  pos = 4
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  itemid = 148
|  |  |  |  |  pos = 5
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  itemid = 120
|  |  |  |  |  pos = 6
|  |  |  |  }
|  |  |  }
|  |  |  speed = 460
|  |  |  star = 3
|  |  }
|  |  [19] = {
|  |  |  abnormal_attr_ratio = 1450
|  |  |  amount = 1
|  |  |  attack = 2870
|  |  |  awake = 1
|  |  |  critical_damage = 16630
|  |  |  critical_ratio = 2700
|  |  |  cure_critical_ratio = 1650
|  |  |  defense = 476
|  |  |  equip_list = {
|  |  |  |  [1] = 327
|  |  |  |  [2] = 339
|  |  |  |  [3] = 335
|  |  |  |  [4] = 337
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 1638172
|  |  |  grade = 64
|  |  |  hp = 24626
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 24626
|  |  |  model_info = {
|  |  |  |  shape = 306
|  |  |  |  skin = 203060
|  |  |  }
|  |  |  name = "袁雀"
|  |  |  parid = 19
|  |  |  partner_type = 306
|  |  |  patahp = 24626
|  |  |  power = 12978
|  |  |  res_abnormal_ratio = 1550
|  |  |  res_critical_ratio = 800
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 4
|  |  |  |  |  sk = 30601
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 3
|  |  |  |  |  sk = 30602
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 5
|  |  |  |  |  sk = 30603
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 5
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 162
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 163
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 166
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  itemid = 169
|  |  |  |  |  pos = 4
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  itemid = 168
|  |  |  |  |  pos = 5
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  itemid = 179
|  |  |  |  |  pos = 6
|  |  |  |  }
|  |  |  }
|  |  |  speed = 700
|  |  |  star = 5
|  |  }
|  |  [2] = {
|  |  |  abnormal_attr_ratio = 1370
|  |  |  amount = 1
|  |  |  attack = 2676
|  |  |  awake = 1
|  |  |  critical_damage = 15340
|  |  |  critical_ratio = 1330
|  |  |  cure_critical_ratio = 2900
|  |  |  defense = 560
|  |  |  equip_list = {
|  |  |  |  [1] = 333
|  |  |  |  [2] = 325
|  |  |  |  [3] = 304
|  |  |  |  [4] = 321
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 2333120
|  |  |  grade = 69
|  |  |  hp = 28573
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 28573
|  |  |  model_info = {
|  |  |  |  shape = 407
|  |  |  |  skin = 204070
|  |  |  }
|  |  |  name = "莲"
|  |  |  parid = 2
|  |  |  partner_type = 407
|  |  |  patahp = 28573
|  |  |  power = 12106
|  |  |  res_abnormal_ratio = 2380
|  |  |  res_critical_ratio = 1000
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 4
|  |  |  |  |  sk = 40701
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 3
|  |  |  |  |  sk = 40702
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 2
|  |  |  |  |  sk = 40703
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 5
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 131
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 132
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 134
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  itemid = 219
|  |  |  |  |  pos = 4
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  itemid = 135
|  |  |  |  |  pos = 5
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  itemid = 136
|  |  |  |  |  pos = 6
|  |  |  |  }
|  |  |  }
|  |  |  speed = 380
|  |  |  star = 5
|  |  }
|  |  [20] = {
|  |  |  abnormal_attr_ratio = 300
|  |  |  amount = 1
|  |  |  attack = 357
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 1100
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 90
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 12000
|  |  |  grade = 11
|  |  |  hp = 3711
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 3711
|  |  |  model_info = {
|  |  |  |  shape = 312
|  |  |  |  skin = 203120
|  |  |  }
|  |  |  name = "伊露"
|  |  |  parid = 20
|  |  |  partner_type = 312
|  |  |  patahp = 3711
|  |  |  power = 1416
|  |  |  res_abnormal_ratio = 850
|  |  |  res_critical_ratio = 500
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31202
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31201
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 46
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 198
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 55
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 156
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  }
|  |  |  speed = 1015
|  |  |  star = 1
|  |  }
|  |  [21] = {
|  |  |  abnormal_attr_ratio = 300
|  |  |  amount = 1
|  |  |  attack = 295
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 600
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 55
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  grade = 1
|  |  |  mask = "fffffefe"
|  |  |  max_hp = 1764
|  |  |  model_info = {
|  |  |  |  shape = 410
|  |  |  |  skin = 204100
|  |  |  }
|  |  |  name = "青竹"
|  |  |  parid = 21
|  |  |  partner_type = 410
|  |  |  patahp = 1764
|  |  |  power = 485
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 400
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 41002
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 41001
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 35
|  |  |  speed = 1330
|  |  |  star = 2
|  |  }
|  |  [22] = {
|  |  |  abnormal_attr_ratio = 600
|  |  |  amount = 1
|  |  |  attack = 273
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 57
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  grade = 1
|  |  |  mask = "fffffefe"
|  |  |  max_hp = 1960
|  |  |  model_info = {
|  |  |  |  shape = 417
|  |  |  |  skin = 204170
|  |  |  }
|  |  |  name = "松姑子"
|  |  |  parid = 22
|  |  |  partner_type = 417
|  |  |  patahp = 1960
|  |  |  power = 759
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 400
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 41702
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 41701
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 34
|  |  |  speed = 1365
|  |  |  star = 2
|  |  }
|  |  [23] = {
|  |  |  abnormal_attr_ratio = 600
|  |  |  amount = 1
|  |  |  attack = 290
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 61
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  grade = 1
|  |  |  mask = "fffffefe"
|  |  |  max_hp = 1607
|  |  |  model_info = {
|  |  |  |  shape = 506
|  |  |  |  skin = 205060
|  |  |  }
|  |  |  name = "噬地"
|  |  |  parid = 23
|  |  |  partner_type = 506
|  |  |  patahp = 1607
|  |  |  power = 432
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 600
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50601
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50602
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50603
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 3
|  |  |  speed = 686
|  |  |  star = 2
|  |  }
|  |  [24] = {
|  |  |  abnormal_attr_ratio = 600
|  |  |  amount = 1
|  |  |  attack = 302
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 50
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  grade = 1
|  |  |  mask = "fffffefe"
|  |  |  max_hp = 2019
|  |  |  model_info = {
|  |  |  |  shape = 505
|  |  |  |  skin = 205050
|  |  |  }
|  |  |  name = "吞天"
|  |  |  parid = 24
|  |  |  partner_type = 505
|  |  |  patahp = 2019
|  |  |  power = 669
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 300
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50501
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50502
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50503
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 10
|  |  |  speed = 140
|  |  |  star = 2
|  |  }
|  |  [25] = {
|  |  |  abnormal_attr_ratio = 600
|  |  |  amount = 1
|  |  |  attack = 246
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 600
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 54
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  grade = 1
|  |  |  mask = "fffffefe"
|  |  |  max_hp = 2293
|  |  |  model_info = {
|  |  |  |  shape = 504
|  |  |  |  skin = 205040
|  |  |  }
|  |  |  name = "白"
|  |  |  parid = 25
|  |  |  partner_type = 504
|  |  |  patahp = 2293
|  |  |  power = 668
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 400
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50401
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50402
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50403
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 33
|  |  |  speed = 595
|  |  |  star = 2
|  |  }
|  |  [26] = {
|  |  |  abnormal_attr_ratio = 600
|  |  |  amount = 1
|  |  |  attack = 254
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 600
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 62
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  grade = 1
|  |  |  mask = "fffffefe"
|  |  |  max_hp = 2234
|  |  |  model_info = {
|  |  |  |  shape = 401
|  |  |  |  skin = 204010
|  |  |  }
|  |  |  name = "北溟"
|  |  |  parid = 26
|  |  |  partner_type = 401
|  |  |  patahp = 2234
|  |  |  power = 810
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 600
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40101
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40102
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40103
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 24
|  |  |  speed = 980
|  |  |  star = 2
|  |  }
|  |  [3] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 800
|  |  |  critical_damage = 15800
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 197
|  |  |  equip_list = {
|  |  |  |  [1] = 343
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 238740
|  |  |  grade = 43
|  |  |  hp = 7788
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 7788
|  |  |  model_info = {
|  |  |  |  shape = 502
|  |  |  |  skin = 205020
|  |  |  }
|  |  |  name = "马面面"
|  |  |  parid = 3
|  |  |  partner_type = 502
|  |  |  patahp = 7788
|  |  |  power = 2724
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 500
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50201
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50202
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50203
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 5
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 174
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 172
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 157
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  }
|  |  |  speed = 354
|  |  |  star = 1
|  |  |  status = 32
|  |  }
|  |  [4] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 1355
|  |  |  critical_damage = 16040
|  |  |  critical_ratio = 1650
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 332
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 560660
|  |  |  grade = 52
|  |  |  hp = 12941
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 12941
|  |  |  model_info = {
|  |  |  |  shape = 800
|  |  |  |  skin = 205032
|  |  |  }
|  |  |  name = "黑"
|  |  |  parid = 4
|  |  |  partner_type = 503
|  |  |  patahp = 12941
|  |  |  power = 4920
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 500
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50301
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50302
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50303
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 5
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 178
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 185
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 189
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  itemid = 195
|  |  |  |  |  pos = 4
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  itemid = 101
|  |  |  |  |  pos = 5
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  itemid = 54
|  |  |  |  |  pos = 6
|  |  |  |  }
|  |  |  }
|  |  |  speed = 455
|  |  |  star = 5
|  |  }
|  |  [5] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 862
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 1250
|  |  |  cure_critical_ratio = 1000
|  |  |  defense = 185
|  |  |  equip_list = {
|  |  |  |  [1] = 334
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 103420
|  |  |  grade = 35
|  |  |  hp = 7135
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 7135
|  |  |  model_info = {
|  |  |  |  shape = 403
|  |  |  |  skin = 204030
|  |  |  }
|  |  |  name = "蛇姬"
|  |  |  parid = 5
|  |  |  partner_type = 403
|  |  |  patahp = 7135
|  |  |  power = 2915
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 600
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40301
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40302
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40303
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 35
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 59
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 160
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 69
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  itemid = 48
|  |  |  |  |  pos = 4
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  itemid = 47
|  |  |  |  |  pos = 5
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  itemid = 46
|  |  |  |  |  pos = 6
|  |  |  |  }
|  |  |  }
|  |  |  speed = 756
|  |  |  star = 1
|  |  }
|  |  [6] = {
|  |  |  abnormal_attr_ratio = 900
|  |  |  amount = 1
|  |  |  attack = 1460
|  |  |  awake = 1
|  |  |  critical_damage = 16040
|  |  |  critical_ratio = 2150
|  |  |  cure_critical_ratio = 1000
|  |  |  defense = 315
|  |  |  equip_list = {
|  |  |  |  [1] = 314
|  |  |  |  [2] = 315
|  |  |  |  [3] = 316
|  |  |  |  [4] = 317
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 441050
|  |  |  grade = 49
|  |  |  hp = 11933
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 13326
|  |  |  model_info = {
|  |  |  |  shape = 501
|  |  |  |  skin = 205010
|  |  |  }
|  |  |  name = "阿坊"
|  |  |  parid = 6
|  |  |  partner_type = 501
|  |  |  patahp = 13326
|  |  |  power = 6520
|  |  |  res_abnormal_ratio = 1000
|  |  |  res_critical_ratio = 900
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 6
|  |  |  |  |  sk = 50101
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 2
|  |  |  |  |  sk = 50102
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50103
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 5
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 161
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 164
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 165
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  itemid = 170
|  |  |  |  |  pos = 4
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  itemid = 167
|  |  |  |  |  pos = 5
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  itemid = 188
|  |  |  |  |  pos = 6
|  |  |  |  }
|  |  |  }
|  |  |  speed = 140
|  |  |  star = 3
|  |  }
|  |  [7] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 580
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 1000
|  |  |  defense = 123
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 30000
|  |  |  grade = 20
|  |  |  hp = 4434
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 4434
|  |  |  model_info = {
|  |  |  |  shape = 514
|  |  |  |  skin = 205140
|  |  |  }
|  |  |  name = "曼珠沙华"
|  |  |  parid = 7
|  |  |  partner_type = 514
|  |  |  patahp = 4434
|  |  |  power = 2170
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 1400
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 51401
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 51402
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 51403
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 3
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 33
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 90
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 95
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  itemid = 60
|  |  |  |  |  pos = 4
|  |  |  |  }
|  |  |  }
|  |  |  speed = 240
|  |  |  star = 1
|  |  }
|  |  [8] = {
|  |  |  abnormal_attr_ratio = 600
|  |  |  amount = 1
|  |  |  attack = 654
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 1000
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 159
|  |  |  equip_list = {
|  |  |  |  [1] = 332
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 30370
|  |  |  grade = 20
|  |  |  hp = 5094
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 5272
|  |  |  model_info = {
|  |  |  |  shape = 313
|  |  |  |  skin = 203130
|  |  |  }
|  |  |  name = "檀"
|  |  |  parid = 8
|  |  |  partner_type = 313
|  |  |  patahp = 5272
|  |  |  power = 2462
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 1200
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31301
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31302
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31303
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 9
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 58
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 35
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 214
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  itemid = 37
|  |  |  |  |  pos = 4
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  itemid = 66
|  |  |  |  |  pos = 5
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  itemid = 41
|  |  |  |  |  pos = 6
|  |  |  |  }
|  |  |  }
|  |  |  speed = 704
|  |  |  star = 3
|  |  }
|  |  [9] = {
|  |  |  abnormal_attr_ratio = 1130
|  |  |  amount = 1
|  |  |  attack = 3134
|  |  |  awake = 1
|  |  |  critical_damage = 16320
|  |  |  critical_ratio = 3380
|  |  |  cure_critical_ratio = 1650
|  |  |  defense = 485
|  |  |  equip_list = {
|  |  |  |  [1] = 328
|  |  |  |  [2] = 344
|  |  |  |  [3] = 330
|  |  |  |  [4] = 338
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 2333120
|  |  |  grade = 69
|  |  |  hp = 23130
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 23130
|  |  |  model_info = {
|  |  |  |  shape = 510
|  |  |  |  skin = 205100
|  |  |  }
|  |  |  name = "奉主夜鹤"
|  |  |  parid = 9
|  |  |  partner_type = 510
|  |  |  patahp = 23130
|  |  |  power = 12706
|  |  |  res_abnormal_ratio = 1230
|  |  |  res_critical_ratio = 800
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 5
|  |  |  |  |  sk = 51001
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 4
|  |  |  |  |  sk = 51002
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 2
|  |  |  |  |  sk = 51003
|  |  |  |  }
|  |  |  }
|  |  |  soul_type = 5
|  |  |  souls = {
|  |  |  |  [1] = {
|  |  |  |  |  itemid = 180
|  |  |  |  |  pos = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  itemid = 184
|  |  |  |  |  pos = 2
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  itemid = 187
|  |  |  |  |  pos = 3
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  itemid = 102
|  |  |  |  |  pos = 4
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  itemid = 176
|  |  |  |  |  pos = 5
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  itemid = 183
|  |  |  |  |  pos = 6
|  |  |  |  }
|  |  |  }
|  |  |  speed = 540
|  |  |  star = 5
|  |  |  status = 4
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartner = {
|  fight_info = {
|  |  [1] = {
|  |  |  parid = 19
|  |  |  pos = 1
|  |  }
|  |  [2] = {
|  |  |  parid = 10
|  |  |  pos = 2
|  |  }
|  |  [3] = {
|  |  |  parid = 4
|  |  |  pos = 3
|  |  }
|  |  [4] = {
|  |  |  parid = 15
|  |  |  pos = 4
|  |  }
|  }
|  owned_equip_list = {
|  |  [1] = 6401001
|  |  [2] = 6101001
|  |  [3] = 6301001
|  |  [4] = 6201001
|  }
|  owned_partner_list = {
|  |  [1] = 417
|  |  [10] = 409
|  |  [11] = 504
|  |  [12] = 401
|  |  [13] = 306
|  |  [14] = 403
|  |  [15] = 404
|  |  [16] = 405
|  |  [17] = 502
|  |  [18] = 503
|  |  [19] = 312
|  |  [2] = 514
|  |  [20] = 313
|  |  [21] = 314
|  |  [22] = 501
|  |  [23] = 412
|  |  [24] = 413
|  |  [25] = 510
|  |  [26] = 415
|  |  [3] = 511
|  |  [4] = 506
|  |  [5] = 509
|  |  [6] = 505
|  |  [7] = 407
|  |  [8] = 410
|  |  [9] = 302
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPicturePosList = {}
[core/table.lua:94]:-->Net Receive: skill.GS2CLoginSkill = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 9
|  |  |  needcost = 5
|  |  |  sk = 3201
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 6
|  |  |  needcost = 10
|  |  |  sk = 3202
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  needcost = 15
|  |  |  sk = 3204
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  needcost = 25
|  |  |  sk = 3205
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  level = 4
|  |  |  needcost = 18
|  |  |  sk = 3206
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  level = 1
|  |  |  needcost = 18
|  |  |  sk = 3207
|  |  |  type = 1
|  |  }
|  }
}
[logic/skill/CSkillCtrl.lua:112]:技能数据登陆协议返回:
[core/table.lua:94]:Table = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 9
|  |  |  needcost = 5
|  |  |  sk = 3201
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 6
|  |  |  needcost = 10
|  |  |  sk = 3202
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  needcost = 15
|  |  |  sk = 3204
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  needcost = 25
|  |  |  sk = 3205
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  level = 4
|  |  |  needcost = 18
|  |  |  sk = 3206
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  level = 1
|  |  |  needcost = 18
|  |  |  sk = 3207
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginParSoulPlan = {}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  abnormal_attr_ratio = 1527
|  |  attack = 2204
|  |  critical_damage = 19531
|  |  critical_ratio = 3639
|  |  cure_critical_ratio = 500
|  |  defense = 612
|  |  mask = "87ff00"
|  |  max_hp = 20567
|  |  power = 11704
|  |  res_abnormal_ratio = 1845
|  |  res_critical_ratio = 1517
|  |  speed = 1045
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 1527
|  attack = 2204
|  critical_damage = 19531
|  critical_ratio = 3639
|  cure_critical_ratio = 500
|  defense = 612
|  hp = 0
|  max_hp = 20567
|  power = 11704
|  res_abnormal_ratio = 1845
|  res_critical_ratio = 1517
|  speed = 1045
}
[core/table.lua:94]:-->Net Receive: org.GS2CUpdateOrgInfo = {
|  info = {
|  |  leadername = "废柴v寿司"
|  |  mask = "10"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: org = {
|  leadername = "废柴v寿司"
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1600
|  |  |  }
|  |  |  name = "熊霸"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 210
|  |  npctype = 5014
|  }
|  eid = 2
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 2
|  pos_info = {
|  |  x = 20000
|  |  y = 19000
|  }
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1600
|  }
|  name = "熊霸"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1018
|  |  |  }
|  |  |  name = "甘"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 212
|  |  npctype = 5015
|  }
|  eid = 3
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 3
|  pos_info = {
|  |  x = 17000
|  |  y = 18000
|  }
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1018
|  }
|  name = "甘"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1102
|  |  |  }
|  |  |  name = "峰馆长"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 214
|  |  npctype = 5016
|  }
|  eid = 4
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 4
|  pos_info = {
|  |  x = 24500
|  |  y = 20000
|  }
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1102
|  }
|  name = "峰馆长"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1009
|  |  |  }
|  |  |  name = "桀修"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 216
|  |  npctype = 5017
|  }
|  eid = 5
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 5
|  pos_info = {
|  |  x = 29000
|  |  y = 18000
|  }
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1009
|  }
|  name = "桀修"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1016
|  |  |  }
|  |  |  name = "水生"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 218
|  |  npctype = 5018
|  }
|  eid = 6
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 6
|  pos_info = {
|  |  x = 31000
|  |  y = 15000
|  }
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1016
|  }
|  name = "水生"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  scale = 1
|  |  |  |  shape = 1002
|  |  |  }
|  |  |  name = "看守者"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 295
|  |  npctype = 73001
|  }
|  eid = 7
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 7
|  pos_info = {
|  |  x = 6000
|  |  y = 18000
|  }
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 1
|  |  shape = 1002
|  }
|  name = "看守者"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  scale = 0.69999998807907
|  |  |  |  shape = 3006
|  |  |  }
|  |  |  name = "灵魂宝箱"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 839
|  |  npctype = 70001
|  }
|  eid = 9
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 9
|  pos_info = {
|  |  x = 12320
|  |  y = 7199
|  }
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 0.69999998807907
|  |  shape = 3006
|  }
|  name = "灵魂宝箱"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  scale = 0.69999998807907
|  |  |  |  shape = 3006
|  |  |  }
|  |  |  name = "灵魂宝箱"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 840
|  |  npctype = 70001
|  }
|  eid = 10
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 10
|  pos_info = {
|  |  x = 8480
|  |  y = 13599
|  }
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 0.69999998807907
|  |  shape = 3006
|  }
|  name = "灵魂宝箱"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  scale = 0.69999998807907
|  |  |  |  shape = 3006
|  |  |  }
|  |  |  name = "灵魂宝箱"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 841
|  |  npctype = 70001
|  }
|  eid = 11
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 11
|  pos_info = {
|  |  x = 27040
|  |  y = 18079
|  }
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 0.69999998807907
|  |  shape = 3006
|  }
|  name = "灵魂宝箱"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  scale = 0.69999998807907
|  |  |  |  shape = 3006
|  |  |  }
|  |  |  name = "灵魂宝箱"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 842
|  |  npctype = 70001
|  }
|  eid = 12
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 12
|  pos_info = {
|  |  x = 25120
|  |  y = 15519
|  }
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 0.69999998807907
|  |  shape = 3006
|  }
|  name = "灵魂宝箱"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  scale = 0.69999998807907
|  |  |  |  shape = 3006
|  |  |  }
|  |  |  name = "灵魂宝箱"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 844
|  |  npctype = 70001
|  }
|  eid = 14
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 14
|  pos_info = {
|  |  x = 6560
|  |  y = 6239
|  }
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 0.69999998807907
|  |  shape = 3006
|  }
|  name = "灵魂宝箱"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  scale = 0.69999998807907
|  |  |  |  shape = 3006
|  |  |  }
|  |  |  name = "灵魂宝箱"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 845
|  |  npctype = 70001
|  }
|  eid = 15
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 15
|  pos_info = {
|  |  x = 4319
|  |  y = 18079
|  }
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 0.69999998807907
|  |  shape = 3006
|  }
|  name = "灵魂宝箱"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  scale = 0.69999998807907
|  |  |  |  shape = 3006
|  |  |  }
|  |  |  name = "灵魂宝箱"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 838
|  |  npctype = 70001
|  }
|  eid = 17
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 17
|  pos_info = {
|  |  x = 11040
|  |  y = 10399
|  }
|  scene_id = 14
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 0.69999998807907
|  |  shape = 3006
|  }
|  name = "灵魂宝箱"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: title.GS2CTitleInfoList = {
|  infos = {
|  |  [1] = {
|  |  |  name = "8段"
|  |  |  progress = 1109
|  |  |  tid = 1008
|  |  }
|  |  [10] = {
|  |  |  name = "应用宝贵族"
|  |  |  tid = 1017
|  |  }
|  |  [11] = {
|  |  |  name = "王者公会"
|  |  |  tid = 1018
|  |  }
|  |  [12] = {
|  |  |  name = "王者公会会长"
|  |  |  tid = 1019
|  |  }
|  |  [13] = {
|  |  |  name = "黄金公会"
|  |  |  tid = 1020
|  |  }
|  |  [14] = {
|  |  |  name = "黄金公会会长"
|  |  |  tid = 1021
|  |  }
|  |  [15] = {
|  |  |  name = "白银公会"
|  |  |  tid = 1022
|  |  }
|  |  [16] = {
|  |  |  name = "白银公会会长"
|  |  |  tid = 1023
|  |  }
|  |  [17] = {
|  |  |  create_time = 1723824137
|  |  |  left_time = **********
|  |  |  name = "明日之星"
|  |  |  tid = 1024
|  |  }
|  |  [18] = {
|  |  |  name = "独孤求败"
|  |  |  tid = 1025
|  |  }
|  |  [19] = {
|  |  |  create_time = **********
|  |  |  left_time = **********
|  |  |  name = "谁与争锋"
|  |  |  tid = 1026
|  |  }
|  |  [2] = {
|  |  |  name = "测-贵族宝"
|  |  |  tid = 1009
|  |  }
|  |  [20] = {
|  |  |  name = "所向披靡"
|  |  |  tid = 1027
|  |  }
|  |  [21] = {
|  |  |  name = "地牢终结者"
|  |  |  tid = 1028
|  |  }
|  |  [22] = {
|  |  |  create_time = **********
|  |  |  left_time = **********
|  |  |  name = "地牢征服者"
|  |  |  tid = 1029
|  |  }
|  |  [23] = {
|  |  |  name = "地牢破坏者"
|  |  |  tid = 1030
|  |  }
|  |  [24] = {
|  |  |  name = "眯眯眼是变态"
|  |  |  tid = 1031
|  |  }
|  |  [25] = {
|  |  |  name = "邻家妹妹"
|  |  |  tid = 1032
|  |  }
|  |  [26] = {
|  |  |  name = "兄控"
|  |  |  tid = 1033
|  |  }
|  |  [27] = {
|  |  |  name = "农夫三拳"
|  |  |  tid = 1034
|  |  }
|  |  [28] = {
|  |  |  create_time = **********
|  |  |  left_time = **********
|  |  |  name = "赌神"
|  |  |  progress = 1
|  |  |  tid = 1035
|  |  }
|  |  [29] = {
|  |  |  name = "神之右手"
|  |  |  tid = 1036
|  |  }
|  |  [3] = {
|  |  |  create_time = **********
|  |  |  left_time = **********
|  |  |  name = "测-我就是托"
|  |  |  progress = 1
|  |  |  tid = 1010
|  |  }
|  |  [30] = {
|  |  |  name = "茶会噩梦"
|  |  |  tid = 1037
|  |  }
|  |  [31] = {
|  |  |  name = "我是傲娇"
|  |  |  tid = 1038
|  |  }
|  |  [32] = {
|  |  |  name = "闷骚美男子"
|  |  |  tid = 1039
|  |  }
|  |  [33] = {
|  |  |  name = "脸炮"
|  |  |  tid = 1040
|  |  }
|  |  [34] = {
|  |  |  name = "妹控"
|  |  |  tid = 1041
|  |  }
|  |  [35] = {
|  |  |  name = "守夜人"
|  |  |  tid = 1042
|  |  }
|  |  [36] = {
|  |  |  name = "泡泡娘"
|  |  |  tid = 1043
|  |  }
|  |  [37] = {
|  |  |  name = "姐控"
|  |  |  tid = 1044
|  |  }
|  |  [38] = {
|  |  |  name = "迷妹"
|  |  |  tid = 1045
|  |  }
|  |  [39] = {
|  |  |  name = "小天使"
|  |  |  tid = 1046
|  |  }
|  |  [4] = {
|  |  |  name = "测-粗壮大腿"
|  |  |  tid = 1011
|  |  }
|  |  [40] = {
|  |  |  name = "欺诈兔子"
|  |  |  tid = 1047
|  |  }
|  |  [41] = {
|  |  |  create_time = **********
|  |  |  left_time = **********
|  |  |  name = "天然呆"
|  |  |  progress = 1
|  |  |  tid = 1048
|  |  }
|  |  [42] = {
|  |  |  name = "开局一条狗"
|  |  |  tid = 1049
|  |  }
|  |  [43] = {
|  |  |  name = "种竹专业户"
|  |  |  tid = 1050
|  |  }
|  |  [44] = {
|  |  |  create_time = **********
|  |  |  left_time = **********
|  |  |  name = "顾家好男人"
|  |  |  progress = 1
|  |  |  tid = 1051
|  |  }
|  |  [45] = {
|  |  |  create_time = **********
|  |  |  left_time = **********
|  |  |  name = "无敌小钢炮"
|  |  |  progress = 1
|  |  |  tid = 1052
|  |  }
|  |  [46] = {
|  |  |  name = "小豆丁"
|  |  |  tid = 1053
|  |  }
|  |  [47] = {
|  |  |  create_time = **********
|  |  |  left_time = **********
|  |  |  name = "浪里白条"
|  |  |  progress = 1
|  |  |  tid = 1054
|  |  }
|  |  [48] = {
|  |  |  name = "话唠"
|  |  |  tid = 1055
|  |  }
|  |  [49] = {
|  |  |  name = "角落种蘑菇"
|  |  |  tid = 1056
|  |  }
|  |  [5] = {
|  |  |  name = "攻防战第一公会"
|  |  |  tid = 1012
|  |  }
|  |  [50] = {
|  |  |  name = "天然疯"
|  |  |  tid = 1057
|  |  }
|  |  [51] = {
|  |  |  create_time = **********
|  |  |  left_time = **********
|  |  |  name = "吃货"
|  |  |  progress = 1
|  |  |  tid = 1058
|  |  }
|  |  [52] = {
|  |  |  name = "控制狂"
|  |  |  tid = 1059
|  |  }
|  |  [53] = {
|  |  |  name = "女王大人"
|  |  |  tid = 1060
|  |  }
|  |  [54] = {
|  |  |  name = "八卦小王子"
|  |  |  tid = 1061
|  |  }
|  |  [55] = {
|  |  |  name = "三无"
|  |  |  tid = 1062
|  |  }
|  |  [56] = {
|  |  |  name = "养得累"
|  |  |  tid = 1063
|  |  }
|  |  [57] = {
|  |  |  name = "扑街之王"
|  |  |  tid = 1064
|  |  }
|  |  [58] = {
|  |  |  name = "抖S"
|  |  |  tid = 1065
|  |  }
|  |  [59] = {
|  |  |  name = "工作狂人"
|  |  |  tid = 1066
|  |  }
|  |  [6] = {
|  |  |  name = "攻防战第一司令"
|  |  |  tid = 1013
|  |  }
|  |  [60] = {
|  |  |  create_time = **********
|  |  |  left_time = **********
|  |  |  name = "精分中毒患者"
|  |  |  progress = 1
|  |  |  tid = 1067
|  |  }
|  |  [61] = {
|  |  |  name = "神神叨叨"
|  |  |  tid = 1068
|  |  }
|  |  [62] = {
|  |  |  name = "浮夸之神"
|  |  |  tid = 1069
|  |  }
|  |  [63] = {
|  |  |  name = "抖M"
|  |  |  tid = 1070
|  |  }
|  |  [64] = {
|  |  |  name = "自恋狂"
|  |  |  tid = 1071
|  |  }
|  |  [65] = {
|  |  |  name = "天然黑"
|  |  |  tid = 1072
|  |  }
|  |  [66] = {
|  |  |  name = "为所欲为"
|  |  |  tid = 1073
|  |  }
|  |  [67] = {
|  |  |  name = "氪金大佬"
|  |  |  tid = 1074
|  |  }
|  |  [68] = {
|  |  |  name = "应用宝帝王"
|  |  |  tid = 1075
|  |  }
|  |  [69] = {
|  |  |  name = "应用宝至尊"
|  |  |  tid = 1076
|  |  }
|  |  [7] = {
|  |  |  name = "梦魇之主"
|  |  |  tid = 1014
|  |  }
|  |  [70] = {
|  |  |  name = "公会新人"
|  |  |  tid = 1077
|  |  }
|  |  [71] = {
|  |  |  name = "公会成员"
|  |  |  tid = 1078
|  |  }
|  |  [72] = {
|  |  |  name = "公会精英"
|  |  |  tid = 1079
|  |  }
|  |  [73] = {
|  |  |  name = "公会副会"
|  |  |  tid = 1080
|  |  }
|  |  [74] = {
|  |  |  create_time = 1724084072
|  |  |  left_time = **********
|  |  |  name = "玩了个寂寞会长"
|  |  |  tid = 1081
|  |  }
|  |  [75] = {
|  |  |  name = "情侣专属称谓"
|  |  |  tid = 1082
|  |  }
|  |  [76] = {
|  |  |  name = "命运之子"
|  |  |  tid = 1083
|  |  }
|  |  [77] = {
|  |  |  name = "不做大多数"
|  |  |  tid = 1084
|  |  }
|  |  [78] = {
|  |  |  create_time = **********
|  |  |  left_time = **********
|  |  |  name = "1段"
|  |  |  tid = 1001
|  |  }
|  |  [79] = {
|  |  |  name = "2段"
|  |  |  progress = 1109
|  |  |  tid = 1002
|  |  }
|  |  [8] = {
|  |  |  create_time = 1724169600
|  |  |  left_time = **********
|  |  |  name = "梦魇骑士"
|  |  |  tid = 1015
|  |  }
|  |  [80] = {
|  |  |  name = "3段"
|  |  |  progress = 1109
|  |  |  tid = 1003
|  |  }
|  |  [81] = {
|  |  |  name = "4段"
|  |  |  progress = 1109
|  |  |  tid = 1004
|  |  }
|  |  [82] = {
|  |  |  name = "5段"
|  |  |  progress = 1109
|  |  |  tid = 1005
|  |  }
|  |  [83] = {
|  |  |  name = "6段"
|  |  |  progress = 1109
|  |  |  tid = 1006
|  |  }
|  |  [84] = {
|  |  |  name = "7段"
|  |  |  progress = 1109
|  |  |  tid = 1007
|  |  }
|  |  [9] = {
|  |  |  name = "梦魇猎人"
|  |  |  tid = 1016
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = **********
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2024/08/21 13:38:22
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewBase.lua:125]:CConvoyingView LoadDone!
[core/table.lua:94]:-->Net Receive: friend.GS2CSendSimpleInfo = {
|  frdlist = {
|  |  [1] = {
|  |  |  grade = 66
|  |  |  name = "慌乱v最游记"
|  |  |  pid = 40035
|  |  }
|  |  [2] = {
|  |  |  grade = 60
|  |  |  name = "夜色"
|  |  |  pid = 40006
|  |  }
|  |  [3] = {
|  |  |  grade = 70
|  |  |  name = "废材"
|  |  |  pid = 40023
|  |  }
|  |  [4] = {
|  |  |  grade = 35
|  |  |  name = "流鼻血与蛋疼"
|  |  |  pid = 40034
|  |  }
|  }
}
[logic/ui/CViewBase.lua:125]:CMainMenuRedPackedView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CMainMenuRedPackedView     CloseView
[logic/base/CResCtrl.lua:626]:res gc finish!
[logic/platform/CSdkCtrl.lua:380]:sdk upload: not init
[core/table.lua:94]:Table = {
|  [1] = "16"
|  [2] = "20"
}
[core/global.lua:59]:<color=#ffeb04>66 16 20</color>
[core/table.lua:94]:Table = {
|  [1] = "21"
|  [2] = "24"
}
[core/global.lua:59]:<color=#ffeb04>66 21 24</color>
[core/table.lua:94]:Table = {
|  [1] = "25"
|  [2] = "27"
}
[core/global.lua:59]:<color=#ffeb04>66 25 27</color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/activity/CActivityCtrl"]:380: in function 'ClickTargetCheck'
	[string "logic/map/CMapTouchCtrl"]:128: in function <[string "logic/map/CMapTouchCtrl"]:87>
	[string "logic/base/CEasyTouchCtrl"]:73: in function <[string "logic/base/CEasyTouchCtrl"]:68>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/activity/CActivityCtrl"]:380: in function 'ClickTargetCheck'
	[string "logic/map/CMapTouchCtrl"]:128: in function <[string "logic/map/CMapTouchCtrl"]:87>
	[string "logic/base/CEasyTouchCtrl"]:73: in function <[string "logic/base/CEasyTouchCtrl"]:68>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/activity/CActivityCtrl"]:380: in function 'ClickTargetCheck'
	[string "logic/map/CMapTouchCtrl"]:128: in function <[string "logic/map/CMapTouchCtrl"]:87>
	[string "logic/base/CEasyTouchCtrl"]:73: in function <[string "logic/base/CEasyTouchCtrl"]:68>
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/activity/CActivityCtrl"]:380: in function 'ClickTargetCheck'
	[string "logic/map/CMapTouchCtrl"]:128: in function <[string "logic/map/CMapTouchCtrl"]:87>
	[string "logic/base/CEasyTouchCtrl"]:73: in function <[string "logic/base/CEasyTouchCtrl"]:68>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1724218712
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2024/08/21 13:38:32
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/activity/CActivityCtrl"]:380: in function 'ClickTargetCheck'
	[string "logic/map/CMapTouchCtrl"]:128: in function <[string "logic/map/CMapTouchCtrl"]:87>
	[string "logic/base/CEasyTouchCtrl"]:73: in function <[string "logic/base/CEasyTouchCtrl"]:68>
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/activity/CActivityCtrl"]:380: in function 'ClickTargetCheck'
	[string "logic/map/CMapTouchCtrl"]:128: in function <[string "logic/map/CMapTouchCtrl"]:87>
	[string "logic/base/CEasyTouchCtrl"]:73: in function <[string "logic/base/CEasyTouchCtrl"]:68>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/activity/CActivityCtrl"]:380: in function 'ClickTargetCheck'
	[string "logic/map/CMapTouchCtrl"]:128: in function <[string "logic/map/CMapTouchCtrl"]:87>
	[string "logic/base/CEasyTouchCtrl"]:73: in function <[string "logic/base/CEasyTouchCtrl"]:68>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/activity/CActivityCtrl"]:380: in function 'ClickTargetCheck'
	[string "logic/map/CMapTouchCtrl"]:128: in function <[string "logic/map/CMapTouchCtrl"]:87>
	[string "logic/base/CEasyTouchCtrl"]:73: in function <[string "logic/base/CEasyTouchCtrl"]:68>
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/activity/CActivityCtrl"]:380: in function 'ClickTargetCheck'
	[string "logic/map/CMapTouchCtrl"]:128: in function <[string "logic/map/CMapTouchCtrl"]:87>
	[string "logic/base/CEasyTouchCtrl"]:73: in function <[string "logic/base/CEasyTouchCtrl"]:68>
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/activity/CActivityCtrl"]:380: in function 'ClickTargetCheck'
	[string "logic/map/CMapTouchCtrl"]:128: in function <[string "logic/map/CMapTouchCtrl"]:87>
	[string "logic/base/CEasyTouchCtrl"]:73: in function <[string "logic/base/CEasyTouchCtrl"]:68>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1724218722
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2024/08/21 13:38:42
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/activity/CActivityCtrl"]:380: in function 'ClickTargetCheck'
	[string "logic/map/CMapTouchCtrl"]:128: in function <[string "logic/map/CMapTouchCtrl"]:87>
	[string "logic/base/CEasyTouchCtrl"]:73: in function <[string "logic/base/CEasyTouchCtrl"]:68>
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/activity/CActivityCtrl"]:380: in function 'ClickTargetCheck'
	[string "logic/map/CMapTouchCtrl"]:128: in function <[string "logic/map/CMapTouchCtrl"]:87>
	[string "logic/base/CEasyTouchCtrl"]:73: in function <[string "logic/base/CEasyTouchCtrl"]:68>
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/activity/CActivityCtrl"]:380: in function 'ClickTargetCheck'
	[string "logic/map/CMapTouchCtrl"]:128: in function <[string "logic/map/CMapTouchCtrl"]:87>
	[string "logic/base/CEasyTouchCtrl"]:73: in function <[string "logic/base/CEasyTouchCtrl"]:68>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/activity/CActivityCtrl"]:380: in function 'ClickTargetCheck'
	[string "logic/map/CMapTouchCtrl"]:128: in function <[string "logic/map/CMapTouchCtrl"]:87>
	[string "logic/base/CEasyTouchCtrl"]:73: in function <[string "logic/base/CEasyTouchCtrl"]:68>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:<--Net Send: huodong.C2GSGiveUpConvoy = {}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8003
}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x400531f8"
|  |  AssociatedPick = "function: 0x40053258"
|  |  AssociatedSubmit = "function: 0x40053228"
|  |  CreateDefalutData = "function: 0x4005a050"
|  |  GetChaptetFubenData = "function: 0x400555e0"
|  |  GetProgressThing = "function: 0x400550f8"
|  |  GetRemainTime = "function: 0x40055520"
|  |  GetStatus = "function: 0x40055158"
|  |  GetTaskClientExtStrDic = "function: 0x400550c8"
|  |  GetTaskTypeSpriteteName = "function: 0x400555b0"
|  |  GetTraceInfo = "function: 0x40055640"
|  |  GetTraceNpcType = "function: 0x40055580"
|  |  GetValue = "function: 0x40053290"
|  |  IsAbandon = "function: 0x4005a478"
|  |  IsAddEscortDynamicNpc = "function: 0x40055490"
|  |  IsMissMengTask = "function: 0x40055550"
|  |  IsPassChaterFuben = "function: 0x40055610"
|  |  IsTaskSpecityAction = "function: 0x4005a4a8"
|  |  IsTaskSpecityCategory = "function: 0x400531c8"
|  |  New = "function: 0x40052758"
|  |  NewByData = "function: 0x4005a4e0"
|  |  RaiseProgressIdx = "function: 0x40055128"
|  |  RefreshTask = "function: 0x400532c0"
|  |  ResetEndTime = "function: 0x400554f0"
|  |  SetStatus = "function: 0x400554c0"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4005a088"
|  }
|  m_CData = {
|  |  id = 999999
|  |  name = "未导表任务:999999"
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5016
|  |  autotype = 0
|  |  detaildesc = ""
|  |  isdone = 0
|  |  name = ""
|  |  playid = 0
|  |  rewardinfo = 0
|  |  statusinfo = {
|  |  |  status = 2
|  |  }
|  |  submitRewardStr = {}
|  |  target = 0
|  |  targetdesc = ""
|  |  taskid = 999999
|  |  tasktype = 0
|  |  time = 0
|  |  type = 0
|  }
|  m_TaskType = {
|  |  id = 0
|  |  name = "未导表任务分类信息:0"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8003
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickConvoyNpc = {
|  npcid = 214
}
