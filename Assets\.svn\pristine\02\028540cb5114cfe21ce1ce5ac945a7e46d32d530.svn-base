fileFormatVersion: 2
guid: 01589e2bea183054ca498409ba3b09d1
timeCreated: **********
licenseType: Pro
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: Bone036
    100002: Bone037
    100004: Bone038
    100006: Bone039
    100008: Bone040
    100010: Bone041
    100012: Bone042
    100014: Bone043
    100016: Bone044
    100018: Bone045
    100020: Bone046
    100022: Bone047
    100024: Bone048
    100026: Bone049
    100028: Bone050
    100030: Bone051
    100032: Bone052
    100034: Bone053
    100036: chibang007
    100038: chibang06
    100040: Point001
    100042: //RootNode
    100044: Particle View 01
    100046: Particle View 01 1
    100048: Particle View 01 2
    400000: Bone036
    400002: Bone037
    400004: Bone038
    400006: Bone039
    400008: Bone040
    400010: Bone041
    400012: Bone042
    400014: Bone043
    400016: Bone044
    400018: Bone045
    400020: Bone046
    400022: Bone047
    400024: Bone048
    400026: Bone049
    400028: Bone050
    400030: Bone051
    400032: Bone052
    400034: Bone053
    400036: chibang007
    400038: chibang06
    400040: Point001
    400042: //RootNode
    400044: Particle View 01
    400046: Particle View 01 1
    400048: Particle View 01 2
    2300000: chibang007
    2300002: chibang06
    3300000: chibang007
    3300002: chibang06
    4300000: chibang06
    4300002: chibang007
    7400000: Take 001
    9500000: //RootNode
    11100000: //RootNode
    13700000: chibang007
    13700002: chibang06
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 1
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
